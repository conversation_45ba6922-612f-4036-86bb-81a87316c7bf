#!/usr/bin/env python3
"""
测试RL集成到现有回测系统

这个脚本测试新的 --rl-model 参数是否正确集成到 backtest_money_quick.py 中
"""

import subprocess
import sys
import os
from pathlib import Path

def test_help_message():
    """测试帮助信息是否包含新的RL参数"""
    print("🧪 测试1: 检查帮助信息中的RL参数...")
    
    try:
        result = subprocess.run([
            sys.executable, "backtest_money_quick.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        help_text = result.stdout
        
        # 检查是否包含RL相关参数
        rl_params = ["--rl-model", "--rl-config"]
        missing_params = []
        
        for param in rl_params:
            if param not in help_text:
                missing_params.append(param)
        
        if missing_params:
            print(f"❌ 缺少RL参数: {missing_params}")
            return False
        else:
            print("✅ 所有RL参数都已添加到帮助信息中")
            return True
            
    except subprocess.TimeoutExpired:
        print("❌ 帮助命令超时")
        return False
    except Exception as e:
        print(f"❌ 运行帮助命令时出错: {e}")
        return False

def test_rl_mode_without_model():
    """测试没有RL模型时的错误处理"""
    print("\n🧪 测试2: 测试缺少RL模型时的错误处理...")
    
    try:
        result = subprocess.run([
            sys.executable, "backtest_money_quick.py",
            "--rl-model", "nonexistent_model",
            "--coin", "ETH",
            "--interval", "5m"
        ], capture_output=True, text=True, timeout=30)
        
        # 应该返回非零退出码
        if result.returncode != 0:
            print("✅ 正确处理了缺失的RL模型文件")
            return True
        else:
            print("❌ 没有正确处理缺失的RL模型文件")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 命令超时")
        return False
    except Exception as e:
        print(f"❌ 运行命令时出错: {e}")
        return False

def test_traditional_mode():
    """测试传统模式是否仍然工作"""
    print("\n🧪 测试3: 测试传统模式是否仍然工作...")
    
    # 检查是否有必要的模型文件
    model_files = [
        "models/eth_5m_model.joblib",
        "models/eth_5m_config.json"
    ]
    
    missing_files = [f for f in model_files if not os.path.exists(f)]
    if missing_files:
        print(f"⚠️ 跳过测试，缺少模型文件: {missing_files}")
        return True
    
    try:
        # 运行一个非常短的回测来测试基本功能
        result = subprocess.run([
            sys.executable, "backtest_money_quick.py",
            "--coin", "ETH",
            "--interval", "5m",
            "--start-time", "2024-01-01 00:00",
            "--end-time", "2024-01-01 01:00",  # 只测试1小时的数据
            "--initial-capital", "1000",
            "--quick"
        ], capture_output=True, text=True, timeout=60)
        
        # 检查是否成功运行（可能没有数据，但不应该崩溃）
        if "传统回测模式" in result.stdout or "数据不足" in result.stdout or "未加载到任何数据" in result.stdout:
            print("✅ 传统模式正常工作")
            return True
        else:
            print(f"❌ 传统模式可能有问题")
            print(f"stdout: {result.stdout[:500]}...")
            print(f"stderr: {result.stderr[:500]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 传统模式测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试传统模式时出错: {e}")
        return False

def test_import_compatibility():
    """测试导入兼容性"""
    print("\n🧪 测试4: 测试导入兼容性...")
    
    try:
        # 测试能否导入修改后的模块
        result = subprocess.run([
            sys.executable, "-c", 
            "import backtest_money_quick; print('导入成功')"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "导入成功" in result.stdout:
            print("✅ 模块导入成功")
            return True
        else:
            print(f"❌ 模块导入失败")
            print(f"stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 导入测试超时")
        return False
    except Exception as e:
        print(f"❌ 导入测试出错: {e}")
        return False

def main():
    """运行所有测试"""
    print("🚀 开始测试RL集成...")
    print("=" * 50)
    
    tests = [
        test_import_compatibility,
        test_help_message,
        test_rl_mode_without_model,
        test_traditional_mode
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！RL集成成功")
        return True
    else:
        print("⚠️ 部分测试失败，请检查集成")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)