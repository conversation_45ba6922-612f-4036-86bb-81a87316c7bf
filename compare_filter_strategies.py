#!/usr/bin/env python3
# compare_filter_strategies.py
# 比较不同过滤策略的效果

import pandas as pd
import json
from backtest_after_filter import BacktestFilter

def compare_strategies():
    """比较不同过滤策略"""
    
    csv_file = "backtest_money_log_quick.csv"
    initial_capital = 1000
    
    try:
        filter_engine = BacktestFilter(csv_file)
        print(f"📊 原始数据: {len(filter_engine.original_df)} 条交易记录")
        
        # 计算原始性能
        original_df = filter_engine.recalculate_capital_and_metrics(
            filter_engine.original_df.copy(), initial_capital
        )
        original_metrics = filter_engine.calculate_performance_metrics(original_df, initial_capital)
        
        # 定义不同的过滤策略
        strategies = {
            "保守策略": {
                "confidence_filter": {"enabled": True, "min_confidence": 0.8},
                "consecutive_filter": {"enabled": True, "max_consecutive_losses": 2}
            },
            "平衡策略": {
                "confidence_filter": {"enabled": True, "min_confidence": 0.7},
                "consecutive_filter": {"enabled": True, "max_consecutive_losses": 3}
            },
            "积极策略": {
                "confidence_filter": {"enabled": True, "min_confidence": 0.65},
                "consecutive_filter": {"enabled": True, "max_consecutive_losses": 4}
            },
            "时间优化策略": {
                "confidence_filter": {"enabled": True, "min_confidence": 0.7},
                "time_filter": {
                    "enabled": True, 
                    "allowed_hours": [10, 11, 14, 15, 16, 20, 21]
                },
                "consecutive_filter": {"enabled": True, "max_consecutive_losses": 3}
            }
        }
        
        results = []
        
        print("\n" + "="*80)
        print("🚀 策略对比分析")
        print("="*80)
        
        # 原始策略
        results.append({
            "策略名称": "原始策略",
            "交易数量": original_metrics['total_trades'],
            "总收益率": f"{original_metrics['total_return_pct']:.2f}%",
            "胜率": f"{original_metrics['win_rate']:.2f}%",
            "最大回撤": f"{original_metrics['max_drawdown_pct']:.2f}%",
            "夏普比率": f"{original_metrics['sharpe_ratio']:.3f}",
            "盈亏比": f"{original_metrics['profit_factor']:.2f}" if original_metrics['profit_factor'] != float('inf') else "∞"
        })
        
        # 测试各种策略
        for strategy_name, config in strategies.items():
            print(f"\n--- 测试 {strategy_name} ---")
            
            # 设置完整配置
            full_config = {
                "initial_capital": initial_capital,
                "confidence_filter": {"enabled": False},
                "ma_filter": {"enabled": False},
                "volume_filter": {"enabled": False},
                "time_filter": {"enabled": False},
                "consecutive_filter": {"enabled": False}
            }
            full_config.update(config)
            
            # 运行过滤
            filtered_df, metrics = filter_engine.run_comprehensive_filter(full_config)
            
            if len(filtered_df) > 0:
                results.append({
                    "策略名称": strategy_name,
                    "交易数量": metrics['total_trades'],
                    "总收益率": f"{metrics['total_return_pct']:.2f}%",
                    "胜率": f"{metrics['win_rate']:.2f}%",
                    "最大回撤": f"{metrics['max_drawdown_pct']:.2f}%",
                    "夏普比率": f"{metrics['sharpe_ratio']:.3f}",
                    "盈亏比": f"{metrics['profit_factor']:.2f}" if metrics['profit_factor'] != float('inf') else "∞"
                })
            else:
                results.append({
                    "策略名称": strategy_name,
                    "交易数量": 0,
                    "总收益率": "0.00%",
                    "胜率": "0.00%",
                    "最大回撤": "0.00%",
                    "夏普比率": "0.000",
                    "盈亏比": "0.00"
                })
        
        # 创建对比表格
        results_df = pd.DataFrame(results)
        
        print(f"\n📊 策略对比结果:")
        print("="*100)
        print(results_df.to_string(index=False))
        
        # 保存结果
        results_df.to_csv("strategy_comparison.csv", index=False)
        print(f"\n💾 对比结果已保存到: strategy_comparison.csv")
        
        # 找出最佳策略
        numeric_results = results_df.copy()
        numeric_results['总收益率_数值'] = numeric_results['总收益率'].str.replace('%', '').astype(float)
        numeric_results['夏普比率_数值'] = numeric_results['夏普比率'].astype(float)
        
        best_return = numeric_results.loc[numeric_results['总收益率_数值'].idxmax()]
        best_sharpe = numeric_results.loc[numeric_results['夏普比率_数值'].idxmax()]
        
        print(f"\n🏆 最佳收益策略: {best_return['策略名称']} (收益率: {best_return['总收益率']})")
        print(f"🏆 最佳风险调整收益策略: {best_sharpe['策略名称']} (夏普比率: {best_sharpe['夏普比率']})")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    compare_strategies()