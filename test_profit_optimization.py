#!/usr/bin/env python3
"""
测试收益优化的训练脚本
"""

import subprocess
import sys
import os

def test_profit_optimization():
    """测试收益优化功能"""
    print("=== 测试收益优化训练 ===")
    
    # 检查必要文件是否存在
    required_files = ['trainopt2.py', 'model_utils_815.py', 'data_loader.py']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少必要文件: {file}")
            return False
    
    # 测试命令
    test_commands = [
        # 使用收益优化
        [
            'python', 'trainopt2.py', 
            '--coin', 'ETH',
            '--optimize-profit',
            '--cv-trials', '10',  # 减少试验次数以加快测试
            '--cv-splits', '3',
            '--no-time-series-cv'  # 先测试不使用CV的情况
        ],
        # 使用AUC优化（对比）
        [
            'python', 'trainopt2.py', 
            '--coin', 'ETH',
            '--use-auc',
            '--cv-trials', '10',
            '--cv-splits', '3',
            '--no-time-series-cv'
        ]
    ]
    
    for i, cmd in enumerate(test_commands):
        print(f"\n--- 测试 {i+1}: {' '.join(cmd)} ---")
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                print("✅ 测试成功")
                print("输出摘要:")
                lines = result.stdout.split('\n')
                for line in lines[-10:]:  # 显示最后10行
                    if line.strip():
                        print(f"  {line}")
            else:
                print("❌ 测试失败")
                print("错误信息:")
                print(result.stderr)
                return False
        except subprocess.TimeoutExpired:
            print("⏰ 测试超时")
            return False
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            return False
    
    print("\n✅ 所有测试完成")
    return True

if __name__ == '__main__':
    success = test_profit_optimization()
    sys.exit(0 if success else 1)