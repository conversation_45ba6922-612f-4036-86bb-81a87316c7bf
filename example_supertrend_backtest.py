#!/usr/bin/env python3
"""
SuperTrend 过滤回测示例
演示如何使用 SuperTrend 指标来过滤交易信号
"""

import subprocess
import sys
import os
from datetime import datetime, timedelta

def run_backtest_comparison():
    """运行对比回测：有无 SuperTrend 过滤的效果对比"""
    
    print("=== SuperTrend 过滤回测对比示例 ===")
    print("本示例将运行两次回测进行对比：")
    print("1. 不使用 SuperTrend 过滤")
    print("2. 使用 SuperTrend 过滤")
    print()
    
    # 基础参数
    base_params = [
        "python", "backtest_money_quick.py",
        "--coin", "ETH",
        "--interval", "5m",
        "--market", "spot",
        "--db", "coin_data.db",
        "--initial-capital", "1000",
        "--risk-per-trade", "2.0",
        "--max-active-predictions", "10",
        "--quick"
    ]
    
    # 添加时间范围（最近7天）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    base_params.extend([
        "--start-time", start_date.strftime("%Y-%m-%d"),
        "--end-time", end_date.strftime("%Y-%m-%d")
    ])
    
    print("回测参数:")
    print(f"  币种: ETH")
    print(f"  时间间隔: 5m")
    print(f"  时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    print(f"  初始资金: $1000")
    print(f"  单次风险: 2%")
    print()
    
    # 第一次回测：不使用 SuperTrend
    print("🔄 运行回测 1: 不使用 SuperTrend 过滤")
    print("-" * 50)
    
    try:
        result1 = subprocess.run(base_params, capture_output=True, text=True, timeout=300)
        if result1.returncode == 0:
            print("✅ 回测 1 完成")
            # 重命名输出文件
            if os.path.exists("backtest_money_log_quick.csv"):
                os.rename("backtest_money_log_quick.csv", "backtest_no_supertrend.csv")
                print("📊 结果保存为: backtest_no_supertrend.csv")
        else:
            print("❌ 回测 1 失败:")
            print(result1.stderr)
            return
    except subprocess.TimeoutExpired:
        print("❌ 回测 1 超时")
        return
    except Exception as e:
        print(f"❌ 回测 1 出错: {e}")
        return
    
    print()
    
    # 第二次回测：使用 SuperTrend
    print("🔄 运行回测 2: 使用 SuperTrend 过滤")
    print("-" * 50)
    
    supertrend_params = base_params + [
        "--use-supertrend",
        "--supertrend-interval", "15m",
        "--supertrend-atr-period", "10",
        "--supertrend-multiplier", "3.0"
    ]
    
    try:
        result2 = subprocess.run(supertrend_params, capture_output=True, text=True, timeout=300)
        if result2.returncode == 0:
            print("✅ 回测 2 完成")
            # 重命名输出文件
            if os.path.exists("backtest_money_log_quick.csv"):
                os.rename("backtest_money_log_quick.csv", "backtest_with_supertrend.csv")
                print("📊 结果保存为: backtest_with_supertrend.csv")
        else:
            print("❌ 回测 2 失败:")
            print(result2.stderr)
            return
    except subprocess.TimeoutExpired:
        print("❌ 回测 2 超时")
        return
    except Exception as e:
        print(f"❌ 回测 2 出错: {e}")
        return
    
    print()
    print("=" * 60)
    print("✅ 对比回测完成！")
    print()
    print("结果文件:")
    print("  📄 backtest_no_supertrend.csv    - 无 SuperTrend 过滤")
    print("  📄 backtest_with_supertrend.csv  - 有 SuperTrend 过滤")
    print()
    print("可以对比以下指标:")
    print("  • 总预测数量")
    print("  • 预测成功率")
    print("  • 总收益率")
    print("  • 最大回撤")
    print("  • 风险调整收益")

def show_usage_examples():
    """显示使用示例"""
    print("\n=== SuperTrend 回测使用示例 ===")
    
    examples = [
        {
            "title": "基础 SuperTrend 过滤",
            "command": "python backtest_money_quick.py --coin ETH --interval 5m --use-supertrend --quick",
            "description": "使用默认 SuperTrend 参数（15m 间隔，ATR周期10，倍数3.0）"
        },
        {
            "title": "自定义 SuperTrend 参数",
            "command": "python backtest_money_quick.py --coin BTC --interval 5m --use-supertrend --supertrend-interval 1h --supertrend-atr-period 14 --supertrend-multiplier 2.5 --quick",
            "description": "使用 1 小时 SuperTrend，ATR周期14，倍数2.5"
        },
        {
            "title": "结合时间过滤",
            "command": "python backtest_money_quick.py --coin ETH --interval 5m --use-supertrend --use-chushou --start-time '2024-01-01' --end-time '2024-01-31' --quick",
            "description": "同时使用 SuperTrend 过滤和时间段过滤"
        },
        {
            "title": "添加止损",
            "command": "python backtest_money_quick.py --coin ETH --interval 5m --use-supertrend --stop-loss 2.0 --risk-per-trade 1.5 --quick",
            "description": "SuperTrend 过滤 + 2% 止损 + 1.5% 单次风险"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}")
        print(f"   {example['description']}")
        print(f"   命令: {example['command']}")
    
    print(f"\n参数说明:")
    print(f"  --use-supertrend              启用 SuperTrend 过滤")
    print(f"  --supertrend-interval 15m     SuperTrend 计算时间间隔")
    print(f"  --supertrend-atr-period 10    ATR 计算周期")
    print(f"  --supertrend-multiplier 3.0   ATR 倍数")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--run-comparison":
        run_backtest_comparison()
    else:
        print("SuperTrend 过滤回测功能示例")
        print("=" * 40)
        
        print("\n选项:")
        print("1. 运行对比回测示例")
        print("2. 查看使用示例")
        print("3. 退出")
        
        while True:
            try:
                choice = input("\n请选择 (1-3): ").strip()
                
                if choice == "1":
                    run_backtest_comparison()
                    break
                elif choice == "2":
                    show_usage_examples()
                    break
                elif choice == "3":
                    print("退出")
                    break
                else:
                    print("无效选择，请输入 1-3")
            except KeyboardInterrupt:
                print("\n\n退出")
                break

if __name__ == "__main__":
    main()