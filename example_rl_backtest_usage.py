#!/usr/bin/env python3
"""
RL回测使用示例

这个脚本展示如何使用新集成的RL功能进行回测
"""

import os
import subprocess
import sys
from pathlib import Path

def check_requirements():
    """检查运行RL回测的必要条件"""
    print("🔍 检查运行条件...")
    
    requirements = {
        'backtest_script': 'backtest_money_quick.py',
        'database': 'coin_data.db',
        'model_file': 'models/eth_5m_model.joblib',
        'config_file': 'models/eth_5m_config.json'
    }
    
    missing = []
    for name, path in requirements.items():
        if not os.path.exists(path):
            missing.append(f"{name}: {path}")
    
    if missing:
        print("❌ 缺少必要文件:")
        for item in missing:
            print(f"   - {item}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def show_help():
    """显示帮助信息"""
    print("\n📖 RL回测使用指南")
    print("=" * 50)
    print("""
1. 基本用法 - 传统模式:
   python backtest_money_quick.py --coin ETH --interval 5m --quick

2. RL模式 - 需要训练好的RL模型:
   python backtest_money_quick.py --coin ETH --interval 5m --quick --rl-model path/to/rl_model

3. RL模式 - 带自定义配置:
   python backtest_money_quick.py --coin ETH --interval 5m --quick \\
       --rl-model path/to/rl_model --rl-config path/to/rl_config.json

4. 完整参数示例:
   python backtest_money_quick.py \\
       --coin ETH \\
       --interval 5m \\
       --start-time "2024-01-01 00:00" \\
       --end-time "2024-01-31 23:59" \\
       --initial-capital 10000 \\
       --risk-per-trade 2.0 \\
       --rl-model trained_models/eth_5m_rl_agent \\
       --rl-config configs/rl_config.json \\
       --quick

参数说明:
- --rl-model: RL模型路径（不包含.pth扩展名）
- --rl-config: RL环境配置文件（可选）
- --quick: 使用快速模式（推荐）
- 其他参数与传统模式相同
""")

def run_example_traditional():
    """运行传统模式示例"""
    print("\n🚀 运行传统模式示例...")
    
    cmd = [
        sys.executable, "backtest_money_quick.py",
        "--coin", "ETH",
        "--interval", "5m", 
        "--start-time", "2024-08-01 00:00",
        "--end-time", "2024-08-01 12:00",  # 只测试12小时
        "--initial-capital", "10000",
        "--risk-per-trade", "1.0",
        "--quick"
    ]
    
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, timeout=120)
        if result.returncode == 0:
            print("✅ 传统模式示例运行成功")
            return True
        else:
            print("❌ 传统模式示例运行失败")
            return False
    except subprocess.TimeoutExpired:
        print("❌ 传统模式示例超时")
        return False
    except Exception as e:
        print(f"❌ 运行传统模式示例时出错: {e}")
        return False

def run_example_rl():
    """运行RL模式示例（如果有训练好的模型）"""
    print("\n🤖 检查RL模式示例...")
    
    # 查找可能的RL模型
    rl_model_paths = [
        "trained_models/eth_5m_rl_agent",
        "rl/trained_models/eth_5m_rl_agent", 
        "models/eth_5m_rl_agent"
    ]
    
    rl_model = None
    for path in rl_model_paths:
        if os.path.exists(f"{path}.pth"):
            rl_model = path
            break
    
    if not rl_model:
        print("⚠️ 未找到训练好的RL模型，跳过RL模式示例")
        print("   要运行RL模式，请先训练RL模型:")
        print("   python rl/train_rl_agent.py --model-file models/eth_5m_model.joblib")
        return True
    
    print(f"✅ 找到RL模型: {rl_model}")
    
    cmd = [
        sys.executable, "backtest_money_quick.py",
        "--coin", "ETH",
        "--interval", "5m",
        "--start-time", "2024-08-01 00:00", 
        "--end-time", "2024-08-01 12:00",  # 只测试12小时
        "--initial-capital", "10000",
        "--risk-per-trade", "1.0",
        "--rl-model", rl_model,
        "--quick"
    ]
    
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, timeout=120)
        if result.returncode == 0:
            print("✅ RL模式示例运行成功")
            return True
        else:
            print("❌ RL模式示例运行失败")
            return False
    except subprocess.TimeoutExpired:
        print("❌ RL模式示例超时")
        return False
    except Exception as e:
        print(f"❌ 运行RL模式示例时出错: {e}")
        return False

def show_output_files():
    """显示输出文件信息"""
    print("\n📁 输出文件说明:")
    print("=" * 30)
    
    output_files = {
        'backtest_money_log_quick.csv': '传统模式详细回测日志',
        'backtest_rl_log_quick.csv': 'RL模式详细回测日志',
        'backtest_rl_decisions.csv': 'RL决策历史记录',
        'backtest_money_quick_analysis.png': '传统模式分析图表',
        'backtest_rl_quick_analysis.png': 'RL模式分析图表'
    }
    
    for filename, description in output_files.items():
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"✅ {filename} ({size} bytes) - {description}")
        else:
            print(f"⚪ {filename} - {description}")

def main():
    """主函数"""
    print("🎯 RL回测集成使用示例")
    print("=" * 40)
    
    # 检查运行条件
    if not check_requirements():
        print("\n❌ 请先准备必要的文件后再运行示例")
        return False
    
    # 显示帮助信息
    show_help()
    
    # 询问用户是否要运行示例
    print("\n" + "=" * 40)
    response = input("是否运行示例？(y/N): ").strip().lower()
    
    if response not in ['y', 'yes']:
        print("👋 示例已跳过")
        return True
    
    # 运行传统模式示例
    success_traditional = run_example_traditional()
    
    # 运行RL模式示例
    success_rl = run_example_rl()
    
    # 显示输出文件
    show_output_files()
    
    # 总结
    print("\n" + "=" * 40)
    print("📊 示例运行总结:")
    print(f"传统模式: {'✅ 成功' if success_traditional else '❌ 失败'}")
    print(f"RL模式: {'✅ 成功' if success_rl else '⚠️ 跳过/失败'}")
    
    if success_traditional:
        print("\n🎉 RL集成功能正常工作！")
        print("现在你可以:")
        print("1. 训练自己的RL模型")
        print("2. 使用 --rl-model 参数进行RL回测")
        print("3. 使用 compare_rl_vs_traditional.py 比较性能")
        return True
    else:
        print("\n⚠️ 存在一些问题，请检查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)