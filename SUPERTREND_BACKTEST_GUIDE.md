# SuperTrend 回测过滤功能使用指南

## 功能概述

本功能为 `backtest_money_quick.py` 添加了 SuperTrend 指标过滤，可以根据 SuperTrend 趋势方向来过滤交易信号：
- **SuperTrend 看涨时**：只执行做多预测（prediction=1）
- **SuperTrend 看跌时**：只执行做空预测（prediction=0）

## SuperTrend 指标说明

SuperTrend 是一个基于 ATR（平均真实波幅）的趋势跟踪指标：
- **参数**：
  - `ATR Period`：ATR 计算周期，默认 10
  - `Multiplier`：ATR 倍数，默认 3.0
- **信号**：
  - `trend = 1`：上涨趋势（看涨）
  - `trend = -1`：下跌趋势（看跌）

## 新增命令行参数

```bash
# SuperTrend 过滤相关参数
--use-supertrend              # 启用SuperTrend过滤
--supertrend-interval 15m     # SuperTrend计算使用的K线间隔（默认: 15m）
--supertrend-atr-period 10    # ATR计算周期（默认: 10）
--supertrend-multiplier 3.0   # ATR倍数（默认: 3.0）
```

## 使用示例

### 1. 基础 SuperTrend 过滤
```bash
# 使用默认参数启用SuperTrend过滤
python backtest_money_quick.py --coin ETH --interval 5m --use-supertrend --quick

# 指定SuperTrend参数
python backtest_money_quick.py \
    --coin ETH --interval 5m \
    --use-supertrend \
    --supertrend-interval 15m \
    --supertrend-atr-period 14 \
    --supertrend-multiplier 2.5 \
    --quick
```

### 2. 不同时间周期组合
```bash
# 回测使用5分钟数据，SuperTrend使用15分钟数据
python backtest_money_quick.py \
    --coin ETH --interval 5m \
    --use-supertrend --supertrend-interval 15m \
    --start-time "2024-01-01" --end-time "2024-01-31" \
    --quick

# 回测使用15分钟数据，SuperTrend使用1小时数据
python backtest_money_quick.py \
    --coin BTC --interval 15m \
    --use-supertrend --supertrend-interval 1h \
    --supertrend-atr-period 20 \
    --quick
```

### 3. 结合其他过滤条件
```bash
# 同时使用SuperTrend过滤和时间过滤
python backtest_money_quick.py \
    --coin ETH --interval 5m \
    --use-supertrend --supertrend-interval 15m \
    --use-chushou --chushou-file chushou.json \
    --stop-loss 2.0 \
    --quick
```

## 输出变化

### 1. 初始化信息
```
🔍 SuperTrend过滤: 已启用 (看涨时只做多，看跌时只做空)
📊 加载SuperTrend数据: ETH 15m (ATR周期: 10, 倍数: 3.0)
✅ SuperTrend数据加载完成，共 2880 条记录
```

### 2. 预测日志
```
[2024-01-15 10:30:00 UTC+8] 新预测: 先涨2.0%, 信心: 0.756, 价格: 2456.7800, 风险暴露: $10.00, ST:1
```
- `ST:1` 表示 SuperTrend 看涨
- `ST:-1` 表示 SuperTrend 看跌

### 3. 结果摘要
```
=== 回测结果摘要 ===
总预测数: 45, 成功: 28, 失败: 12, 超时: 5
🔍 SuperTrend过滤: 23 个预测被过滤
```

### 4. CSV 输出
新增 `SuperTrendSignal` 列记录每个预测的 SuperTrend 信号值。

## 过滤逻辑

```python
# 过滤规则
if prediction == 1 and supertrend_signal == 1:   # 模型看涨 + SuperTrend看涨 → 执行
    return True
elif prediction == 0 and supertrend_signal == -1: # 模型看跌 + SuperTrend看跌 → 执行  
    return True
else:                                             # 其他情况 → 过滤
    return False
```

## 参数建议

### 1. 时间周期选择
- **保守策略**：SuperTrend 使用更大时间周期（如回测5m，SuperTrend用15m或1h）
- **激进策略**：SuperTrend 使用相同或更小时间周期

### 2. SuperTrend 参数调优
- **ATR Period**：
  - 较小值（7-10）：更敏感，信号更频繁
  - 较大值（14-21）：更平滑，减少假信号
- **Multiplier**：
  - 较小值（2.0-2.5）：更敏感，更多信号
  - 较大值（3.0-4.0）：更保守，减少噪音

### 3. 常用组合
```bash
# 短线策略（敏感）
--supertrend-atr-period 7 --supertrend-multiplier 2.0

# 中线策略（平衡）
--supertrend-atr-period 10 --supertrend-multiplier 3.0

# 长线策略（保守）
--supertrend-atr-period 14 --supertrend-multiplier 4.0
```

## 性能影响

- **数据加载**：需要额外加载 SuperTrend 时间周期的数据
- **计算开销**：SuperTrend 计算相对轻量，对性能影响很小
- **内存使用**：增加 SuperTrend 数据的内存占用

## 注意事项

1. **数据完整性**：确保数据库中有足够的 SuperTrend 时间周期数据
2. **时间对齐**：SuperTrend 信号会自动对齐到回测时间点
3. **过滤统计**：被过滤的预测数量会在结果中显示
4. **兼容性**：与现有的时间过滤、止损等功能完全兼容

## 效果评估

使用 SuperTrend 过滤后，可以对比以下指标：
- **预测准确率**：是否有提升
- **总收益率**：风险调整后的收益
- **最大回撤**：风险控制效果
- **交易频率**：过滤后的交易数量变化

通过调整 SuperTrend 参数和时间周期，可以找到最适合特定市场和策略的配置。