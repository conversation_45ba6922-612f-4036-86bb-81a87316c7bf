#!/usr/bin/env python3
# example_filter_usage.py
# 演示如何使用 backtest_after_filter.py 进行回测结果过滤分析

import os
import subprocess
import json

def run_filter_analysis():
    """运行过滤分析示例"""
    
    # 检查是否存在回测结果文件
    csv_file = "backtest_money_log_quick.csv"
    if not os.path.exists(csv_file):
        print(f"❌ 回测结果文件 {csv_file} 不存在")
        print("请先运行 backtest_money_quick.py 生成回测结果")
        return
    
    print("🚀 开始过滤分析示例...")
    
    # 示例1: 使用默认配置进行过滤
    print("\n=== 示例1: 使用默认配置过滤 ===")
    cmd1 = [
        "python", "backtest_after_filter.py",
        "--csv", csv_file,
        "--output", "example_default_filter"
    ]
    
    try:
        result = subprocess.run(cmd1, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 默认配置过滤完成")
        else:
            print(f"❌ 默认配置过滤失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 运行过滤失败: {e}")
    
    # 示例2: 使用命令行参数进行快速过滤
    print("\n=== 示例2: 高置信度 + 强成交量过滤 ===")
    cmd2 = [
        "python", "backtest_after_filter.py",
        "--csv", csv_file,
        "--min-confidence", "0.7",
        "--min-volume-ratio", "2.0",
        "--max-consecutive-losses", "2",
        "--output", "example_strict_filter"
    ]
    
    try:
        result = subprocess.run(cmd2, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 严格过滤完成")
        else:
            print(f"❌ 严格过滤失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 运行过滤失败: {e}")
    
    # 示例3: 创建自定义配置并使用
    print("\n=== 示例3: 自定义配置过滤 ===")
    
    custom_config = {
        "coin": "ETH",
        "interval": "15m", 
        "market": "spot",
        "initial_capital": 1000,
        "confidence_filter": {
            "enabled": True,
            "min_confidence": 0.75,  # 更高的置信度要求
            "max_confidence": 1.0
        },
        "ma_filter": {
            "enabled": True,
            "short_period": 3,       # 更短的均线周期
            "long_period": 15,
            "same_direction_only": True
        },
        "volume_filter": {
            "enabled": True,
            "volume_period": 10,     # 更短的成交量周期
            "min_volume_ratio": 2.5  # 更高的成交量要求
        },
        "time_filter": {
            "enabled": True,         # 启用时间过滤
            "allowed_hours": [10, 11, 14, 15, 16, 20, 21],  # 特定交易时间
            "allowed_days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
        },
        "consecutive_filter": {
            "enabled": True,
            "max_consecutive_losses": 2  # 更严格的连续亏损限制
        }
    }
    
    # 保存自定义配置
    custom_config_file = "custom_filter_config.json"
    with open(custom_config_file, 'w', encoding='utf-8') as f:
        json.dump(custom_config, f, indent=2, ensure_ascii=False)
    
    cmd3 = [
        "python", "backtest_after_filter.py",
        "--csv", csv_file,
        "--config", custom_config_file,
        "--output", "example_custom_filter"
    ]
    
    try:
        result = subprocess.run(cmd3, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 自定义配置过滤完成")
        else:
            print(f"❌ 自定义配置过滤失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 运行过滤失败: {e}")
    
    print("\n🎉 过滤分析示例完成！")
    print("\n生成的文件:")
    for prefix in ["example_default_filter", "example_strict_filter", "example_custom_filter"]:
        files = [
            f"{prefix}.csv",
            f"{prefix}_metrics_comparison.csv", 
            f"{prefix}_config.json",
            f"{prefix}_analysis.png"
        ]
        for file in files:
            if os.path.exists(file):
                print(f"  ✅ {file}")
            else:
                print(f"  ❌ {file} (未生成)")

def show_filter_options():
    """显示可用的过滤选项说明"""
    print("\n📋 可用的过滤选项说明:")
    print("="*50)
    
    print("\n1. 置信度过滤 (confidence_filter):")
    print("   - 过滤掉模型置信度过低或过高的预测")
    print("   - min_confidence: 最小置信度阈值 (0.0-1.0)")
    print("   - max_confidence: 最大置信度阈值 (0.0-1.0)")
    
    print("\n2. 双均线过滤 (ma_filter):")
    print("   - 使用移动平均线趋势过滤信号")
    print("   - short_period: 短期均线周期")
    print("   - long_period: 长期均线周期") 
    print("   - same_direction_only: 是否只允许与均线趋势一致的信号")
    
    print("\n3. 成交量过滤 (volume_filter):")
    print("   - 过滤成交量不足的时段")
    print("   - volume_period: 成交量均线计算周期")
    print("   - min_volume_ratio: 最小成交量比率 (当前成交量/平均成交量)")
    
    print("\n4. 时间段过滤 (time_filter):")
    print("   - 只在特定时间段进行交易")
    print("   - allowed_hours: 允许的小时列表 [0-23]")
    print("   - allowed_days: 允许的星期列表 ['Monday', 'Tuesday', ...]")
    
    print("\n5. 连续亏损过滤 (consecutive_filter):")
    print("   - 在连续亏损达到上限后暂停交易")
    print("   - max_consecutive_losses: 最大连续亏损次数")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--help-filters':
        show_filter_options()
    else:
        run_filter_analysis()