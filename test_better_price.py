#!/usr/bin/env python3
# 测试挂单买入功能

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_better_price_logic():
    """测试挂单价格计算逻辑"""
    
    # 测试做多挂单
    signal_price = 10.0
    better_price_pct = 0.1  # 0.1%
    
    # 做多时应该挂更低的价格
    expected_long_order_price = signal_price * (1 - better_price_pct / 100)
    print(f"做多信号价格: {signal_price}")
    print(f"期望更好价格: {better_price_pct}%")
    print(f"做多挂单价格: {expected_long_order_price}")
    print(f"预期节省: {signal_price - expected_long_order_price:.4f}")
    
    # 做空时应该挂更高的价格
    expected_short_order_price = signal_price * (1 + better_price_pct / 100)
    print(f"\n做空信号价格: {signal_price}")
    print(f"期望更好价格: {better_price_pct}%")
    print(f"做空挂单价格: {expected_short_order_price}")
    print(f"预期收益: {expected_short_order_price - signal_price:.4f}")

def test_fill_logic():
    """测试成交逻辑"""
    print("\n=== 测试成交逻辑 ===")
    
    # 做多挂单测试
    order_price = 9.99
    print(f"做多挂单价格: {order_price}")
    
    # 测试不同的K线数据
    test_cases = [
        {"high": 10.05, "low": 10.00, "close": 10.02, "should_fill": False},
        {"high": 10.05, "low": 9.98, "close": 10.02, "should_fill": True},
        {"high": 9.99, "low": 9.95, "close": 9.97, "should_fill": True},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n测试案例 {i+1}:")
        print(f"  K线: 高={case['high']}, 低={case['low']}, 收={case['close']}")
        
        # 做多挂单成交条件：最低价 <= 挂单价
        filled = case['low'] <= order_price
        print(f"  是否成交: {filled} (预期: {case['should_fill']})")
        print(f"  结果: {'✅ 正确' if filled == case['should_fill'] else '❌ 错误'}")

if __name__ == "__main__":
    print("=== 测试挂单买入功能 ===")
    test_better_price_logic()
    test_fill_logic()
    print("\n测试完成！")
