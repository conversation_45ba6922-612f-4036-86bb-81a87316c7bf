# 性能指标详解

## 概述

`backtest_money_quick.py` 现在提供了全面的性能指标分析，包括风险调整收益指标，帮助更好地评估交易策略的表现。

## 新增指标

### 1. Sortino Ratio (索提诺比率)

**定义**: Sortino比率是夏普比率的改进版本，只考虑下行风险（负收益的波动性）。

**公式**: 
```
Sortino Ratio = (平均收益 - 目标收益) / 下行标准差
```

**解释**:
- 只惩罚负收益的波动，不惩罚正收益的波动
- 比夏普比率更适合评估交易策略，因为投资者通常不介意上涨的波动
- 数值越高越好

**评判标准**:
- `> 2.0`: 优秀
- `1.0 - 2.0`: 良好  
- `0.5 - 1.0`: 一般
- `< 0.5`: 较差

### 2. Calmar Ratio (卡玛比率)

**定义**: Calmar比率衡量年化收益率与最大回撤的比值。

**公式**:
```
Calmar Ratio = 年化收益率 / |最大回撤|
```

**解释**:
- 评估策略在控制最大损失的同时获得收益的能力
- 考虑了时间因素，将收益年化处理
- 数值越高越好，表示单位回撤获得更多收益

**评判标准**:
- `> 3.0`: 优秀
- `1.0 - 3.0`: 良好
- `0.5 - 1.0`: 一般  
- `< 0.5`: 较差

## 完整指标列表

### 基础指标
- **初始资金**: 回测开始时的资金
- **最终资金**: 回测结束时的资金
- **总收益率**: (最终资金 - 初始资金) / 初始资金
- **胜率**: 盈利交易数 / 总交易数

### 风险指标
- **最大回撤**: 资金曲线的最大下跌幅度
- **夏普比率**: (平均收益 - 无风险利率) / 收益标准差
- **Sortino比率**: (平均收益 - 目标收益) / 下行标准差
- **Calmar比率**: 年化收益率 / |最大回撤|

### 交易统计
- **平均盈利**: 盈利交易的平均金额
- **平均亏损**: 亏损交易的平均金额  
- **盈亏比**: |总盈利| / |总亏损|

## 使用示例

### 查看详细指标

```bash
python backtest_money_quick.py \
    --coin ETH --interval 5m \
    --use-supertrend \
    --quick
```

输出示例:
```
=== 基础指标 ===
初始资金: $1,000.00
最终资金: $1,156.78
总收益率: +15.68%
胜率: 65.22%

=== 风险指标 ===
最大回撤: -8.45%
夏普比率: 1.234
Sortino比率: 1.567
Calmar比率: 1.856

=== 交易统计 ===
平均盈利: $+45.67
平均亏损: $-23.45
盈亏比: 1.95
```

### 性能指标文件

所有指标会自动保存到 `backtest_performance_metrics.csv`：

```csv
initial_capital,final_capital,total_return,total_return_pct,max_drawdown,max_drawdown_pct,sharpe_ratio,sortino_ratio,calmar_ratio,win_rate,win_rate_pct,avg_win,avg_loss,profit_factor
1000.0,1156.78,0.1568,15.68,-0.0845,-8.45,1.234,1.567,1.856,0.6522,65.22,45.67,-23.45,1.95
```

## 指标解读

### 优秀策略特征
- **高收益率** + **低回撤**: 理想的收益风险比
- **高Sortino比率**: 下行风险控制良好
- **高Calmar比率**: 回撤控制优秀
- **高胜率** + **高盈亏比**: 稳定盈利能力

### 风险警示信号
- **Sortino比率 < 0**: 策略产生负收益
- **Calmar比率 < 0.5**: 回撤过大相对于收益
- **胜率 < 40%**: 成功率偏低
- **盈亏比 < 1.0**: 平均亏损大于平均盈利

## 优化建议

### 基于指标优化策略

1. **提高Sortino比率**:
   - 减少大幅亏损交易
   - 优化止损策略
   - 改善入场时机

2. **提高Calmar比率**:
   - 控制最大回撤
   - 提高资金利用效率
   - 优化仓位管理

3. **平衡收益与风险**:
   - 不要单纯追求高收益
   - 关注风险调整后的收益
   - 考虑策略的可持续性

### 参数优化中的应用

在SuperTrend参数优化中，这些指标的权重分配：

```python
# 综合评分公式
score = (
    total_return * 0.3 +      # 总收益率 30%
    win_rate * 0.2 +          # 胜率 20%  
    (-max_drawdown) * 0.2 +   # 最大回撤 20%
    sharpe_ratio * 0.1 +      # 夏普比率 10%
    sortino_ratio * 0.1 +     # Sortino比率 10%
    calmar_ratio * 0.1        # Calmar比率 10%
)
```

## 注意事项

### 指标局限性

1. **样本依赖**: 指标基于历史数据，未来表现可能不同
2. **时间敏感**: 短期回测的指标可能不够稳定
3. **市场环境**: 不同市场环境下指标表现会有差异

### 使用建议

1. **综合评估**: 不要依赖单一指标
2. **长期观察**: 关注指标的稳定性和趋势
3. **定期更新**: 定期重新计算和评估指标
4. **风险优先**: 在追求收益的同时优先控制风险

## 总结

新增的Sortino比率和Calmar比率为策略评估提供了更全面的视角：

- **Sortino比率**: 更准确地衡量风险调整收益
- **Calmar比率**: 评估回撤控制能力
- **综合指标**: 提供多维度的策略评估

通过这些指标，可以更好地：
- 识别优秀的交易策略
- 优化参数配置
- 控制投资风险
- 提高长期收益稳定性