"""
Market Data Integration Example
Shows how to use market microstructure indicators with real data sources
"""

import asyncio
import websockets
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from market_microstructure_indicators import MarketMicrostructureIndicators
from advanced_market_microstructure import AdvancedMarketMicrostructure

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MarketDataProcessor:
    """
    Process real-time market data and calculate microstructure indicators
    """
    
    def __init__(self, symbol: str = "BTCUSDT"):
        self.symbol = symbol
        self.basic_calculator = MarketMicrostructureIndicators()
        self.advanced_analyzer = AdvancedMarketMicrostructure(history_length=5000)
        
        # Data storage
        self.current_order_book = {'bids': [], 'asks': []}
        self.recent_trades = []
        self.indicators_df = pd.DataFrame()
        
        # Configuration
        self.max_trade_history = 1000
        self.indicator_calculation_interval = 5  # seconds
        
    def parse_binance_order_book(self, data: Dict[str, Any]) -> Dict[str, List[tuple]]:
        """
        Parse Binance order book data
        
        Args:
            data: Raw order book data from Binance
            
        Returns:
            Formatted order book
        """
        try:
            order_book = {
                'bids': [(float(bid[0]), float(bid[1])) for bid in data.get('bids', [])],
                'asks': [(float(ask[0]), float(ask[1])) for ask in data.get('asks', [])]
            }
            return order_book
        except Exception as e:
            logger.error(f"Error parsing order book: {e}")
            return {'bids': [], 'asks': []}
    
    def parse_binance_trade(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse Binance trade data
        
        Args:
            data: Raw trade data from Binance
            
        Returns:
            Formatted trade data
        """
        try:
            return {
                'timestamp': datetime.fromtimestamp(data['T'] / 1000).isoformat(),
                'price': float(data['p']),
                'volume': float(data['q']),
                'side': 'buy' if data['m'] else 'sell',  # m=true means buyer is market maker (sell)
                'trade_id': data['t']
            }
        except Exception as e:
            logger.error(f"Error parsing trade: {e}")
            return None
    
    def update_order_book(self, order_book_data: Dict[str, Any]):
        """
        Update current order book and calculate indicators
        
        Args:
            order_book_data: New order book data
        """
        try:
            # Parse and update order book
            self.current_order_book = self.parse_binance_order_book(order_book_data)
            
            # Calculate basic indicators
            timestamp = datetime.now().isoformat()
            indicators = self.basic_calculator.process_order_book_snapshot(
                self.current_order_book, timestamp
            )
            
            # Add to advanced analyzer
            self.advanced_analyzer.add_order_book_snapshot(self.current_order_book, timestamp)
            
            # Store indicators
            self.store_indicators(indicators)
            
            # Log key metrics
            if 'bid_ask_spread_pct' in indicators and not np.isnan(indicators['bid_ask_spread_pct']):
                logger.info(f"Spread: {indicators['bid_ask_spread_pct']:.4f}%, "
                          f"Imbalance: {indicators.get('liquidity_imbalance_volume', 0):.4f}")
                
        except Exception as e:
            logger.error(f"Error updating order book: {e}")
    
    def add_trade(self, trade_data: Dict[str, Any]):
        """
        Add new trade and update trade-based indicators
        
        Args:
            trade_data: New trade data
        """
        try:
            trade = self.parse_binance_trade(trade_data)
            if trade:
                # Add to recent trades
                self.recent_trades.append(trade)
                
                # Keep only recent trades
                if len(self.recent_trades) > self.max_trade_history:
                    self.recent_trades = self.recent_trades[-self.max_trade_history:]
                
                # Calculate trade-based indicators
                self.calculate_trade_indicators()
                
        except Exception as e:
            logger.error(f"Error adding trade: {e}")
    
    def calculate_trade_indicators(self):
        """Calculate indicators based on recent trades"""
        try:
            if len(self.recent_trades) < 10:
                return
            
            # Order flow imbalance
            ofi_1min = self.advanced_analyzer.calculate_order_flow_imbalance(
                self.recent_trades, time_window=60
            )
            ofi_5min = self.advanced_analyzer.calculate_order_flow_imbalance(
                self.recent_trades, time_window=300
            )
            
            # VPIN
            vpin = self.advanced_analyzer.calculate_vpin(self.recent_trades)
            
            # Roll's spread
            prices = [trade['price'] for trade in self.recent_trades[-50:]]
            roll_spread = self.advanced_analyzer.calculate_roll_spread(prices)
            
            # Kyle's lambda (if we have enough data)
            if len(prices) > 20:
                price_changes = np.diff(prices)
                signed_volumes = [
                    trade['volume'] * (1 if trade['side'] == 'buy' else -1) 
                    for trade in self.recent_trades[-49:]
                ]
                kyle_lambda = self.advanced_analyzer.calculate_kyle_lambda(
                    price_changes.tolist(), signed_volumes
                )
            else:
                kyle_lambda = np.nan
            
            # Store trade-based indicators
            trade_indicators = {
                'timestamp': datetime.now().isoformat(),
                'order_flow_imbalance_1min': ofi_1min,
                'order_flow_imbalance_5min': ofi_5min,
                'vpin': vpin,
                'roll_spread': roll_spread,
                'kyle_lambda': kyle_lambda
            }
            
            self.store_indicators(trade_indicators)
            
        except Exception as e:
            logger.error(f"Error calculating trade indicators: {e}")
    
    def store_indicators(self, indicators: Dict[str, Any]):
        """
        Store calculated indicators
        
        Args:
            indicators: Dictionary of indicators
        """
        try:
            # Convert to DataFrame row
            df_row = pd.DataFrame([indicators])
            
            # Append to main DataFrame
            if self.indicators_df.empty:
                self.indicators_df = df_row
            else:
                self.indicators_df = pd.concat([self.indicators_df, df_row], ignore_index=True)
            
            # Keep only recent data (last 1000 rows)
            if len(self.indicators_df) > 1000:
                self.indicators_df = self.indicators_df.tail(1000).reset_index(drop=True)
                
        except Exception as e:
            logger.error(f"Error storing indicators: {e}")
    
    def get_current_summary(self) -> Dict[str, Any]:
        """
        Get current market microstructure summary
        
        Returns:
            Summary of current market conditions
        """
        try:
            if self.indicators_df.empty:
                return {}
            
            latest = self.indicators_df.iloc[-1].to_dict()
            
            # Calculate rolling statistics for key indicators
            summary = {
                'timestamp': latest.get('timestamp'),
                'current_spread_pct': latest.get('bid_ask_spread_pct', np.nan),
                'current_liquidity_imbalance': latest.get('liquidity_imbalance_volume', np.nan),
                'current_order_flow_1min': latest.get('order_flow_imbalance_1min', np.nan),
                'current_vpin': latest.get('vpin', np.nan)
            }
            
            # Add rolling statistics if we have enough data
            if len(self.indicators_df) >= 20:
                recent_data = self.indicators_df.tail(20)
                
                for col in ['bid_ask_spread_pct', 'liquidity_imbalance_volume', 'vpin']:
                    if col in recent_data.columns:
                        values = recent_data[col].dropna()
                        if len(values) > 0:
                            summary[f'{col}_mean_20'] = values.mean()
                            summary[f'{col}_std_20'] = values.std()
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting summary: {e}")
            return {}
    
    def save_data(self, filename: Optional[str] = None):
        """
        Save indicators data to CSV
        
        Args:
            filename: Optional filename
        """
        try:
            if self.indicators_df.empty:
                logger.warning("No data to save")
                return
            
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"market_microstructure_{self.symbol}_{timestamp}.csv"
            
            self.indicators_df.to_csv(filename, index=False)
            logger.info(f"Data saved to {filename}")
            
        except Exception as e:
            logger.error(f"Error saving data: {e}")


# Example usage with simulated data
def simulate_market_data():
    """Simulate market data for testing"""
    processor = MarketDataProcessor("BTCUSDT")
    
    # Simulate order book updates
    base_price = 50000.0
    
    for i in range(100):
        # Generate realistic order book
        mid_price = base_price + np.random.normal(0, 10)
        spread = np.random.uniform(0.01, 0.05) * mid_price / 100
        
        bids = []
        asks = []
        
        # Generate bid levels
        for j in range(10):
            price = mid_price - spread/2 - j * 0.01 * mid_price / 100
            volume = np.random.exponential(1000)
            bids.append([str(price), str(volume)])
        
        # Generate ask levels
        for j in range(10):
            price = mid_price + spread/2 + j * 0.01 * mid_price / 100
            volume = np.random.exponential(1000)
            asks.append([str(price), str(volume)])
        
        order_book_data = {'bids': bids, 'asks': asks}
        processor.update_order_book(order_book_data)
        
        # Simulate some trades
        if i % 5 == 0:  # Trade every 5 order book updates
            trade_data = {
                'T': int(datetime.now().timestamp() * 1000),
                'p': str(mid_price + np.random.normal(0, spread/4)),
                'q': str(np.random.exponential(100)),
                'm': np.random.choice([True, False]),
                't': i
            }
            processor.add_trade(trade_data)
    
    # Get summary
    summary = processor.get_current_summary()
    print("\nMarket Microstructure Summary:")
    print("=" * 40)
    for key, value in summary.items():
        if isinstance(value, float) and not np.isnan(value):
            print(f"{key}: {value:.6f}")
        elif not isinstance(value, float):
            print(f"{key}: {value}")
    
    # Save data
    processor.save_data("simulated_market_data.csv")
    
    return processor


if __name__ == "__main__":
    # Run simulation
    processor = simulate_market_data()
    
    print(f"\nProcessed {len(processor.indicators_df)} data points")
    print(f"Recent trades: {len(processor.recent_trades)}")
    
    # Show recent indicators
    if not processor.indicators_df.empty:
        print("\nRecent Indicators (last 5 rows):")
        print(processor.indicators_df.tail().to_string())