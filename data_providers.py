# data_providers.py
# 数据提供者模块 - 只支持SQLite数据源

import pandas as pd
import sqlite3
import os
from datetime import datetime
from typing import Optional, Tuple
from abc import ABC, abstractmethod
import pytz

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def to_beijing_time(timestamp):
    """将时间戳转换为北京时间"""
    if isinstance(timestamp, pd.Timestamp):
        if timestamp.tz is None:
            # 假设无时区的时间戳是UTC
            timestamp = timestamp.tz_localize('UTC')
        return timestamp.tz_convert(BEIJING_TZ)
    elif isinstance(timestamp, datetime):
        if timestamp.tzinfo is None:
            # 假设无时区的datetime是UTC
            timestamp = pytz.UTC.localize(timestamp)
        return timestamp.astimezone(BEIJING_TZ)
    return timestamp

def format_beijing_time(timestamp):
    """格式化北京时间为字符串"""
    beijing_time = to_beijing_time(timestamp)
    return beijing_time.strftime('%Y-%m-%d %H:%M:%S UTC+8')

class DataProvider(ABC):
    """抽象数据提供者接口"""
    
    @abstractmethod
    def get_initial_data(self, initial_count: int = 1000, start_time: Optional[pd.Timestamp] = None, 
                        end_time: Optional[pd.Timestamp] = None) -> pd.DataFrame:
        """获取初始数据（指定条数）"""
        pass
    
    @abstractmethod
    def get_next_data(self) -> Optional[Tuple[pd.Timestamp, float]]:
        """获取下一条数据，返回 (timestamp, price) 或 None 表示没有更多数据"""
        pass
    
    @abstractmethod
    def add_new_data(self, timestamp: pd.Timestamp, open_price: float, high: float, 
                    low: float, close: float, volume: float):
        """添加新的数据点（用于实时数据）"""
        pass
    
    @abstractmethod
    def reset(self):
        """重置数据提供者状态"""
        pass
    
    @abstractmethod
    def get_total_count(self) -> int:
        """获取总数据条数"""
        pass
    
    @abstractmethod
    def get_current_position(self) -> int:
        """获取当前位置"""
        pass
    
    @abstractmethod
    def get_current_data(self) -> pd.DataFrame:
        """获取当前所有已加载的数据"""
        pass



class SQLiteDataProvider(DataProvider):
    """SQLite数据库数据提供者 - 支持增量加载"""
    
    def __init__(self, db_path: str, symbol: str, interval: str, market: str = "spot", 
                 price_multiplier: float = 1.0):
        self.db_path = db_path
        self.symbol = symbol
        self.interval = interval
        self.market = market
        self.price_multiplier = price_multiplier
        self.loaded_data = pd.DataFrame()  # 当前已加载的数据
        self.all_data = None  # 所有数据（延迟加载）
        self.current_idx = 0
        self.start_idx = 0
        self.connection = None
        
    def _get_table_name(self) -> str:
        """获取表名"""
        safe_symbol = self.symbol.replace('/', '_').replace('-', '_')
        safe_interval = self.interval.replace('m', 'min').replace('h', 'hour').replace('d', 'day')
        return f"{safe_symbol}_{safe_interval}_{self.market}"
    
    def _get_connection(self):
        """获取数据库连接"""
        if self.connection is None:
            self.connection = sqlite3.connect(self.db_path)
        return self.connection
    
    def get_initial_data(self, initial_count: int = 1000, start_time: Optional[pd.Timestamp] = None, 
                        end_time: Optional[pd.Timestamp] = None) -> pd.DataFrame:
        """从SQLite加载初始数据（指定条数）"""
        if not os.path.exists(self.db_path):
            raise FileNotFoundError(f"SQLite数据库文件不存在: {self.db_path}")
        
        table_name = self._get_table_name()
        print(f"📊 从SQLite数据库加载初始数据: {self.db_path}")
        print(f"📋 表名: {table_name}")
        print(f"📈 初始数据条数: {initial_count}")
        
        try:
            conn = self._get_connection()
            
            # 首先加载所有数据（不限制条数）
            query = f"""
            SELECT timestamp, open, high, low, close, volume,taker_buy_base_volume,taker_buy_quote_volume
            FROM {table_name}
            WHERE 1=1
            """
            params = []
            
            if start_time is not None:
                query += " AND timestamp >= ?"
                params.append(int(start_time.timestamp()))
            
            if end_time is not None:
                query += " AND timestamp <= ?"
                params.append(int(end_time.timestamp()))
            
            query += " ORDER BY timestamp"
            
            # 执行查询获取所有数据
            all_df = pd.read_sql_query(query, conn, params=params)
            
            if all_df.empty:
                raise ValueError(f"表 {table_name} 中没有找到数据")
            
            # 转换时间戳为datetime
            all_df['timestamp'] = pd.to_datetime(all_df['timestamp'], unit='s')
            all_df.set_index('timestamp', inplace=True)
            
            # 应用价格缩放
            if self.price_multiplier != 1.0:
                price_cols = ['open', 'high', 'low', 'close']
                for col in price_cols:
                    all_df[col] = all_df[col] * self.price_multiplier
                print(f"💰 价格列已乘以 {self.price_multiplier}")
            
            all_df.dropna(inplace=True)
            all_df.sort_index(inplace=True)
            
            # 保存所有数据
            self.all_data = all_df
            
            # 获取指定条数的初始数据
            actual_count = min(initial_count, len(self.all_data))
            self.loaded_data = self.all_data.iloc[:actual_count].copy()
            
            self.current_idx = 0
            self.start_idx = 0
            
            print(f"✅ 成功加载 {len(self.loaded_data)} 条初始数据（总共 {len(self.all_data)} 条）")
            print(f"📅 时间范围: {format_beijing_time(self.loaded_data.index[0])} 到 {format_beijing_time(self.loaded_data.index[-1])}")
            
            return self.loaded_data
            
        except Exception as e:
            print(f"❌ 加载SQLite数据时出错: {e}")
            return None
    
    def get_next_data(self) -> Optional[Tuple[pd.Timestamp, float]]:
        """获取下一条数据"""
        if self.loaded_data is None or self.current_idx >= len(self.loaded_data):
            return None
        
        timestamp = self.loaded_data.index[self.current_idx]
        price = self.loaded_data.iloc[self.current_idx]['close']
        self.current_idx += 1
        
        return timestamp, price
    
    def add_new_data(self, timestamp: pd.Timestamp, open_price: float, high: float, 
                    low: float, close: float, volume: float):
        """添加新的数据点"""
        new_row = pd.DataFrame({
            'open': [open_price],
            'high': [high],
            'low': [low],
            'close': [close],
            'volume': [volume]
        }, index=[timestamp])
        
        self.loaded_data = pd.concat([self.loaded_data, new_row])
        self.loaded_data = self.loaded_data.sort_index()
    
    def reset(self):
        """重置到开始位置"""
        self.current_idx = self.start_idx
    
    def get_total_count(self) -> int:
        """获取总数据条数（已加载的数据）"""
        return len(self.loaded_data) if self.loaded_data is not None else 0
    
    def get_current_position(self) -> int:
        """获取当前位置"""
        return self.current_idx
    
    def get_current_data(self) -> pd.DataFrame:
        """获取当前所有已加载的数据"""
        return self.loaded_data.copy() if self.loaded_data is not None else pd.DataFrame()
    
    def __del__(self):
        """清理数据库连接"""
        if self.connection is not None:
            self.connection.close()



def create_data_provider(data_source, price_multiplier: float = 1.0) -> DataProvider:
    """根据数据源配置创建SQLite数据提供者"""
    if isinstance(data_source, dict) and data_source.get('type') == 'sqlite':
        # SQLite数据库
        return SQLiteDataProvider(
            db_path=data_source['db_path'],
            symbol=data_source['symbol'],
            interval=data_source['interval'],
            market=data_source.get('market', 'spot'),
            price_multiplier=price_multiplier
        )
    else:
        raise ValueError(f"只支持SQLite数据源，请提供包含'type': 'sqlite'的配置字典")
