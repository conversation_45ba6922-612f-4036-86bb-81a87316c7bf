# 实现计划

- [x] 1. 创建项目基础结构和配置管理

  - 在 hunhe 文件夹中创建基本的项目结构
  - 实现配置管理模块，支持加载和验证集成模型配置
  - 创建基础的工具函数和常量定义
  - _需求: 2.1, 2.2_

- [x] 2. 实现模型加载器
- [x] 2.1 创建 ModelLoader 类的基础结构

  - 实现 ModelLoader 类，支持加载 standalone 和 money management 模型
  - 添加模型文件和配置文件的验证逻辑
  - 实现错误处理和日志记录
  - _需求: 1.2, 2.2, 4.1_

- [x] 2.2 实现模型预测功能

  - 实现 standalone 模型的预测方法，返回 3 类概率分布
  - 实现 money management 模型的预测方法，返回 2 类概率分布
  - 添加批量预测支持以提高性能
  - _需求: 1.2, 2.3_

- [x] 3. 实现特征生成器
- [x] 3.1 创建基础特征计算模块

  - 实现 FeatureGenerator 类，复用现有的技术指标计算逻辑
  - 添加特征选择和过滤功能
  - 实现特征缓存机制以提高性能
  - _需求: 1.3, 2.3_

- [x] 3.2 实现模型特征生成

  - 实现从两个模型生成概率特征的逻辑
  - 添加特征命名和元数据管理
  - 实现特征对齐和时间窗口同步
  - _需求: 1.3, 4.2_

- [x] 3.3 实现特征合并和验证

  - 实现传统技术指标与模型预测特征的合并
  - 添加特征完整性检查和缺失值处理
  - 实现特征标准化和预处理
  - _需求: 1.3, 2.3_

- [x] 4. 实现集成模型训练器
- [x] 4.1 创建训练数据准备逻辑

  - 实现数据加载和预处理流程
  - 添加标签生成逻辑，支持与现有模型相同的目标定义
  - 实现训练/验证/测试数据分割
  - _需求: 1.1, 1.4_

- [x] 4.2 实现模型训练和优化

  - 实现 LightGBM 集成模型的训练逻辑
  - 添加超参数优化和交叉验证支持
  - 实现早停和模型选择机制
  - _需求: 1.4, 4.3_

- [x] 4.3 实现模型保存和配置生成

  - 实现模型保存功能，使用与现有模型兼容的格式
  - 生成包含所有必要参数的配置文件
  - 添加模型元数据和组件模型信息记录
  - _需求: 2.4, 3.1, 3.2, 4.3_

- [x] 5. 实现主训练脚本
- [x] 5.1 创建命令行接口

  - 实现主训练脚本，支持命令行参数配置
  - 添加数据源配置和时间范围选择
  - 实现训练模式和参数配置选项
  - _需求: 2.1, 4.1_

- [x] 5.2 集成所有组件

  - 将所有组件集成到主训练流程中
  - 实现完整的训练管道，从数据加载到模型保存
  - 添加进度监控和状态报告
  - _需求: 1.1, 1.2, 1.3, 1.4_

- [-] 6. 实现分析和评估功能
- [x] 6.1 创建特征重要性分析

  - 实现特征重要性分析和可视化
  - 添加组件模型贡献度分析
  - 生成特征选择建议和优化报告
  - _需求: 4.2, 4.3_

- [ ] 6.2 实现性能对比分析

  - 实现集成模型与单个组件模型的性能对比
  - 添加回测兼容性验证
  - 生成详细的训练报告和模型评估
  - _需求: 3.3, 4.4_

- [x] 7. 创建测试和验证
- [x] 7.1 实现单元测试

  - 为所有核心组件创建单元测试
  - 测试模型加载、特征生成和训练逻辑
  - 添加错误处理和边界条件测试
  - _需求: 2.3, 2.4_

- [x] 7.2 实现集成测试和示例
  - 创建端到端的集成测试
  - 实现与现有回测系统的兼容性测试
  - 创建使用示例和文档
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [x] 8. 修复特征兼容性问题
- [x] 8.1 更新特征生成器使用现有模型的特征计算方法

  - 修改 FeatureGenerator 类，使用与现有 standalone 和 money management 模型预测的概率作为新特征。
  - _需求: 1.3, 2.3, 3.1_

- [x] 8.2 验证特征一致性和模型预测功能

  - 验证集成系统生成的特征与现有模型期望的特征完全一致
  - 测试模型加载器能够正确使用现有模型进行预测
  - 确保特征名称、数量和计算方法与现有系统完全兼容
  - _需求: 1.2, 2.2, 3.2_
