# 混合模型训练设计文档

## 概述

这个设计实现了一个混合模型训练系统，它会加载两个现有的模型（standalone 和 money management），使用它们的预测概率作为新特征，结合传统技术指标来训练一个更强的集成模型。

设计的核心思想很简单：既然两个模型各有优势，那就让它们"投票"，然后训练一个新模型来学习如何最好地组合这些投票结果。

## 架构

### 系统组件

```
hunhe/
├── ensemble_trainer.py          # 主训练脚本
├── model_loader.py             # 模型加载器
├── feature_generator.py        # 特征生成器
├── ensemble_config.py          # 配置管理
└── utils.py                    # 工具函数
```

### 数据流

1. **数据加载** → 从数据库加载历史K线数据
2. **基础特征计算** → 计算传统技术指标
3. **模型预测** → 使用两个现有模型生成概率分数
4. **特征合并** → 将模型预测与技术指标合并
5. **标签生成** → 创建训练标签
6. **模型训练** → 训练新的集成模型

## 组件和接口

### ModelLoader 类

负责加载和管理现有模型：

```python
class ModelLoader:
    def __init__(self, standalone_model_path, money_model_path):
        # 加载两个模型和它们的配置
        
    def load_standalone_model(self, model_path):
        # 加载 standalone 模型
        
    def load_money_model(self, model_path):
        # 加载 money management 模型
        
    def predict_standalone(self, features_df):
        # 使用 standalone 模型预测，返回 3 类概率
        
    def predict_money(self, features_df):
        # 使用 money 模型预测，返回 2 类概率
```

### FeatureGenerator 类

生成集成特征：

```python
class FeatureGenerator:
    def __init__(self, model_loader):
        self.model_loader = model_loader
        
    def calculate_base_features(self, df, timeframe):
        # 计算基础技术指标
        
    def generate_model_features(self, df):
        # 生成模型预测特征
        
    def combine_features(self, base_features, model_features):
        # 合并所有特征
```

### EnsembleTrainer 类

主训练逻辑：

```python
class EnsembleTrainer:
    def __init__(self, config):
        self.config = config
        
    def prepare_data(self, df):
        # 准备训练数据
        
    def create_labels(self, df):
        # 创建训练标签
        
    def train_model(self, X, y):
        # 训练集成模型
        
    def save_model(self, model, config):
        # 保存模型和配置
```

## 数据模型

### 特征结构

集成模型的特征包括：

1. **传统技术指标**（从现有特征列表中选择）
2. **Standalone 模型特征**：
   - `standalone_prob_lowest`: 预测最低点的概率
   - `standalone_prob_neutral`: 预测中性点的概率  
   - `standalone_prob_highest`: 预测最高点的概率
   - `standalone_prediction`: 预测类别 (0/1/2)
3. **Money 模型特征**：
   - `money_prob_down`: 预测下跌的概率
   - `money_prob_up`: 预测上涨的概率
   - `money_prediction`: 预测类别 (0/1)

### 配置结构

```json
{
  "ensemble_config": {
    "name": "eth_5m_ensemble",
    "timeframe_minutes": 5,
    "up_threshold": 0.02,
    "down_threshold": 0.02,
    "max_lookforward_minutes": 240,
    "models": {
      "standalone": {
        "model_path": "models/standalone_eth_5m_model.joblib",
        "config_path": "models/standalone_eth_5m_config.json"
      },
      "money": {
        "model_path": "models/eth_5m_model.joblib", 
        "config_path": "models/eth_5m_config.json"
      }
    },
    "feature_selection": {
      "use_traditional_features": true,
      "traditional_feature_count": 20,
      "use_model_probabilities": true,
      "use_model_predictions": true
    }
  }
}
```

## 错误处理

### 模型加载错误
- 检查模型文件是否存在
- 验证配置文件格式
- 处理模型版本不兼容问题

### 特征生成错误
- 处理缺失的技术指标
- 处理模型预测失败
- 处理特征维度不匹配

### 训练错误
- 处理数据不足的情况
- 处理标签分布不均衡
- 处理训练过程中的异常

## 测试策略

### 单元测试
- 测试模型加载功能
- 测试特征生成逻辑
- 测试配置解析

### 集成测试
- 测试完整的训练流程
- 测试与现有回测系统的兼容性
- 测试不同数据集上的表现

### 性能测试
- 测试特征生成速度
- 测试内存使用情况
- 测试训练时间

## 实现注意事项

### 特征对齐
确保两个模型使用相同的时间窗口和数据，避免前瞻偏差。

### 模型兼容性
保持与现有回测系统的兼容性，使用相同的文件格式和接口。

### 性能优化
- 批量计算模型预测以提高效率
- 缓存重复计算的特征
- 使用向量化操作加速特征生成

### 可扩展性
设计时考虑未来添加更多模型的可能性，使用插件式架构。