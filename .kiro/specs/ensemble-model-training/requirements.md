# 混合模型训练需求文档

## 简介

这个功能要实现一个混合训练系统，把两个现有模型（standalone 和 money management 模型）的预测结果合并起来，训练一个新的更强的混合模型。系统会提取两个模型的概率分数作为特征，结合传统技术指标来训练新模型。

## 需求

### 需求 1

**用户故事:** 作为量化交易员，我想要合并多个模型的预测来创建集成模型，这样我就能通过利用不同建模方法的优势来提高预测准确性。

#### 验收标准

1. WHEN 系统加载数据 THEN 系统应该加载两个现有模型使用的相同历史数据
2. WHEN 计算特征 THEN 系统应该从 standalone 模型和 money management 模型生成概率分数
3. WHEN 创建集成特征 THEN 系统应该将两个模型的概率作为新特征，与传统技术指标一起使用
4. WHEN 训练集成模型 THEN 系统应该使用组合特征集来训练新的 LightGBM 模型

### 需求 2

**用户故事:** 作为开发者，我想要集成训练系统是模块化和可配置的，这样我就能轻松地实验不同的模型组合和特征集。

#### 验收标准

1. WHEN 配置系统 THEN 系统应该允许指定在集成中包含哪些模型
2. WHEN 加载模型 THEN 系统应该验证所有必需的模型文件和配置都存在
3. WHEN 生成特征 THEN 系统应该优雅地处理缺失或无效的模型预测
4. WHEN 保存结果 THEN 系统应该保存集成模型，并包含关于其组件模型的清晰元数据

### 需求 3

**用户故事:** 作为交易员，我想要集成模型与现有回测基础设施保持兼容，这样我就能使用相同的工具来评估其性能。

#### 验收标准

1. WHEN 保存集成模型 THEN 系统应该使用与现有模型相同的文件格式和结构
2. WHEN 创建配置文件 THEN 系统应该包含回测所需的所有必要参数
3. WHEN 生成预测 THEN 集成模型应该输出与现有模型相同格式的概率
4. WHEN 与回测系统集成 THEN 集成模型应该与 standalone 和 money management 回测方法都兼容

### 需求 4

**用户故事:** 作为研究员，我想要集成训练过程的全面日志和分析，这样我就能理解不同模型如何对最终预测做出贡献。

#### 验收标准

1. WHEN 训练开始 THEN 系统应该记录所有组件模型的配置和参数
2. WHEN 生成集成特征 THEN 系统应该跟踪每个组件模型的特征重要性
3. WHEN 训练完成 THEN 系统应该保存特征贡献和模型性能的详细分析
4. WHEN 评估结果 THEN 系统应该比较集成性能与单个组件模型的性能