# 强化学习交易优化需求文档

## 简介

基于现有的 `backtest_money_quick.py`，实现一个强化学习系统来优化交易策略。不搞复杂的，就是让AI学会什么时候进场、出场，用多少钱，比现在的固定百分比策略更聪明。

## 需求

### 需求 1: 智能仓位管理

**用户故事:** 作为交易者，我希望AI能学会最优的仓位大小，而不是每次都用固定的1%。

#### 验收标准

1. 当收到交易信号时，AI决定用多少资金（0-10%之间）
2. 当信号置信度低时，AI可以选择不交易
3. 当连续亏损时，AI应该减少仓位
4. 当连续盈利时，AI可以适当增加仓位

### 需求 2: 动态止盈止损

**用户故事:** 作为交易者，我希望AI能根据市场情况调整止盈止损，而不是固定的百分比。

#### 验收标准

1. 当进入仓位时，AI设置初始止损点
2. 当价格有利变动时，AI决定是否移动止损（追踪止损）
3. 当市场波动加大时，AI调整止损距离
4. 当接近最大持仓时间时，AI决定是否提前平仓

### 需求 3: 进出场时机优化

**用户故事:** 作为交易者，我希望AI能学会最佳的进出场时机，不只是看到信号就立即交易。

#### Acceptance Criteria

1. 当有交易信号时，AI决定立即进场还是等待更好时机
2. 当持有仓位时，AI持续评估是否应该提前出场
3. 当有多个信号时，AI选择最优的交易机会
4. 当市场条件变化时，AI调整交易策略

### 需求 4: 简单的训练环境

**用户故事:** 作为开发者，我需要一个能训练AI的环境，基于现有的回测框架。

#### 验收标准

1. 使用现有的数据和特征，不重新造轮子
2. 能够模拟真实的交易成本和滑点
3. 支持多种奖励函数（总收益、夏普比率、最大回撤等）
4. 训练过程可视化，能看到AI的学习进度

### 需求 5: 与现有系统集成

**用户故事:** 作为开发者，我希望新的RL系统能直接替换现有的交易逻辑。

#### 验收标准

1. 使用相同的数据输入格式
2. 输出相同格式的交易决策
3. 兼容现有的回测和分析工具
4. 可以保存和加载训练好的模型