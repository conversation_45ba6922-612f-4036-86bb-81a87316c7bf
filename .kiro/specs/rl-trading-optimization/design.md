# 强化学习交易优化设计文档

## 概述

基于现有的 `HistoricalBacktester` 类和已训练好的预测模型，设计一个强化学习交易代理来优化交易执行策略。

**关键点**: 
- **不重新训练预测模型**，直接使用现有模型的信号和置信度
- **专注于交易执行优化**：仓位管理、进出场时机、止盈止损
- **在现有信号基础上学习**：什么时候跟随信号，什么时候等待更好时机

## 架构

### 核心组件

```
RLTradingAgent (强化学习代理)
├── TradingEnvironment (交易环境)
├── PolicyNetwork (策略网络) 
├── RLBacktester (RL回测器)
└── TrainingManager (训练管理器)
```

### 设计原则

1. **简单优先**: 不搞复杂的多智能体，就一个代理管理所有决策
2. **渐进式**: 先让基本功能跑起来，再优化
3. **兼容性**: 复用现有的数据管道和特征工程
4. **可解释**: 能看懂AI为什么这么决策

## 组件设计

### 1. TradingEnvironment (交易环境)

**职责**: 模拟真实交易环境，给AI提供状态和奖励

**状态空间**:
```python
state = {
    # 现有模型的预测结果
    'model_signal': 1,  # 0 or 1，现有模型的预测
    'signal_confidence': 0.75,  # 模型预测置信度
    'signal_probability': 0.82,  # 原始概率值
    
    # 市场基础信息
    'current_price': 2500.0,
    'price_change_1h': 0.02,  # 1小时价格变化
    'price_change_4h': -0.01,  # 4小时价格变化
    'volatility_recent': 0.03,  # 最近波动率
    
    # 投资组合状态
    'portfolio_state': {
        'cash_ratio': 0.8,  # 现金比例
        'position_count': 2,  # 当前持仓数
        'unrealized_pnl': 150.0,  # 未实现盈亏
        'recent_win_rate': 0.6,  # 最近10笔交易胜率
        'consecutive_losses': 0  # 连续亏损次数
    },
    
    # 时间和市场状态
    'time_features': {
        'hour': 14,
        'day_of_week': 2,
        'is_good_trading_time': True  # 基于chushou.json
    }
}
```

**动作空间**:
```python
actions = {
    'position_size': 0.03,  # 0-0.1 (0-10%资金)
    'stop_loss_pct': 0.025,  # 0.01-0.05 (1-5%止损)
    'take_profit_pct': 0.02,  # 0.01-0.04 (1-4%止盈)
    'max_hold_time': 120,  # 60-240分钟
    'enter_trade': True  # 是否进场
}
```

**奖励函数**:
```python
def calculate_reward(self, trade_result):
    # 基础收益奖励
    pnl_reward = trade_result['pnl'] / self.initial_capital
    
    # 风险调整
    risk_penalty = -abs(trade_result['max_drawdown']) * 0.5
    
    # 时间效率奖励
    time_efficiency = 1.0 / max(trade_result['hold_time_minutes'], 1)
    
    # 组合奖励
    total_reward = pnl_reward + risk_penalty + time_efficiency * 0.1
    return total_reward
```

### 2. PolicyNetwork (策略网络)

**架构**: 简单的全连接网络，不搞花哨的

```python
class PolicyNetwork(nn.Module):
    def __init__(self, state_dim, action_dim):
        super().__init__()
        self.shared = nn.Sequential(
            nn.Linear(state_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU()
        )
        
        # 分别输出不同类型的动作
        self.position_size_head = nn.Linear(128, 1)  # 仓位大小
        self.stop_loss_head = nn.Linear(128, 1)      # 止损比例
        self.take_profit_head = nn.Linear(128, 1)    # 止盈比例
        self.hold_time_head = nn.Linear(128, 1)      # 持仓时间
        self.enter_trade_head = nn.Linear(128, 2)    # 是否进场
```

**训练算法**: 使用PPO (Proximal Policy Optimization)
- 简单稳定，不容易发散
- 适合连续动作空间
- 有现成的实现

### 3. RLBacktester (RL回测器)

继承现有的 `HistoricalBacktester`，替换决策逻辑：

```python
class RLBacktester(HistoricalBacktester):
    def __init__(self, rl_agent, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.rl_agent = rl_agent
        
    def make_trading_decision(self, market_state):
        # 用RL代理替换原来的固定规则
        action = self.rl_agent.get_action(market_state)
        return action
        
    def should_enter_trade(self, signal, confidence, market_state):
        # AI决定是否进场
        action = self.make_trading_decision(market_state)
        return action['enter_trade'] and action['position_size'] > 0.001
        
    def get_position_size(self, market_state):
        # AI决定仓位大小
        action = self.make_trading_decision(market_state)
        return min(action['position_size'], 0.1)  # 最大10%
```

### 4. TrainingManager (训练管理器)

**职责**: 管理整个训练流程

```python
class TrainingManager:
    def __init__(self, config):
        self.env = TradingEnvironment(config)
        self.agent = RLTradingAgent(config)
        self.logger = TrainingLogger()
        
    def train(self, episodes=1000):
        for episode in range(episodes):
            # 随机选择训练数据段
            data_segment = self.sample_training_data()
            
            # 运行一个episode
            episode_reward = self.run_episode(data_segment)
            
            # 更新策略
            self.agent.update_policy()
            
            # 记录和可视化
            self.logger.log_episode(episode, episode_reward)
            
            if episode % 100 == 0:
                self.evaluate_and_save(episode)
```

## 数据流

```
历史数据 -> 现有模型 -> 信号+置信度 -> RL代理 -> 交易动作 -> 环境反馈 -> 奖励 -> 策略更新
```

**具体流程**:
1. 使用现有的joblib模型对历史数据生成信号
2. RL代理接收信号、置信度、市场状态作为输入
3. RL代理决定是否交易、用多少仓位、设置什么止损
4. 环境模拟交易结果并给出奖励
5. 代理根据奖励优化策略

## 训练策略

### 数据准备
- 使用现有的SQLite数据库和已训练的模型
- 先运行现有模型生成所有历史信号和置信度
- 按时间段分割：70%训练，15%验证，15%测试
- RL代理学习如何基于这些信号做最优交易决策

### 训练流程
1. **预训练**: 用简单的规则策略初始化
2. **在线学习**: 在历史数据上模拟交易
3. **策略更新**: 每100个交易后更新一次策略
4. **验证**: 定期在验证集上测试性能

### 超参数
```python
training_config = {
    'learning_rate': 3e-4,
    'batch_size': 64,
    'episode_length': 1000,  # 1000个时间步
    'update_frequency': 100,
    'gamma': 0.99,  # 折扣因子
    'clip_epsilon': 0.2,  # PPO裁剪参数
}
```

## 评估指标

### 性能指标
- 总收益率
- 夏普比率
- 最大回撤
- 胜率
- 平均持仓时间

### 学习指标
- 策略熵 (探索程度)
- 价值函数损失
- 策略损失
- 奖励曲线

## 部署集成

### 模型保存
```python
# 保存训练好的模型
torch.save({
    'policy_state_dict': agent.policy.state_dict(),
    'config': training_config,
    'performance_metrics': metrics
}, 'rl_trading_model.pth')
```

### 与现有系统集成
- 保持相同的输入输出接口
- 兼容现有的回测框架
- 支持实时交易部署

## 风险控制

### 训练阶段
- 限制最大仓位 (10%)
- 限制最大回撤 (-20%)
- 异常检测和熔断机制

### 部署阶段
- 模型性能监控
- 回退到规则策略的机制
- 实时风险指标监控

## 实现优先级

### Phase 1: 基础框架
1. TradingEnvironment 基础实现
2. 简单的PolicyNetwork
3. 基础的训练循环

### Phase 2: 核心功能
1. 完整的状态空间设计
2. 奖励函数优化
3. 与现有回测系统集成

### Phase 3: 优化和部署
1. 超参数调优
2. 性能分析工具
3. 生产部署支持