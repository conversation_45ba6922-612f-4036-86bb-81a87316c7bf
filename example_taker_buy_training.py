#!/usr/bin/env python3
"""
使用主动买卖量特征进行模型训练的示例
"""

import sys
import os
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score

# 添加项目根目录到路径
sys.path.append('/Users/<USER>/project/ai/vibe/daily/eth-trade')

from model_utils_815 import (
    load_and_prepare_data_from_db, 
    calculate_features, 
    get_feature_list,
    create_percentage_target
)

def train_with_taker_buy_features():
    """使用主动买卖量特征训练模型"""
    
    print("=" * 60)
    print("使用主动买卖量特征训练模型")
    print("=" * 60)
    
    # 1. 加载数据
    db_path = "/Users/<USER>/project/ai/vibe/daily/eth-trade/coin_data.db"
    table_name = "ETHUSDT_5min_spot"
    
    print(f"从数据库加载数据: {table_name}")
    df = load_and_prepare_data_from_db(db_path, table_name, limit=50000)
    
    if df is None:
        print("❌ 数据加载失败")
        return False
    
    print(f"✅ 数据加载成功，共 {len(df)} 条记录")
    
    # 2. 计算特征
    print("\n计算特征...")
    timeframe = 5
    df_with_features = calculate_features(df, timeframe)
    print(f"✅ 特征计算完成，数据形状: {df_with_features.shape}")
    
    # 3. 生成标签
    print("\n生成标签...")
    up_threshold = 0.015  # 1.5%上涨
    down_threshold = 0.015  # 1.5%下跌
    max_lookforward_minutes = 240  # 4小时
    
    labels = create_percentage_target(
        df_with_features, 
        up_threshold, 
        down_threshold, 
        max_lookforward_minutes, 
        timeframe
    )
    
    print(f"✅ 标签生成完成，共 {len(labels)} 个标签")
    print(f"标签分布: {labels.value_counts().to_dict()}")
    
    # 4. 准备特征
    print("\n准备特征...")
    feature_list = get_feature_list(df_with_features, timeframe)
    
    # 分析主动买卖量特征的重要性
    taker_features = [f for f in feature_list if any(keyword in f for keyword in ['taker', 'buy_sell', 'extreme', 'balanced'])]
    traditional_features = [f for f in feature_list if f not in taker_features]
    
    print(f"总特征数: {len(feature_list)}")
    print(f"传统特征数: {len(traditional_features)}")
    print(f"主动买卖量特征数: {len(taker_features)}")
    
    # 5. 对齐数据
    common_index = df_with_features.index.intersection(labels.index)
    X = df_with_features.loc[common_index, feature_list]
    y = labels.loc[common_index]
    
    # 删除包含NaN的行
    mask = ~(X.isnull().any(axis=1) | y.isnull())
    X = X[mask]
    y = y[mask]
    
    print(f"✅ 最终数据集: {X.shape[0]} 样本, {X.shape[1]} 特征")
    
    # 6. 分割数据集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"训练集: {X_train.shape[0]} 样本")
    print(f"测试集: {X_test.shape[0]} 样本")
    
    # 7. 训练模型 - 使用所有特征
    print("\n训练模型 (包含主动买卖量特征)...")
    model_with_taker = RandomForestClassifier(
        n_estimators=100, 
        random_state=42, 
        max_depth=10,
        min_samples_split=20
    )
    model_with_taker.fit(X_train, y_train)
    
    # 预测和评估
    y_pred_with_taker = model_with_taker.predict(X_test)
    accuracy_with_taker = accuracy_score(y_test, y_pred_with_taker)
    
    print(f"✅ 包含主动买卖量特征的模型准确率: {accuracy_with_taker:.4f}")
    
    # 8. 训练对比模型 - 仅使用传统特征
    print("\n训练对比模型 (仅传统特征)...")
    X_train_traditional = X_train[traditional_features]
    X_test_traditional = X_test[traditional_features]
    
    model_traditional = RandomForestClassifier(
        n_estimators=100, 
        random_state=42, 
        max_depth=10,
        min_samples_split=20
    )
    model_traditional.fit(X_train_traditional, y_train)
    
    y_pred_traditional = model_traditional.predict(X_test_traditional)
    accuracy_traditional = accuracy_score(y_test, y_pred_traditional)
    
    print(f"✅ 仅传统特征的模型准确率: {accuracy_traditional:.4f}")
    
    # 9. 特征重要性分析
    print("\n" + "=" * 40)
    print("特征重要性分析")
    print("=" * 40)
    
    feature_importance = pd.DataFrame({
        'feature': feature_list,
        'importance': model_with_taker.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("Top 20 重要特征:")
    print(feature_importance.head(20).to_string(index=False))
    
    # 主动买卖量特征的重要性
    taker_importance = feature_importance[
        feature_importance['feature'].isin(taker_features)
    ].sort_values('importance', ascending=False)
    
    print(f"\n主动买卖量特征重要性 (Top 10):")
    print(taker_importance.head(10).to_string(index=False))
    
    # 10. 结果总结
    print("\n" + "=" * 60)
    print("结果总结")
    print("=" * 60)
    
    improvement = accuracy_with_taker - accuracy_traditional
    improvement_pct = (improvement / accuracy_traditional) * 100
    
    print(f"传统特征模型准确率: {accuracy_traditional:.4f}")
    print(f"包含主动买卖量特征模型准确率: {accuracy_with_taker:.4f}")
    print(f"准确率提升: {improvement:.4f} ({improvement_pct:+.2f}%)")
    
    if improvement > 0:
        print("✅ 主动买卖量特征带来了性能提升！")
    else:
        print("⚠️ 主动买卖量特征未带来明显提升")
    
    # 保存特征重要性
    output_file = "/Users/<USER>/project/ai/vibe/daily/eth-trade/taker_buy_feature_importance.csv"
    feature_importance.to_csv(output_file, index=False)
    print(f"\n特征重要性已保存到: {output_file}")
    
    return True

if __name__ == "__main__":
    success = train_with_taker_buy_features()
    if success:
        print("\n🎉 训练完成！")
    else:
        print("\n❌ 训练失败！")
        sys.exit(1)
