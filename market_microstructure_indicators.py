"""
Market Microstructure Indicators
Calculates Bid-Ask Spread, <PERSON><PERSON><PERSON>lope, and Liquidity Imbalance using Level 2/3 data
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import warnings

class MarketMicrostructureIndicators:
    """
    Calculate advanced market microstructure indicators from Level 2/3 order book data
    """
    
    def __init__(self):
        self.indicators = {}
    
    def calculate_bid_ask_spread(self, 
                               best_bid: float, 
                               best_ask: float, 
                               spread_type: str = 'absolute') -> float:
        """
        Calculate bid-ask spread
        
        Args:
            best_bid: Best bid price
            best_ask: Best ask price  
            spread_type: 'absolute', 'relative', or 'percentage'
            
        Returns:
            Calculated spread value
        """
        if best_bid <= 0 or best_ask <= 0 or best_ask <= best_bid:
            return np.nan
            
        if spread_type == 'absolute':
            return best_ask - best_bid
        elif spread_type == 'relative':
            mid_price = (best_bid + best_ask) / 2
            return (best_ask - best_bid) / mid_price
        elif spread_type == 'percentage':
            mid_price = (best_bid + best_ask) / 2
            return ((best_ask - best_bid) / mid_price) * 100
        else:
            raise ValueError("spread_type must be 'absolute', 'relative', or 'percentage'")
    
    def calculate_depth_slope(self, 
                            order_book: Dict[str, List[Tuple[float, float]]], 
                            depth_levels: int = 5) -> Dict[str, float]:
        """
        Calculate depth slope for bid and ask sides
        
        Args:
            order_book: {'bids': [(price, size), ...], 'asks': [(price, size), ...]}
            depth_levels: Number of levels to consider
            
        Returns:
            Dictionary with bid_slope and ask_slope
        """
        results = {'bid_slope': np.nan, 'ask_slope': np.nan}
        
        try:
            # Process bids (sorted descending by price)
            bids = sorted(order_book.get('bids', []), key=lambda x: x[0], reverse=True)
            if len(bids) >= depth_levels:
                bid_prices = [bid[0] for bid in bids[:depth_levels]]
                bid_volumes = [bid[1] for bid in bids[:depth_levels]]
                
                # Calculate cumulative volume
                cum_volumes = np.cumsum(bid_volumes)
                
                # Linear regression to find slope
                if len(bid_prices) > 1:
                    coeffs = np.polyfit(bid_prices, cum_volumes, 1)
                    results['bid_slope'] = coeffs[0]
            
            # Process asks (sorted ascending by price)
            asks = sorted(order_book.get('asks', []), key=lambda x: x[0])
            if len(asks) >= depth_levels:
                ask_prices = [ask[0] for ask in asks[:depth_levels]]
                ask_volumes = [ask[1] for ask in asks[:depth_levels]]
                
                # Calculate cumulative volume
                cum_volumes = np.cumsum(ask_volumes)
                
                # Linear regression to find slope
                if len(ask_prices) > 1:
                    coeffs = np.polyfit(ask_prices, cum_volumes, 1)
                    results['ask_slope'] = coeffs[0]
                    
        except Exception as e:
            warnings.warn(f"Error calculating depth slope: {e}")
            
        return results
    
    def calculate_liquidity_imbalance(self, 
                                    order_book: Dict[str, List[Tuple[float, float]]], 
                                    depth_levels: int = 5,
                                    imbalance_type: str = 'volume') -> float:
        """
        Calculate liquidity imbalance between bid and ask sides
        
        Args:
            order_book: {'bids': [(price, size), ...], 'asks': [(price, size), ...]}
            depth_levels: Number of levels to consider
            imbalance_type: 'volume' or 'order_count'
            
        Returns:
            Imbalance ratio (-1 to 1, positive means more bid liquidity)
        """
        try:
            bids = sorted(order_book.get('bids', []), key=lambda x: x[0], reverse=True)
            asks = sorted(order_book.get('asks', []), key=lambda x: x[0])
            
            if imbalance_type == 'volume':
                bid_liquidity = sum([bid[1] for bid in bids[:depth_levels]])
                ask_liquidity = sum([ask[1] for ask in asks[:depth_levels]])
            elif imbalance_type == 'order_count':
                bid_liquidity = min(len(bids), depth_levels)
                ask_liquidity = min(len(asks), depth_levels)
            else:
                raise ValueError("imbalance_type must be 'volume' or 'order_count'")
            
            total_liquidity = bid_liquidity + ask_liquidity
            if total_liquidity == 0:
                return 0.0
                
            return (bid_liquidity - ask_liquidity) / total_liquidity
            
        except Exception as e:
            warnings.warn(f"Error calculating liquidity imbalance: {e}")
            return np.nan
    
    def calculate_weighted_mid_price(self, 
                                   order_book: Dict[str, List[Tuple[float, float]]], 
                                   depth_levels: int = 3) -> float:
        """
        Calculate volume-weighted mid price
        
        Args:
            order_book: Order book data
            depth_levels: Number of levels to consider
            
        Returns:
            Weighted mid price
        """
        try:
            bids = sorted(order_book.get('bids', []), key=lambda x: x[0], reverse=True)
            asks = sorted(order_book.get('asks', []), key=lambda x: x[0])
            
            if not bids or not asks:
                return np.nan
            
            # Get top levels
            top_bids = bids[:depth_levels]
            top_asks = asks[:depth_levels]
            
            # Calculate weighted prices
            bid_weighted_price = sum([price * volume for price, volume in top_bids])
            bid_total_volume = sum([volume for _, volume in top_bids])
            
            ask_weighted_price = sum([price * volume for price, volume in top_asks])
            ask_total_volume = sum([volume for _, volume in top_asks])
            
            if bid_total_volume == 0 or ask_total_volume == 0:
                return (bids[0][0] + asks[0][0]) / 2
            
            bid_avg = bid_weighted_price / bid_total_volume
            ask_avg = ask_weighted_price / ask_total_volume
            
            return (bid_avg + ask_avg) / 2
            
        except Exception as e:
            warnings.warn(f"Error calculating weighted mid price: {e}")
            return np.nan
    
    def calculate_order_book_pressure(self, 
                                    order_book: Dict[str, List[Tuple[float, float]]], 
                                    depth_levels: int = 10) -> Dict[str, float]:
        """
        Calculate order book pressure indicators
        
        Args:
            order_book: Order book data
            depth_levels: Number of levels to analyze
            
        Returns:
            Dictionary with pressure indicators
        """
        results = {
            'total_bid_volume': 0.0,
            'total_ask_volume': 0.0,
            'volume_imbalance': 0.0,
            'price_impact_bid': np.nan,
            'price_impact_ask': np.nan
        }
        
        try:
            bids = sorted(order_book.get('bids', []), key=lambda x: x[0], reverse=True)
            asks = sorted(order_book.get('asks', []), key=lambda x: x[0])
            
            # Calculate total volumes
            bid_volumes = [bid[1] for bid in bids[:depth_levels]]
            ask_volumes = [ask[1] for ask in asks[:depth_levels]]
            
            results['total_bid_volume'] = sum(bid_volumes)
            results['total_ask_volume'] = sum(ask_volumes)
            
            # Volume imbalance
            total_volume = results['total_bid_volume'] + results['total_ask_volume']
            if total_volume > 0:
                results['volume_imbalance'] = (results['total_bid_volume'] - results['total_ask_volume']) / total_volume
            
            # Price impact (price difference from best to worst level)
            if len(bids) >= depth_levels and bids[0][0] > 0:
                results['price_impact_bid'] = (bids[0][0] - bids[depth_levels-1][0]) / bids[0][0]
            
            if len(asks) >= depth_levels and asks[0][0] > 0:
                results['price_impact_ask'] = (asks[depth_levels-1][0] - asks[0][0]) / asks[0][0]
                
        except Exception as e:
            warnings.warn(f"Error calculating order book pressure: {e}")
            
        return results
    
    def process_order_book_snapshot(self, 
                                  order_book: Dict[str, List[Tuple[float, float]]], 
                                  timestamp: Optional[str] = None) -> Dict[str, float]:
        """
        Process a single order book snapshot and calculate all indicators
        
        Args:
            order_book: Order book data
            timestamp: Optional timestamp
            
        Returns:
            Dictionary with all calculated indicators
        """
        results = {'timestamp': timestamp}
        
        try:
            bids = sorted(order_book.get('bids', []), key=lambda x: x[0], reverse=True)
            asks = sorted(order_book.get('asks', []), key=lambda x: x[0])
            
            if not bids or not asks:
                return results
            
            best_bid = bids[0][0]
            best_ask = asks[0][0]
            
            # Basic spread calculations
            results['bid_ask_spread_abs'] = self.calculate_bid_ask_spread(best_bid, best_ask, 'absolute')
            results['bid_ask_spread_rel'] = self.calculate_bid_ask_spread(best_bid, best_ask, 'relative')
            results['bid_ask_spread_pct'] = self.calculate_bid_ask_spread(best_bid, best_ask, 'percentage')
            
            # Depth slope
            depth_slopes = self.calculate_depth_slope(order_book)
            results.update(depth_slopes)
            
            # Liquidity imbalance
            results['liquidity_imbalance_volume'] = self.calculate_liquidity_imbalance(order_book, imbalance_type='volume')
            results['liquidity_imbalance_orders'] = self.calculate_liquidity_imbalance(order_book, imbalance_type='order_count')
            
            # Weighted mid price
            results['weighted_mid_price'] = self.calculate_weighted_mid_price(order_book)
            
            # Order book pressure
            pressure_indicators = self.calculate_order_book_pressure(order_book)
            results.update(pressure_indicators)
            
            # Additional derived indicators
            results['mid_price'] = (best_bid + best_ask) / 2
            results['best_bid'] = best_bid
            results['best_ask'] = best_ask
            
        except Exception as e:
            warnings.warn(f"Error processing order book snapshot: {e}")
            
        return results


def create_sample_order_book() -> Dict[str, List[Tuple[float, float]]]:
    """Create sample order book data for testing"""
    return {
        'bids': [
            (100.50, 1000),
            (100.49, 1500),
            (100.48, 2000),
            (100.47, 1200),
            (100.46, 800),
            (100.45, 1800),
            (100.44, 900),
            (100.43, 1100),
            (100.42, 1300),
            (100.41, 700)
        ],
        'asks': [
            (100.51, 1200),
            (100.52, 1800),
            (100.53, 1000),
            (100.54, 1600),
            (100.55, 900),
            (100.56, 1400),
            (100.57, 1100),
            (100.58, 1300),
            (100.59, 800),
            (100.60, 1500)
        ]
    }


if __name__ == "__main__":
    # Example usage
    calculator = MarketMicrostructureIndicators()
    
    # Create sample data
    sample_order_book = create_sample_order_book()
    
    # Calculate all indicators
    results = calculator.process_order_book_snapshot(sample_order_book, "2025-01-01 12:00:00")
    
    print("Market Microstructure Indicators:")
    print("=" * 50)
    for key, value in results.items():
        if isinstance(value, float):
            if not np.isnan(value):
                print(f"{key}: {value:.6f}")
            else:
                print(f"{key}: NaN")
        else:
            print(f"{key}: {value}")