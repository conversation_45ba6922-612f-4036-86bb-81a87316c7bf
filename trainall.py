# train_model.py
# (已集成特征优化流程)

import pandas as pd
import numpy as np
from datetime import datetime
import lightgbm as lgb
from sklearn.calibration import CalibratedClassifierCV
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import roc_auc_score
import joblib
import json
import argparse
import os
import pickle
import optuna

# 假设这些工具函数存在于您的项目中
from model_utils_815 import (
    calculate_features, get_output_dir, get_feature_list
)
import sqlite3

# --- 配置将从命令行参数和配置文件中获取 ---
# 全局变量
TIMEFRAME_MINUTES = None
UP_THRESHOLD = None
DOWN_THRESHOLD = None
MAX_LOOKFORWARD_MINUTES = None
MODEL_BASENAME = None

def get_optimized_feature_list(df_clean, importances_df, top_n=50, corr_threshold=0.9):
    """根据特征重要性和相关性筛选最优特征"""
    print(f"开始特征优化: Top {top_n} 特征, 相关性阈值 {corr_threshold}")
    
    # 选择前N个最重要的特征
    top_features = importances_df.head(top_n)['feature'].tolist()
    print(f"选择前 {len(top_features)} 个重要特征")
    
    # 计算特征相关性矩阵
    feature_data = df_clean[top_features]
    corr_matrix = feature_data.corr().abs()
    
    # 移除高相关性特征
    features_to_remove = set()
    for i in range(len(corr_matrix.columns)):
        for j in range(i+1, len(corr_matrix.columns)):
            if corr_matrix.iloc[i, j] > corr_threshold:
                # 保留重要性更高的特征
                feature_i = corr_matrix.columns[i]
                feature_j = corr_matrix.columns[j]
                
                importance_i = importances_df[importances_df['feature'] == feature_i]['importance'].iloc[0]
                importance_j = importances_df[importances_df['feature'] == feature_j]['importance'].iloc[0]
                
                if importance_i < importance_j:
                    features_to_remove.add(feature_i)
                else:
                    features_to_remove.add(feature_j)
    
    # 最终特征列表
    optimized_features = [f for f in top_features if f not in features_to_remove]
    
    print(f"移除 {len(features_to_remove)} 个高相关性特征")
    print(f"最终优化特征数量: {len(optimized_features)}")
    
    return optimized_features

def create_percentage_target(df, up_threshold, down_threshold, max_lookforward_minutes, timeframe):
    """创建目标标签"""
    max_lookforward_candles = max_lookforward_minutes // timeframe
    print(f"创建目标标签：先涨{up_threshold*100:.1f}%还是先跌{down_threshold*100:.1f}% (最大等待 {max_lookforward_candles} 根K线)")
    
    # 验证参数合理性
    if max_lookforward_candles <= 0:
        print(f"❌ 错误: max_lookforward_candles = {max_lookforward_candles}，请检查时间参数设置")
        return pd.Series(dtype=int)
    
    if len(df) <= max_lookforward_candles:
        print(f"❌ 错误: 数据量 ({len(df)}) 小于等于前瞻窗口 ({max_lookforward_candles})，无法生成标签")
        return pd.Series(dtype=int)
    
    labels = np.full(len(df), np.nan)
    
    # 使用NumPy进行向量化操作以提高效率
    close_prices = df['close'].to_numpy()
    up_targets = close_prices * (1 + up_threshold)
    down_targets = close_prices * (1 - down_threshold)

    print(f"数据范围检查:")
    print(f"  - 总数据量: {len(df)}")
    print(f"  - 可处理范围: {len(df) - max_lookforward_candles}")
    print(f"  - 价格范围: {close_prices.min():.4f} - {close_prices.max():.4f}")
    print(f"  - 平均涨跌幅阈值: ±{up_threshold*100:.1f}%")

    valid_count = 0
    for i in range(len(df) - max_lookforward_candles):
        if i % 20000 == 0:
            print(f"处理标签进度: {i}/{len(df) - max_lookforward_candles} ({i/(len(df) - max_lookforward_candles)*100:.1f}%)")
        
        future_window = close_prices[i+1 : i+1+max_lookforward_candles]
        
        if len(future_window) == 0:
            continue
            
        up_hit_indices = np.where(future_window >= up_targets[i])[0]
        down_hit_indices = np.where(future_window <= down_targets[i])[0]

        first_up_hit = up_hit_indices[0] if len(up_hit_indices) > 0 else np.inf
        first_down_hit = down_hit_indices[0] if len(down_hit_indices) > 0 else np.inf

        if first_up_hit < first_down_hit:
            labels[i] = 1
            valid_count += 1
        elif first_down_hit < first_up_hit:
            labels[i] = 0
            valid_count += 1
        
        # 调试信息：前100个样本
        if i < 5:
            print(f"  样本 {i}: 当前价格={close_prices[i]:.4f}, 上涨目标={up_targets[i]:.4f}, 下跌目标={down_targets[i]:.4f}")
            print(f"    未来窗口: {len(future_window)} 个价格点")
            print(f"    上涨命中: {len(up_hit_indices)} 次, 下跌命中: {len(down_hit_indices)} 次")
            print(f"    标签: {labels[i]}")

    valid_mask = ~np.isnan(labels)
    valid_indices = df.index[valid_mask]
    valid_labels = labels[valid_mask].astype(int)
    
    print(f"有效标签数量: {len(valid_labels)}/{len(df)} ({len(valid_labels)/len(df)*100:.1f}%)")
    label_series = pd.Series(index=valid_indices, data=valid_labels)

    if len(valid_labels) > 0:
        up_count = np.sum(valid_labels)
        down_count = len(valid_labels) - up_count
        print(f"标签分布: 先涨 = {up_count} ({up_count/len(valid_labels)*100:.1f}%), 先跌 = {down_count} ({down_count/len(valid_labels)*100:.1f}%)")
    else:
        print("❌ 未生成任何有效标签。可能原因:")
        print("  1. 涨跌阈值设置过高")
        print("  2. 时间窗口设置不合理") 
        print("  3. 数据波动性不足")
        print("  4. 数据质量问题")
        print(f"建议: 尝试降低阈值 (当前: ±{up_threshold*100:.1f}%) 或增加时间窗口")
    return label_series

def prepare_features_and_labels(df, symbol='ETHUSDT', market='spot'):
    """准备特征和标签"""
    print("在完整数据集上计算增强特征...")
    df_with_features = calculate_features(df.copy(), timeframe=TIMEFRAME_MINUTES)
    
    print("创建目标标签...")
    target_labels = create_percentage_target(df, UP_THRESHOLD, DOWN_THRESHOLD, MAX_LOOKFORWARD_MINUTES, TIMEFRAME_MINUTES)
    
    print("合并特征与标签...")
    df_combined = df_with_features.join(target_labels.rename('label'), how='inner')
    df_clean = df_combined.dropna()
    print(f"清理NaN后剩余 {len(df_clean)} 条记录")
    return df_clean

def load_train_config(config_file='train.json'):
    """加载训练配置文件"""
    if not os.path.exists(config_file):
        print(f"❌ 配置文件 {config_file} 不存在")
        return None
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    return config

def get_coin_config_from_json(config, coin_name):
    """从JSON配置文件获取币种配置"""
    if not config or 'coin_configs' not in config:
        print(f"❌ 配置文件中未找到 coin_configs 部分")
        return None
    
    coin_key = coin_name.lower()
    if coin_key not in config['coin_configs']:
        print(f"❌ 配置文件中未找到币种 {coin_name} 的配置")
        return None
    
    return config['coin_configs'][coin_key]

def split_data_by_config(df_clean, coin_name, config=None):
    """根据配置文件按时间分割数据"""
    if config is None:
        # 使用默认的时间顺序分割
        return split_data_default(df_clean)
    
    coin_key = coin_name.lower()
    if coin_key not in config.get('data', {}):
        print(f"配置文件中未找到币种 {coin_name} 的配置，使用默认分割方式")
        return split_data_default(df_clean)
    
    coin_config = config['data'][coin_key]
    
    # 分别处理训练集、验证集、测试集的时间范围
    train_df = extract_data_by_time_ranges(df_clean, coin_config.get('train', []))
    val_df = extract_data_by_time_ranges(df_clean, coin_config.get('val', []))
    test_df = extract_data_by_time_ranges(df_clean, coin_config.get('test', []))
    
    print(f"=== 按配置文件分割数据 ===")
    print(f"训练集: {len(train_df)} 条记录")
    if len(train_df) > 0:
        print(f"  时间范围: {train_df.index.min()} 到 {train_df.index.max()}")
    
    print(f"验证集: {len(val_df)} 条记录")
    if len(val_df) > 0:
        print(f"  时间范围: {val_df.index.min()} 到 {val_df.index.max()}")
    
    print(f"测试集: {len(test_df)} 条记录")
    if len(test_df) > 0:
        print(f"  时间范围: {test_df.index.min()} 到 {test_df.index.max()}")
    
    return train_df, val_df, test_df

def extract_data_by_time_ranges(df, time_ranges):
    """根据时间范围列表提取数据"""
    if not time_ranges:
        return pd.DataFrame()
    
    combined_df = pd.DataFrame()
    
    for time_range in time_ranges:
        if len(time_range) != 2:
            print(f"警告: 时间范围格式错误 {time_range}，跳过")
            continue
        
        start_time, end_time = time_range
        try:
            start_dt = pd.to_datetime(start_time)
            end_dt = pd.to_datetime(end_time)
            
            # 提取指定时间范围的数据
            mask = (df.index >= start_dt) & (df.index <= end_dt)
            range_df = df[mask].copy()
            
            if len(range_df) > 0:
                print(f"  提取时间段 {start_time} 到 {end_time}: {len(range_df)} 条记录")
                combined_df = pd.concat([combined_df, range_df])
            else:
                print(f"  警告: 时间段 {start_time} 到 {end_time} 无数据")
                
        except Exception as e:
            print(f"错误: 解析时间范围 {time_range} 失败: {e}")
            continue
    
    # 按时间排序并去重
    if len(combined_df) > 0:
        combined_df = combined_df.sort_index().drop_duplicates()
    
    return combined_df

def split_data_default(df_clean):
    """默认的时间顺序分割数据"""
    train_size = int(len(df_clean) * 0.70)
    val_size = int(len(df_clean) * 0.15)
    train_df = df_clean.iloc[:train_size]
    val_df = df_clean.iloc[train_size:train_size + val_size]
    test_df = df_clean.iloc[train_size + val_size:]
    print(f"使用默认分割方式:")
    print(f"训练集: {len(train_df)} ({train_df.index.min()} to {train_df.index.max()})")
    print(f"验证集: {len(val_df)} ({val_df.index.min()} to {val_df.index.max()})")
    print(f"测试集: {len(test_df)} ({test_df.index.min()} to {test_df.index.max()})")
    return train_df, val_df, test_df

def time_series_cross_validation(df_clean, features, target='label', n_splits=2, n_trials=100):
    """使用Optuna进行时序交叉验证"""
    print(f"\n=== 开始使用 Optuna 进行时序交叉验证 (n_splits={n_splits}, n_trials={n_trials}) ===")
    tscv = TimeSeriesSplit(n_splits=n_splits)
    X = df_clean[features]
    y = df_clean[target]

    def objective(trial):
        params = {
            'objective': 'binary', 'metric': 'auc', 'random_state': 42, 'verbose': -1, 'n_jobs': -1,
            'n_estimators': trial.suggest_int('n_estimators', 800, 2500),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.1, log=True),
            'max_depth': trial.suggest_int('max_depth', 5, 12),
            'num_leaves': trial.suggest_int('num_leaves', 20, 150),
            'min_child_samples': trial.suggest_int('min_child_samples', 20, 100),
            'subsample': trial.suggest_float('subsample', 0.7, 1.0),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.7, 1.0),
        }
        fold_scores = []
        for train_idx, val_idx in tscv.split(X):
            X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
            y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]
            if len(set(y_train_fold)) < 2 or len(set(y_val_fold)) < 2: continue
            try:
                lgbm = lgb.LGBMClassifier(**params)
                lgbm.fit(X_train_fold, y_train_fold, eval_set=[(X_val_fold, y_val_fold)], eval_metric='auc',
                         callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False)])
                y_pred_proba = lgbm.predict_proba(X_val_fold)[:, 1]
                fold_scores.append(roc_auc_score(y_val_fold, y_pred_proba))
            except Exception: continue
        return np.mean(fold_scores) if fold_scores else 0

    optuna.logging.set_verbosity(optuna.logging.WARNING)
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials)

    print(f"=== Optuna 交叉验证完成 ===")
    if study.best_value == 0:
        print("⚠️ 警告: Optuna 未找到有效的参数组合，将使用默认参数。")
        return {}, 0
    else:
        print(f"最佳AUC: {study.best_value:.4f}")
        print(f"最佳参数: {study.best_params}")
    
    cv_results_df = study.trials_dataframe()
    output_dir = get_output_dir()
    cv_results_file = os.path.join(output_dir, f'cv_results_optuna_{MODEL_BASENAME}.csv')
    cv_results_df.to_csv(cv_results_file, index=False)
    print(f"交叉验证结果已保存到: {cv_results_file}")
    
    return study.best_params, study.best_value

def load_multi_coin_data(coins, config, args):
    """加载多币种数据并合并"""
    all_data = []
    
    for coin in coins:
        print(f"\n=== 加载币种: {coin.upper()} ===")
        
        # 从配置文件获取币种配置
        coin_config = get_coin_config_from_json(config, coin)
        if coin_config is None:
            print(f"跳过币种 {coin}: 配置不存在")
            continue
        
        # 加载数据
        df = load_data_for_training(
            coin, args.db_path, 
            symbol=args.symbol or coin_config['symbol'],
            interval=args.interval or f"{coin_config['timeframe_minutes']}min",
            market=args.market,
            start_time=args.start_time, 
            end_time=args.end_time
        )
        
        if df is None:
            print(f"跳过币种 {coin}: 数据加载失败")
            continue
        
        # 添加币种标识
        df['coin'] = coin.upper()
        
        # 计算特征 - 使用统一的时间框架
        df_with_features = calculate_features(df.copy(), timeframe=TIMEFRAME_MINUTES)
        
        # 创建标签 - 使用统一的阈值参数
        target_labels = create_percentage_target(
            df, 
            UP_THRESHOLD, 
            DOWN_THRESHOLD, 
            MAX_LOOKFORWARD_MINUTES, 
            TIMEFRAME_MINUTES
        )
        
        # 合并特征与标签
        df_combined = df_with_features.join(target_labels.rename('label'), how='inner')
        df_clean = df_combined.dropna()
        
        print(f"币种 {coin} 清理后数据: {len(df_clean)} 条记录")
        all_data.append(df_clean)
    
    if not all_data:
        print("❌ 没有成功加载任何币种数据")
        return None
    
    # 合并所有币种数据
    combined_df = pd.concat(all_data, ignore_index=False)
    combined_df = combined_df.sort_index()
    
    print(f"\n=== 多币种数据合并完成 ===")
    print(f"总数据量: {len(combined_df)} 条记录")
    print(f"币种分布:")
    coin_counts = combined_df['coin'].value_counts()
    for coin, count in coin_counts.items():
        print(f"  {coin}: {count} 条记录 ({count/len(combined_df)*100:.1f}%)")
    
    return combined_df

def get_multi_coin_config(config, config_name):
    """获取多币种配置"""
    if not config or 'multi_coin_configs' not in config:
        return None
    
    multi_configs = config['multi_coin_configs']
    if config_name not in multi_configs:
        return None
    
    return multi_configs[config_name]

def get_primary_coin_config(config, coins, multi_coin_config=None):
    """获取主要币种配置，用于设置全局参数"""
    if multi_coin_config and 'use_config_from' in multi_coin_config:
        primary_coin = multi_coin_config['use_config_from']
    else:
        primary_coin = coins[0]
    
    return get_coin_config_from_json(config, primary_coin)

def load_data_from_sqlite(db_path, symbol, interval, market='spot', start_time=None, end_time=None):
    """从SQLite数据库加载数据"""
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return None
        
        # 构建表名
        table_name = f"{symbol}_{interval}_{market}"
        
        print(f"从数据库加载数据: {db_path}")
        print(f"表名: {table_name}")
        
        # 首先检查表是否存在
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        if not cursor.fetchone():
            print(f"❌ 表 {table_name} 不存在")
            conn.close()
            return None
        
        # 构建SQL查询
        query = f"SELECT * FROM {table_name}"
        conditions = []
        
        if start_time:
            conditions.append(f"timestamp >= '{start_time}'")
        if end_time:
            conditions.append(f"timestamp <= '{end_time}'")
        
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        query += " ORDER BY timestamp"
        
        if start_time or end_time:
            print(f"时间范围: {start_time or '开始'} 到 {end_time or '结束'}")
        
        # 加载数据
        df = pd.read_sql_query(query, conn, index_col='timestamp', parse_dates=['timestamp'])
        conn.close()
        
        if len(df) == 0:
            print("❌ 查询结果为空")
            return None
        
        print(f"✅ 成功加载 {len(df)} 条记录")
        print(f"时间范围: {df.index[0]} 到 {df.index[-1]}")
        
        # 确保列名正确
        expected_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in expected_columns if col not in df.columns]
        if missing_columns:
            print(f"❌ 缺少必要的列: {missing_columns}")
            return None
        
        # 数据类型转换
        for col in ['open', 'high', 'low', 'close', 'volume']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 检查是否有NaN值
        if df.isnull().any().any():
            print("⚠️ 数据中包含NaN值，将进行清理")
            df = df.dropna()
            print(f"清理后剩余 {len(df)} 条记录")
        
        return df
        
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return None

def load_data_for_training(coin_name, db_path, symbol=None, interval=None, market='spot', start_time=None, end_time=None):
    """为训练加载数据的统一接口"""
    try:
        print(f"\n=== 加载 {coin_name.upper()} 训练数据 ===")
        
        # 使用提供的参数或默认值
        if not symbol:
            symbol = f"{coin_name.upper()}USDT"
        if not interval:
            interval = "5m"  # 默认5分钟
        
        print(f"币种: {coin_name.upper()}")
        print(f"交易对: {symbol}")
        print(f"时间间隔: {interval}")
        print(f"市场类型: {market}")
        
        # 从SQLite加载数据
        df = load_data_from_sqlite(db_path, symbol, interval, market, start_time, end_time)
        
        if df is None:
            print(f"❌ 无法加载 {coin_name} 的数据")
            return None
        
        # 基本数据验证
        if len(df) < 1000:
            print(f"⚠️ 警告: 数据量较少 ({len(df)} 条)，可能影响训练效果")
        
        return df
        
    except Exception as e:
        print(f"❌ 加载训练数据失败: {e}")
        return None

def validate_time_ranges(config, coins):
    """验证配置文件中的时间范围"""
    if not config or 'data' not in config:
        return True
    
    issues = []
    
    for coin in coins:
        coin_key = coin.lower()
        if coin_key not in config['data']:
            issues.append(f"币种 {coin} 在配置文件中未找到")
            continue
        
        coin_data = config['data'][coin_key]
        
        for split_type in ['train', 'val', 'test']:
            if split_type not in coin_data:
                issues.append(f"币种 {coin} 缺少 {split_type} 数据配置")
                continue
            
            time_ranges = coin_data[split_type]
            for i, time_range in enumerate(time_ranges):
                if len(time_range) != 2:
                    issues.append(f"币种 {coin} {split_type} 第{i+1}个时间范围格式错误")
                    continue
                
                try:
                    start_dt = pd.to_datetime(time_range[0])
                    end_dt = pd.to_datetime(time_range[1])
                    
                    if start_dt >= end_dt:
                        issues.append(f"币种 {coin} {split_type} 第{i+1}个时间范围: 开始时间不能晚于结束时间")
                        
                except Exception as e:
                    issues.append(f"币种 {coin} {split_type} 第{i+1}个时间范围解析失败: {e}")
    
    if issues:
        print("⚠️ 配置文件验证发现问题:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    
    print("✅ 配置文件验证通过")
    return True

def train_model(args, coins_list):
    """主训练函数，集成了特征优化逻辑"""
    print(f"开始训练LightGBM模型 - {MODEL_BASENAME.replace('_', ' ').title()}")
    
    # 加载训练配置
    train_config = load_train_config(args.config_file)
    
    data_file = args.data_file or f"{MODEL_BASENAME}.pkl"

    if args.load_data:
        df_clean, train_df, val_df, test_df, _ = load_processed_data(data_file)
        if df_clean is None: args.load_data = False
    
    if not args.load_data:
        # 使用传入的币种列表
        coins = coins_list
        
        if len(coins) > 1:
            print(f"=== 多币种联合训练模式: {', '.join(coins)} ===")
            df_clean = load_multi_coin_data(coins, train_config, args)
            if df_clean is None:
                print("❌ 多币种数据加载失败，退出训练。")
                return
            
            # 对于多币种，使用第一个币种的配置进行分割
            train_df, val_df, test_df = split_data_by_config(df_clean, coins[0], train_config)
        else:
            # 单币种训练
            coin_config = get_coin_config_from_json(train_config, args.coin)
            if coin_config is None:
                print("❌ 币种配置获取失败，退出训练。")
                return
                
            df = load_data_for_training(
                args.coin, args.db_path, 
                symbol=args.symbol or coin_config['symbol'], 
                interval=args.interval or f"{coin_config['timeframe_minutes']}m", 
                market=args.market,
                start_time=args.start_time, 
                end_time=args.end_time
            )
            if df is None: 
                print("❌ 数据加载失败，退出训练。")
                return
            
            df_clean = prepare_features_and_labels(df, args.symbol or coin_config['symbol'], args.market)
            train_df, val_df, test_df = split_data_by_config(df_clean, args.coin, train_config)
        
        if args.save_data:
            # 在保存前获取一次所有特征列表
            all_features = get_feature_list(df_clean, time_frame=TIMEFRAME_MINUTES)
            save_processed_data(df_clean, train_df, val_df, test_df, all_features, data_file)

    target = 'label'
    X_train, y_train = train_df.drop(columns=[target]), train_df[target]
    X_val, y_val = val_df.drop(columns=[target]), val_df[target]
    X_test, y_test = test_df.drop(columns=[target]), test_df[target]

    # 验证数据集是否为空
    if len(train_df) == 0 or len(val_df) == 0 or len(test_df) == 0:
        print("❌ 训练、验证或测试集为空，请检查配置文件中的时间范围设置")
        return

    # --- 特征选择流程 ---
    all_features = get_feature_list(df_clean, time_frame=TIMEFRAME_MINUTES)
    features = all_features

    if args.optimize_features:
        print("\n--- 阶段1: 训练初步模型以获取特征重要性 ---")
        prelim_model = lgb.LGBMClassifier(objective='binary', metric='auc', n_jobs=-1, random_state=42)
        prelim_model.fit(X_train[all_features], y_train)
        
        importances_df = pd.DataFrame({
            'feature': all_features,
            'importance': prelim_model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("--- 阶段2: 根据重要性和相关性筛选最优特征 ---")
        features = get_optimized_feature_list(
            df_clean, importances_df, 
            top_n=args.top_n, 
            corr_threshold=args.corr_threshold
        )
    else:
        print("\n使用所有可用特征进行训练。")

    # --- 模型训练 ---
    best_params = {}
    best_cv_score = 0
    use_time_series_cv = not args.no_time_series_cv

    if use_time_series_cv:
        print(f"🔍 使用时序交叉验证对优化后的 {len(features)} 个特征进行超参数搜索...")
        train_val_df = pd.concat([train_df, val_df])
        best_params, best_cv_score = time_series_cross_validation(
            train_val_df, features, target, n_splits=args.cv_splits, n_trials=args.cv_trials
        )
    else:
        print(f"\n⚠️  禁用时序交叉验证，使用默认参数...")
    
    final_params = {
        'objective': 'binary', 'metric': 'auc', 'n_jobs': -1, 'random_state': 42,
        'n_estimators': 2000, 'learning_rate': 0.05
    }
    final_params.update(best_params)

    print(f"\n开始使用 {len(features)} 个最终特征训练LightGBM模型...")
    lgbm = lgb.LGBMClassifier(**final_params)
    lgbm.fit(X_train[features], y_train, eval_set=[(X_val[features], y_val)], eval_metric='auc',
             callbacks=[lgb.early_stopping(stopping_rounds=100, verbose=False)])

    print("基础模型训练完成，开始概率校准...")
    calibrated_model = CalibratedClassifierCV(lgbm, method='isotonic', cv='prefit')
    calibrated_model.fit(X_val[features], y_val)

    print("概率校准完成，开始阈值优化...")
    val_probabilities = calibrated_model.predict_proba(X_val[features])[:, 1]
    best_score, best_threshold = find_best_threshold(val_probabilities, y_val)
    print(f"验证集上的最优得分: {best_score:.0f}, 最优信心阈值: {best_threshold:.3f}")

    evaluate_on_test_set(calibrated_model, X_test, y_test, test_df, best_threshold, features)
    
    # 准备保存的配置
    coins = coins_list
    extra_config = {
        'time_series_cv_used': use_time_series_cv,
        'feature_optimization_used': args.optimize_features,
        'final_feature_count': len(features),
        'coins_used': coins,
        'multi_coin_training': len(coins) > 1,
        'config_file_used': args.config_file
    }
    if use_time_series_cv:
        extra_config.update({'cv_splits': args.cv_splits, 'cv_trials': args.cv_trials,
                             'best_cv_score': float(best_cv_score), 'best_cv_params': best_params})
    if args.optimize_features:
        extra_config.update({'top_n_features': args.top_n, 'corr_threshold': args.corr_threshold})

    save_model_and_config(calibrated_model, features, best_threshold, len(X_train), len(X_val), len(X_test), extra_config)
    analyze_feature_importance(lgbm, features)

def find_best_threshold(probabilities, y_true):
    """寻找最优预测阈值"""
    thresholds_to_test = np.arange(0.52, 0.80, 0.01)
    best_score, best_threshold = -np.inf, 0.5
    for threshold in thresholds_to_test:
        predictions = np.where(probabilities > threshold, 1, np.where(probabilities < (1 - threshold), 0, -1))
        correct_trades = (predictions == y_true) & (predictions != -1)
        incorrect_trades = (predictions != y_true) & (predictions != -1)
        current_score = correct_trades.sum() - incorrect_trades.sum()
        if current_score > best_score:
            best_score, best_threshold = current_score, threshold
    return best_score, best_threshold

# ... 其他函数 (save_processed_data, load_processed_data, evaluate_on_test_set,等) 保持类似结构 ...
# (此处省略未大改的函数以保持简洁，实际使用时请保留它们)

def save_processed_data(df_clean, train_df, val_df, test_df, features, data_file):
    print(f"保存预处理数据到 {data_file}...")
    data_dict = {'df_clean': df_clean, 'train_df': train_df, 'val_df': val_df, 'test_df': test_df, 'features': features, 'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    with open(data_file, 'wb') as f: pickle.dump(data_dict, f)
    print(f"数据已保存到 {data_file}")

def load_processed_data(data_file):
    print(f"从 {data_file} 加载预处理数据...")
    if not os.path.exists(data_file):
        print(f"错误：数据文件 {data_file} 不存在！"); return None, None, None, None, None
    with open(data_file, 'rb') as f: data_dict = pickle.load(f)
    print(f"数据加载完成，保存时间: {data_dict.get('save_time', '未知')}")
    return data_dict['df_clean'], data_dict['train_df'], data_dict['val_df'], data_dict['test_df'], data_dict.get('features')

def evaluate_on_test_set(model, X_test, y_test, test_df, best_threshold, features):
    """在测试集上评估模型"""
    print(f"\n--- 测试集评估 (使用阈值: {best_threshold:.3f}) ---")
    
    # 确保测试集只包含模型训练时使用的特征
    X_test_final = X_test[features]
    
    test_probabilities = model.predict_proba(X_test_final)[:, 1]
    results_log = []

    for i in range(len(test_probabilities)):
        prob, actual_result = test_probabilities[i], y_test.iloc[i]
        current_price, timestamp = test_df.iloc[i]['close'], test_df.index[i]
        guess = -1
        if prob > best_threshold: guess = 1
        elif prob < (1 - best_threshold): guess = 0
        
        log_entry = {'Timestamp': timestamp, 'ClosePrice': current_price, 'ConfidenceUp': prob,
                     'Prediction': guess, 'ActualResult': actual_result,
                     'Score': 1 if guess == actual_result and guess != -1 else (-1 if guess != -1 else 0)}
        results_log.append(log_entry)

    results_df = pd.DataFrame(results_log)
    trades_made = (results_df['Prediction'] != -1).sum()
    wins = (results_df['Score'] == 1).sum()
    losses = (results_df['Score'] == -1).sum()
    print(f"总样本数: {len(test_df)}, 猜测次数: {trades_made} ({trades_made/len(test_df)*100:.2f}%)")
    if trades_made > 0: print(f"胜率: {wins/trades_made*100:.2f}%")
    print(f"总得分: {wins - losses:+d}")

    results_filename = os.path.join(get_output_dir(), f'test_results_{MODEL_BASENAME}.csv')
    results_df.to_csv(results_filename, index=False, float_format='%.4f')
    print(f"\n详细测试结果已保存到: {results_filename}")

def save_model_and_config(model, features, best_threshold, train_size, val_size, test_size, extra_config=None):
    output_dir = get_output_dir()
    model_file = os.path.join(output_dir, f'{MODEL_BASENAME}_model.joblib')
    config_file = os.path.join(output_dir, f'{MODEL_BASENAME}_config.json')
    
    joblib.dump(model, model_file)
    config = {
        'best_threshold': best_threshold, 'feature_list': features, 'model_type': f'LGBM_{MODEL_BASENAME}',
        'target_description': f'predict_first_{UP_THRESHOLD*100}%_move_within_{MAX_LOOKFORWARD_MINUTES}_minutes',
        'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'train_size': train_size, 'val_size': val_size, 'test_size': test_size,
        'up_threshold': UP_THRESHOLD, 'down_threshold': DOWN_THRESHOLD,
        'max_lookforward_minutes': MAX_LOOKFORWARD_MINUTES, 'timeframe_minutes': TIMEFRAME_MINUTES
    }
    if extra_config: config.update(extra_config)
    with open(config_file, 'w') as f: json.dump(config, f, indent=2)
    print(f"\n模型和配置已保存。\n模型文件: {model_file}\n配置文件: {config_file}")

def analyze_feature_importance(lgbm, features):
    output_dir = get_output_dir()
    importance_df = pd.DataFrame({'feature': features, 'importance': lgbm.feature_importances_}).sort_values('importance', ascending=False)
    print("\n" + "="*20 + " 特征重要性 (Top 20) " + "="*20)
    print(importance_df.head(20).to_string(index=False))
    importance_file = os.path.join(output_dir, f'feature_importance_{MODEL_BASENAME}.csv')
    importance_df.to_csv(importance_file, index=False)
    print(f"\n完整特征重要性已保存到 {importance_file}")

def main():
    parser = argparse.ArgumentParser(description="多币种 LGBM 模型训练与验证器")
    parser.add_argument("--coin", default="ETH", help="币种名称 (如: DOT, SEI)")
    parser.add_argument("--coins", nargs='+', help="多币种训练 (如: --coins ETH BTC DOT)")
    parser.add_argument("--multi-coin-config", help="使用配置文件中的多币种配置 (如: crypto_major)")
    parser.add_argument("--mode", choices=['train', 'validate'], default='train', help="运行模式")
    parser.add_argument("--save-data", action='store_true', help="保存预处理后的数据")
    parser.add_argument("--load-data", action='store_true', help="加载预处理后的数据")
    parser.add_argument("--data-file", help="预处理数据文件路径")
    parser.add_argument("--no-time-series-cv", action='store_true', help="禁用时序交叉验证")
    parser.add_argument("--cv-splits", type=int, default=2, help="CV分割数")
    parser.add_argument("--cv-trials", type=int, default=100, help="Optuna尝试次数")
    parser.add_argument("--db-path", default='coin_data.db', help="SQLite数据库路径")
    parser.add_argument("--symbol", help="交易对符号")
    parser.add_argument("--interval", help="时间间隔")
    parser.add_argument("--market", choices=['spot', 'futures'], default='spot', help="市场类型")
    parser.add_argument("--start-time", help="数据开始时间 (YYYY-MM-DD)")
    parser.add_argument("--end-time", help="数据结束时间 (YYYY-MM-DD)")
    parser.add_argument("--config-file", default="train.json", help="训练配置文件路径")
    
    # --- 特征优化参数 ---
    parser.add_argument("--optimize-features", action='store_true', help="启用特征优化流程")
    parser.add_argument("--top-n", type=int, default=50, help="特征优化：选择最重要的N个特征")
    parser.add_argument("--corr-threshold", type=float, default=0.9, help="特征优化：移除相关性的阈值")

    args = parser.parse_args()

    # 加载配置文件
    train_config = load_train_config(args.config_file)
    if train_config is None:
        exit(1)

    # 确定使用的币种列表
    coins = []
    multi_config = None
    if args.multi_coin_config:
        # 从配置文件加载多币种配置
        multi_config = get_multi_coin_config(train_config, args.multi_coin_config)
        if multi_config:
            coins = multi_config['coins']
            print(f"使用多币种配置 '{args.multi_coin_config}': {multi_config.get('description', '')}")
        else:
            print(f"❌ 未找到多币种配置 '{args.multi_coin_config}'")
            exit(1)
    elif args.coins:
        coins = args.coins
    else:
        coins = [args.coin]
    
    # 获取主要币种配置
    primary_coin_config = get_primary_coin_config(train_config, coins, multi_config)
    if primary_coin_config is None: 
        print(f"❌ 无法获取主要币种配置")
        exit(1)

    global TIMEFRAME_MINUTES, UP_THRESHOLD, DOWN_THRESHOLD, MAX_LOOKFORWARD_MINUTES, MODEL_BASENAME
    TIMEFRAME_MINUTES = primary_coin_config['timeframe_minutes']
    UP_THRESHOLD = primary_coin_config['up_threshold']
    DOWN_THRESHOLD = primary_coin_config['down_threshold']
    MAX_LOOKFORWARD_MINUTES = primary_coin_config['max_lookforward_minutes']
    
    # 对于多币种训练，调整模型名称
    if len(coins) > 1:
        MODEL_BASENAME = f"multi_coin_{'_'.join([c.lower() for c in coins])}"
        print(f"=== 多币种联合训练模型任务 ({args.mode}) ===")
        print(f"币种: {', '.join(coins)}")
        print(f"使用 {coins[0] if not multi_config else multi_config.get('use_config_from', coins[0])} 的参数配置")
    else:
        MODEL_BASENAME = primary_coin_config['model_basename']
        print(f"=== {primary_coin_config['display_name']} 模型任务 ({args.mode}) ===")
    
    if args.mode == 'train':
        print(f"配置文件: {args.config_file}")
        print(f"特征优化: {'启用' if args.optimize_features else '禁用'}")
        if args.optimize_features:
            print(f" - Top N: {args.top_n}, Corr Threshold: {args.corr_threshold}")
        
        # 验证配置文件
        if not validate_time_ranges(train_config, coins):
            print("❌ 配置文件验证失败，请修正后重试")
            exit(1)
        
        train_model(args, coins)
    else:
        # 验证模式目前不涉及训练，直接使用配置文件中的特征
        # validate_model(...) # 验证逻辑可以根据需要调整
        print("验证模式待实现。")

if __name__ == '__main__':
    main()