#!/usr/bin/env python3
"""
SuperTrend 参数优化使用示例
演示如何使用不同的优化方法来找到最佳 SuperTrend 参数
"""

import subprocess
import sys
import os
from datetime import datetime, timedel<PERSON>

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    print(f"命令: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        if result.returncode == 0:
            print("✅ 执行成功")
            # 只显示最后几行输出
            output_lines = result.stdout.strip().split('\n')
            if len(output_lines) > 20:
                print("...")
                print('\n'.join(output_lines[-20:]))
            else:
                print(result.stdout)
        else:
            print("❌ 执行失败")
            print(result.stderr)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("⏰ 执行超时")
        return False
    except Exception as e:
        print(f"❌ 执行出错: {e}")
        return False

def main():
    """主函数"""
    print("SuperTrend 参数优化示例")
    print("=" * 40)
    
    # 检查必要文件
    required_files = [
        "backtest_money_quick.py",
        "quick_supertrend_optimize.py", 
        "optimize_supertrend_params.py"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return
    
    print("✅ 所有必要文件都存在")
    
    # 设置时间范围（最近一周）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    start_time_str = start_date.strftime("%Y-%m-%d")
    end_time_str = end_date.strftime("%Y-%m-%d")
    
    print(f"\n📅 优化时间范围: {start_time_str} 到 {end_time_str}")
    
    # 示例 1: 快速网格搜索优化
    print(f"\n📊 示例 1: 快速网格搜索优化")
    quick_cmd = [
        "python", "quick_supertrend_optimize.py",
        "--coin", "ETH",
        "--interval", "5m", 
        "--start-time", start_time_str,
        "--end-time", end_time_str,
        "--max-workers", "4"
    ]
    
    success1 = run_command(quick_cmd, "运行快速网格搜索优化")
    
    if success1:
        print("\n💡 快速优化的优势:")
        print("  • 全面覆盖参数空间")
        print("  • 并行计算加速")
        print("  • 多维度结果排序")
        print("  • 适合初步筛选")
    
    # 示例 2: 使用最佳参数进行回测验证
    print(f"\n🧪 示例 2: 使用推荐参数进行回测验证")
    backtest_cmd = [
        "python", "backtest_money_quick.py",
        "--coin", "ETH",
        "--interval", "5m",
        "--start-time", start_time_str,
        "--end-time", end_time_str,
        "--use-supertrend",
        "--supertrend-interval", "1h",
        "--supertrend-atr-period", "14", 
        "--supertrend-multiplier", "2.0",
        "--initial-capital", "1000",
        "--risk-per-trade", "2.0",
        "--quick"
    ]
    
    success2 = run_command(backtest_cmd, "使用推荐参数进行回测验证")
    
    if success2:
        print("\n📈 回测验证的意义:")
        print("  • 确认优化结果的有效性")
        print("  • 查看详细的交易记录")
        print("  • 分析风险和收益特征")
    
    # 示例 3: 对比测试（有无 SuperTrend 过滤）
    print(f"\n⚖️ 示例 3: 对比测试（无 SuperTrend 过滤）")
    compare_cmd = [
        "python", "backtest_money_quick.py",
        "--coin", "ETH", 
        "--interval", "5m",
        "--start-time", start_time_str,
        "--end-time", end_time_str,
        "--initial-capital", "1000",
        "--risk-per-trade", "2.0", 
        "--quick"
    ]
    
    success3 = run_command(compare_cmd, "运行无 SuperTrend 过滤的对比回测")
    
    if success3:
        print("\n📊 对比分析建议:")
        print("  • 比较总收益率差异")
        print("  • 分析胜率变化")
        print("  • 评估最大回撤改善")
        print("  • 观察交易频率变化")
    
    # 总结和建议
    print(f"\n{'='*60}")
    print("🎯 优化流程总结")
    print(f"{'='*60}")
    
    print("\n📋 推荐的优化流程:")
    print("1. 🔍 快速网格搜索 - 全面筛选参数")
    print("2. 🧪 回测验证 - 确认最佳参数")
    print("3. ⚖️ 对比分析 - 评估改善效果")
    print("4. 📈 长期测试 - 验证参数稳定性")
    
    print("\n⚙️ 参数调优建议:")
    print("• ATR 周期: 7-20 (短期敏感 vs 长期稳定)")
    print("• ATR 倍数: 2.0-4.0 (过滤强度)")
    print("• 时间间隔: 5m-1h (趋势识别周期)")
    
    print("\n📊 评估指标权重:")
    print("• 总收益率: 40% (主要目标)")
    print("• 胜率: 30% (稳定性)")
    print("• 最大回撤: 20% (风险控制)")
    print("• 夏普比率: 10% (风险调整收益)")
    
    print("\n🔄 定期优化建议:")
    print("• 每月重新优化参数")
    print("• 根据市场环境调整")
    print("• 关注参数稳定性")
    print("• 避免过度拟合")
    
    print(f"\n✅ 示例演示完成！")
    
    # 显示生成的文件
    output_files = [
        "supertrend_quick_optimization_ETH_5m_*.csv",
        "backtest_money_log_quick.csv"
    ]
    
    print(f"\n📁 生成的文件:")
    for pattern in output_files:
        import glob
        files = glob.glob(pattern)
        for file in files:
            if os.path.exists(file):
                print(f"  📄 {file}")

if __name__ == "__main__":
    main()