"""
Advanced Market Microstructure Analysis
Extended functionality for Level 2/3 market data analysis
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import warnings
from collections import deque
from datetime import datetime, timedelta
import json

class AdvancedMarketMicrostructure:
    """
    Advanced market microstructure analysis with time series capabilities
    """
    
    def __init__(self, history_length: int = 1000):
        self.history_length = history_length
        self.order_book_history = deque(maxlen=history_length)
        self.indicators_history = deque(maxlen=history_length)
        
    def calculate_effective_spread(self, 
                                 trade_price: float, 
                                 mid_price: float, 
                                 trade_side: str) -> float:
        """
        Calculate effective spread from trade data
        
        Args:
            trade_price: Actual trade price
            mid_price: Mid price at time of trade
            trade_side: 'buy' or 'sell'
            
        Returns:
            Effective spread
        """
        if trade_side.lower() == 'buy':
            return 2 * (trade_price - mid_price)
        elif trade_side.lower() == 'sell':
            return 2 * (mid_price - trade_price)
        else:
            return np.nan
    
    def calculate_realized_spread(self, 
                                trade_price: float, 
                                mid_price_t0: float, 
                                mid_price_t1: float, 
                                trade_side: str) -> float:
        """
        Calculate realized spread (permanent price impact)
        
        Args:
            trade_price: Trade price at t0
            mid_price_t0: Mid price at t0
            mid_price_t1: Mid price at t1 (after some time)
            trade_side: 'buy' or 'sell'
            
        Returns:
            Realized spread
        """
        if trade_side.lower() == 'buy':
            return 2 * (mid_price_t1 - mid_price_t0)
        elif trade_side.lower() == 'sell':
            return 2 * (mid_price_t0 - mid_price_t1)
        else:
            return np.nan
    
    def calculate_price_impact(self, 
                             order_book_before: Dict[str, List[Tuple[float, float]]], 
                             order_book_after: Dict[str, List[Tuple[float, float]]], 
                             trade_volume: float, 
                             trade_side: str) -> Dict[str, float]:
        """
        Calculate price impact from order book changes
        
        Args:
            order_book_before: Order book before trade
            order_book_after: Order book after trade
            trade_volume: Volume of the trade
            trade_side: 'buy' or 'sell'
            
        Returns:
            Dictionary with price impact metrics
        """
        results = {
            'temporary_impact': np.nan,
            'permanent_impact': np.nan,
            'impact_per_volume': np.nan
        }
        
        try:
            # Calculate mid prices
            bids_before = sorted(order_book_before.get('bids', []), key=lambda x: x[0], reverse=True)
            asks_before = sorted(order_book_before.get('asks', []), key=lambda x: x[0])
            
            bids_after = sorted(order_book_after.get('bids', []), key=lambda x: x[0], reverse=True)
            asks_after = sorted(order_book_after.get('asks', []), key=lambda x: x[0])
            
            if not (bids_before and asks_before and bids_after and asks_after):
                return results
            
            mid_before = (bids_before[0][0] + asks_before[0][0]) / 2
            mid_after = (bids_after[0][0] + asks_after[0][0]) / 2
            
            # Calculate impacts
            if trade_side.lower() == 'buy':
                results['temporary_impact'] = asks_before[0][0] - mid_before
                results['permanent_impact'] = mid_after - mid_before
            elif trade_side.lower() == 'sell':
                results['temporary_impact'] = mid_before - bids_before[0][0]
                results['permanent_impact'] = mid_before - mid_after
            
            # Impact per unit volume
            if trade_volume > 0:
                results['impact_per_volume'] = results['permanent_impact'] / trade_volume
                
        except Exception as e:
            warnings.warn(f"Error calculating price impact: {e}")
            
        return results
    
    def calculate_order_flow_imbalance(self, 
                                     trades: List[Dict[str, Union[float, str]]], 
                                     time_window: int = 60) -> float:
        """
        Calculate order flow imbalance over time window
        
        Args:
            trades: List of trades with 'volume', 'side', 'timestamp'
            time_window: Time window in seconds
            
        Returns:
            Order flow imbalance (-1 to 1)
        """
        try:
            if not trades:
                return 0.0
            
            # Filter trades within time window
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(seconds=time_window)
            
            buy_volume = 0.0
            sell_volume = 0.0
            
            for trade in trades:
                trade_time = datetime.fromisoformat(trade['timestamp']) if isinstance(trade['timestamp'], str) else trade['timestamp']
                
                if trade_time >= cutoff_time:
                    if trade['side'].lower() == 'buy':
                        buy_volume += trade['volume']
                    elif trade['side'].lower() == 'sell':
                        sell_volume += trade['volume']
            
            total_volume = buy_volume + sell_volume
            if total_volume == 0:
                return 0.0
            
            return (buy_volume - sell_volume) / total_volume
            
        except Exception as e:
            warnings.warn(f"Error calculating order flow imbalance: {e}")
            return np.nan
    
    def calculate_vpin(self, 
                      trades: List[Dict[str, Union[float, str]]], 
                      volume_bucket_size: float = 10000) -> float:
        """
        Calculate Volume-Synchronized Probability of Informed Trading (VPIN)
        
        Args:
            trades: List of trades
            volume_bucket_size: Size of volume buckets
            
        Returns:
            VPIN value
        """
        try:
            if not trades:
                return np.nan
            
            # Group trades into volume buckets
            buckets = []
            current_bucket = {'buy_volume': 0.0, 'sell_volume': 0.0, 'total_volume': 0.0}
            
            for trade in trades:
                volume = trade['volume']
                side = trade['side'].lower()
                
                if current_bucket['total_volume'] + volume <= volume_bucket_size:
                    # Add to current bucket
                    current_bucket['total_volume'] += volume
                    if side == 'buy':
                        current_bucket['buy_volume'] += volume
                    elif side == 'sell':
                        current_bucket['sell_volume'] += volume
                else:
                    # Start new bucket
                    if current_bucket['total_volume'] > 0:
                        buckets.append(current_bucket)
                    
                    current_bucket = {'buy_volume': 0.0, 'sell_volume': 0.0, 'total_volume': volume}
                    if side == 'buy':
                        current_bucket['buy_volume'] = volume
                    elif side == 'sell':
                        current_bucket['sell_volume'] = volume
            
            # Add last bucket if not empty
            if current_bucket['total_volume'] > 0:
                buckets.append(current_bucket)
            
            if len(buckets) < 2:
                return np.nan
            
            # Calculate VPIN
            vpin_values = []
            for bucket in buckets:
                if bucket['total_volume'] > 0:
                    imbalance = abs(bucket['buy_volume'] - bucket['sell_volume'])
                    vpin_values.append(imbalance / bucket['total_volume'])
            
            return np.mean(vpin_values) if vpin_values else np.nan
            
        except Exception as e:
            warnings.warn(f"Error calculating VPIN: {e}")
            return np.nan
    
    def calculate_kyle_lambda(self, 
                            price_changes: List[float], 
                            signed_volumes: List[float]) -> float:
        """
        Calculate Kyle's lambda (price impact coefficient)
        
        Args:
            price_changes: List of price changes
            signed_volumes: List of signed trade volumes (positive for buy, negative for sell)
            
        Returns:
            Kyle's lambda
        """
        try:
            if len(price_changes) != len(signed_volumes) or len(price_changes) < 2:
                return np.nan
            
            # Linear regression: price_change = lambda * signed_volume + error
            coeffs = np.polyfit(signed_volumes, price_changes, 1)
            return coeffs[0]  # Lambda is the slope
            
        except Exception as e:
            warnings.warn(f"Error calculating Kyle's lambda: {e}")
            return np.nan
    
    def calculate_roll_spread(self, prices: List[float]) -> float:
        """
        Calculate Roll's spread estimator
        
        Args:
            prices: List of transaction prices
            
        Returns:
            Roll's spread estimate
        """
        try:
            if len(prices) < 2:
                return np.nan
            
            # Calculate first differences
            price_changes = np.diff(prices)
            
            if len(price_changes) < 2:
                return np.nan
            
            # Calculate autocovariance at lag 1
            autocovariance = np.cov(price_changes[:-1], price_changes[1:])[0, 1]
            
            if autocovariance >= 0:
                return 0.0  # No bid-ask bounce detected
            
            return 2 * np.sqrt(-autocovariance)
            
        except Exception as e:
            warnings.warn(f"Error calculating Roll's spread: {e}")
            return np.nan
    
    def calculate_amihud_illiquidity(self, 
                                   returns: List[float], 
                                   volumes: List[float]) -> float:
        """
        Calculate Amihud's illiquidity measure
        
        Args:
            returns: List of returns
            volumes: List of volumes
            
        Returns:
            Amihud illiquidity measure
        """
        try:
            if len(returns) != len(volumes) or len(returns) == 0:
                return np.nan
            
            illiquidity_values = []
            for ret, vol in zip(returns, volumes):
                if vol > 0:
                    illiquidity_values.append(abs(ret) / vol)
            
            return np.mean(illiquidity_values) if illiquidity_values else np.nan
            
        except Exception as e:
            warnings.warn(f"Error calculating Amihud illiquidity: {e}")
            return np.nan
    
    def add_order_book_snapshot(self, 
                              order_book: Dict[str, List[Tuple[float, float]]], 
                              timestamp: Optional[str] = None):
        """
        Add order book snapshot to history
        
        Args:
            order_book: Order book data
            timestamp: Optional timestamp
        """
        snapshot = {
            'timestamp': timestamp or datetime.now().isoformat(),
            'order_book': order_book
        }
        self.order_book_history.append(snapshot)
    
    def get_historical_indicators(self, lookback_periods: int = 100) -> pd.DataFrame:
        """
        Get historical indicators as DataFrame
        
        Args:
            lookback_periods: Number of periods to look back
            
        Returns:
            DataFrame with historical indicators
        """
        if not self.indicators_history:
            return pd.DataFrame()
        
        # Get recent indicators
        recent_indicators = list(self.indicators_history)[-lookback_periods:]
        
        # Convert to DataFrame
        df = pd.DataFrame(recent_indicators)
        
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
        
        return df
    
    def calculate_rolling_statistics(self, 
                                   indicator_name: str, 
                                   window: int = 20) -> Dict[str, float]:
        """
        Calculate rolling statistics for an indicator
        
        Args:
            indicator_name: Name of the indicator
            window: Rolling window size
            
        Returns:
            Dictionary with rolling statistics
        """
        df = self.get_historical_indicators()
        
        if df.empty or indicator_name not in df.columns:
            return {'mean': np.nan, 'std': np.nan, 'min': np.nan, 'max': np.nan}
        
        series = df[indicator_name].dropna()
        
        if len(series) < window:
            return {'mean': np.nan, 'std': np.nan, 'min': np.nan, 'max': np.nan}
        
        rolling_stats = series.rolling(window=window)
        
        return {
            'mean': rolling_stats.mean().iloc[-1],
            'std': rolling_stats.std().iloc[-1],
            'min': rolling_stats.min().iloc[-1],
            'max': rolling_stats.max().iloc[-1]
        }


def create_sample_trade_data() -> List[Dict[str, Union[float, str]]]:
    """Create sample trade data for testing"""
    trades = []
    base_time = datetime.now()
    
    for i in range(100):
        trade = {
            'timestamp': (base_time - timedelta(seconds=i)).isoformat(),
            'price': 100.50 + np.random.normal(0, 0.1),
            'volume': np.random.exponential(1000),
            'side': np.random.choice(['buy', 'sell'])
        }
        trades.append(trade)
    
    return trades


if __name__ == "__main__":
    # Example usage
    analyzer = AdvancedMarketMicrostructure()
    
    # Create sample data
    sample_trades = create_sample_trade_data()
    
    # Calculate various indicators
    print("Advanced Market Microstructure Analysis:")
    print("=" * 50)
    
    # Order flow imbalance
    ofi = analyzer.calculate_order_flow_imbalance(sample_trades, time_window=300)
    print(f"Order Flow Imbalance (5min): {ofi:.6f}")
    
    # VPIN
    vpin = analyzer.calculate_vpin(sample_trades, volume_bucket_size=5000)
    print(f"VPIN: {vpin:.6f}")
    
    # Roll's spread
    prices = [trade['price'] for trade in sample_trades]
    roll_spread = analyzer.calculate_roll_spread(prices)
    print(f"Roll's Spread: {roll_spread:.6f}")
    
    # Kyle's lambda
    price_changes = np.diff(prices)
    signed_volumes = [trade['volume'] * (1 if trade['side'] == 'buy' else -1) for trade in sample_trades[1:]]
    kyle_lambda = analyzer.calculate_kyle_lambda(price_changes.tolist(), signed_volumes)
    print(f"Kyle's Lambda: {kyle_lambda:.8f}")
    
    # Amihud illiquidity
    returns = price_changes / prices[:-1]
    volumes = [trade['volume'] for trade in sample_trades[1:]]
    amihud = analyzer.calculate_amihud_illiquidity(returns.tolist(), volumes)
    print(f"Amihud Illiquidity: {amihud:.8f}")