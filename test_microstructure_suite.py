"""
Test Suite for Market Microstructure Indicators
Comprehensive testing of all microstructure functionality
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import sys
import traceback

# Import our modules
from market_microstructure_indicators import MarketMicrostructureIndicators, create_sample_order_book
from advanced_market_microstructure import AdvancedMarketMicrostructure, create_sample_trade_data
from microstructure_trading_signals import MicrostructureTradingSignals, SignalType

# Skip data integration test if websockets not available
try:
    from market_data_integration_example import MarketDataProcessor
    HAS_WEBSOCKETS = True
except ImportError:
    HAS_WEBSOCKETS = False

def test_basic_indicators():
    """Test basic market microstructure indicators"""
    print("Testing Basic Indicators...")
    print("-" * 30)
    
    try:
        calculator = MarketMicrostructureIndicators()
        sample_book = create_sample_order_book()
        
        # Test individual calculations
        spread_abs = calculator.calculate_bid_ask_spread(100.50, 100.51, 'absolute')
        spread_rel = calculator.calculate_bid_ask_spread(100.50, 100.51, 'relative')
        spread_pct = calculator.calculate_bid_ask_spread(100.50, 100.51, 'percentage')
        
        print(f"Bid-Ask Spread (absolute): {spread_abs:.4f}")
        print(f"Bid-Ask Spread (relative): {spread_rel:.6f}")
        print(f"Bid-Ask Spread (percentage): {spread_pct:.4f}%")
        
        # Test depth slope
        depth_slopes = calculator.calculate_depth_slope(sample_book)
        print(f"Bid Slope: {depth_slopes['bid_slope']:.2f}")
        print(f"Ask Slope: {depth_slopes['ask_slope']:.2f}")
        
        # Test liquidity imbalance
        imbalance = calculator.calculate_liquidity_imbalance(sample_book)
        print(f"Liquidity Imbalance: {imbalance:.4f}")
        
        # Test comprehensive snapshot processing
        results = calculator.process_order_book_snapshot(sample_book)
        print(f"Weighted Mid Price: {results.get('weighted_mid_price', 'N/A'):.4f}")
        
        print("✓ Basic indicators test passed\n")
        return True
        
    except Exception as e:
        print(f"✗ Basic indicators test failed: {e}")
        traceback.print_exc()
        return False

def test_advanced_indicators():
    """Test advanced market microstructure indicators"""
    print("Testing Advanced Indicators...")
    print("-" * 30)
    
    try:
        analyzer = AdvancedMarketMicrostructure()
        sample_trades = create_sample_trade_data()
        
        # Test order flow imbalance
        ofi = analyzer.calculate_order_flow_imbalance(sample_trades, time_window=300)
        print(f"Order Flow Imbalance (5min): {ofi:.4f}")
        
        # Test VPIN
        vpin = analyzer.calculate_vpin(sample_trades, volume_bucket_size=5000)
        print(f"VPIN: {vpin:.4f}")
        
        # Test Roll's spread
        prices = [trade['price'] for trade in sample_trades]
        roll_spread = analyzer.calculate_roll_spread(prices)
        print(f"Roll's Spread: {roll_spread:.4f}")
        
        # Test Kyle's lambda
        price_changes = np.diff(prices)
        signed_volumes = [trade['volume'] * (1 if trade['side'] == 'buy' else -1) 
                         for trade in sample_trades[1:]]
        kyle_lambda = analyzer.calculate_kyle_lambda(price_changes.tolist(), signed_volumes)
        print(f"Kyle's Lambda: {kyle_lambda:.8f}")
        
        # Test Amihud illiquidity
        returns = price_changes / prices[:-1]
        volumes = [trade['volume'] for trade in sample_trades[1:]]
        amihud = analyzer.calculate_amihud_illiquidity(returns.tolist(), volumes)
        print(f"Amihud Illiquidity: {amihud:.8f}")
        
        print("✓ Advanced indicators test passed\n")
        return True
        
    except Exception as e:
        print(f"✗ Advanced indicators test failed: {e}")
        traceback.print_exc()
        return False

def test_data_integration():
    """Test market data integration"""
    print("Testing Data Integration...")
    print("-" * 30)
    
    if not HAS_WEBSOCKETS:
        print("⚠️  Skipping data integration test (websockets not available)")
        return True
    
    try:
        processor = MarketDataProcessor("TESTCOIN")
        
        # Simulate some order book updates
        base_price = 1000.0
        
        for i in range(10):
            # Generate realistic order book
            mid_price = base_price + np.random.normal(0, 1)
            spread = 0.02  # 2 bps
            
            bids = []
            asks = []
            
            # Generate levels
            for j in range(5):
                bid_price = mid_price - spread/2 - j * 0.01
                ask_price = mid_price + spread/2 + j * 0.01
                volume = np.random.exponential(100)
                
                bids.append([str(bid_price), str(volume)])
                asks.append([str(ask_price), str(volume)])
            
            order_book_data = {'bids': bids, 'asks': asks}
            processor.update_order_book(order_book_data)
            
            # Add some trades
            if i % 3 == 0:
                trade_data = {
                    'T': int(datetime.now().timestamp() * 1000),
                    'p': str(mid_price + np.random.normal(0, 0.005)),
                    'q': str(np.random.exponential(50)),
                    'm': np.random.choice([True, False]),
                    't': i
                }
                processor.add_trade(trade_data)
        
        # Get summary
        summary = processor.get_current_summary()
        print(f"Processed {len(processor.indicators_df)} snapshots")
        print(f"Current spread: {summary.get('current_spread_pct', 'N/A')}")
        print(f"Current imbalance: {summary.get('current_liquidity_imbalance', 'N/A')}")
        
        print("✓ Data integration test passed\n")
        return True
        
    except Exception as e:
        print(f"✗ Data integration test failed: {e}")
        traceback.print_exc()
        return False

def test_trading_signals():
    """Test trading signal generation"""
    print("Testing Trading Signals...")
    print("-" * 30)
    
    try:
        signal_generator = MicrostructureTradingSignals()
        
        # Test different market scenarios
        scenarios = [
            {
                'name': 'Bullish scenario',
                'indicators': {
                    'timestamp': datetime.now().isoformat(),
                    'bid_ask_spread_pct': 0.008,  # Low spread
                    'liquidity_imbalance_volume': 0.4,  # Bid heavy
                    'order_flow_imbalance_1min': 0.5,  # Buy pressure
                    'vpin': 0.2,  # Low informed trading
                    'bid_slope': -1500,
                    'ask_slope': -800
                }
            },
            {
                'name': 'Bearish scenario',
                'indicators': {
                    'timestamp': (datetime.now() + timedelta(seconds=60)).isoformat(),
                    'bid_ask_spread_pct': 0.015,
                    'liquidity_imbalance_volume': -0.4,  # Ask heavy
                    'order_flow_imbalance_1min': -0.6,  # Sell pressure
                    'vpin': 0.3,
                    'bid_slope': -600,
                    'ask_slope': -1200
                }
            },
            {
                'name': 'Neutral scenario',
                'indicators': {
                    'timestamp': (datetime.now() + timedelta(seconds=120)).isoformat(),
                    'bid_ask_spread_pct': 0.012,
                    'liquidity_imbalance_volume': 0.1,
                    'order_flow_imbalance_1min': 0.1,
                    'vpin': 0.4,
                    'bid_slope': -800,
                    'ask_slope': -850
                }
            }
        ]
        
        signals_generated = 0
        
        for scenario in scenarios:
            signal = signal_generator.generate_signal(scenario['indicators'])
            
            if signal:
                signals_generated += 1
                print(f"{scenario['name']}: {signal.signal_type.name} "
                      f"(confidence: {signal.confidence:.3f})")
            else:
                print(f"{scenario['name']}: No signal")
        
        print(f"Generated {signals_generated} signals out of {len(scenarios)} scenarios")
        print("✓ Trading signals test passed\n")
        return True
        
    except Exception as e:
        print(f"✗ Trading signals test failed: {e}")
        traceback.print_exc()
        return False

def test_performance_analysis():
    """Test performance analysis functionality"""
    print("Testing Performance Analysis...")
    print("-" * 30)
    
    try:
        # Create some sample data for performance testing
        signal_generator = MicrostructureTradingSignals()
        
        # Generate some signals
        base_time = datetime.now()
        for i in range(5):
            indicators = {
                'timestamp': (base_time + timedelta(minutes=i*5)).isoformat(),
                'bid_ask_spread_pct': 0.01 + np.random.normal(0, 0.005),
                'liquidity_imbalance_volume': np.random.normal(0, 0.3),
                'order_flow_imbalance_1min': np.random.normal(0, 0.4),
                'vpin': 0.4 + np.random.normal(0, 0.1),
            }
            signal_generator.generate_signal(indicators)
        
        # Create sample price data
        price_data = []
        base_price = 1000.0
        for i in range(100):
            timestamp = (base_time + timedelta(minutes=i)).isoformat()
            price = base_price + np.cumsum(np.random.normal(0, 0.1))[0]
            price_data.append((timestamp, price))
        
        # Analyze performance
        performance = signal_generator.get_signal_performance(price_data, holding_period=300)
        
        if performance:
            print(f"Total signals: {performance.get('total_signals', 0)}")
            print(f"Win rate: {performance.get('win_rate', 0):.3f}")
            print(f"Average return: {performance.get('avg_return', 0):.6f}")
            print(f"Sharpe ratio: {performance.get('sharpe_ratio', 0):.3f}")
        else:
            print("No performance data available")
        
        print("✓ Performance analysis test passed\n")
        return True
        
    except Exception as e:
        print(f"✗ Performance analysis test failed: {e}")
        traceback.print_exc()
        return False

def run_comprehensive_test():
    """Run all tests"""
    print("Market Microstructure Indicators Test Suite")
    print("=" * 50)
    print()
    
    tests = [
        test_basic_indicators,
        test_advanced_indicators,
        test_data_integration,
        test_trading_signals,
        test_performance_analysis
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"Test {test_func.__name__} crashed: {e}")
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print(f"⚠️  {total - passed} tests failed")
    
    return passed == total

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)