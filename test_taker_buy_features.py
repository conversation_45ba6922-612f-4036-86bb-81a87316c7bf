#!/usr/bin/env python3
"""
测试主动买卖量特征的计算
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目根目录到路径
sys.path.append('/Users/<USER>/project/ai/vibe/daily/eth-trade')

from model_utils_815 import load_and_prepare_data_from_db, calculate_features, get_feature_list

def test_taker_buy_features():
    """测试主动买卖量特征计算"""
    
    print("=" * 60)
    print("测试主动买卖量特征计算")
    print("=" * 60)
    
    # 1. 从数据库加载数据
    db_path = "/Users/<USER>/project/ai/vibe/daily/eth-trade/coin_data.db"
    table_name = "ETHUSDT_5min_spot"
    
    print(f"从数据库加载数据: {table_name}")
    df = load_and_prepare_data_from_db(db_path, table_name, limit=10000)
    
    if df is None:
        print("❌ 数据加载失败")
        return False
    
    print(f"✅ 数据加载成功，共 {len(df)} 条记录")
    print(f"数据列: {list(df.columns)}")
    
    # 2. 检查原始数据
    print("\n" + "=" * 40)
    print("原始数据检查")
    print("=" * 40)
    
    print("前5行数据:")
    print(df.head())
    
    print(f"\n主动买卖量数据统计:")
    if 'taker_buy_base_volume' in df.columns:
        print(f"taker_buy_base_volume: min={df['taker_buy_base_volume'].min():.2f}, max={df['taker_buy_base_volume'].max():.2f}, mean={df['taker_buy_base_volume'].mean():.2f}")
        print(f"volume: min={df['volume'].min():.2f}, max={df['volume'].max():.2f}, mean={df['volume'].mean():.2f}")
        
        # 计算基本比例
        buy_ratio = df['taker_buy_base_volume'] / df['volume']
        print(f"主动买入比例: min={buy_ratio.min():.3f}, max={buy_ratio.max():.3f}, mean={buy_ratio.mean():.3f}")
    else:
        print("❌ 未找到 taker_buy_base_volume 列")
        return False
    
    # 3. 计算特征
    print("\n" + "=" * 40)
    print("计算特征")
    print("=" * 40)
    
    timeframe = 5  # 5分钟
    df_with_features = calculate_features(df, timeframe)
    
    print(f"✅ 特征计算完成，数据形状: {df_with_features.shape}")
    
    # 4. 检查新增的主动买卖量特征
    print("\n" + "=" * 40)
    print("主动买卖量特征检查")
    print("=" * 40)
    
    taker_features = [
        'taker_buy_ratio', 'taker_sell_ratio', 'buy_sell_ratio', 'buy_sell_diff',
        'taker_buy_ratio_ma_120', 'taker_buy_ratio_dev_120',
        'buy_sell_ratio_ma_120', 'buy_sell_ratio_dev_120',
        'taker_buy_ratio_std_120', 'buy_sell_ratio_std_120',
        'taker_buy_ratio_change_1', 'buy_sell_ratio_change_1',
        'extreme_buy', 'extreme_sell', 'balanced_trading',
        'price_vs_buy_pressure'
    ]
    
    available_taker_features = [col for col in taker_features if col in df_with_features.columns]
    print(f"可用的主动买卖量特征 ({len(available_taker_features)}):")
    for feature in available_taker_features:
        values = df_with_features[feature].dropna()
        if len(values) > 0:
            print(f"  {feature}: min={values.min():.4f}, max={values.max():.4f}, mean={values.mean():.4f}")
        else:
            print(f"  {feature}: 全部为NaN")
    
    # 5. 检查特征列表
    print("\n" + "=" * 40)
    print("特征列表检查")
    print("=" * 40)
    
    feature_list = get_feature_list(df_with_features, timeframe)
    print(f"总特征数量: {len(feature_list)}")
    
    taker_features_in_list = [f for f in feature_list if any(keyword in f for keyword in ['taker', 'buy_sell', 'extreme', 'balanced'])]
    print(f"主动买卖量相关特征 ({len(taker_features_in_list)}):")
    for feature in taker_features_in_list:
        print(f"  {feature}")
    
    # 6. 数据质量检查
    print("\n" + "=" * 40)
    print("数据质量检查")
    print("=" * 40)
    
    # 检查NaN值
    nan_counts = df_with_features[available_taker_features].isnull().sum()
    print("NaN值统计:")
    for feature, nan_count in nan_counts.items():
        if nan_count > 0:
            print(f"  {feature}: {nan_count} ({nan_count/len(df_with_features)*100:.1f}%)")
    
    # 检查极值
    print("\n极值检查:")
    for feature in ['taker_buy_ratio', 'buy_sell_ratio']:
        if feature in df_with_features.columns:
            values = df_with_features[feature].dropna()
            if len(values) > 0:
                q1, q99 = values.quantile([0.01, 0.99])
                print(f"  {feature}: 1%分位={q1:.4f}, 99%分位={q99:.4f}")
    
    # 7. 保存样本数据用于检查
    print("\n" + "=" * 40)
    print("保存样本数据")
    print("=" * 40)
    
    sample_features = ['close', 'volume', 'taker_buy_base_volume'] + available_taker_features[:10]
    sample_data = df_with_features[sample_features].tail(100)
    
    output_file = "/Users/<USER>/project/ai/vibe/daily/eth-trade/taker_buy_features_sample.csv"
    sample_data.to_csv(output_file)
    print(f"✅ 样本数据已保存到: {output_file}")
    
    print("\n" + "=" * 60)
    print("✅ 主动买卖量特征测试完成")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = test_taker_buy_features()
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)
