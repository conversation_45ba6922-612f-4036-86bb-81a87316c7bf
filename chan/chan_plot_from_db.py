#!/usr/bin/env python3
# -*- coding:utf-8 -*-

"""
使用coin_data.db中的数据进行缠论分析和画图
"""

import sys
import os
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime

# 添加chan模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Chan import CChan
from ChanConfig import CChanConfig
from Common.CEnum import DATA_SRC, KL_TYPE, AUTYPE
from Plot.PlotDriver import CPlotDriver

class CoinDataReader:
    """从coin_data.db读取数据的适配器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
    
    def get_available_tables(self):
        """获取所有可用的数据表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name != 'table_info'")
        tables = [row[0] for row in cursor.fetchall()]
        conn.close()
        return tables
    
    def load_kline_data(self, symbol: str, interval: str, market: str = 'spot', limit: int = 2000):
        """
        加载K线数据并转换为chan.py需要的格式
        
        Args:
            symbol: 交易对符号，如 'ETHUSDT'
            interval: 时间间隔，如 '15min', '5min', '1day'
            market: 市场类型，'spot' 或 'futures'
            limit: 限制返回的数据条数
            
        Returns:
            适配chan.py的K线数据列表
        """
        table_name = f"{symbol}_{interval}_{market}"
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 查询最新的数据
            query = f"""
            SELECT timestamp, open, high, low, close, volume
            FROM {table_name}
            ORDER BY timestamp ASC
            LIMIT {limit}
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                print(f"No data found for {table_name}")
                return None
            
            # 转换为chan.py需要的格式
            kline_data = []
            for _, row in df.iterrows():
                # 将时间戳转换为datetime字符串
                dt_str = datetime.fromtimestamp(row['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                
                kline_data.append([
                    dt_str,           # 时间
                    float(row['open']),    # 开盘价
                    float(row['close']),   # 收盘价
                    float(row['high']),    # 最高价
                    float(row['low']),     # 最低价
                    float(row['volume']),  # 成交量
                    0.0,              # 成交额（暂时设为0）
                    0.0               # 换手率（暂时设为0）
                ])
            
            return kline_data
            
        except Exception as e:
            print(f"Error loading data from {table_name}: {e}")
            return None

class CustomDataAPI:
    """自定义数据API，用于从coin_data.db读取数据"""
    
    def __init__(self, db_path: str):
        self.reader = CoinDataReader(db_path)
        self.symbol = None
        self.interval = None
        self.market = None
        self.limit = None
    
    def set_params(self, symbol: str, interval: str, market: str = 'spot', limit: int = 2000):
        """设置查询参数"""
        self.symbol = symbol
        self.interval = interval
        self.market = market
        self.limit = limit
    
    def get_kl_data(self, code, begin_date=None, end_date=None, autype=AUTYPE.QFQ):
        """获取K线数据 - chan.py框架调用的接口"""
        if not all([self.symbol, self.interval, self.market]):
            raise ValueError("请先调用set_params设置查询参数")
        
        return self.reader.load_kline_data(self.symbol, self.interval, self.market, self.limit)

def plot_chan_from_db(symbol: str, interval: str, market: str = 'spot', 
                     limit: int = 1000, save_path: str = None):
    """
    从数据库读取数据并进行缠论分析画图
    
    Args:
        symbol: 交易对符号
        interval: 时间间隔
        market: 市场类型
        limit: 数据条数限制
        save_path: 保存路径
    """
    
    # 创建自定义数据API
    db_path = os.path.join(os.path.dirname(__file__), 'coin_data.db')
    data_api = CustomDataAPI(db_path)
    data_api.set_params(symbol, interval, market, limit)
    
    # 缠论配置
    config = CChanConfig({
        "bi_strict": True,
        "seg_algo": "chan",
        "zs_combine": True,
        "zs_combine_mode": "zs",
        "divergence_rate": 0.9,
        "min_zs_cnt": 1,
        "max_bs2_rate": 0.618,
        "bs1_peak": True,
        "macd_algo": "peak",
        "bs_type": '1,2,3a,3b',
        "mean_metrics": [5, 20],
        "boll_n": 20,
        "macd": {
            "fast": 12,
            "slow": 26,
            "signal": 9
        }
    })
    
    # 根据interval确定K线级别
    kl_type_map = {
        '1min': KL_TYPE.K_1M,
        '5min': KL_TYPE.K_5M,
        '15min': KL_TYPE.K_15M,
        '30min': KL_TYPE.K_30M,
        '1hour': KL_TYPE.K_60M,
        '1day': KL_TYPE.K_DAY
    }
    
    kl_type = kl_type_map.get(interval, KL_TYPE.K_15M)
    
    try:
        # 创建CChan对象
        chan = CChan(
            code=f"{symbol}_{interval}_{market}",
            begin_time=None,
            end_time=None,
            data_src="custom:CoinDataAPI:CustomDataAPI",  # 使用自定义数据源
            lv_list=[kl_type],
            config=config,
            autype=AUTYPE.QFQ
        )
        
        # 手动设置数据
        kline_data = data_api.get_kl_data(None)
        if not kline_data:
            print(f"无法获取 {symbol}_{interval}_{market} 的数据")
            return
        
        print(f"成功加载 {len(kline_data)} 条K线数据")
        
        # 画图配置
        plot_config = {
            "plot_kline": True,
            "plot_kline_combine": True,
            "plot_bi": True,
            "plot_seg": True,
            "plot_zs": True,
            "plot_bsp": True,
            "plot_macd": True,
            "plot_mean": True,
            "plot_boll": True,
        }
        
        plot_para = {
            "figure": {
                "width": 20,
                "height": 12,
            },
            "bi": {
                "show_num": True,
                "disp_end": True,
            },
            "seg": {
                "plot_trendline": True,
                "show_num": True,
            },
            "zs": {
                "show_text": True,
            },
            "bsp": {
                "fontsize": 12,
            }
        }
        
        # 生成图表
        if not save_path:
            save_path = f"{symbol}_{interval}_{market}_chan_analysis.png"
        
        plot_driver = CPlotDriver(
            chan,
            plot_config=plot_config,
            plot_para=plot_para,
        )
        
        # 保存图片
        plot_driver.figure.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"缠论分析图表已保存: {save_path}")
        
        # 显示分析结果统计
        chan_data = chan[kl_type]
        print(f"\n=== 缠论分析结果 ===")
        print(f"笔数量: {len(chan_data.bi_list)}")
        print(f"线段数量: {len(chan_data.seg_list)}")
        print(f"中枢数量: {len(chan_data.zs_list)}")
        print(f"买卖点数量: {len(chan_data.bs_point_lst)}")
        
        return chan
        
    except Exception as e:
        print(f"缠论分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def list_available_data():
    """列出所有可用的数据"""
    db_path = os.path.join(os.path.dirname(__file__), 'coin_data.db')
    reader = CoinDataReader(db_path)
    tables = reader.get_available_tables()
    
    if not tables:
        print("没有找到可用的数据表")
        return
    
    print(f"找到 {len(tables)} 个数据表:\n")
    
    # 按交易对分组显示
    data_by_symbol = {}
    for table in tables:
        parts = table.split('_')
        if len(parts) >= 3:
            symbol = parts[0]
            interval = parts[1]
            market = parts[2]
            
            if symbol not in data_by_symbol:
                data_by_symbol[symbol] = []
            data_by_symbol[symbol].append((interval, market))
    
    for symbol in sorted(data_by_symbol.keys()):
        print(f"{symbol}:")
        for interval, market in sorted(data_by_symbol[symbol]):
            print(f"  {interval} {market}")
        print()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='使用coin_data.db进行缠论分析')
    parser.add_argument('--symbol', '-s', type=str, help='交易对符号 (如: ETHUSDT)')
    parser.add_argument('--interval', '-i', type=str, help='时间间隔 (如: 15min, 5min)')
    parser.add_argument('--market', '-m', type=str, default='spot', help='市场类型 (默认: spot)')
    parser.add_argument('--limit', '-l', type=int, default=1000, help='数据条数限制 (默认: 1000)')
    parser.add_argument('--output', '-o', type=str, help='输出文件名')
    parser.add_argument('--list', action='store_true', help='列出所有可用数据')
    
    args = parser.parse_args()
    
    if args.list:
        list_available_data()
        return
    
    if not args.symbol or not args.interval:
        print("请指定 --symbol 和 --interval 参数，或使用 --list 查看可用数据")
        return
    
    # 进行缠论分析
    plot_chan_from_db(args.symbol, args.interval, args.market, args.limit, args.output)

if __name__ == "__main__":
    main()