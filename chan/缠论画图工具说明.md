# 缠论画图工具 - 最终版本

## 🎯 功能特点

✅ **显示标准K线图**：红涨绿跌的经典K线显示  
✅ **自动识别线段**：基于缠论理论的线段构建  
✅ **标记中枢区域**：重要的支撑阻力位  
✅ **成交量分析**：配合价格分析的成交量图表  
✅ **简单易用**：一键生成专业图表  

## 🚀 快速使用

### 生成单个图表
```bash
cd chan
python simple_zen_plot.py --symbol ETHUSDT --interval 15min --limit 300
```

### 指定时间范围
```bash
# 指定日期范围
python simple_zen_plot.py --symbol ETHUSDT --interval 15min --start-time "2024-01-01" --end-time "2024-01-31"

# 指定具体时间范围
python simple_zen_plot.py --symbol ETHUSDT --interval 5min --start-time "2024-08-01 10:00" --end-time "2024-08-01 18:00"

# 只指定开始时间
python simple_zen_plot.py --symbol BTCUSDT --interval 5min --start-time "2024-08-01" --limit 300
```

### 批量生成图表
```bash
python simple_zen_plot.py --batch
```

### 查看可用数据
```bash
python simple_zen_plot.py --list
```

## 📊 图表说明

### 主图（上方）
- **K线图**：
  - 红色K线：上涨（收盘价 > 开盘价）
  - 绿色K线：下跌（收盘价 < 开盘价）
  - 黑色影线：最高价和最低价
  
- **线段**：
  - 红色粗线：上升线段
  - 绿色粗线：下降线段
  
- **中枢**：
  - 橙色半透明矩形：重要的价格区间

### 成交量图（下方）
- 红色柱：上涨时的成交量
- 绿色柱：下跌时的成交量

## 📈 支持的数据

### 主要交易对
- **ETHUSDT** (以太坊): 1min, 5min, 15min, 1day
- **BTCUSDT** (比特币): 5min
- **SUIUSDT** (SUI): 5min, 15min
- **UNIUSDT** (Uniswap): 5min, 15min
- **DOTUSDT** (Polkadot): 5min, 15min
- **LINKUSDT** (Chainlink): 5min, 15min

### 时间周期
- 1min, 5min, 15min, 30min, 1hour, 1day

## 🎯 实际应用

### 交易策略
1. **中枢交易**：在橙色中枢区域寻找买卖机会
2. **线段突破**：关注红绿线段的突破点
3. **支撑阻力**：中枢上下边界作为关键位置

### 风险管理
- 利用中枢边界设置止损位
- 观察线段方向判断趋势
- 结合成交量确认信号强度

## 📋 使用示例

### 基础分析
```bash
# ETH 15分钟图，300根K线
python simple_zen_plot.py --symbol ETHUSDT --interval 15min --limit 300

# BTC 5分钟图，400根K线
python simple_zen_plot.py --symbol BTCUSDT --interval 5min --limit 400

# SUI 15分钟图，500根K线
python simple_zen_plot.py --symbol SUIUSDT --interval 15min --limit 500
```

### 时间范围分析
```bash
# 分析2024年1月的ETH数据
python simple_zen_plot.py --symbol ETHUSDT --interval 15min --start-time "2024-01-01" --end-time "2024-01-31"

# 分析特定日期的日内数据
python simple_zen_plot.py --symbol ETHUSDT --interval 5min --start-time "2024-08-01 09:00" --end-time "2024-08-01 17:00"

# 从某个时间点开始的最新数据
python simple_zen_plot.py --symbol BTCUSDT --interval 5min --start-time "2024-08-01" --limit 500
```

### 自定义输出
```bash
# 指定输出文件名
python simple_zen_plot.py --symbol ETHUSDT --interval 15min --output eth_analysis.png

# 时间范围分析并指定文件名
python simple_zen_plot.py --symbol ETHUSDT --interval 15min --start-time "2024-01-01" --end-time "2024-01-31" --output eth_jan_2024.png
```

## 📊 分析结果示例

**ETHUSDT 15分钟数据 (300根K线)**：
- 构建了 30 个线段
- 发现了 20 个中枢

**BTCUSDT 5分钟数据 (400根K线)**：
- 构建了 37 个线段  
- 发现了 17 个中枢

**SUIUSDT 15分钟数据 (500根K线)**：
- 构建了 52 个线段
- 发现了 38 个中枢

## 🔧 技术原理

### 线段识别
1. 寻找价格的局部极值点（分型）
2. 连接分型点形成笔
3. 将同向笔合并成线段

### 中枢识别
1. 寻找三个或以上线段的重叠区间
2. 计算重叠区域的上下边界
3. 标记为重要的价格中枢

## 📁 输出文件

生成的图表保存为PNG格式：
```
{交易对}_{时间周期}_{市场类型}_简单缠论分析.png
```

例如：
- `ETHUSDT_15min_spot_简单缠论分析.png`
- `BTCUSDT_5min_spot_简单缠论分析.png`

## ⏰ 时间范围功能

### 支持的时间格式
- `YYYY-MM-DD` - 如：2024-01-01
- `YYYY-MM-DD HH:MM` - 如：2024-01-01 10:00
- `YYYY-MM-DD HH:MM:SS` - 如：2024-01-01 10:00:00
- `MM-DD HH:MM` - 如：01-15 10:00 (使用当前年份)
- `YYYY/MM/DD` - 如：2024/01/01

### 时间范围使用场景
- **特定月份分析**：`--start-time "2024-01-01" --end-time "2024-01-31"`
- **日内交易分析**：`--start-time "2024-08-01 09:00" --end-time "2024-08-01 17:00"`
- **最近数据分析**：`--start-time "2024-08-01" --limit 500`
- **历史回测**：`--start-time "2023-12-01" --end-time "2023-12-31"`

### 时间范围优势
- ✅ **精确控制**：分析特定时间段的市场行为
- ✅ **事件分析**：研究重要事件前后的价格走势
- ✅ **策略回测**：在历史数据上验证交易策略
- ✅ **性能优化**：只加载需要的数据，提高处理速度

## ⚡ 性能优化

### 推荐数据量
- **短期分析**：200-500根K线
- **中期分析**：500-1000根K线  
- **长期分析**：1000-2000根K线

### 时间周期选择
- **日内交易**：5min, 15min
- **短线交易**：15min, 30min
- **中线交易**：1hour, 1day

## 🛠️ 故障排除

### 常见问题
1. **数据不存在**：使用 `--list` 查看可用数据
2. **图表异常**：检查数据量是否足够（建议至少100根K线）
3. **中文显示**：字体问题不影响分析结果

### 调试命令
```bash
# 检查可用数据
python simple_zen_plot.py --list

# 小数据量测试
python simple_zen_plot.py --symbol ETHUSDT --interval 15min --limit 100
```

## 🎉 总结

这个工具提供了：
- ✅ 清晰的K线图显示
- ✅ 专业的缠论线段分析
- ✅ 直观的中枢区域标记
- ✅ 实用的成交量信息
- ✅ 简单的操作方式

现在你可以轻松地对任何加密货币进行专业的缠论分析了！

**开始使用**：
```bash
cd chan
python simple_zen_plot.py --symbol ETHUSDT --interval 15min --limit 300
```

祝你交易顺利！🚀📈