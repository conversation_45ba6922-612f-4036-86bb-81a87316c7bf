#!/usr/bin/env python3
# -*- coding:utf-8 -*-

"""
简单的缠论画图示例
使用ZenPlot工具从coin_data.db生成缠论图表
"""

import sys
import os

# 添加ZenPlot路径
zenplot_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'ZenPlot')
sys.path.append(zenplot_path)

from sqlite_zen_plotter import SQLiteZenPlotter

def main():
    """主函数 - 生成几个常用的缠论图表"""
    
    # 数据库路径
    db_path = os.path.join(os.path.dirname(__file__), 'coin_data.db')
    
    # 创建绘图器
    plotter = SQLiteZenPlotter(db_path)
    
    # 获取可用的数据表
    tables = plotter.reader.get_available_tables()
    print("可用的数据表:")
    for i, table in enumerate(tables[:10]):  # 显示前10个
        print(f"{i+1}. {table}")
    print()
    
    # 生成几个常用的图表
    charts_to_generate = [
        ('ETHUSDT', '15min', 'spot', 800, 'ETH_15min_缠论分析.png'),
        ('ETHUSDT', '5min', 'spot', 1000, 'ETH_5min_缠论分析.png'),
        ('BTCUSDT', '5min', 'spot', 1000, 'BTC_5min_缠论分析.png'),
        ('SUIUSDT', '15min', 'spot', 800, 'SUI_15min_缠论分析.png'),
    ]
    
    for symbol, interval, market, limit, filename in charts_to_generate:
        table_name = f"{symbol}_{interval}_{market}"
        if table_name in tables:
            print(f"正在生成 {symbol} {interval} {market} 缠论图表...")
            try:
                plotter.plot_chan_chart(symbol, interval, market, limit, filename)
                print(f"✓ 成功生成: {filename}")
            except Exception as e:
                print(f"✗ 生成失败: {e}")
        else:
            print(f"⚠ 跳过 {table_name} (数据不存在)")
        print()

if __name__ == "__main__":
    main()