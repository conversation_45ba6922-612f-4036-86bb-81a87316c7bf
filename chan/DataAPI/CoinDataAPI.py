#!/usr/bin/env python3
# -*- coding:utf-8 -*-

"""
coin_data.db数据源适配器
用于chan.py框架从SQLite数据库读取K线数据
"""

import sqlite3
import pandas as pd
from datetime import datetime
from typing import List, Optional
import sys
import os

class CCoinDataAPI:
    """coin_data.db数据API"""
    
    def __init__(self, db_path: str = 'coin_data.db'):
        self.db_path = db_path
        self._symbol = None
        self._interval = None
        self._market = None
        self._limit = None
    
    def set_params(self, symbol: str, interval: str, market: str = 'spot', limit: int = 2000):
        """设置查询参数"""
        self._symbol = symbol
        self._interval = interval
        self._market = market
        self._limit = limit
    
    def get_kl_data(self, code, begin_date=None, end_date=None, autype=None):
        """
        获取K线数据
        
        Args:
            code: 股票代码（这里会被忽略，使用set_params设置的参数）
            begin_date: 开始日期（暂时忽略）
            end_date: 结束日期（暂时忽略）
            autype: 复权类型（暂时忽略）
            
        Returns:
            K线数据列表，格式: [[时间, 开盘, 收盘, 最高, 最低, 成交量, 成交额, 换手率], ...]
        """
        if not all([self._symbol, self._interval, self._market]):
            raise ValueError("请先调用set_params设置查询参数")
        
        table_name = f"{self._symbol}_{self._interval}_{self._market}"
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 构建查询语句
            query = f"""
            SELECT timestamp, open, high, low, close, volume, quote_volume
            FROM {table_name}
            ORDER BY timestamp ASC
            """
            
            if self._limit:
                query += f" LIMIT {self._limit}"
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                print(f"No data found for {table_name}")
                return []
            
            # 转换为chan.py需要的格式
            kline_data = []
            for _, row in df.iterrows():
                # 将时间戳转换为datetime字符串
                dt_str = datetime.fromtimestamp(row['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                
                kline_data.append([
                    dt_str,                    # 时间
                    float(row['open']),        # 开盘价
                    float(row['close']),       # 收盘价
                    float(row['high']),        # 最高价
                    float(row['low']),         # 最低价
                    float(row['volume']),      # 成交量
                    float(row.get('quote_volume', 0)),  # 成交额
                    0.0                        # 换手率（暂时设为0）
                ])
            
            return kline_data
            
        except Exception as e:
            print(f"Error loading data from {table_name}: {e}")
            return []
    
    def get_available_tables(self) -> List[str]:
        """获取所有可用的数据表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name != 'table_info'")
            tables = [row[0] for row in cursor.fetchall()]
            conn.close()
            return tables
        except Exception as e:
            print(f"Error getting tables: {e}")
            return []
    
    def get_data_info(self, symbol: str, interval: str, market: str = 'spot') -> dict:
        """获取数据基本信息"""
        table_name = f"{symbol}_{interval}_{market}"
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取数据条数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            
            # 获取时间范围
            cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table_name}")
            min_ts, max_ts = cursor.fetchone()
            
            conn.close()
            
            return {
                'table_name': table_name,
                'count': count,
                'start_time': datetime.fromtimestamp(min_ts).strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': datetime.fromtimestamp(max_ts).strftime('%Y-%m-%d %H:%M:%S'),
                'start_timestamp': min_ts,
                'end_timestamp': max_ts
            }
            
        except Exception as e:
            print(f"Error getting info for {table_name}: {e}")
            return {}

# 全局实例，用于chan.py框架调用
coin_data_api = CCoinDataAPI()

def set_coin_data_params(symbol: str, interval: str, market: str = 'spot', limit: int = 2000):
    """设置全局API参数的便捷函数"""
    coin_data_api.set_params(symbol, interval, market, limit)