# 使用 coin_data.db 进行缠论分析

本目录包含了使用 `coin_data.db` 数据库进行缠论分析和画图的工具。

## 文件说明

### 核心文件
- `simple_chan_plot.py` - 使用 ZenPlot 工具的简单示例
- `example_coin_chan.py` - 使用原生 chan.py 框架的完整示例
- `DataAPI/CoinDataAPI.py` - coin_data.db 数据源适配器
- `README_coin_data.md` - 本说明文档

### 数据库结构
`coin_data.db` 包含多个加密货币的K线数据表，命名格式为：`{SYMBOL}_{INTERVAL}_{MARKET}`

例如：
- `ETHUSDT_15min_spot` - ETH/USDT 15分钟现货数据
- `BTCUSDT_5min_spot` - BTC/USDT 5分钟现货数据
- `ETHUSDT_5min_futures` - ETH/USDT 5分钟期货数据

## 使用方法

### 1. 查看可用数据

```bash
# 使用 ZenPlot 工具查看
cd ZenPlot
python zen_plot_cli.py --list

# 使用 chan.py 框架查看
cd chan
python example_coin_chan.py --list
```

### 2. 使用 ZenPlot 工具（推荐新手使用）

ZenPlot 是一个简化的缠论分析工具，使用简单，生成的图表清晰易懂。

```bash
cd chan
python simple_chan_plot.py
```

或者使用命令行工具：

```bash
cd ZenPlot
python zen_plot_cli.py --symbol ETHUSDT --interval 15min --limit 800
python zen_plot_cli.py --symbol BTCUSDT --interval 5min --limit 1000 --output btc_chart.png
```

### 3. 使用原生 chan.py 框架（高级用户）

原生框架功能更强大，支持更多配置选项和分析功能。

```bash
cd chan
python example_coin_chan.py --symbol ETHUSDT --interval 15min --limit 1000
python example_coin_chan.py --symbol BTCUSDT --interval 5min --limit 800 --output btc_analysis.png
```

## 支持的参数

### 交易对符号 (symbol)
- ETHUSDT - 以太坊/USDT
- BTCUSDT - 比特币/USDT
- SUIUSDT - SUI/USDT
- UNIUSDT - Uniswap/USDT
- DOTUSDT - Polkadot/USDT
- LINKUSDT - Chainlink/USDT
- 等等...

### 时间间隔 (interval)
- 1min - 1分钟
- 5min - 5分钟
- 15min - 15分钟
- 30min - 30分钟
- 1hour - 1小时
- 1day - 1天

### 市场类型 (market)
- spot - 现货市场（默认）
- futures - 期货市场

## 生成的图表说明

### ZenPlot 图表包含：
1. **价格图表**（上方）
   - 价格线和填充区域
   - 线段（红色上升，绿色下降）
   - 中枢（彩色矩形区域）

2. **成交量图表**（中间）
   - 红绿柱状图表示成交量

3. **MACD 图表**（下方）
   - DIF 和 DEA 线
   - MACD 柱状图

### chan.py 框架图表包含：
1. **完整的缠论元素**
   - K线图
   - 笔（蓝色线条）
   - 线段（粗线条）
   - 中枢（矩形区域）
   - 买卖点标记
   - 趋势线

2. **技术指标**
   - MACD
   - 均线
   - 布林线

## 示例用法

### 快速开始
```bash
# 1. 查看可用数据
cd chan
python example_coin_chan.py --list

# 2. 生成 ETH 15分钟缠论图表
python example_coin_chan.py --symbol ETHUSDT --interval 15min

# 3. 生成 BTC 5分钟图表，限制800条数据
python example_coin_chan.py --symbol BTCUSDT --interval 5min --limit 800
```

### 批量生成图表
```bash
cd chan
python simple_chan_plot.py
```

## 缠论要素说明

- **分型**：局部高点或低点
- **笔**：连接相邻异向分型的直线
- **线段**：由多个同向笔组成的更大级别结构
- **中枢**：三个或以上线段的重叠区间
- **买卖点**：根据缠论理论识别的交易机会点

## 注意事项

1. 确保 `coin_data.db` 文件存在且包含数据
2. 数据量较大时处理时间较长，建议使用 `--limit` 参数限制数据量
3. 生成的图表文件会保存在当前目录
4. 如果遇到问题，请先使用 `--list` 参数检查数据是否存在

## 故障排除

### 常见问题

1. **找不到数据表**
   ```
   错误: 找不到数据表 ETHUSDT_15min_spot
   ```
   解决：使用 `--list` 参数查看可用数据

2. **数据库文件不存在**
   ```
   Error loading data: no such table
   ```
   解决：确保 `coin_data.db` 文件在正确位置

3. **导入模块失败**
   ```
   ModuleNotFoundError: No module named 'xxx'
   ```
   解决：确保在正确的目录下运行脚本，或安装缺失的依赖

### 依赖检查
```bash
# 检查 Python 版本（需要 3.7+）
python --version

# 检查必要的库
python -c "import pandas, numpy, matplotlib, sqlite3; print('All dependencies OK')"
```

## 更多示例

### 生成多个时间周期的图表
```bash
# ETH 多周期分析
python example_coin_chan.py --symbol ETHUSDT --interval 5min --limit 1000
python example_coin_chan.py --symbol ETHUSDT --interval 15min --limit 800
python example_coin_chan.py --symbol ETHUSDT --interval 1day --limit 200
```

### 期货数据分析
```bash
# 如果有期货数据
python example_coin_chan.py --symbol ETHUSDT --interval 5min --market futures --limit 800
```

这样你就可以使用 `coin_data.db` 中的数据进行专业的缠论分析了！