# 缠论画图工具使用指南

本工具集提供了多种方式来使用 `coin_data.db` 中的数据进行缠论分析和画图。

## 🚀 快速开始

### 1. 查看可用数据
```bash
cd chan
python simple_zen_plot.py --list
```

### 2. 生成单个图表
```bash
# 生成 ETH 15分钟缠论图表
python simple_zen_plot.py --symbol ETHUSDT --interval 15min --limit 500

# 生成 BTC 5分钟缠论图表
python simple_zen_plot.py --symbol BTCUSDT --interval 5min --limit 800
```

### 3. 批量生成图表
```bash
python simple_zen_plot.py --batch
```

## 📊 工具对比

### 1. simple_zen_plot.py（推荐）
- ✅ **简单易用**：直接运行，无复杂依赖
- ✅ **稳定可靠**：经过测试，不会出错
- ✅ **功能完整**：包含分型、笔、线段、中枢
- ✅ **图表清晰**：价格图 + 成交量图
- ✅ **快速生成**：处理速度快

**使用场景**：日常分析、快速查看、批量生成

### 2. ZenPlot工具
- ⚠️ **功能丰富**：包含MACD等技术指标
- ⚠️ **图表专业**：多层图表布局
- ❌ **可能不稳定**：某些数据可能出错

**使用场景**：需要更多技术指标时

### 3. 原生chan.py框架
- ✅ **功能最强**：完整的缠论框架
- ✅ **高度可配置**：支持各种参数调整
- ❌ **复杂度高**：需要了解框架结构
- ❌ **依赖较多**：需要完整的chan.py环境

**使用场景**：专业分析、策略开发

## 📈 支持的数据

### 交易对
- ETHUSDT（以太坊）
- BTCUSDT（比特币）
- SUIUSDT（SUI）
- UNIUSDT（Uniswap）
- DOTUSDT（Polkadot）
- LINKUSDT（Chainlink）
- 等等...

### 时间周期
- 1min（1分钟）
- 5min（5分钟）
- 15min（15分钟）
- 30min（30分钟）
- 1hour（1小时）
- 1day（1天）

### 市场类型
- spot（现货，默认）
- futures（期货）

## 🎯 使用示例

### 基础用法
```bash
# 查看ETH 15分钟数据
python simple_zen_plot.py --symbol ETHUSDT --interval 15min

# 查看BTC 5分钟数据，限制800条
python simple_zen_plot.py --symbol BTCUSDT --interval 5min --limit 800

# 指定输出文件名
python simple_zen_plot.py --symbol SUIUSDT --interval 15min --output sui_analysis.png
```

### 高级用法
```bash
# 查看期货数据（如果有）
python simple_zen_plot.py --symbol ETHUSDT --interval 5min --market futures

# 批量生成多个图表
python simple_zen_plot.py --batch
```

## 📋 图表说明

生成的图表包含两个部分：

### 上方：价格图表
- **红色/绿色K线**：标准K线图（红涨绿跌）
- **黑色细线**：K线影线（最高最低价）
- **红色/绿色粗线**：线段（上升/下降）
- **橙色矩形**：中枢区域

### 下方：成交量图表
- **红色/绿色柱**：成交量（红涨绿跌）

## 🔧 缠论要素解释

### K线
- **红色K线**：上涨K线（收盘价高于开盘价）
- **绿色K线**：下跌K线（收盘价低于开盘价）
- **影线**：显示最高价和最低价

### 线段
- 由多个同向笔组成
- 红色线条：上升线段
- 绿色线条：下降线段

### 中枢
- 三个或以上线段的重叠区间
- 橙色半透明矩形表示
- 是重要的支撑阻力区域

## 📁 输出文件

生成的图表文件命名格式：
```
{交易对}_{时间周期}_{市场类型}_简单缠论分析.png
```

例如：
- `ETHUSDT_15min_spot_简单缠论分析.png`
- `BTCUSDT_5min_spot_简单缠论分析.png`

## ⚡ 性能建议

### 数据量控制
- **日内分析**：建议 limit 500-1000
- **短期分析**：建议 limit 1000-2000
- **长期分析**：建议 limit 2000-5000

### 时间周期选择
- **短线交易**：5min, 15min
- **中线交易**：15min, 30min, 1hour
- **长线分析**：1hour, 1day

## 🛠️ 故障排除

### 常见问题

1. **找不到数据表**
   ```
   No data found for XXXUSDT_15min_spot
   ```
   **解决**：使用 `--list` 查看可用数据

2. **图表显示异常**
   - 检查数据量是否足够（建议至少100条）
   - 尝试调整 `--limit` 参数

3. **中文字体显示问题**
   - 图表会自动尝试使用系统中文字体
   - 如果显示异常，字体问题不影响分析结果

### 调试技巧
```bash
# 先查看数据是否存在
python simple_zen_plot.py --list

# 使用较小的数据量测试
python simple_zen_plot.py --symbol ETHUSDT --interval 15min --limit 100

# 检查数据库文件
ls -la coin_data.db
```

## 📚 进阶学习

### 缠论基础概念
1. **分型**：价格的局部极值点
2. **笔**：连接分型的基本单位
3. **线段**：由笔组成的更大结构
4. **中枢**：重要的价格区间
5. **走势类型**：上涨、下跌、盘整

### 实战应用
- 在中枢附近寻找交易机会
- 关注线段的突破和回调
- 结合成交量确认信号强度

## 🎉 总结

使用 `simple_zen_plot.py` 是最简单、最可靠的方式来进行缠论分析：

```bash
# 一键生成多个图表
python simple_zen_plot.py --batch

# 或者针对特定品种
python simple_zen_plot.py --symbol ETHUSDT --interval 15min --limit 800
```

生成的图表可以帮助你：
- 识别关键的支撑阻力位
- 发现潜在的交易机会
- 理解价格的结构性变化
- 制定更好的交易策略

开始你的缠论分析之旅吧！🚀