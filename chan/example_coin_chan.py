#!/usr/bin/env python3
# -*- coding:utf-8 -*-

"""
使用coin_data.db数据进行缠论分析的完整示例
"""

import sys
import os

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from Chan import CChan
from ChanConfig import CChanConfig
from Common.CEnum import DATA_SRC, KL_TYPE, AUTYPE
from Plot.PlotDriver import CPlotDriver
from DataAPI.CoinDataAPI import CCoinDataAPI, set_coin_data_params

def analyze_coin_with_chan(symbol: str, interval: str, market: str = 'spot', 
                          limit: int = 1000, save_path: str = None):
    """
    使用chan.py框架分析加密货币数据
    
    Args:
        symbol: 交易对符号，如 'ETHUSDT'
        interval: 时间间隔，如 '15min', '5min'
        market: 市场类型，'spot' 或 'futures'
        limit: 数据条数限制
        save_path: 图片保存路径
    """
    
    # 设置数据API参数
    db_path = os.path.join(current_dir, 'coin_data.db')
    api = CCoinDataAPI(db_path)
    api.set_params(symbol, interval, market, limit)
    
    # 检查数据是否存在
    tables = api.get_available_tables()
    table_name = f"{symbol}_{interval}_{market}"
    if table_name not in tables:
        print(f"错误: 找不到数据表 {table_name}")
        print("可用的数据表:")
        for table in tables[:10]:
            print(f"  {table}")
        return None
    
    # 获取数据信息
    info = api.get_data_info(symbol, interval, market)
    print(f"数据信息:")
    print(f"  交易对: {symbol}")
    print(f"  时间间隔: {interval}")
    print(f"  市场类型: {market}")
    print(f"  数据范围: {info.get('start_time', 'N/A')} ~ {info.get('end_time', 'N/A')}")
    print(f"  总记录数: {info.get('count', 0)}")
    print(f"  使用记录数: {min(limit, info.get('count', 0))}")
    print()
    
    # 缠论配置
    config = CChanConfig({
        "bi_strict": True,           # 严格笔
        "seg_algo": "chan",          # 线段算法
        "zs_combine": True,          # 中枢合并
        "zs_combine_mode": "zs",     # 中枢合并模式
        "divergence_rate": 0.9,      # 背驰比例
        "min_zs_cnt": 1,             # 最小中枢数量
        "max_bs2_rate": 0.618,       # 2类买卖点最大回撤
        "bs1_peak": True,            # 1类买卖点必须是极值
        "macd_algo": "peak",         # MACD算法
        "bs_type": '1,2,3a,3b',      # 买卖点类型
        "mean_metrics": [5, 20],     # 均线周期
        "boll_n": 20,                # 布林线周期
        "macd": {
            "fast": 12,
            "slow": 26,
            "signal": 9
        },
        "trigger_step": False,       # 不使用逐步回放
        "print_warning": False,      # 不打印警告
    })
    
    # 根据interval确定K线级别
    kl_type_map = {
        '1min': KL_TYPE.K_1M,
        '5min': KL_TYPE.K_5M,
        '15min': KL_TYPE.K_15M,
        '30min': KL_TYPE.K_30M,
        '1hour': KL_TYPE.K_60M,
        '1day': KL_TYPE.K_DAY
    }
    
    kl_type = kl_type_map.get(interval, KL_TYPE.K_15M)
    
    try:
        print("正在进行缠论分析...")
        
        # 直接使用数据API获取数据
        kline_data = api.get_kl_data(None)
        if not kline_data:
            print(f"无法获取 {symbol}_{interval}_{market} 的数据")
            return None
        
        print(f"成功加载 {len(kline_data)} 条K线数据")
        
        # 创建CChan对象 - 使用离线数据模式
        chan = CChan(
            code=f"{symbol}_{interval}_{market}",
            begin_time=None,
            end_time=None,
            data_src=DATA_SRC.CSV,  # 使用CSV模式，然后手动设置数据
            lv_list=[kl_type],
            config=config,
            autype=AUTYPE.QFQ
        )
        
        # 手动设置K线数据
        from KLine.KLine_List import CKLine_List
        from KLine.KLine_Unit import CKLine_Unit
        from Common.CTime import CTime
        
        kl_list = CKLine_List(kl_type, conf=config)
        
        for kl_data in kline_data:
            time_str, open_price, close_price, high_price, low_price, volume, amount, turnover = kl_data
            
            klu = CKLine_Unit({
                "time": CTime.str2time(time_str, kl_type),
                "open": open_price,
                "close": close_price,
                "high": high_price,
                "low": low_price,
                "volume": volume,
                "amount": amount,
                "turnover": turnover
            })
            
            kl_list.add_single_klu(klu)
        
        # 设置到chan对象
        chan.kl_datas[kl_type] = kl_list
        chan.do_init()
        
        print("缠论分析完成!")
        
        # 显示分析结果统计
        chan_data = chan[kl_type]
        print(f"\n=== 缠论分析结果 ===")
        print(f"K线数量: {len(chan_data.kl_list)}")
        print(f"笔数量: {len(chan_data.bi_list)}")
        print(f"线段数量: {len(chan_data.seg_list)}")
        print(f"中枢数量: {len(chan_data.zs_list)}")
        print(f"买卖点数量: {len(chan_data.bs_point_lst)}")
        
        # 画图配置
        plot_config = {
            "plot_kline": True,
            "plot_kline_combine": True,
            "plot_bi": True,
            "plot_seg": True,
            "plot_zs": True,
            "plot_bsp": True,
            "plot_macd": True,
            "plot_mean": True,
            "plot_boll": True,
        }
        
        plot_para = {
            "figure": {
                "width": 20,
                "height": 12,
            },
            "bi": {
                "show_num": True,
                "disp_end": True,
            },
            "seg": {
                "plot_trendline": True,
                "show_num": True,
            },
            "zs": {
                "show_text": True,
            },
            "bsp": {
                "fontsize": 12,
            }
        }
        
        # 生成图表
        if not save_path:
            save_path = f"{symbol}_{interval}_{market}_chan_analysis.png"
        
        print(f"正在生成图表: {save_path}")
        
        plot_driver = CPlotDriver(
            chan,
            plot_config=plot_config,
            plot_para=plot_para,
        )
        
        # 保存图片
        plot_driver.figure.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"✓ 缠论分析图表已保存: {save_path}")
        
        return chan
        
    except Exception as e:
        print(f"缠论分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def list_available_data():
    """列出所有可用的数据"""
    db_path = os.path.join(current_dir, 'coin_data.db')
    api = CCoinDataAPI(db_path)
    tables = api.get_available_tables()
    
    if not tables:
        print("没有找到可用的数据表")
        return
    
    print(f"找到 {len(tables)} 个数据表:\n")
    
    # 按交易对分组显示
    data_by_symbol = {}
    for table in tables:
        parts = table.split('_')
        if len(parts) >= 3:
            symbol = parts[0]
            interval = parts[1]
            market = parts[2]
            
            if symbol not in data_by_symbol:
                data_by_symbol[symbol] = []
            data_by_symbol[symbol].append((interval, market))
    
    for symbol in sorted(data_by_symbol.keys()):
        print(f"{symbol}:")
        for interval, market in sorted(data_by_symbol[symbol]):
            # 获取数据信息
            info = api.get_data_info(symbol, interval, market)
            if info:
                print(f"  {interval} {market}: {info['count']} 条记录 "
                      f"({info['start_time']} ~ {info['end_time']})")
            else:
                print(f"  {interval} {market}: 无法获取信息")
        print()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='使用chan.py框架分析coin_data.db中的数据')
    parser.add_argument('--symbol', '-s', type=str, help='交易对符号 (如: ETHUSDT)')
    parser.add_argument('--interval', '-i', type=str, help='时间间隔 (如: 15min, 5min)')
    parser.add_argument('--market', '-m', type=str, default='spot', help='市场类型 (默认: spot)')
    parser.add_argument('--limit', '-l', type=int, default=1000, help='数据条数限制 (默认: 1000)')
    parser.add_argument('--output', '-o', type=str, help='输出文件名')
    parser.add_argument('--list', action='store_true', help='列出所有可用数据')
    
    args = parser.parse_args()
    
    if args.list:
        list_available_data()
        return
    
    if not args.symbol or not args.interval:
        print("请指定 --symbol 和 --interval 参数，或使用 --list 查看可用数据")
        print("\n示例用法:")
        print("  python example_coin_chan.py --symbol ETHUSDT --interval 15min")
        print("  python example_coin_chan.py --symbol BTCUSDT --interval 5min --limit 800")
        print("  python example_coin_chan.py --list")
        return
    
    # 进行缠论分析
    analyze_coin_with_chan(args.symbol, args.interval, args.market, args.limit, args.output)

if __name__ == "__main__":
    main()