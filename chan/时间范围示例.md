# 时间范围功能使用示例

## 🕐 新增功能

现在支持 `--start-time` 和 `--end-time` 参数，可以精确控制分析的时间范围！

## 📅 支持的时间格式

### 完整格式
```bash
--start-time "2024-01-01 10:30:00"
--end-time "2024-01-31 15:45:30"
```

### 简化格式
```bash
--start-time "2024-01-01"          # 自动补充为 00:00:00
--end-time "2024-01-31"            # 自动补充为 00:00:00
--start-time "2024-01-01 10:30"    # 自动补充秒数为 00
```

### 当前年份格式
```bash
--start-time "01-15 10:00"         # 使用当前年份
--end-time "01-31 18:00"
```

### 斜杠格式
```bash
--start-time "2024/01/01"
--end-time "2024/01/31 23:59:59"
```

## 🎯 实用场景示例

### 1. 月度分析
```bash
# 分析2024年1月ETH的走势
python simple_zen_plot.py --symbol ETHUSDT --interval 15min \
  --start-time "2024-01-01" --end-time "2024-01-31" \
  --output eth_jan_2024.png
```

### 2. 日内交易分析
```bash
# 分析某天的交易时段（9:00-17:00）
python simple_zen_plot.py --symbol ETHUSDT --interval 5min \
  --start-time "2024-08-01 09:00" --end-time "2024-08-01 17:00" \
  --output eth_trading_hours.png
```

### 3. 事件前后分析
```bash
# 分析重要事件前后的价格走势
python simple_zen_plot.py --symbol BTCUSDT --interval 15min \
  --start-time "2024-07-15" --end-time "2024-07-25" \
  --output btc_event_analysis.png
```

### 4. 最近数据分析
```bash
# 从某个时间点开始的最新500条数据
python simple_zen_plot.py --symbol SUIUSDT --interval 15min \
  --start-time "2024-08-01" --limit 500 \
  --output sui_recent.png
```

### 5. 短时间窗口分析
```bash
# 分析2小时内的5分钟数据
python simple_zen_plot.py --symbol ETHUSDT --interval 5min \
  --start-time "2024-08-01 14:00" --end-time "2024-08-01 16:00" \
  --output eth_2hour_window.png
```

## 📊 实际测试结果

### 测试1：月度分析
```bash
python simple_zen_plot.py --symbol ETHUSDT --interval 15min \
  --start-time "2024-01-01" --end-time "2024-01-31" --limit 500
```
**结果**：
- 开始时间: 2024-01-01 00:00:00
- 结束时间: 2024-01-31 00:00:00
- 实际数据范围: 2024-01-25 19:15:00 ~ 2024-01-31 00:00:00
- 加载了 500 条K线数据
- 找到 56 个线段，35 个中枢

### 测试2：日内分析
```bash
python simple_zen_plot.py --symbol ETHUSDT --interval 5min \
  --start-time "2024-08-01 10:00" --end-time "2024-08-01 18:00"
```
**结果**：
- 开始时间: 2024-08-01 10:00:00
- 结束时间: 2024-08-01 18:00:00
- 实际数据范围: 2024-08-01 10:00:00 ~ 2024-08-01 18:00:00
- 加载了 97 条K线数据
- 找到 9 个线段，2 个中枢

## 🔧 高级用法

### 组合参数使用
```bash
# 时间范围 + 数据限制 + 自定义输出
python simple_zen_plot.py \
  --symbol ETHUSDT \
  --interval 15min \
  --start-time "2024-01-01" \
  --end-time "2024-01-31" \
  --limit 800 \
  --output eth_jan_analysis.png
```

### 只指定开始时间
```bash
# 从指定时间开始到最新数据
python simple_zen_plot.py --symbol BTCUSDT --interval 5min \
  --start-time "2024-08-01" --limit 300
```

### 只指定结束时间
```bash
# 到指定时间为止的最新数据
python simple_zen_plot.py --symbol SUIUSDT --interval 15min \
  --end-time "2024-07-31" --limit 500
```

## 💡 使用技巧

### 1. 数据量控制
- 使用时间范围时，建议配合 `--limit` 参数控制数据量
- 如果时间范围内数据过多，系统会自动取最新的limit条数据

### 2. 时间格式建议
- 推荐使用 `YYYY-MM-DD HH:MM` 格式，清晰易读
- 对于日期分析，使用 `YYYY-MM-DD` 即可
- 当前年份数据可以使用 `MM-DD HH:MM` 简化格式

### 3. 性能优化
- 精确的时间范围可以减少数据加载时间
- 避免过大的时间跨度，建议单次分析不超过3个月数据

### 4. 图表标题
- 使用时间范围时，图表标题会自动显示时间信息
- 便于区分不同时间段的分析结果

## 🎯 应用场景

### 交易策略验证
```bash
# 验证策略在特定月份的表现
python simple_zen_plot.py --symbol ETHUSDT --interval 15min \
  --start-time "2024-03-01" --end-time "2024-03-31"
```

### 市场事件分析
```bash
# 分析重要公告前后的市场反应
python simple_zen_plot.py --symbol BTCUSDT --interval 5min \
  --start-time "2024-07-20 08:00" --end-time "2024-07-20 20:00"
```

### 历史回测
```bash
# 回测历史数据中的缠论信号
python simple_zen_plot.py --symbol SUIUSDT --interval 15min \
  --start-time "2023-12-01" --end-time "2023-12-31"
```

现在你可以精确控制分析的时间范围，进行更有针对性的缠论分析了！🎉