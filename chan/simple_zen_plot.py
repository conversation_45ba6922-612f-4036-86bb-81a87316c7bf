#!/usr/bin/env python3
# -*- coding:utf-8 -*-

"""
简单的缠论画图工具
直接从coin_data.db读取数据并生成基础的缠论图表
"""

import sqlite3
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from datetime import datetime
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class SimpleChanPlotter:
    """简单的缠论绘图器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
    
    def get_available_tables(self):
        """获取所有可用的数据表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name != 'table_info'")
        tables = [row[0] for row in cursor.fetchall()]
        conn.close()
        return tables
    
    def parse_time_string(self, time_str: str) -> int:
        """将时间字符串转换为时间戳"""
        try:
            # 支持多种时间格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M',
                '%Y-%m-%d',
                '%Y/%m/%d %H:%M:%S',
                '%Y/%m/%d %H:%M',
                '%Y/%m/%d',
                '%m-%d %H:%M',
                '%m/%d %H:%M'
            ]
            
            for fmt in formats:
                try:
                    if fmt.startswith('%m'):
                        # 对于只有月日的格式，添加当前年份
                        current_year = datetime.now().year
                        time_str_with_year = f"{current_year}-{time_str}"
                        dt = datetime.strptime(time_str_with_year, f"%Y-{fmt}")
                    else:
                        dt = datetime.strptime(time_str, fmt)
                    return int(dt.timestamp())
                except ValueError:
                    continue
            
            raise ValueError(f"无法解析时间格式: {time_str}")
            
        except Exception as e:
            print(f"时间解析错误: {e}")
            return None

    def load_data(self, symbol: str, interval: str, market: str = 'spot', 
                  limit: int = 1000, start_time: str = None, end_time: str = None):
        """加载K线数据"""
        table_name = f"{symbol}_{interval}_{market}"
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 构建基础查询
            query = f"""
            SELECT timestamp, open, high, low, close, volume
            FROM {table_name}
            """
            
            # 添加时间条件
            conditions = []
            if start_time:
                start_timestamp = self.parse_time_string(start_time)
                if start_timestamp:
                    conditions.append(f"timestamp >= {start_timestamp}")
                    print(f"开始时间: {datetime.fromtimestamp(start_timestamp).strftime('%Y-%m-%d %H:%M:%S')}")
            
            if end_time:
                end_timestamp = self.parse_time_string(end_time)
                if end_timestamp:
                    conditions.append(f"timestamp <= {end_timestamp}")
                    print(f"结束时间: {datetime.fromtimestamp(end_timestamp).strftime('%Y-%m-%d %H:%M:%S')}")
            
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            
            # 添加排序和限制
            if start_time or end_time:
                query += " ORDER BY timestamp ASC"
            else:
                query += " ORDER BY timestamp DESC"
            
            if limit and not (start_time or end_time):
                query += f" LIMIT {limit}"
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                print(f"No data found for {table_name}")
                if start_time or end_time:
                    print("请检查时间范围是否正确")
                return None
            
            # 如果使用了时间范围但没有指定limit，或者数据量超过limit，进行截取
            if limit and len(df) > limit:
                if start_time or end_time:
                    # 时间范围查询时，取最新的limit条数据
                    df = df.tail(limit)
                    print(f"数据量超过限制，取最新的 {limit} 条数据")
            
            # 按时间正序排列
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            # 显示数据时间范围
            if len(df) > 0:
                start_dt = datetime.fromtimestamp(df.iloc[0]['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                end_dt = datetime.fromtimestamp(df.iloc[-1]['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                print(f"实际数据范围: {start_dt} ~ {end_dt}")
            
            return df
            
        except Exception as e:
            print(f"Error loading data: {e}")
            return None
    
    def find_fractals(self, df: pd.DataFrame):
        """寻找分型点"""
        fractals = []
        
        for i in range(1, len(df) - 1):
            # 顶分型
            if (df.iloc[i]['high'] > df.iloc[i-1]['high'] and 
                df.iloc[i]['high'] > df.iloc[i+1]['high']):
                fractals.append({
                    'index': i,
                    'type': 'top',
                    'price': df.iloc[i]['high']
                })
            
            # 底分型
            elif (df.iloc[i]['low'] < df.iloc[i-1]['low'] and 
                  df.iloc[i]['low'] < df.iloc[i+1]['low']):
                fractals.append({
                    'index': i,
                    'type': 'bottom',
                    'price': df.iloc[i]['low']
                })
        
        return fractals
    
    def build_strokes(self, fractals):
        """构建笔"""
        if len(fractals) < 2:
            return []
        
        strokes = []
        current_type = None
        current_extreme = None
        current_index = None
        
        for fractal in fractals:
            if current_type is None:
                current_type = fractal['type']
                current_extreme = fractal['price']
                current_index = fractal['index']
            elif fractal['type'] != current_type:
                # 形成一笔
                strokes.append({
                    'start_idx': current_index,
                    'end_idx': fractal['index'],
                    'start_price': current_extreme,
                    'end_price': fractal['price'],
                    'direction': 'up' if current_type == 'bottom' else 'down'
                })
                
                current_type = fractal['type']
                current_extreme = fractal['price']
                current_index = fractal['index']
            else:
                # 更新极值
                if ((fractal['type'] == 'top' and fractal['price'] > current_extreme) or
                    (fractal['type'] == 'bottom' and fractal['price'] < current_extreme)):
                    current_extreme = fractal['price']
                    current_index = fractal['index']
        
        return strokes
    
    def build_segments(self, strokes):
        """构建线段（简化版）"""
        if len(strokes) < 3:
            return []
        
        segments = []
        i = 0
        
        while i < len(strokes) - 2:
            # 寻找同向的笔
            start_stroke = i
            current_direction = strokes[i]['direction']
            
            # 跳过反向笔
            j = i + 1
            while j < len(strokes) and strokes[j]['direction'] != current_direction:
                j += 1
            
            if j < len(strokes):
                # 构成线段
                segments.append({
                    'start_idx': strokes[start_stroke]['start_idx'],
                    'end_idx': strokes[j]['end_idx'],
                    'start_price': strokes[start_stroke]['start_price'],
                    'end_price': strokes[j]['end_price'],
                    'direction': current_direction
                })
                i = j + 1
            else:
                break
        
        return segments
    
    def find_centers(self, segments):
        """识别中枢（简化版）"""
        if len(segments) < 3:
            return []
        
        centers = []
        
        for i in range(len(segments) - 2):
            seg1 = segments[i]
            seg2 = segments[i + 1]
            seg3 = segments[i + 2]
            
            # 计算重叠区间
            prices = [seg1['start_price'], seg1['end_price'],
                     seg2['start_price'], seg2['end_price'],
                     seg3['start_price'], seg3['end_price']]
            
            high_min = min(max(seg1['start_price'], seg1['end_price']),
                          max(seg2['start_price'], seg2['end_price']),
                          max(seg3['start_price'], seg3['end_price']))
            
            low_max = max(min(seg1['start_price'], seg1['end_price']),
                         min(seg2['start_price'], seg2['end_price']),
                         min(seg3['start_price'], seg3['end_price']))
            
            if high_min > low_max:  # 有重叠
                centers.append({
                    'start_idx': seg1['start_idx'],
                    'end_idx': seg3['end_idx'],
                    'high': high_min,
                    'low': low_max
                })
        
        return centers
    
    def draw_klines(self, ax, df):
        """绘制K线图"""
        for i in range(len(df)):
            open_price = df.iloc[i]['open']
            close_price = df.iloc[i]['close']
            high_price = df.iloc[i]['high']
            low_price = df.iloc[i]['low']
            
            # 确定颜色：红涨绿跌
            color = 'red' if close_price >= open_price else 'green'
            
            # 绘制影线（最高最低价）
            ax.plot([i, i], [low_price, high_price], color='black', linewidth=1)
            
            # 绘制实体（开盘收盘价）
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)
            
            if body_height > 0:
                # 有实体的K线
                rect = patches.Rectangle((i-0.3, body_bottom), 0.6, body_height,
                                       facecolor=color, edgecolor='black', 
                                       linewidth=0.5, alpha=0.8)
                ax.add_patch(rect)
            else:
                # 十字星（开盘价等于收盘价）
                ax.plot([i-0.3, i+0.3], [close_price, close_price], 
                       color='black', linewidth=1)

    def plot_chart(self, symbol: str, interval: str, market: str = 'spot', 
                   limit: int = 1000, save_path: str = None, 
                   start_time: str = None, end_time: str = None):
        """绘制缠论图表"""
        
        # 加载数据
        df = self.load_data(symbol, interval, market, limit, start_time, end_time)
        if df is None:
            return
        
        print(f"加载了 {len(df)} 条K线数据")
        
        # 缠论分析
        fractals = self.find_fractals(df)
        strokes = self.build_strokes(fractals)
        segments = self.build_segments(strokes)
        centers = self.find_centers(segments)
        
        print(f"找到 {len(segments)} 个线段")
        print(f"找到 {len(centers)} 个中枢")
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 10), 
                                       gridspec_kw={'height_ratios': [3, 1]})
        
        # 绘制K线图
        self.draw_klines(ax1, df)
        
        # 绘制线段
        for segment in segments:
            color = 'red' if segment['direction'] == 'up' else 'green'
            ax1.plot([segment['start_idx'], segment['end_idx']], 
                    [segment['start_price'], segment['end_price']], 
                    color=color, linewidth=3, alpha=0.8)
        
        # 绘制中枢
        for center in centers:
            width = center['end_idx'] - center['start_idx']
            height = center['high'] - center['low']
            rect = patches.Rectangle((center['start_idx'], center['low']), 
                                   width, height, linewidth=2, 
                                   edgecolor='orange', facecolor='orange', 
                                   alpha=0.3)
            ax1.add_patch(rect)
        
        # 设置标题和标签
        title = f"{symbol} {interval} {market.upper()} - 缠论分析"
        if start_time or end_time:
            time_range = ""
            if start_time:
                time_range += f"从 {start_time}"
            if end_time:
                time_range += f" 到 {end_time}" if start_time else f"到 {end_time}"
            title += f" ({time_range})"
        
        ax1.set_title(title, fontsize=14)
        ax1.set_ylabel("价格", fontsize=12)
        ax1.grid(True, alpha=0.3)
        
        # 绘制成交量
        colors = ['red' if df.iloc[i]['close'] >= df.iloc[i]['open'] else 'green' 
                 for i in range(len(df))]
        ax2.bar(range(len(df)), df['volume'], color=colors, alpha=0.7)
        ax2.set_ylabel("成交量", fontsize=12)
        ax2.set_xlabel("时间", fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        # 设置x轴标签
        n_labels = min(10, len(df))
        indices = np.linspace(0, len(df)-1, n_labels, dtype=int)
        labels = []
        for idx in indices:
            dt_str = datetime.fromtimestamp(df.iloc[idx]['timestamp']).strftime('%m-%d %H:%M')
            labels.append(dt_str)
        
        ax2.set_xticks(indices)
        ax2.set_xticklabels(labels, rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"图表已保存: {save_path}")
        else:
            save_path = f"{symbol}_{interval}_{market}_简单缠论分析.png"
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"图表已保存: {save_path}")
        
        plt.close()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='简单缠论分析工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
时间格式示例:
  --start-time "2024-01-01"
  --start-time "2024-01-01 10:00"
  --start-time "2024-01-01 10:00:00"
  --start-time "01-15 10:00" (当前年份)
  --end-time "2024-12-31 23:59:59"

使用示例:
  %(prog)s --symbol ETHUSDT --interval 15min --start-time "2024-01-01" --end-time "2024-01-31"
  %(prog)s --symbol BTCUSDT --interval 5min --start-time "01-15 10:00" --limit 500
        """
    )
    parser.add_argument('--symbol', '-s', type=str, help='交易对符号')
    parser.add_argument('--interval', '-i', type=str, help='时间间隔')
    parser.add_argument('--market', '-m', type=str, default='spot', help='市场类型')
    parser.add_argument('--limit', '-l', type=int, default=1000, help='数据条数限制')
    parser.add_argument('--start-time', type=str, help='开始时间 (格式: YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS)')
    parser.add_argument('--end-time', type=str, help='结束时间 (格式: YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS)')
    parser.add_argument('--output', '-o', type=str, help='输出文件名')
    parser.add_argument('--list', action='store_true', help='列出所有可用数据')
    parser.add_argument('--batch', action='store_true', help='批量生成常用图表')
    
    args = parser.parse_args()
    
    db_path = os.path.join(os.path.dirname(__file__), 'coin_data.db')
    plotter = SimpleChanPlotter(db_path)
    
    if args.list:
        tables = plotter.get_available_tables()
        print(f"找到 {len(tables)} 个数据表:")
        for table in tables:
            print(f"  {table}")
        return
    
    if args.batch:
        # 批量生成常用图表
        charts = [
            ('ETHUSDT', '15min', 'spot', 800),
            ('ETHUSDT', '5min', 'spot', 1000),
            ('BTCUSDT', '5min', 'spot', 1000),
            ('SUIUSDT', '15min', 'spot', 800),
        ]
        
        for symbol, interval, market, limit in charts:
            print(f"\n正在生成 {symbol} {interval} {market} 图表...")
            try:
                plotter.plot_chart(symbol, interval, market, limit)
            except Exception as e:
                print(f"生成失败: {e}")
        return
    
    if not args.symbol or not args.interval:
        print("请指定 --symbol 和 --interval 参数")
        print("或使用 --list 查看可用数据")
        print("或使用 --batch 批量生成图表")
        print("\n时间范围示例:")
        print("  --start-time '2024-01-01' --end-time '2024-01-31'")
        print("  --start-time '01-15 10:00' --limit 500")
        return
    
    # 验证时间参数
    if args.start_time or args.end_time:
        print(f"使用时间范围查询:")
        if args.start_time:
            print(f"  开始时间: {args.start_time}")
        if args.end_time:
            print(f"  结束时间: {args.end_time}")
        print()
    
    # 生成单个图表
    plotter.plot_chart(args.symbol, args.interval, args.market, args.limit, 
                      args.output, args.start_time, args.end_time)

if __name__ == "__main__":
    main()