#!/usr/bin/env python3
"""
RL vs 传统模式性能对比脚本

这个脚本运行相同的回测数据，分别使用传统模式和RL模式，
然后比较两者的性能指标。
"""

import subprocess
import sys
import os
import pandas as pd
import json
from datetime import datetime
import argparse

def run_backtest(mode, **kwargs):
    """
    运行回测
    
    Args:
        mode: 'traditional' 或 'rl'
        **kwargs: 回测参数
    """
    cmd = [
        sys.executable, "backtest_money_quick.py",
        "--coin", kwargs.get('coin', 'ETH'),
        "--interval", kwargs.get('interval', '5m'),
        "--initial-capital", str(kwargs.get('initial_capital', 10000)),
        "--risk-per-trade", str(kwargs.get('risk_per_trade', 1.0)),
        "--quick"  # 使用快速模式
    ]
    
    # 添加时间范围（如果指定）
    if kwargs.get('start_time'):
        cmd.extend(["--start-time", kwargs['start_time']])
    if kwargs.get('end_time'):
        cmd.extend(["--end-time", kwargs['end_time']])
    
    # 添加模式特定参数
    if mode == 'rl':
        if not kwargs.get('rl_model'):
            raise ValueError("RL模式需要指定 rl_model 参数")
        cmd.extend(["--rl-model", kwargs['rl_model']])
        if kwargs.get('rl_config'):
            cmd.extend(["--rl-config", kwargs['rl_config']])
    
    print(f"🚀 运行{mode}模式回测...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode != 0:
            print(f"❌ {mode}模式回测失败")
            print(f"stderr: {result.stderr}")
            return None
        
        print(f"✅ {mode}模式回测完成")
        return result.stdout
        
    except subprocess.TimeoutExpired:
        print(f"❌ {mode}模式回测超时")
        return None
    except Exception as e:
        print(f"❌ {mode}模式回测出错: {e}")
        return None

def parse_backtest_results(output_text, mode):
    """
    从回测输出中解析结果
    
    Args:
        output_text: 回测输出文本
        mode: 'traditional' 或 'rl'
    
    Returns:
        dict: 解析的结果
    """
    results = {
        'mode': mode,
        'total_predictions': 0,
        'successful_predictions': 0,
        'failed_predictions': 0,
        'timeout_predictions': 0,
        'stop_loss_predictions': 0,
        'initial_capital': 0,
        'final_capital': 0,
        'total_return_pct': 0.0
    }
    
    lines = output_text.split('\n')
    
    for line in lines:
        line = line.strip()
        
        # 解析预测统计
        if '总预测数:' in line:
            parts = line.split(',')
            for part in parts:
                part = part.strip()
                if '总预测数:' in part:
                    results['total_predictions'] = int(part.split(':')[1].strip())
                elif '成功:' in part:
                    results['successful_predictions'] = int(part.split(':')[1].strip())
                elif '失败:' in part:
                    results['failed_predictions'] = int(part.split(':')[1].strip())
                elif '超时:' in part:
                    results['timeout_predictions'] = int(part.split(':')[1].strip())
                elif '止损:' in part:
                    results['stop_loss_predictions'] = int(part.split(':')[1].strip())
        
        # 解析资金信息
        elif '初始资金:' in line:
            amount_str = line.split('$')[1].replace(',', '')
            results['initial_capital'] = float(amount_str)
        elif '最终资金:' in line:
            amount_str = line.split('$')[1].replace(',', '')
            results['final_capital'] = float(amount_str)
        elif '总收益率:' in line:
            pct_str = line.split(':')[1].strip().replace('%', '').replace('+', '')
            results['total_return_pct'] = float(pct_str)
    
    return results

def load_detailed_results(mode):
    """
    加载详细的回测结果CSV文件
    
    Args:
        mode: 'traditional' 或 'rl'
    
    Returns:
        DataFrame or None
    """
    if mode == 'traditional':
        filename = "backtest_money_log_quick.csv"
    else:
        filename = "backtest_rl_log_quick.csv"
    
    if os.path.exists(filename):
        try:
            df = pd.read_csv(filename)
            return df
        except Exception as e:
            print(f"⚠️ 加载{filename}失败: {e}")
            return None
    else:
        print(f"⚠️ 文件{filename}不存在")
        return None

def calculate_advanced_metrics(df):
    """
    计算高级性能指标
    
    Args:
        df: 回测结果DataFrame
    
    Returns:
        dict: 高级指标
    """
    if df is None or df.empty:
        return {}
    
    metrics = {}
    
    # 胜率
    if 'Score' in df.columns:
        winning_trades = df[df['Score'] > 0]
        metrics['win_rate'] = len(winning_trades) / len(df) if len(df) > 0 else 0
        
        # 平均盈利和亏损
        if len(winning_trades) > 0:
            metrics['avg_win'] = winning_trades['ProfitLoss'].mean()
        else:
            metrics['avg_win'] = 0
        
        losing_trades = df[df['Score'] < 0]
        if len(losing_trades) > 0:
            metrics['avg_loss'] = losing_trades['ProfitLoss'].mean()
        else:
            metrics['avg_loss'] = 0
        
        # 盈亏比
        if metrics['avg_loss'] != 0:
            metrics['profit_loss_ratio'] = abs(metrics['avg_win'] / metrics['avg_loss'])
        else:
            metrics['profit_loss_ratio'] = float('inf') if metrics['avg_win'] > 0 else 0
    
    # 最大回撤
    if 'CapitalAfter' in df.columns:
        capital_series = df['CapitalAfter']
        running_max = capital_series.expanding().max()
        drawdown = (capital_series - running_max) / running_max
        metrics['max_drawdown'] = drawdown.min()
    
    # 平均持仓时间
    if 'DurationMinutes' in df.columns:
        metrics['avg_duration_minutes'] = df['DurationMinutes'].mean()
    
    return metrics

def compare_results(traditional_results, rl_results, traditional_df=None, rl_df=None):
    """
    比较两种模式的结果
    
    Args:
        traditional_results: 传统模式结果
        rl_results: RL模式结果
        traditional_df: 传统模式详细结果
        rl_df: RL模式详细结果
    """
    print("\n" + "=" * 60)
    print("📊 性能对比结果")
    print("=" * 60)
    
    # 基础指标对比
    print("\n📈 基础指标对比:")
    print(f"{'指标':<20} {'传统模式':<15} {'RL模式':<15} {'差异':<15}")
    print("-" * 65)
    
    metrics = [
        ('总预测数', 'total_predictions', ''),
        ('成功预测', 'successful_predictions', ''),
        ('失败预测', 'failed_predictions', ''),
        ('超时预测', 'timeout_predictions', ''),
        ('总收益率', 'total_return_pct', '%'),
        ('最终资金', 'final_capital', '$')
    ]
    
    for name, key, unit in metrics:
        trad_val = traditional_results.get(key, 0)
        rl_val = rl_results.get(key, 0)
        
        if key == 'total_return_pct':
            diff = rl_val - trad_val
            diff_str = f"{diff:+.2f}%"
        elif key == 'final_capital':
            diff = rl_val - trad_val
            diff_str = f"${diff:+,.2f}"
        else:
            diff = rl_val - trad_val
            diff_str = f"{diff:+d}"
        
        print(f"{name:<20} {trad_val:<15} {rl_val:<15} {diff_str:<15}")
    
    # 高级指标对比
    if traditional_df is not None and rl_df is not None:
        print("\n📊 高级指标对比:")
        trad_advanced = calculate_advanced_metrics(traditional_df)
        rl_advanced = calculate_advanced_metrics(rl_df)
        
        advanced_metrics = [
            ('胜率', 'win_rate', '%'),
            ('平均盈利', 'avg_win', '$'),
            ('平均亏损', 'avg_loss', '$'),
            ('盈亏比', 'profit_loss_ratio', ''),
            ('最大回撤', 'max_drawdown', '%'),
            ('平均持仓时间', 'avg_duration_minutes', 'min')
        ]
        
        print(f"{'指标':<20} {'传统模式':<15} {'RL模式':<15} {'差异':<15}")
        print("-" * 65)
        
        for name, key, unit in advanced_metrics:
            trad_val = trad_advanced.get(key, 0)
            rl_val = rl_advanced.get(key, 0)
            
            if unit == '%':
                trad_str = f"{trad_val*100:.2f}%"
                rl_str = f"{rl_val*100:.2f}%"
                diff = (rl_val - trad_val) * 100
                diff_str = f"{diff:+.2f}%"
            elif unit == '$':
                trad_str = f"${trad_val:.2f}"
                rl_str = f"${rl_val:.2f}"
                diff = rl_val - trad_val
                diff_str = f"${diff:+.2f}"
            elif unit == 'min':
                trad_str = f"{trad_val:.1f}"
                rl_str = f"{rl_val:.1f}"
                diff = rl_val - trad_val
                diff_str = f"{diff:+.1f}"
            else:
                trad_str = f"{trad_val:.2f}"
                rl_str = f"{rl_val:.2f}"
                diff = rl_val - trad_val
                diff_str = f"{diff:+.2f}"
            
            print(f"{name:<20} {trad_str:<15} {rl_str:<15} {diff_str:<15}")
    
    # 总结
    print("\n🎯 总结:")
    if rl_results['total_return_pct'] > traditional_results['total_return_pct']:
        improvement = rl_results['total_return_pct'] - traditional_results['total_return_pct']
        print(f"✅ RL模式表现更好，收益率提升 {improvement:.2f}%")
    elif rl_results['total_return_pct'] < traditional_results['total_return_pct']:
        decline = traditional_results['total_return_pct'] - rl_results['total_return_pct']
        print(f"❌ RL模式表现较差，收益率下降 {decline:.2f}%")
    else:
        print("🤝 两种模式表现相当")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="比较RL模式和传统模式的回测性能")
    parser.add_argument("--rl-model", required=True, help="RL模型路径")
    parser.add_argument("--rl-config", help="RL配置文件路径")
    parser.add_argument("--coin", default="ETH", help="交易对")
    parser.add_argument("--interval", default="5m", help="时间间隔")
    parser.add_argument("--start-time", help="开始时间")
    parser.add_argument("--end-time", help="结束时间")
    parser.add_argument("--initial-capital", type=float, default=10000, help="初始资金")
    parser.add_argument("--risk-per-trade", type=float, default=1.0, help="单次风险比例")
    
    args = parser.parse_args()
    
    # 检查RL模型是否存在
    if not os.path.exists(args.rl_model + ".pth"):
        print(f"❌ RL模型文件不存在: {args.rl_model}.pth")
        return False
    
    backtest_params = {
        'coin': args.coin,
        'interval': args.interval,
        'initial_capital': args.initial_capital,
        'risk_per_trade': args.risk_per_trade,
        'start_time': args.start_time,
        'end_time': args.end_time,
        'rl_model': args.rl_model,
        'rl_config': args.rl_config
    }
    
    print("🔄 开始性能对比测试...")
    
    # 运行传统模式回测
    traditional_output = run_backtest('traditional', **backtest_params)
    if traditional_output is None:
        print("❌ 传统模式回测失败")
        return False
    
    # 运行RL模式回测
    rl_output = run_backtest('rl', **backtest_params)
    if rl_output is None:
        print("❌ RL模式回测失败")
        return False
    
    # 解析结果
    traditional_results = parse_backtest_results(traditional_output, 'traditional')
    rl_results = parse_backtest_results(rl_output, 'rl')
    
    # 加载详细结果
    traditional_df = load_detailed_results('traditional')
    rl_df = load_detailed_results('rl')
    
    # 比较结果
    compare_results(traditional_results, rl_results, traditional_df, rl_df)
    
    # 保存对比报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"rl_vs_traditional_comparison_{timestamp}.json"
    
    comparison_report = {
        'timestamp': timestamp,
        'parameters': backtest_params,
        'traditional_results': traditional_results,
        'rl_results': rl_results,
        'traditional_advanced': calculate_advanced_metrics(traditional_df) if traditional_df is not None else {},
        'rl_advanced': calculate_advanced_metrics(rl_df) if rl_df is not None else {}
    }
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        json.dump(comparison_report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细对比报告已保存到: {report_filename}")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)