# 主动买卖量特征使用指南

## 概述

本指南介绍如何使用新增的主动买卖量特征来提升模型性能。基于 `taker_buy_base_volume` 数据，我们开发了一套强大的特征工程系统，在测试中实现了 **7.76%** 的准确率提升。

## 快速开始

### 1. 更新模型工具

确保使用最新的 `model_utils_815.py`，它包含了主动买卖量特征的计算逻辑。

### 2. 数据加载

使用新的数据库加载函数来获取包含主动买卖量的数据：

```python
from model_utils_815 import load_and_prepare_data_from_db

# 从数据库加载数据（包含主动买卖量字段）
df = load_and_prepare_data_from_db(
    db_path="coin_data.db",
    table_name="ETHUSDT_5min_spot",
    limit=50000  # 可选：限制数据量
)
```

### 3. 特征计算

计算特征时会自动检测并包含主动买卖量特征：

```python
from model_utils_815 import calculate_features

# 计算所有特征（包括新的主动买卖量特征）
df_with_features = calculate_features(df, timeframe=5)
```

### 4. 特征选择

获取包含主动买卖量特征的完整特征列表：

```python
from model_utils_815 import get_feature_list

# 获取特征列表（自动包含可用的主动买卖量特征）
feature_list = get_feature_list(df_with_features, time_frame=5)
print(f"总特征数: {len(feature_list)}")
```

## 新增特征详解

### 基础特征

1. **taker_buy_ratio**: 主动买入占总成交量比例
   - 范围: 0-1
   - 高值表示买盘强劲

2. **taker_sell_ratio**: 主动卖出占总成交量比例
   - 范围: 0-1  
   - 高值表示卖盘强劲

3. **buy_sell_ratio**: 买卖力量对比
   - 计算: 主动买入量 / 主动卖出量
   - >1 表示买盘占优，<1 表示卖盘占优

### 时间序列特征

4. **移动平均特征**: 
   - `taker_buy_ratio_ma_{120,360,720}`: 不同时间窗口的买入比例均值
   - `buy_sell_ratio_ma_{120,360,720}`: 买卖力量比的均值

5. **偏离度特征**:
   - `taker_buy_ratio_dev_{120,360,720}`: 当前值相对历史均值的偏离
   - `buy_sell_ratio_dev_{120,360,720}`: 买卖力量比的偏离度

### 波动性特征

6. **标准差特征**:
   - `taker_buy_ratio_std_{120,360}`: 买入比例的波动性
   - `buy_sell_ratio_std_{120,360}`: 买卖力量比的波动性

### 市场状态特征

7. **极端状态标识**:
   - `extreme_buy`: 极端买入 (买入比例 > 0.7)
   - `extreme_sell`: 极端卖出 (买入比例 < 0.3)
   - `balanced_trading`: 平衡交易 (0.4 ≤ 买入比例 ≤ 0.6)

## 性能验证

### 测试结果

在 ETH 5分钟数据上的对比测试：

| 模型类型 | 准确率 | 提升幅度 |
|---------|--------|----------|
| 传统特征 | 85.74% | - |
| 包含主动买卖量特征 | 92.39% | +7.76% |

### 重要特征排名

Top 10 主动买卖量特征（按重要性排序）：

1. `buy_sell_ratio_std_360` (6.20%)
2. `buy_sell_ratio_ma_720` (6.10%)
3. `taker_buy_ratio_ma_720` (5.29%)
4. `taker_buy_ratio_std_360` (5.24%)
5. `buy_sell_ratio_ma_360` (3.27%)
6. `taker_buy_ratio_ma_360` (2.93%)
7. `buy_sell_ratio_std_120` (2.72%)
8. `taker_buy_ratio_std_120` (2.69%)
9. `taker_buy_ratio_ma_120` (2.24%)
10. `buy_sell_ratio_ma_120` (1.96%)

## 实际应用

### 在现有训练脚本中使用

只需要将数据加载部分替换为新的数据库加载函数：

```python
# 原来的方式（仅支持基础OHLCV）
# df = load_and_prepare_data(csv_file)

# 新的方式（支持主动买卖量）
df = load_and_prepare_data_from_db(db_path, table_name)
```

其他代码无需修改，特征计算和模型训练会自动使用新特征。

### 兼容性

- 如果数据库中没有主动买卖量字段，系统会自动回退到传统特征
- 现有的训练脚本无需修改即可使用新特征
- 支持所有时间框架（1min, 5min, 15min等）

## 注意事项

1. **数据质量**: 确保数据库中的 `taker_buy_base_volume` 字段有有效数据
2. **计算时间**: 新特征会增加一些计算时间，但提升的性能值得这个开销
3. **内存使用**: 特征数量从30个增加到55个，内存使用会相应增加
4. **模型复杂度**: 更多特征可能需要调整模型参数以避免过拟合

## 故障排除

### 常见问题

1. **"未找到主动买卖量数据"警告**
   - 检查数据库表是否包含 `taker_buy_base_volume` 字段
   - 确认数据不全为0或NULL

2. **特征计算失败**
   - 检查数据量是否足够（至少需要720个数据点用于长期特征）
   - 确认时间戳格式正确

3. **性能提升不明显**
   - 检查数据质量和时间范围
   - 尝试调整模型超参数
   - 考虑数据的市场环境是否适合

## 下一步

1. 在更多币种上验证特征效果
2. 探索更复杂的主动买卖量特征组合
3. 研究特征在不同市场条件下的表现
4. 集成到实时交易系统中
