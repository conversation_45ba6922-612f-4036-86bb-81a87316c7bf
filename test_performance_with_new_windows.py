#!/usr/bin/env python3
"""
测试新时间窗口设置对模型性能的影响
"""

import sys
import os
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report

# 添加项目根目录到路径
sys.path.append('/Users/<USER>/project/ai/vibe/daily/eth-trade')

from model_utils_815 import (
    load_and_prepare_data_from_db, 
    calculate_features, 
    get_feature_list,
    create_percentage_target
)

def test_performance_with_new_windows():
    """测试新时间窗口设置的性能"""
    
    print("=" * 60)
    print("测试新时间窗口设置的模型性能")
    print("=" * 60)
    
    # 1. 加载数据
    db_path = "/Users/<USER>/project/ai/vibe/daily/eth-trade/coin_data.db"
    table_name = "ETHUSDT_5min_spot"
    
    print(f"从数据库加载数据: {table_name}")
    df = load_and_prepare_data_from_db(db_path, table_name, limit=20000)
    
    if df is None:
        print("❌ 数据加载失败")
        return False
    
    print(f"✅ 数据加载成功，共 {len(df)} 条记录")
    
    # 2. 计算特征
    print("\n计算特征...")
    timeframe = 5
    df_with_features = calculate_features(df, timeframe)
    print(f"✅ 特征计算完成，数据形状: {df_with_features.shape}")
    
    # 3. 生成标签
    print("\n生成标签...")
    up_threshold = 0.015  # 1.5%上涨
    down_threshold = 0.015  # 1.5%下跌
    max_lookforward_minutes = 240  # 4小时
    
    labels = create_percentage_target(
        df_with_features, 
        up_threshold, 
        down_threshold, 
        max_lookforward_minutes, 
        timeframe
    )
    
    print(f"✅ 标签生成完成，共 {len(labels)} 个标签")
    print(f"标签分布: {labels.value_counts().to_dict()}")
    
    # 4. 准备特征
    print("\n准备特征...")
    feature_list = get_feature_list(df_with_features, timeframe)
    
    # 分析特征类型
    new_window_features = []
    traditional_features = []
    taker_features = []
    
    for feature in feature_list:
        if any(window in feature for window in ['_30', '_60']) and not any(keyword in feature for keyword in ['taker', 'buy_sell']):
            new_window_features.append(feature)
        elif any(keyword in feature for keyword in ['taker', 'buy_sell', 'extreme', 'balanced']):
            taker_features.append(feature)
        else:
            traditional_features.append(feature)
    
    print(f"总特征数: {len(feature_list)}")
    print(f"新时间窗口特征 (30min, 60min): {len(new_window_features)}")
    print(f"主动买卖量特征: {len(taker_features)}")
    print(f"传统特征: {len(traditional_features)}")
    
    # 5. 对齐数据
    common_index = df_with_features.index.intersection(labels.index)
    X = df_with_features.loc[common_index, feature_list]
    y = labels.loc[common_index]
    
    # 删除包含NaN的行
    mask = ~(X.isnull().any(axis=1) | y.isnull())
    X = X[mask]
    y = y[mask]
    
    print(f"✅ 最终数据集: {X.shape[0]} 样本, {X.shape[1]} 特征")
    
    # 6. 分割数据集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"训练集: {X_train.shape[0]} 样本")
    print(f"测试集: {X_test.shape[0]} 样本")
    
    # 7. 训练完整模型
    print("\n训练完整模型 (包含所有新特征)...")
    model_full = RandomForestClassifier(
        n_estimators=100, 
        random_state=42, 
        max_depth=10,
        min_samples_split=20
    )
    model_full.fit(X_train, y_train)
    
    y_pred_full = model_full.predict(X_test)
    accuracy_full = accuracy_score(y_test, y_pred_full)
    
    print(f"✅ 完整模型准确率: {accuracy_full:.4f}")
    
    # 8. 训练不包含新时间窗口特征的模型
    print("\n训练传统模型 (不包含30min, 60min特征)...")
    traditional_and_taker = traditional_features + taker_features
    X_train_traditional = X_train[traditional_and_taker]
    X_test_traditional = X_test[traditional_and_taker]
    
    model_traditional = RandomForestClassifier(
        n_estimators=100, 
        random_state=42, 
        max_depth=10,
        min_samples_split=20
    )
    model_traditional.fit(X_train_traditional, y_train)
    
    y_pred_traditional = model_traditional.predict(X_test_traditional)
    accuracy_traditional = accuracy_score(y_test, y_pred_traditional)
    
    print(f"✅ 传统模型准确率: {accuracy_traditional:.4f}")
    
    # 9. 特征重要性分析
    print("\n" + "=" * 40)
    print("特征重要性分析")
    print("=" * 40)
    
    feature_importance = pd.DataFrame({
        'feature': feature_list,
        'importance': model_full.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("Top 15 重要特征:")
    print(feature_importance.head(15).to_string(index=False))
    
    # 新时间窗口特征的重要性
    new_window_importance = feature_importance[
        feature_importance['feature'].isin(new_window_features)
    ].sort_values('importance', ascending=False)
    
    if len(new_window_importance) > 0:
        print(f"\n新时间窗口特征重要性 (Top 10):")
        print(new_window_importance.head(10).to_string(index=False))
        
        # 计算新时间窗口特征的总重要性
        total_new_importance = new_window_importance['importance'].sum()
        print(f"\n新时间窗口特征总重要性: {total_new_importance:.4f} ({total_new_importance*100:.2f}%)")
    
    # 10. 结果总结
    print("\n" + "=" * 60)
    print("结果总结")
    print("=" * 60)
    
    improvement = accuracy_full - accuracy_traditional
    improvement_pct = (improvement / accuracy_traditional) * 100
    
    print(f"传统模型准确率 (不含30min,60min特征): {accuracy_traditional:.4f}")
    print(f"完整模型准确率 (含所有新特征): {accuracy_full:.4f}")
    print(f"准确率提升: {improvement:.4f} ({improvement_pct:+.2f}%)")
    
    if improvement > 0:
        print("✅ 新时间窗口特征带来了性能提升！")
    elif improvement < -0.001:
        print("⚠️ 新时间窗口特征可能带来了轻微的性能下降")
    else:
        print("➖ 新时间窗口特征对性能影响不大")
    
    # 特征数量对比
    print(f"\n特征数量对比:")
    print(f"传统模型特征数: {len(traditional_and_taker)}")
    print(f"完整模型特征数: {len(feature_list)}")
    print(f"新增特征数: {len(new_window_features)}")
    
    # 保存结果
    results = {
        'traditional_accuracy': accuracy_traditional,
        'full_accuracy': accuracy_full,
        'improvement': improvement,
        'improvement_pct': improvement_pct,
        'traditional_features': len(traditional_and_taker),
        'full_features': len(feature_list),
        'new_window_features': len(new_window_features)
    }
    
    output_file = "/Users/<USER>/project/ai/vibe/daily/eth-trade/new_time_windows_performance_test.csv"
    pd.DataFrame([results]).to_csv(output_file, index=False)
    print(f"\n结果已保存到: {output_file}")
    
    return True

if __name__ == "__main__":
    success = test_performance_with_new_windows()
    if success:
        print("\n🎉 性能测试完成！")
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)
