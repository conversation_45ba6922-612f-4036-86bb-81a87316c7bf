# 训练脚本使用示例 (trainall.py)

## 基本用法

### 1. 单币种训练（使用配置文件）
```bash
python trainall.py --coin eth
```

### 2. 指定数据库路径
```bash
python trainall.py --coin eth --db-path /path/to/coin_data.db
```

### 3. 多币种联合训练
```bash
python trainall.py --coins eth btc dot
```

### 4. 使用预定义的多币种配置
```bash
python trainall.py --multi-coin-config crypto_major
```

### 5. 使用自定义配置文件
```bash
python trainall.py --coin eth --config-file my_config.json
```

### 6. 指定时间范围
```bash
python trainall.py --coin eth --start-time "2024-01-01" --end-time "2024-12-31"
```

## 高级功能

### 7. 启用特征优化
```bash
python trainall.py --coin eth --optimize-features --top-n 30 --corr-threshold 0.85
```

### 8. 禁用时序交叉验证（快速训练）
```bash
python trainall.py --coin eth --no-time-series-cv
```

### 9. 自定义交叉验证参数
```bash
python trainall.py --coin eth --cv-splits 3 --cv-trials 50
```

### 10. 保存和加载预处理数据
```bash
# 首次训练时保存数据
python trainall.py --coin eth --save-data --data-file eth_processed.pkl

# 后续训练时加载数据
python trainall.py --coin eth --load-data --data-file eth_processed.pkl
```

### 11. 自定义交易对和时间间隔
```bash
python trainall.py --coin eth --symbol ETHUSDT --interval 1m --market spot
```

## 数据库要求

### SQLite数据库结构
训练脚本需要SQLite数据库，表名格式为: `{SYMBOL}_{INTERVAL}_{MARKET}`

例如: `ETHUSDT_5m_spot`

### 必需的列
- `timestamp`: 时间戳 (主键)
- `open`: 开盘价
- `high`: 最高价  
- `low`: 最低价
- `close`: 收盘价
- `volume`: 成交量

### 示例表结构
```sql
CREATE TABLE ETHUSDT_5m_spot (
    timestamp DATETIME PRIMARY KEY,
    open REAL,
    high REAL,
    low REAL,
    close REAL,
    volume REAL
);
```

## 配置文件说明

### train.json 结构
```json
{
  "description": "多币种训练配置文件",
  "coin_configs": {
    "eth": {
      "display_name": "以太坊",
      "symbol": "ETHUSDT",
      "timeframe_minutes": 5,
      "up_threshold": 0.015,
      "down_threshold": 0.015,
      "max_lookforward_minutes": 240,
      "model_basename": "eth_5m_1_5pct_4h"
    }
  },
  "data": {
    "eth": {
      "train": [["2024-01-01", "2024-06-30"]],
      "val": [["2023-06-01", "2023-12-31"]],
      "test": [["2025-03-01", "2025-05-31"]]
    }
  },
  "multi_coin_configs": {
    "crypto_major": {
      "coins": ["eth", "btc"],
      "description": "主要加密货币联合训练",
      "use_config_from": "eth"
    }
  }
}
```

### 配置说明
- **coin_configs**: 每个币种的具体配置参数
  - `display_name`: 显示名称
  - `symbol`: 交易对符号
  - `timeframe_minutes`: 时间框架（分钟）
  - `up_threshold/down_threshold`: 涨跌阈值
  - `max_lookforward_minutes`: 最大前瞻时间
  - `model_basename`: 模型基础名称
- **data**: 每个币种的时间分割配置
  - `train`: 训练集时间范围，支持多个不连续的时间段
  - `val`: 验证集时间范围，支持多个不连续的时间段  
  - `test`: 测试集时间范围，支持多个不连续的时间段
- **multi_coin_configs**: 预定义的多币种组合配置
  - `use_config_from`: 指定使用哪个币种的参数配置

## 完整示例

### 多币种特征优化训练
```bash
python trainall.py \
  --multi-coin-config crypto_major \
  --optimize-features \
  --top-n 40 \
  --corr-threshold 0.9 \
  --cv-splits 5 \
  --cv-trials 100 \
  --save-data
```

这个命令将：
1. 使用 crypto_major 配置（ETH + BTC）
2. 启用特征优化，选择前40个重要特征
3. 移除相关性>0.9的特征
4. 使用5折交叉验证，100次Optuna试验
5. 保存预处理数据以便后续使用
6. 按 train.json 中的时间配置分割数据

## 输出文件

训练完成后会生成以下文件：
- `{MODEL_BASENAME}_model.joblib`: 训练好的模型
- `{MODEL_BASENAME}_config.json`: 模型配置信息
- `test_results_{MODEL_BASENAME}.csv`: 测试集详细结果
- `feature_importance_{MODEL_BASENAME}.csv`: 特征重要性排序
- `cv_results_optuna_{MODEL_BASENAME}.csv`: 交叉验证结果（如果启用）