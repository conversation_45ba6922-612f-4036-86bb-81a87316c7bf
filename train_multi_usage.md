# train_multi.py 使用说明

## 概述

`train_multi.py` 是一个多币种联合训练脚本，它可以：

1. **自动发现数据库中的所有可用表** - 不再需要在 config.json 中手动配置币种
2. **通过命令行参数控制所有训练参数** - 包括时间间隔、阈值、前瞻时间等
3. **支持灵活的数据过滤** - 可以按时间间隔、市场类型、时间范围过滤
4. **生成完整的训练记录** - 包括模型文件、配置文件、特征重要性等

## 主要改进

### 🔄 从 config.json 依赖到数据库自动发现
- **之前**: 需要在 config.json 中为每个币种配置详细参数
- **现在**: 直接扫描 SQLite 数据库，自动发现所有可用的表

### 📊 灵活的参数控制
- **之前**: 训练参数硬编码在脚本中
- **现在**: 所有参数都可以通过命令行传入

### 🎯 智能表过滤
- 支持按时间间隔过滤（如 15min, 5min, 1hour）
- 支持按市场类型过滤（如 spot, futures）
- 支持按时间范围过滤数据

## 使用方法

### 基本用法

```bash
# 使用默认参数训练所有 15 分钟现货数据
python train_multi.py --db-path coin_data.db --interval 15min --market spot
```

### 自定义训练参数

```bash
# 自定义阈值和前瞻时间
python train_multi.py \
    --db-path coin_data.db \
    --interval 15min \
    --market spot \
    --up-threshold 0.03 \
    --down-threshold 0.03 \
    --max-lookforward-minutes 720 \
    --timeframe-minutes 15 \
    --best-threshold 0.6
```

### 指定时间范围

```bash
# 只使用特定时间范围的数据
python train_multi.py \
    --db-path coin_data.db \
    --interval 15min \
    --market spot \
    --start-time 2025-08-01 \
    --end-time 2025-08-25
```

### 自定义模型文件名

```bash
# 指定模型保存路径
python train_multi.py \
    --db-path coin_data.db \
    --interval 5min \
    --market spot \
    --model-file my_5min_model.joblib
```

## 命令行参数详解

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--db-path` | `coin_data.db` | SQLite数据库文件路径 |
| `--model-file` | `multi_coin_lgbm_model.joblib` | 保存模型的文件名 |
| `--test-start-date` | `None` | 测试集开始日期 (格式: 'YYYY-MM-DD') |
| `--start-time` | `None` | 数据加载开始日期 (格式: 'YYYY-MM-DD') |
| `--end-time` | `None` | 数据加载结束日期 (格式: 'YYYY-MM-DD') |
| `--interval` | `15min` | 时间间隔过滤 (例如: 15min, 5min, 1hour) |
| `--market` | `spot` | 市场类型过滤 (例如: spot, futures) |
| `--up-threshold` | `0.05` | 上涨阈值 |
| `--down-threshold` | `0.05` | 下跌阈值 |
| `--max-lookforward-minutes` | `1440` | 最大前瞻时间(分钟) |
| `--timeframe-minutes` | `15` | 时间框架(分钟) |
| `--best-threshold` | `0.7` | 最佳预测阈值 |

## 输出文件

脚本会生成以下文件：

1. **模型文件**: `{model-file}` (例如: `multi_coin_lgbm_model.joblib`)
2. **配置文件**: `{model-file}_config.json` - 包含训练参数和元数据
3. **特征重要性**: `{model-file}_feature_importance.csv` - 特征重要性排序

## 示例场景

### 场景1: 快速原型开发
```bash
# 使用少量数据快速测试
python train_multi.py \
    --db-path coin_data.db \
    --interval 15min \
    --market spot \
    --start-time 2025-08-20 \
    --end-time 2025-08-25
```

### 场景2: 生产环境训练
```bash
# 使用所有可用数据进行完整训练
python train_multi.py \
    --db-path coin_data.db \
    --interval 15min \
    --market spot \
    --up-threshold 0.03 \
    --down-threshold 0.03 \
    --max-lookforward-minutes 960 \
    --model-file production_model_15m.joblib
```

### 场景3: 不同时间框架对比
```bash
# 5分钟模型
python train_multi.py --interval 5min --model-file model_5m.joblib

# 15分钟模型  
python train_multi.py --interval 15min --model-file model_15m.joblib

# 1小时模型
python train_multi.py --interval 1hour --model-file model_1h.joblib
```

## 注意事项

1. **数据量要求**: 确保有足够的数据进行训练，建议至少几千个样本
2. **时间间隔匹配**: `--interval` 参数会自动转换格式（15min → 15m）
3. **内存使用**: 大量数据可能需要较多内存，可以使用时间范围限制
4. **特征过滤**: 脚本会自动过滤掉非数值列（如 created_at, coin_id）

## 故障排除

### 问题1: 未找到符合条件的数据表
```
错误: 未找到符合条件的数据表，训练终止。
```
**解决方案**: 检查数据库中的表名和过滤条件，使用 `python get_coin_history.py --list-tables --db coin_data.db` 查看可用表。

### 问题2: 数据量太少
```
AUC: nan
```
**解决方案**: 增加时间范围或减少阈值，确保有足够的正负样本。

### 问题3: 内存不足
**解决方案**: 使用 `--start-time` 和 `--end-time` 限制数据范围，或者分批训练。
