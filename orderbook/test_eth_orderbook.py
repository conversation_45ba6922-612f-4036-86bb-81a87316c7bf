"""
测试ETH订单薄数据获取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from eth_5m_microstructure import ETH5MinMicrostructureCollector
import json

def test_single_collection():
    """
    测试单次数据采集
    """
    print("测试ETH订单薄数据采集...")
    print("="*40)
    
    collector = ETH5MinMicrostructureCollector()
    
    # 执行单次采集
    data_point = collector.collect_single_datapoint()
    
    if data_point:
        print("✓ 数据采集成功!")
        print("\n关键指标:")
        print(f"最佳买价: {data_point.get('best_bid_price', 'N/A')}")
        print(f"最佳卖价: {data_point.get('best_ask_price', 'N/A')}")
        print(f"价差百分比: {data_point.get('bid_ask_spread_pct', 'N/A')}%")
        print(f"流动性不平衡: {data_point.get('liquidity_imbalance_volume', 'N/A')}")
        print(f"买单深度斜率: {data_point.get('bid_slope', 'N/A')}")
        print(f"卖单深度斜率: {data_point.get('ask_slope', 'N/A')}")
        
        # 保存测试结果
        with open('demo/test_result.json', 'w', encoding='utf-8') as f:
            json.dump(data_point, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n测试结果已保存到: demo/test_result.json")
        return True
    else:
        print("✗ 数据采集失败")
        return False

if __name__ == "__main__":
    success = test_single_collection()
    if success:
        print("\n🎉 测试通过!")
    else:
        print("\n❌ 测试失败，请检查网络连接")