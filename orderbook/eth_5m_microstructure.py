"""
ETH 5分钟市场微观结构数据采集器
专门用于获取和分析ETH的5分钟订单薄数据
"""

import requests
import json
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os
from typing import Dict, List, Optional, Tuple

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from market_microstructure_indicators import MarketMicrostructureIndicators

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('demo/eth_5m_microstructure.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ETH5MinMicrostructureCollector:
    """
    ETH 5分钟市场微观结构数据采集器
    """
    
    def __init__(self):
        self.symbol = "ETHUSDT"
        self.base_url = "https://api.binance.com"
        self.calculator = MarketMicrostructureIndicators()
        
        # 数据存储
        self.data_points = []
        
        # 配置参数
        self.depth_limit = 20  # 获取20档深度
        self.interval_seconds = 300  # 5分钟 = 300秒
        
    def fetch_order_book(self) -> Optional[Dict]:
        """
        获取订单薄数据
        """
        try:
            url = f"{self.base_url}/api/v3/depth"
            params = {
                'symbol': self.symbol,
                'limit': self.depth_limit
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            return {
                'timestamp': datetime.now(),
                'bids': [(float(bid[0]), float(bid[1])) for bid in data['bids']],
                'asks': [(float(ask[0]), float(ask[1])) for ask in data['asks']],
                'lastUpdateId': data['lastUpdateId']
            }
            
        except Exception as e:
            logger.error(f"获取订单薄失败: {e}")
            return None
    
    def calculate_microstructure_indicators(self, order_book: Dict) -> Dict:
        """
        计算市场微观结构指标
        """
        try:
            # 格式化订单薄数据
            formatted_book = {
                'bids': order_book['bids'],
                'asks': order_book['asks']
            }
            
            # 计算基础指标
            indicators = self.calculator.process_order_book_snapshot(
                formatted_book, 
                order_book['timestamp'].isoformat()
            )
            
            # 添加自定义计算
            bids = order_book['bids']
            asks = order_book['asks']
            
            if bids and asks:
                best_bid = bids[0][0]
                best_ask = asks[0][0]
                
                # 计算额外的微观结构指标
                indicators.update({
                    # 价格相关
                    'best_bid_price': best_bid,
                    'best_ask_price': best_ask,
                    'mid_price': (best_bid + best_ask) / 2,
                    
                    # 量价分析
                    'bid_volume_top5': sum([bid[1] for bid in bids[:5]]),
                    'ask_volume_top5': sum([ask[1] for ask in asks[:5]]),
                    'total_bid_volume': sum([bid[1] for bid in bids]),
                    'total_ask_volume': sum([ask[1] for ask in asks]),
                    
                    # 深度分析
                    'bid_levels_count': len(bids),
                    'ask_levels_count': len(asks),
                    
                    # 价格影响分析
                    'price_impact_1000_buy': self.calculate_price_impact(asks, 1000, 'buy'),
                    'price_impact_1000_sell': self.calculate_price_impact(bids, 1000, 'sell'),
                    'price_impact_5000_buy': self.calculate_price_impact(asks, 5000, 'buy'),
                    'price_impact_5000_sell': self.calculate_price_impact(bids, 5000, 'sell'),
                })
            
            return indicators
            
        except Exception as e:
            logger.error(f"计算指标失败: {e}")
            return {}
    
    def calculate_price_impact(self, orders: List[Tuple[float, float]], 
                             volume: float, side: str) -> float:
        """
        计算价格影响
        
        Args:
            orders: 订单列表 [(price, volume), ...]
            volume: 交易量
            side: 'buy' 或 'sell'
        """
        try:
            if not orders:
                return np.nan
            
            remaining_volume = volume
            total_cost = 0.0
            
            for price, available_volume in orders:
                if remaining_volume <= 0:
                    break
                
                trade_volume = min(remaining_volume, available_volume)
                total_cost += trade_volume * price
                remaining_volume -= trade_volume
            
            if remaining_volume > 0:
                # 流动性不足
                return np.nan
            
            avg_price = total_cost / volume
            best_price = orders[0][0]
            
            # 计算价格影响（百分比）
            if side == 'buy':
                return (avg_price - best_price) / best_price * 100
            else:
                return (best_price - avg_price) / best_price * 100
                
        except Exception as e:
            logger.error(f"计算价格影响失败: {e}")
            return np.nan
    
    def collect_single_datapoint(self) -> Optional[Dict]:
        """
        采集单个数据点
        """
        logger.info("开始采集数据点...")
        
        # 获取订单薄
        order_book = self.fetch_order_book()
        if not order_book:
            return None
        
        # 计算指标
        indicators = self.calculate_microstructure_indicators(order_book)
        if not indicators:
            return None
        
        # 添加元数据
        data_point = {
            'collection_time': datetime.now().isoformat(),
            'symbol': self.symbol,
            'lastUpdateId': order_book['lastUpdateId'],
            **indicators
        }
        
        self.data_points.append(data_point)
        logger.info(f"数据点采集完成，当前总数: {len(self.data_points)}")
        
        return data_point
    
    def run_collection(self, duration_hours: float = 1.0, save_interval: int = 6):
        """
        运行数据采集
        
        Args:
            duration_hours: 采集时长（小时）
            save_interval: 保存间隔（每N个数据点保存一次）
        """
        logger.info(f"开始ETH 5分钟微观结构数据采集")
        logger.info(f"采集时长: {duration_hours} 小时")
        logger.info(f"采集间隔: {self.interval_seconds} 秒")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(hours=duration_hours)
        
        collection_count = 0
        
        try:
            while datetime.now() < end_time:
                # 采集数据点
                data_point = self.collect_single_datapoint()
                
                if data_point:
                    collection_count += 1
                    self.print_current_status(data_point)
                    
                    # 定期保存数据
                    if collection_count % save_interval == 0:
                        self.save_data(f"checkpoint_{collection_count}")
                
                # 等待下次采集
                logger.info(f"等待 {self.interval_seconds} 秒...")
                time.sleep(self.interval_seconds)
            
            logger.info(f"数据采集完成，共采集 {collection_count} 个数据点")
            
            # 最终保存
            self.save_data("final")
            self.generate_summary_report()
            
        except KeyboardInterrupt:
            logger.info("用户中断采集")
            self.save_data("interrupted")
            self.generate_summary_report()
        except Exception as e:
            logger.error(f"采集过程出错: {e}")
            self.save_data("error")
    
    def print_current_status(self, data_point: Dict):
        """
        打印当前状态
        """
        print(f"\n{'='*60}")
        print(f"时间: {data_point.get('collection_time', 'N/A')}")
        print(f"数据点: {len(self.data_points)}")
        print(f"{'='*60}")
        
        # 价格信息
        if 'best_bid_price' in data_point:
            print(f"最佳买价: {data_point['best_bid_price']:.4f}")
            print(f"最佳卖价: {data_point['best_ask_price']:.4f}")
            print(f"中间价: {data_point['mid_price']:.4f}")
        
        # 价差
        if 'bid_ask_spread_pct' in data_point:
            print(f"价差: {data_point['bid_ask_spread_pct']:.4f}%")
        
        # 流动性不平衡
        if 'liquidity_imbalance_volume' in data_point:
            imbalance = data_point['liquidity_imbalance_volume']
            status = "买方优势" if imbalance > 0.1 else "卖方优势" if imbalance < -0.1 else "平衡"
            print(f"流动性不平衡: {imbalance:.4f} ({status})")
        
        # 深度斜率
        if 'bid_slope' in data_point and not np.isnan(data_point['bid_slope']):
            print(f"买单深度斜率: {data_point['bid_slope']:.2f}")
        if 'ask_slope' in data_point and not np.isnan(data_point['ask_slope']):
            print(f"卖单深度斜率: {data_point['ask_slope']:.2f}")
        
        # 价格影响
        if 'price_impact_1000_buy' in data_point:
            print(f"1000 ETH买入价格影响: {data_point['price_impact_1000_buy']:.4f}%")
        if 'price_impact_1000_sell' in data_point:
            print(f"1000 ETH卖出价格影响: {data_point['price_impact_1000_sell']:.4f}%")
        
        print(f"{'='*60}")
    
    def save_data(self, suffix: str = ""):
        """
        保存数据
        """
        try:
            if not self.data_points:
                logger.warning("没有数据需要保存")
                return
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存CSV格式
            df = pd.DataFrame(self.data_points)
            csv_filename = f"demo/eth_5m_microstructure_{timestamp}_{suffix}.csv"
            df.to_csv(csv_filename, index=False)
            logger.info(f"数据已保存到: {csv_filename}")
            
            # 保存JSON格式（原始数据）
            json_filename = f"demo/eth_5m_microstructure_{timestamp}_{suffix}.json"
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(self.data_points, f, indent=2, ensure_ascii=False, default=str)
            logger.info(f"原始数据已保存到: {json_filename}")
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
    
    def generate_summary_report(self):
        """
        生成汇总报告
        """
        try:
            if not self.data_points:
                return
            
            df = pd.DataFrame(self.data_points)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            report = []
            report.append("ETH 5分钟市场微观结构分析报告")
            report.append("="*60)
            report.append(f"生成时间: {timestamp}")
            report.append(f"数据点数量: {len(df)}")
            report.append(f"采集时间跨度: {df['collection_time'].min()} 到 {df['collection_time'].max()}")
            report.append("")
            
            # 价差统计
            if 'bid_ask_spread_pct' in df.columns:
                spread_stats = df['bid_ask_spread_pct'].describe()
                report.append("价差统计 (%):")
                report.append(f"  平均值: {spread_stats['mean']:.4f}")
                report.append(f"  标准差: {spread_stats['std']:.4f}")
                report.append(f"  最小值: {spread_stats['min']:.4f}")
                report.append(f"  最大值: {spread_stats['max']:.4f}")
                report.append(f"  中位数: {spread_stats['50%']:.4f}")
                report.append("")
            
            # 流动性不平衡统计
            if 'liquidity_imbalance_volume' in df.columns:
                imbalance_stats = df['liquidity_imbalance_volume'].describe()
                report.append("流动性不平衡统计:")
                report.append(f"  平均值: {imbalance_stats['mean']:.4f}")
                report.append(f"  标准差: {imbalance_stats['std']:.4f}")
                report.append(f"  买方优势次数: {(df['liquidity_imbalance_volume'] > 0.1).sum()}")
                report.append(f"  卖方优势次数: {(df['liquidity_imbalance_volume'] < -0.1).sum()}")
                report.append(f"  平衡次数: {((df['liquidity_imbalance_volume'] >= -0.1) & (df['liquidity_imbalance_volume'] <= 0.1)).sum()}")
                report.append("")
            
            # 价格影响统计
            if 'price_impact_1000_buy' in df.columns:
                impact_buy = df['price_impact_1000_buy'].dropna()
                impact_sell = df['price_impact_1000_sell'].dropna()
                
                if len(impact_buy) > 0:
                    report.append("价格影响统计 (1000 ETH):")
                    report.append(f"  买入平均影响: {impact_buy.mean():.4f}%")
                    report.append(f"  卖出平均影响: {impact_sell.mean():.4f}%")
                    report.append("")
            
            # 保存报告
            report_filename = f"demo/eth_5m_microstructure_report_{timestamp}.txt"
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report))
            
            logger.info(f"分析报告已保存到: {report_filename}")
            
            # 打印报告
            print("\n" + '\n'.join(report))
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")


def main():
    """
    主函数
    """
    print("ETH 5分钟市场微观结构数据采集器")
    print("="*50)
    
    collector = ETH5MinMicrostructureCollector()
    
    print("选择运行模式:")
    print("1. 单次采集")
    print("2. 1小时采集 (12个数据点)")
    print("3. 4小时采集 (48个数据点)")
    print("4. 自定义时长采集")
    
    try:
        choice = input("请选择 (1-4): ").strip()
        
        if choice == "1":
            print("\n执行单次采集...")
            data_point = collector.collect_single_datapoint()
            if data_point:
                collector.print_current_status(data_point)
                collector.save_data("single")
            
        elif choice == "2":
            collector.run_collection(duration_hours=1.0)
            
        elif choice == "3":
            collector.run_collection(duration_hours=4.0)
            
        elif choice == "4":
            hours = float(input("请输入采集时长（小时）: "))
            collector.run_collection(duration_hours=hours)
            
        else:
            print("无效选择，执行单次采集...")
            data_point = collector.collect_single_datapoint()
            if data_point:
                collector.print_current_status(data_point)
                collector.save_data("single")
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")


if __name__ == "__main__":
    main()