"""
订单薄机器学习模型训练器
基于市场微观结构指标训练预测模型
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging
import sys
import os
import joblib
from typing import Dict, List, Optional, Tuple

# Machine Learning imports
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.svm import SVR, SVC
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import xgboost as xgb
import lightgbm as lgb

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from demo.training_data_processor import OrderBookTrainingProcessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OrderBookMLTrainer:
    """
    订单薄机器学习训练器
    """
    
    def __init__(self):
        self.models = {}
        self.model_performance = {}
        self.processor = OrderBookTrainingProcessor()
        
    def get_available_models(self, task_type: str = 'regression') -> Dict:
        """
        获取可用模型
        """
        if task_type == 'regression':
            return {
                'linear': LinearRegression(),
                'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
                'xgboost': xgb.XGBRegressor(n_estimators=100, random_state=42),
                'lightgbm': lgb.LGBMRegressor(n_estimators=100, random_state=42, verbose=-1),
                'svr': SVR(kernel='rbf')
            }
        else:  # classification
            return {
                'logistic': LogisticRegression(random_state=42),
                'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
                'xgboost': xgb.XGBClassifier(n_estimators=100, random_state=42),
                'lightgbm': lgb.LGBMClassifier(n_estimators=100, random_state=42, verbose=-1),
                'svc': SVC(kernel='rbf', random_state=42)
            }
    
    def train_regression_model(self, X_train: pd.DataFrame, X_test: pd.DataFrame, 
                             y_train: pd.Series, y_test: pd.Series, 
                             model_name: str = 'random_forest') -> Dict:
        """
        训练回归模型
        """
        try:
            logger.info(f"开始训练回归模型: {model_name}")
            
            # 获取模型
            models = self.get_available_models('regression')
            if model_name not in models:
                raise ValueError(f"未知模型: {model_name}")
            
            model = models[model_name]
            
            # 训练模型
            model.fit(X_train, y_train)
            
            # 预测
            y_train_pred = model.predict(X_train)
            y_test_pred = model.predict(X_test)
            
            # 计算性能指标
            performance = {
                'model_name': model_name,
                'task_type': 'regression',
                'train_mse': mean_squared_error(y_train, y_train_pred),
                'test_mse': mean_squared_error(y_test, y_test_pred),
                'train_mae': mean_absolute_error(y_train, y_train_pred),
                'test_mae': mean_absolute_error(y_test, y_test_pred),
                'train_r2': r2_score(y_train, y_train_pred),
                'test_r2': r2_score(y_test, y_test_pred),
                'train_rmse': np.sqrt(mean_squared_error(y_train, y_train_pred)),
                'test_rmse': np.sqrt(mean_squared_error(y_test, y_test_pred))
            }
            
            # 特征重要性（如果模型支持）
            if hasattr(model, 'feature_importances_'):
                feature_importance = pd.DataFrame({
                    'feature': X_train.columns,
                    'importance': model.feature_importances_
                }).sort_values('importance', ascending=False)
                performance['feature_importance'] = feature_importance
            
            self.models[model_name] = model
            self.model_performance[model_name] = performance
            
            logger.info(f"模型训练完成 - 测试R²: {performance['test_r2']:.4f}, RMSE: {performance['test_rmse']:.6f}")
            
            return performance
            
        except Exception as e:
            logger.error(f"训练回归模型失败: {e}")
            return {}
    
    def train_classification_model(self, X_train: pd.DataFrame, X_test: pd.DataFrame, 
                                 y_train: pd.Series, y_test: pd.Series, 
                                 model_name: str = 'random_forest') -> Dict:
        """
        训练分类模型
        """
        try:
            logger.info(f"开始训练分类模型: {model_name}")
            
            # 获取模型
            models = self.get_available_models('classification')
            if model_name not in models:
                raise ValueError(f"未知模型: {model_name}")
            
            model = models[model_name]
            
            # 训练模型
            model.fit(X_train, y_train)
            
            # 预测
            y_train_pred = model.predict(X_train)
            y_test_pred = model.predict(X_test)
            
            # 计算性能指标
            performance = {
                'model_name': model_name,
                'task_type': 'classification',
                'train_accuracy': accuracy_score(y_train, y_train_pred),
                'test_accuracy': accuracy_score(y_test, y_test_pred),
                'classification_report': classification_report(y_test, y_test_pred),
                'confusion_matrix': confusion_matrix(y_test, y_test_pred).tolist()
            }
            
            # 特征重要性（如果模型支持）
            if hasattr(model, 'feature_importances_'):
                feature_importance = pd.DataFrame({
                    'feature': X_train.columns,
                    'importance': model.feature_importances_
                }).sort_values('importance', ascending=False)
                performance['feature_importance'] = feature_importance
            
            self.models[model_name] = model
            self.model_performance[model_name] = performance
            
            logger.info(f"模型训练完成 - 测试准确率: {performance['test_accuracy']:.4f}")
            
            return performance
            
        except Exception as e:
            logger.error(f"训练分类模型失败: {e}")
            return {}
    
    def train_multiple_models(self, X_train: pd.DataFrame, X_test: pd.DataFrame, 
                            y_train: pd.Series, y_test: pd.Series, 
                            task_type: str = 'regression') -> Dict:
        """
        训练多个模型并比较性能
        """
        try:
            logger.info(f"开始训练多个{task_type}模型")
            
            models = self.get_available_models(task_type)
            results = {}
            
            for model_name in models.keys():
                try:
                    if task_type == 'regression':
                        performance = self.train_regression_model(
                            X_train, X_test, y_train, y_test, model_name
                        )
                    else:
                        performance = self.train_classification_model(
                            X_train, X_test, y_train, y_test, model_name
                        )
                    
                    if performance:
                        results[model_name] = performance
                        
                except Exception as e:
                    logger.error(f"训练模型 {model_name} 失败: {e}")
                    continue
            
            # 找出最佳模型
            if results:
                if task_type == 'regression':
                    best_model = min(results.keys(), key=lambda x: results[x]['test_rmse'])
                    logger.info(f"最佳回归模型: {best_model} (RMSE: {results[best_model]['test_rmse']:.6f})")
                else:
                    best_model = max(results.keys(), key=lambda x: results[x]['test_accuracy'])
                    logger.info(f"最佳分类模型: {best_model} (准确率: {results[best_model]['test_accuracy']:.4f})")
                
                results['best_model'] = best_model
            
            return results
            
        except Exception as e:
            logger.error(f"训练多个模型失败: {e}")
            return {}
    
    def save_models(self, output_dir: str = "demo/models"):
        """
        保存训练好的模型
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            for model_name, model in self.models.items():
                model_filename = f"{output_dir}/orderbook_{model_name}_{timestamp}.joblib"
                joblib.dump(model, model_filename)
                logger.info(f"模型已保存: {model_filename}")
            
            # 保存性能报告
            performance_filename = f"{output_dir}/model_performance_{timestamp}.json"
            import json
            
            # 转换DataFrame为字典以便JSON序列化
            performance_for_json = {}
            for model_name, perf in self.model_performance.items():
                perf_copy = perf.copy()
                if 'feature_importance' in perf_copy:
                    perf_copy['feature_importance'] = perf_copy['feature_importance'].to_dict('records')
                performance_for_json[model_name] = perf_copy
            
            with open(performance_filename, 'w', encoding='utf-8') as f:
                json.dump(performance_for_json, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"性能报告已保存: {performance_filename}")
            
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
    
    def generate_training_report(self, results: Dict, output_filename: str):
        """
        生成训练报告
        """
        try:
            report = []
            report.append("订单薄机器学习模型训练报告")
            report.append("="*60)
            report.append(f"生成时间: {datetime.now().isoformat()}")
            report.append("")
            
            if 'best_model' in results:
                report.append(f"最佳模型: {results['best_model']}")
                report.append("")
            
            # 模型性能对比
            report.append("模型性能对比:")
            report.append("-"*40)
            
            for model_name, performance in results.items():
                if model_name == 'best_model':
                    continue
                
                report.append(f"\n{model_name.upper()}:")
                
                if performance.get('task_type') == 'regression':
                    report.append(f"  测试R²: {performance.get('test_r2', 0):.4f}")
                    report.append(f"  测试RMSE: {performance.get('test_rmse', 0):.6f}")
                    report.append(f"  测试MAE: {performance.get('test_mae', 0):.6f}")
                else:
                    report.append(f"  测试准确率: {performance.get('test_accuracy', 0):.4f}")
                
                # 特征重要性（前10个）
                if 'feature_importance' in performance:
                    report.append("  重要特征 (前10):")
                    feature_imp = performance['feature_importance']
                    if isinstance(feature_imp, pd.DataFrame):
                        for _, row in feature_imp.head(10).iterrows():
                            report.append(f"    {row['feature']}: {row['importance']:.4f}")
                    else:
                        for item in feature_imp[:10]:
                            report.append(f"    {item['feature']}: {item['importance']:.4f}")
            
            # 保存报告
            with open(output_filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report))
            
            logger.info(f"训练报告已保存: {output_filename}")
            
            # 打印报告摘要
            print("\n" + '\n'.join(report[:20]))  # 打印前20行
            
        except Exception as e:
            logger.error(f"生成训练报告失败: {e}")


def main():
    """
    主函数
    """
    print("订单薄机器学习模型训练器")
    print("="*50)
    
    trainer = OrderBookMLTrainer()
    
    # 获取输入文件
    input_file = input("请输入处理后的训练数据文件路径: ").strip()
    
    if not os.path.exists(input_file):
        print("文件不存在!")
        return
    
    try:
        # 加载数据
        print("加载数据...")
        if input_file.endswith('.pkl'):
            df = pd.read_pickle(input_file)
        else:
            df = pd.read_csv(input_file)
        
        print(f"数据大小: {len(df)} 行 x {len(df.columns)} 列")
        
        # 选择预测任务
        print("\n可用的预测任务:")
        target_options = {
            '1': ('spread_change_5', 'regression', '价差变化预测 (5步)'),
            '2': ('imbalance_change_5', 'regression', '流动性不平衡变化预测 (5步)'),
            '3': ('spread_direction_5', 'classification', '价差方向预测 (5步)'),
            '4': ('market_state_future_5', 'classification', '市场状态预测 (5步)')
        }
        
        for key, (target, task_type, description) in target_options.items():
            if target in df.columns:
                print(f"{key}. {description} ({task_type})")
        
        choice = input("请选择预测任务 (1-4): ").strip()
        
        if choice not in target_options:
            print("无效选择!")
            return
        
        target_column, task_type, description = target_options[choice]
        
        if target_column not in df.columns:
            print(f"目标列 {target_column} 不存在!")
            return
        
        print(f"选择的任务: {description}")
        
        # 准备训练数据
        print("准备训练数据...")
        X_train, X_test, y_train, y_test = trainer.processor.prepare_training_data(
            df, target_column, test_size=0.2
        )
        
        if X_train.empty:
            print("训练数据准备失败!")
            return
        
        print(f"训练集: {len(X_train)} 样本, {len(X_train.columns)} 特征")
        print(f"测试集: {len(X_test)} 样本")
        
        # 选择训练模式
        print("\n训练模式:")
        print("1. 训练单个模型")
        print("2. 训练并比较多个模型")
        
        mode_choice = input("请选择 (1-2): ").strip()
        
        if mode_choice == "1":
            # 单个模型训练
            available_models = list(trainer.get_available_models(task_type).keys())
            print(f"\n可用模型: {', '.join(available_models)}")
            model_name = input("请输入模型名称: ").strip()
            
            if model_name not in available_models:
                print("无效模型名称!")
                return
            
            if task_type == 'regression':
                results = {model_name: trainer.train_regression_model(
                    X_train, X_test, y_train, y_test, model_name
                )}
            else:
                results = {model_name: trainer.train_classification_model(
                    X_train, X_test, y_train, y_test, model_name
                )}
        
        else:
            # 多模型训练
            print("开始训练多个模型...")
            results = trainer.train_multiple_models(
                X_train, X_test, y_train, y_test, task_type
            )
        
        if not results:
            print("模型训练失败!")
            return
        
        # 保存模型和结果
        print("保存模型...")
        trainer.save_models()
        
        # 生成报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"demo/training_report_{timestamp}.txt"
        trainer.generate_training_report(results, report_filename)
        
        print(f"\n训练完成!")
        print(f"训练报告: {report_filename}")
        
        # 显示最佳模型信息
        if 'best_model' in results:
            best_model_name = results['best_model']
            best_performance = results[best_model_name]
            
            print(f"\n最佳模型: {best_model_name}")
            if task_type == 'regression':
                print(f"测试R²: {best_performance.get('test_r2', 0):.4f}")
                print(f"测试RMSE: {best_performance.get('test_rmse', 0):.6f}")
            else:
                print(f"测试准确率: {best_performance.get('test_accuracy', 0):.4f}")
    
    except Exception as e:
        logger.error(f"训练过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()