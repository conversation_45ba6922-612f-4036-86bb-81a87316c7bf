"""
完整工作流程示例
演示从数据收集到模型训练的完整流程
"""

import os
import time
import pandas as pd
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_complete_workflow():
    """
    运行完整的工作流程示例
    """
    print("订单薄机器学习完整工作流程示例")
    print("="*60)
    
    # 步骤1: 快速测试连接
    print("\n步骤1: 测试Binance API连接...")
    try:
        from test_eth_orderbook import test_single_collection
        if test_single_collection():
            print("✓ API连接正常")
        else:
            print("✗ API连接失败，请检查网络")
            return
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return
    
    # 步骤2: 收集少量实时数据（演示用）
    print("\n步骤2: 收集演示数据...")
    try:
        from historical_orderbook_collector import HistoricalOrderBookCollector
        
        collector = HistoricalOrderBookCollector("ETHUSDT")
        
        # 收集10分钟的数据作为演示
        print("收集10分钟的演示数据...")
        collector.collect_realtime_for_training(duration_hours=10/60)  # 10分钟
        
        print("✓ 演示数据收集完成")
        
    except Exception as e:
        print(f"✗ 数据收集失败: {e}")
        return
    
    # 步骤3: 构建训练数据集
    print("\n步骤3: 构建训练数据集...")
    try:
        # 构建数据集（包含实时数据和K线估算数据）
        df = collector.build_training_dataset(days_back=1)
        
        if df.empty:
            print("✗ 训练数据集为空")
            return
        
        # 保存数据集
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        dataset_filename = f"demo/example_dataset_{timestamp}.csv"
        collector.save_training_dataset(df, dataset_filename)
        
        print(f"✓ 训练数据集已保存: {dataset_filename}")
        print(f"  数据量: {len(df)} 条记录")
        
    except Exception as e:
        print(f"✗ 构建训练数据集失败: {e}")
        return
    
    # 步骤4: 数据预处理
    print("\n步骤4: 数据预处理和特征工程...")
    try:
        from training_data_processor import OrderBookTrainingProcessor
        
        processor = OrderBookTrainingProcessor()
        
        # 特征工程
        print("创建特征...")
        df = processor.create_time_features(df)
        df = processor.create_technical_features(df, window_sizes=[3, 5])  # 使用较小窗口
        df = processor.create_lag_features(df, lag_periods=[1, 2, 3])
        df = processor.create_interaction_features(df)
        df = processor.create_target_variables(df, prediction_horizons=[1, 3])
        df = processor.clean_and_prepare_data(df)
        
        # 保存处理后的数据
        processed_filename = f"demo/example_processed_{timestamp}.csv"
        processor.save_processed_data(df, processed_filename)
        
        print(f"✓ 数据预处理完成: {processed_filename}")
        print(f"  特征数: {len(df.columns)}")
        print(f"  最终数据量: {len(df)} 条记录")
        
    except Exception as e:
        print(f"✗ 数据预处理失败: {e}")
        return
    
    # 步骤5: 模型训练
    print("\n步骤5: 模型训练...")
    try:
        from orderbook_ml_trainer import OrderBookMLTrainer
        
        trainer = OrderBookMLTrainer()
        
        # 选择一个简单的预测任务
        target_column = 'spread_change_1'  # 预测1步价差变化
        
        if target_column not in df.columns:
            print(f"✗ 目标列 {target_column} 不存在")
            return
        
        # 准备训练数据
        X_train, X_test, y_train, y_test = trainer.processor.prepare_training_data(
            df, target_column, test_size=0.3
        )
        
        if X_train.empty:
            print("✗ 训练数据准备失败")
            return
        
        print(f"训练集: {len(X_train)} 样本, {len(X_train.columns)} 特征")
        print(f"测试集: {len(X_test)} 样本")
        
        # 训练一个简单的模型
        print("训练Random Forest模型...")
        performance = trainer.train_regression_model(
            X_train, X_test, y_train, y_test, 'random_forest'
        )
        
        if performance:
            print(f"✓ 模型训练完成")
            print(f"  测试R²: {performance.get('test_r2', 0):.4f}")
            print(f"  测试RMSE: {performance.get('test_rmse', 0):.6f}")
            
            # 保存模型
            trainer.save_models(f"demo/example_models_{timestamp}")
            
            # 生成报告
            report_filename = f"demo/example_report_{timestamp}.txt"
            trainer.generate_training_report({'random_forest': performance}, report_filename)
            print(f"  训练报告: {report_filename}")
        else:
            print("✗ 模型训练失败")
            return
        
    except Exception as e:
        print(f"✗ 模型训练失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 完成
    print("\n" + "="*60)
    print("🎉 完整工作流程演示完成!")
    print("\n生成的文件:")
    print(f"  - 原始数据集: {dataset_filename}")
    print(f"  - 处理后数据: {processed_filename}")
    print(f"  - 训练报告: {report_filename}")
    print(f"  - 模型文件: demo/example_models_{timestamp}/")
    
    print("\n下一步建议:")
    print("1. 收集更多数据（建议24-72小时）以提高模型性能")
    print("2. 尝试不同的预测任务和模型算法")
    print("3. 优化特征工程和模型参数")
    print("4. 在实际交易中验证模型效果")


def run_quick_demo():
    """
    运行快速演示（仅使用K线数据）
    """
    print("快速演示模式（仅使用历史K线数据）")
    print("="*50)
    
    try:
        from historical_orderbook_collector import HistoricalOrderBookCollector
        from training_data_processor import OrderBookTrainingProcessor
        from orderbook_ml_trainer import OrderBookMLTrainer
        
        # 步骤1: 获取K线数据并估算特征
        print("获取历史K线数据...")
        collector = HistoricalOrderBookCollector("ETHUSDT")
        klines = collector.get_historical_klines(interval="5m", limit=500)
        
        if not klines:
            print("✗ 获取K线数据失败")
            return
        
        # 估算特征
        features = []
        for kline in klines:
            feature = collector.estimate_orderbook_from_kline(kline)
            if feature:
                features.append(feature)
        
        df = pd.DataFrame(features)
        print(f"✓ 获取到 {len(df)} 条估算数据")
        
        # 步骤2: 简单的特征工程
        print("特征工程...")
        processor = OrderBookTrainingProcessor()
        df = processor.create_time_features(df)
        df = processor.create_technical_features(df, window_sizes=[5, 10])
        
        # 创建简单的目标变量
        df['spread_future_3'] = df['estimated_spread_pct'].shift(-3)
        df['spread_change_3'] = df['spread_future_3'] - df['estimated_spread_pct']
        
        df = processor.clean_and_prepare_data(df)
        print(f"✓ 特征工程完成，数据量: {len(df)}")
        
        # 步骤3: 快速训练
        print("训练模型...")
        trainer = OrderBookMLTrainer()
        
        X_train, X_test, y_train, y_test = trainer.processor.prepare_training_data(
            df, 'spread_change_3', test_size=0.3
        )
        
        if X_train.empty:
            print("✗ 训练数据准备失败")
            return
        
        performance = trainer.train_regression_model(
            X_train, X_test, y_train, y_test, 'linear'
        )
        
        if performance:
            print(f"✓ 快速演示完成!")
            print(f"  模型: Linear Regression")
            print(f"  测试R²: {performance.get('test_r2', 0):.4f}")
            print(f"  测试RMSE: {performance.get('test_rmse', 0):.6f}")
        else:
            print("✗ 模型训练失败")
        
    except Exception as e:
        print(f"✗ 快速演示失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """
    主函数
    """
    print("订单薄机器学习工作流程")
    print("="*40)
    print("1. 完整工作流程演示（需要收集实时数据）")
    print("2. 快速演示（仅使用历史K线数据）")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == "1":
        run_complete_workflow()
    elif choice == "2":
        run_quick_demo()
    else:
        print("无效选择")


if __name__ == "__main__":
    main()