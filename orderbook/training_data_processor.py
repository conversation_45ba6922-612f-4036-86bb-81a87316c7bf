"""
训练数据处理器
用于预处理订单薄数据，生成机器学习特征
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os
from typing import Dict, List, Optional, Tuple
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
import joblib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OrderBookTrainingProcessor:
    """
    订单薄训练数据处理器
    """
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.target_columns = []
        
    def load_training_data(self, filename: str) -> pd.DataFrame:
        """
        加载训练数据
        """
        try:
            if filename.endswith('.pkl'):
                df = pd.read_pickle(filename)
            else:
                df = pd.read_csv(filename)
                
            logger.info(f"加载训练数据: {len(df)} 条记录")
            return df
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            return pd.DataFrame()
    
    def create_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建时间特征
        """
        try:
            df = df.copy()
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # 基础时间特征
            df['hour'] = df['timestamp'].dt.hour
            df['minute'] = df['timestamp'].dt.minute
            df['day_of_week'] = df['timestamp'].dt.dayofweek
            df['day_of_month'] = df['timestamp'].dt.day
            
            # 市场时间特征
            df['is_trading_hours'] = ((df['hour'] >= 9) & (df['hour'] <= 16)).astype(int)
            df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
            
            # 周期性特征（正弦余弦编码）
            df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
            df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
            df['minute_sin'] = np.sin(2 * np.pi * df['minute'] / 60)
            df['minute_cos'] = np.cos(2 * np.pi * df['minute'] / 60)
            df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
            df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
            
            logger.info("时间特征创建完成")
            return df
            
        except Exception as e:
            logger.error(f"创建时间特征失败: {e}")
            return df
    
    def create_technical_features(self, df: pd.DataFrame, window_sizes: List[int] = [5, 10, 20]) -> pd.DataFrame:
        """
        创建技术指标特征
        """
        try:
            df = df.copy()
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            # 价格相关特征
            if 'spread_pct' in df.columns:
                for window in window_sizes:
                    df[f'spread_ma_{window}'] = df['spread_pct'].rolling(window=window).mean()
                    df[f'spread_std_{window}'] = df['spread_pct'].rolling(window=window).std()
                    df[f'spread_min_{window}'] = df['spread_pct'].rolling(window=window).min()
                    df[f'spread_max_{window}'] = df['spread_pct'].rolling(window=window).max()
            
            # 流动性不平衡特征
            if 'liquidity_imbalance' in df.columns:
                for window in window_sizes:
                    df[f'imbalance_ma_{window}'] = df['liquidity_imbalance'].rolling(window=window).mean()
                    df[f'imbalance_std_{window}'] = df['liquidity_imbalance'].rolling(window=window).std()
                    df[f'imbalance_trend_{window}'] = df['liquidity_imbalance'].rolling(window=window).apply(
                        lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == window else np.nan
                    )
            
            # 成交量特征
            if 'total_volume' in df.columns:
                for window in window_sizes:
                    df[f'volume_ma_{window}'] = df['total_volume'].rolling(window=window).mean()
                    df[f'volume_std_{window}'] = df['total_volume'].rolling(window=window).std()
                    df[f'volume_ratio_{window}'] = df['total_volume'] / df[f'volume_ma_{window}']
            
            # 价格影响特征
            if 'price_impact' in df.columns:
                for window in window_sizes:
                    df[f'impact_ma_{window}'] = df['price_impact'].rolling(window=window).mean()
                    df[f'impact_volatility_{window}'] = df['price_impact'].rolling(window=window).std()
            
            # 深度斜率特征
            for slope_col in ['bid_slope', 'ask_slope']:
                if slope_col in df.columns:
                    for window in window_sizes:
                        df[f'{slope_col}_ma_{window}'] = df[slope_col].rolling(window=window).mean()
                        df[f'{slope_col}_std_{window}'] = df[slope_col].rolling(window=window).std()
            
            logger.info("技术指标特征创建完成")
            return df
            
        except Exception as e:
            logger.error(f"创建技术指标特征失败: {e}")
            return df
    
    def create_lag_features(self, df: pd.DataFrame, lag_periods: List[int] = [1, 2, 3, 5, 10]) -> pd.DataFrame:
        """
        创建滞后特征
        """
        try:
            df = df.copy()
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            # 主要指标的滞后特征
            main_features = ['spread_pct', 'liquidity_imbalance', 'total_volume', 'price_impact']
            
            for feature in main_features:
                if feature in df.columns:
                    for lag in lag_periods:
                        df[f'{feature}_lag_{lag}'] = df[feature].shift(lag)
            
            # 变化率特征
            for feature in main_features:
                if feature in df.columns:
                    df[f'{feature}_change_1'] = df[feature].diff(1)
                    df[f'{feature}_change_5'] = df[feature].diff(5)
                    df[f'{feature}_pct_change_1'] = df[feature].pct_change(1)
                    df[f'{feature}_pct_change_5'] = df[feature].pct_change(5)
            
            logger.info("滞后特征创建完成")
            return df
            
        except Exception as e:
            logger.error(f"创建滞后特征失败: {e}")
            return df
    
    def create_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建交互特征
        """
        try:
            df = df.copy()
            
            # 价差与流动性不平衡的交互
            if 'spread_pct' in df.columns and 'liquidity_imbalance' in df.columns:
                df['spread_imbalance_interaction'] = df['spread_pct'] * df['liquidity_imbalance']
                df['spread_imbalance_ratio'] = df['spread_pct'] / (abs(df['liquidity_imbalance']) + 1e-8)
            
            # 成交量与价格影响的交互
            if 'total_volume' in df.columns and 'price_impact' in df.columns:
                df['volume_impact_interaction'] = df['total_volume'] * df['price_impact']
                df['impact_per_volume'] = df['price_impact'] / (df['total_volume'] + 1e-8)
            
            # 深度斜率的交互
            if 'bid_slope' in df.columns and 'ask_slope' in df.columns:
                df['slope_difference'] = df['bid_slope'] - df['ask_slope']
                df['slope_ratio'] = df['bid_slope'] / (df['ask_slope'] + 1e-8)
                df['slope_asymmetry'] = abs(df['bid_slope']) - abs(df['ask_slope'])
            
            # 时间与市场特征的交互
            if 'hour' in df.columns and 'spread_pct' in df.columns:
                df['hour_spread_interaction'] = df['hour'] * df['spread_pct']
            
            logger.info("交互特征创建完成")
            return df
            
        except Exception as e:
            logger.error(f"创建交互特征失败: {e}")
            return df
    
    def create_target_variables(self, df: pd.DataFrame, prediction_horizons: List[int] = [1, 5, 10]) -> pd.DataFrame:
        """
        创建目标变量（用于预测）
        """
        try:
            df = df.copy()
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            # 价差预测目标
            if 'spread_pct' in df.columns:
                for horizon in prediction_horizons:
                    df[f'spread_future_{horizon}'] = df['spread_pct'].shift(-horizon)
                    df[f'spread_change_{horizon}'] = df[f'spread_future_{horizon}'] - df['spread_pct']
                    df[f'spread_direction_{horizon}'] = (df[f'spread_change_{horizon}'] > 0).astype(int)
            
            # 流动性不平衡预测目标
            if 'liquidity_imbalance' in df.columns:
                for horizon in prediction_horizons:
                    df[f'imbalance_future_{horizon}'] = df['liquidity_imbalance'].shift(-horizon)
                    df[f'imbalance_change_{horizon}'] = df[f'imbalance_future_{horizon}'] - df['liquidity_imbalance']
                    df[f'imbalance_direction_{horizon}'] = (df[f'imbalance_change_{horizon}'] > 0).astype(int)
            
            # 价格影响预测目标
            if 'price_impact' in df.columns:
                for horizon in prediction_horizons:
                    df[f'impact_future_{horizon}'] = df['price_impact'].shift(-horizon)
                    df[f'impact_change_{horizon}'] = df[f'impact_future_{horizon}'] - df['price_impact']
            
            # 市场状态分类目标
            if 'spread_pct' in df.columns and 'liquidity_imbalance' in df.columns:
                # 定义市场状态：0=正常, 1=高价差, 2=不平衡, 3=高价差+不平衡
                spread_threshold = df['spread_pct'].quantile(0.75)
                imbalance_threshold = df['liquidity_imbalance'].abs().quantile(0.75)
                
                conditions = [
                    (df['spread_pct'] <= spread_threshold) & (df['liquidity_imbalance'].abs() <= imbalance_threshold),
                    (df['spread_pct'] > spread_threshold) & (df['liquidity_imbalance'].abs() <= imbalance_threshold),
                    (df['spread_pct'] <= spread_threshold) & (df['liquidity_imbalance'].abs() > imbalance_threshold),
                    (df['spread_pct'] > spread_threshold) & (df['liquidity_imbalance'].abs() > imbalance_threshold)
                ]
                choices = [0, 1, 2, 3]
                df['market_state'] = np.select(conditions, choices, default=0)
                
                # 未来市场状态
                for horizon in prediction_horizons:
                    df[f'market_state_future_{horizon}'] = df['market_state'].shift(-horizon)
            
            logger.info("目标变量创建完成")
            return df
            
        except Exception as e:
            logger.error(f"创建目标变量失败: {e}")
            return df
    
    def clean_and_prepare_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理和准备数据
        """
        try:
            df = df.copy()
            
            # 移除无限值和异常值
            df = df.replace([np.inf, -np.inf], np.nan)
            
            # 处理异常值（使用IQR方法）
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            
            for col in numeric_columns:
                if col in df.columns:
                    Q1 = df[col].quantile(0.25)
                    Q3 = df[col].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 3 * IQR
                    upper_bound = Q3 + 3 * IQR
                    
                    # 将异常值设为NaN
                    df.loc[(df[col] < lower_bound) | (df[col] > upper_bound), col] = np.nan
            
            # 填充缺失值
            # 对于数值列，使用前向填充然后后向填充
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = df[col].fillna(method='ffill').fillna(method='bfill')
            
            # 移除仍有缺失值的行
            initial_rows = len(df)
            df = df.dropna()
            final_rows = len(df)
            
            logger.info(f"数据清理完成: {initial_rows} -> {final_rows} 行")
            return df
            
        except Exception as e:
            logger.error(f"数据清理失败: {e}")
            return df
    
    def prepare_training_data(self, df: pd.DataFrame, target_column: str, test_size: float = 0.2) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series]:
        """
        准备训练数据
        """
        try:
            # 选择特征列（排除目标变量和元数据列）
            exclude_columns = ['timestamp', 'data_source'] + [col for col in df.columns if 'future' in col or 'target' in col]
            feature_columns = [col for col in df.columns if col not in exclude_columns and col != target_column]
            
            X = df[feature_columns]
            y = df[target_column]
            
            # 移除包含目标变量缺失值的行
            valid_indices = ~y.isna()
            X = X[valid_indices]
            y = y[valid_indices]
            
            # 分割训练集和测试集
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=42, shuffle=False  # 时间序列不打乱
            )
            
            # 标准化特征
            X_train_scaled = pd.DataFrame(
                self.scaler.fit_transform(X_train),
                columns=X_train.columns,
                index=X_train.index
            )
            
            X_test_scaled = pd.DataFrame(
                self.scaler.transform(X_test),
                columns=X_test.columns,
                index=X_test.index
            )
            
            self.feature_columns = feature_columns
            
            logger.info(f"训练数据准备完成: 特征数={len(feature_columns)}, 训练集={len(X_train)}, 测试集={len(X_test)}")
            
            return X_train_scaled, X_test_scaled, y_train, y_test
            
        except Exception as e:
            logger.error(f"准备训练数据失败: {e}")
            return pd.DataFrame(), pd.DataFrame(), pd.Series(), pd.Series()
    
    def save_processed_data(self, df: pd.DataFrame, filename: str):
        """
        保存处理后的数据
        """
        try:
            # 保存数据
            df.to_csv(filename, index=False)
            df.to_pickle(filename.replace('.csv', '.pkl'))
            
            # 保存标准化器
            scaler_filename = filename.replace('.csv', '_scaler.joblib')
            joblib.dump(self.scaler, scaler_filename)
            
            # 保存特征列表
            features_filename = filename.replace('.csv', '_features.txt')
            with open(features_filename, 'w') as f:
                f.write('\n'.join(self.feature_columns))
            
            logger.info(f"处理后数据已保存: {filename}")
            
        except Exception as e:
            logger.error(f"保存处理后数据失败: {e}")
    
    def generate_feature_report(self, df: pd.DataFrame, filename: str):
        """
        生成特征报告
        """
        try:
            report = []
            report.append("特征工程报告")
            report.append("="*50)
            report.append(f"生成时间: {datetime.now().isoformat()}")
            report.append(f"数据集大小: {len(df)} 行 x {len(df.columns)} 列")
            report.append("")
            
            # 特征类型统计
            feature_types = {
                'time_features': [col for col in df.columns if any(x in col for x in ['hour', 'minute', 'day', 'sin', 'cos'])],
                'technical_features': [col for col in df.columns if any(x in col for x in ['ma_', 'std_', 'min_', 'max_'])],
                'lag_features': [col for col in df.columns if 'lag_' in col],
                'change_features': [col for col in df.columns if 'change_' in col],
                'interaction_features': [col for col in df.columns if any(x in col for x in ['interaction', 'ratio', 'difference'])],
                'target_features': [col for col in df.columns if 'future_' in col or 'target_' in col]
            }
            
            report.append("特征类型分布:")
            for feature_type, features in feature_types.items():
                report.append(f"  {feature_type}: {len(features)} 个特征")
            report.append("")
            
            # 数据质量统计
            report.append("数据质量:")
            report.append(f"  缺失值总数: {df.isnull().sum().sum()}")
            report.append(f"  完整行数: {len(df.dropna())}")
            report.append(f"  数据完整率: {len(df.dropna())/len(df)*100:.2f}%")
            report.append("")
            
            # 主要特征统计
            main_features = ['spread_pct', 'liquidity_imbalance', 'total_volume', 'price_impact']
            report.append("主要特征统计:")
            for feature in main_features:
                if feature in df.columns:
                    data = df[feature].dropna()
                    if len(data) > 0:
                        report.append(f"  {feature}:")
                        report.append(f"    均值: {data.mean():.6f}")
                        report.append(f"    标准差: {data.std():.6f}")
                        report.append(f"    范围: [{data.min():.6f}, {data.max():.6f}]")
            
            # 保存报告
            with open(filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report))
            
            logger.info(f"特征报告已保存: {filename}")
            
        except Exception as e:
            logger.error(f"生成特征报告失败: {e}")


def main():
    """
    主函数
    """
    print("订单薄训练数据处理器")
    print("="*50)
    
    processor = OrderBookTrainingProcessor()
    
    # 获取输入文件
    input_file = input("请输入训练数据文件路径: ").strip()
    
    if not os.path.exists(input_file):
        print("文件不存在!")
        return
    
    try:
        # 加载数据
        print("加载数据...")
        df = processor.load_training_data(input_file)
        
        if df.empty:
            print("数据加载失败!")
            return
        
        print(f"原始数据: {len(df)} 行 x {len(df.columns)} 列")
        
        # 特征工程
        print("创建时间特征...")
        df = processor.create_time_features(df)
        
        print("创建技术指标特征...")
        df = processor.create_technical_features(df)
        
        print("创建滞后特征...")
        df = processor.create_lag_features(df)
        
        print("创建交互特征...")
        df = processor.create_interaction_features(df)
        
        print("创建目标变量...")
        df = processor.create_target_variables(df)
        
        print("清理数据...")
        df = processor.clean_and_prepare_data(df)
        
        print(f"处理后数据: {len(df)} 行 x {len(df.columns)} 列")
        
        # 保存处理后的数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"demo/processed_training_data_{timestamp}.csv"
        
        processor.save_processed_data(df, output_file)
        
        # 生成报告
        report_file = f"demo/feature_engineering_report_{timestamp}.txt"
        processor.generate_feature_report(df, report_file)
        
        print(f"\n处理完成!")
        print(f"输出文件: {output_file}")
        print(f"特征报告: {report_file}")
        
        # 显示一些统计信息
        print(f"\n数据统计:")
        print(f"  最终数据量: {len(df)} 条")
        print(f"  特征数量: {len(df.columns)}")
        print(f"  时间跨度: {df['timestamp'].min()} 到 {df['timestamp'].max()}")
        
    except Exception as e:
        logger.error(f"处理过程出错: {e}")


if __name__ == "__main__":
    main()