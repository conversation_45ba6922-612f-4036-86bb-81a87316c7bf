# 收益优化训练示例

## 概述

修改后的 `trainopt2.py` 现在支持直接优化收益而非AUC，这更符合实际交易的目标。

## 主要改进

1. **收益优化目标**: 使用与 `backtest_money_quick.py` 相同的得分计算逻辑
2. **阈值联合优化**: 在超参数搜索中同时优化预测阈值
3. **灵活的优化目标**: 可以选择使用收益优化或传统的AUC优化

## 使用方法

### 1. 收益优化训练（推荐）

```bash
python trainopt2.py \
    --coin ETH \
    --optimize-profit \
    --optimize-features \
    --cv-trials 100 \
    --cv-splits 5
```

### 2. 传统AUC优化

```bash
python trainopt2.py \
    --coin ETH \
    --use-auc \
    --optimize-features \
    --cv-trials 100 \
    --cv-splits 5
```

### 3. 快速测试（不使用交叉验证）

```bash
python trainopt2.py \
    --coin ETH \
    --optimize-profit \
    --no-time-series-cv
```

## 新增参数

- `--optimize-profit`: 启用收益优化（推荐）
- `--use-auc`: 强制使用AUC优化，覆盖收益优化设置

## 优化逻辑

### 收益计算

模拟 `backtest_money_quick.py` 中的得分计算：
- 预测正确: +1分
- 预测错误: -1分
- 不确定预测: 跳过

### 阈值优化

在收益优化模式下，同时优化：
1. LightGBM模型参数
2. 预测阈值（0.52-0.80范围）

这确保了模型和阈值的最佳组合。

## 预期效果

1. **更好的实际收益**: 直接优化交易收益而非统计指标
2. **更合适的阈值**: 自动找到最优的预测阈值
3. **更稳定的表现**: 在实际回测中表现更一致

## 验证方法

训练完成后，使用相同数据进行回测验证：

```bash
python backtest_money_quick.py \
    --coin ETH \
    --quick \
    --start-time "2024-01-01" \
    --end-time "2024-12-31"
```

比较收益优化和AUC优化的模型在实际回测中的表现差异。

## 注意事项

1. 收益优化可能需要更多的计算时间
2. 建议先用较少的试验次数测试
3. 确保有足够的历史数据进行交叉验证
4. 定期验证模型在新数据上的表现