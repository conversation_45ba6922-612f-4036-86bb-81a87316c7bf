#!/usr/bin/env python3
# 测试挂单买入功能的修复

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pytz

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """创建测试用的K线数据"""
    # 创建一些测试数据
    start_time = datetime(2024, 1, 1, tzinfo=pytz.UTC)
    timestamps = [start_time + timedelta(minutes=15*i) for i in range(100)]
    
    # 生成价格数据
    base_price = 2000.0
    prices = []
    for i in range(100):
        # 简单的随机游走
        change = np.random.normal(0, 0.01)  # 1% 标准差
        if i == 0:
            price = base_price
        else:
            price = prices[-1] * (1 + change)
        prices.append(price)
    
    # 创建OHLCV数据
    data = []
    for i, (ts, close) in enumerate(zip(timestamps, prices)):
        high = close * (1 + abs(np.random.normal(0, 0.005)))
        low = close * (1 - abs(np.random.normal(0, 0.005)))
        open_price = close * (1 + np.random.normal(0, 0.002))
        volume = np.random.uniform(1000, 10000)
        
        data.append({
            'timestamp': ts,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    return df

def test_prediction_dict_structure():
    """测试预测字典结构"""
    print("=== 测试预测字典结构 ===")
    
    # 模拟挂单预测字典
    pending_prediction = {
        'id': 'test_001',
        'guess': 1,
        'probability': 0.75,
        'signal_price': 2000.0,
        'order_price': 1998.0,  # 0.1% 更好价格
        'start_timestamp': datetime.now(pytz.UTC),
        'status': 'pending',
        'filled_price': None,
        'filled_timestamp': None
    }
    
    # 模拟成交后的预测字典
    filled_prediction = {
        'id': 'test_002',
        'guess': 1,
        'probability': 0.75,
        'signal_price': 2000.0,
        'order_price': 1998.0,
        'start_price': 1998.0,  # 成交后设置
        'start_timestamp': datetime.now(pytz.UTC),
        'status': 'active',
        'filled_price': 1998.0,
        'filled_timestamp': datetime.now(pytz.UTC)
    }
    
    # 测试获取起始价格的逻辑
    def get_start_price(pred):
        return pred.get('start_price', pred.get('order_price', 0))
    
    pending_start = get_start_price(pending_prediction)
    filled_start = get_start_price(filled_prediction)
    
    print(f"挂单状态起始价格: {pending_start}")
    print(f"成交状态起始价格: {filled_start}")
    
    assert pending_start == 1998.0, f"挂单状态起始价格错误: {pending_start}"
    assert filled_start == 1998.0, f"成交状态起始价格错误: {filled_start}"
    
    print("✅ 预测字典结构测试通过")

def test_price_calculation():
    """测试价格计算逻辑"""
    print("\n=== 测试价格计算逻辑 ===")
    
    signal_price = 2000.0
    better_price_pct = 0.1  # 0.1%
    
    # 做多挂单价格
    long_order_price = signal_price * (1 - better_price_pct / 100)
    print(f"做多信号价格: {signal_price}")
    print(f"做多挂单价格: {long_order_price}")
    print(f"价格改善: {signal_price - long_order_price:.4f} ({((signal_price - long_order_price) / signal_price * 100):.3f}%)")
    
    # 做空挂单价格
    short_order_price = signal_price * (1 + better_price_pct / 100)
    print(f"\n做空信号价格: {signal_price}")
    print(f"做空挂单价格: {short_order_price}")
    print(f"价格改善: {short_order_price - signal_price:.4f} ({((short_order_price - signal_price) / signal_price * 100):.3f}%)")
    
    assert abs(long_order_price - 1998.0) < 0.01, f"做多挂单价格计算错误: {long_order_price}"
    assert abs(short_order_price - 2002.0) < 0.01, f"做空挂单价格计算错误: {short_order_price}"
    
    print("\n✅ 价格计算逻辑测试通过")

def test_fill_logic():
    """测试成交逻辑"""
    print("\n=== 测试成交逻辑 ===")
    
    order_price = 1998.0
    
    test_cases = [
        {"high": 2005.0, "low": 2000.0, "close": 2002.0, "should_fill": False, "desc": "价格未触及挂单价"},
        {"high": 2005.0, "low": 1997.0, "close": 2002.0, "should_fill": True, "desc": "最低价触及挂单价"},
        {"high": 1998.0, "low": 1995.0, "close": 1997.0, "should_fill": True, "desc": "最高价等于挂单价"},
    ]
    
    for i, case in enumerate(test_cases):
        # 做多挂单成交条件：最低价 <= 挂单价
        filled = case['low'] <= order_price
        
        print(f"测试案例 {i+1}: {case['desc']}")
        print(f"  K线: 高={case['high']}, 低={case['low']}, 收={case['close']}")
        print(f"  挂单价: {order_price}")
        print(f"  是否成交: {filled} (预期: {case['should_fill']})")
        
        assert filled == case['should_fill'], f"成交逻辑错误: 案例{i+1}"
        print(f"  结果: ✅ 正确")
    
    print("\n✅ 成交逻辑测试通过")

if __name__ == "__main__":
    print("=== 挂单买入功能修复测试 ===")
    
    try:
        test_prediction_dict_structure()
        test_price_calculation()
        test_fill_logic()
        
        print("\n🎉 所有测试通过！挂单买入功能修复成功！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
