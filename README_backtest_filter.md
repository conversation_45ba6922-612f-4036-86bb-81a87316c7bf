# 回测结果过滤分析工具

## 概述

`backtest_after_filter.py` 是一个强大的后处理工具，用于分析和过滤 `backtest_money_quick.py` 生成的详细回测结果。通过应用各种过滤策略，可以显著提高交易策略的性能，而无需重新运行耗时的回测计算。

## 主要特性

### 🔍 多种过滤策略
- **置信度过滤**: 基于模型预测置信度筛选信号
- **双均线过滤**: 使用移动平均线趋势过滤
- **成交量过滤**: 基于成交量活跃度筛选
- **时间段过滤**: 限制在特定时间段交易
- **连续亏损过滤**: 控制风险暴露

### 📊 完整的性能分析
- 重新计算资金序列和收益指标
- 详细的过滤前后对比报告
- 自动生成可视化图表
- 保存过滤配置以便复现

### ⚡ 高效的处理速度
- 直接处理CSV结果，无需重新计算模型
- 支持批量过滤策略测试
- 灵活的配置系统

## 安装和依赖

确保已安装以下依赖：
```bash
pip install pandas numpy sqlite3
```

## 基本使用方法

### 1. 快速开始

```bash
# 使用默认配置过滤回测结果
python backtest_after_filter.py --csv backtest_money_log_quick.csv
```

### 2. 命令行参数过滤

```bash
# 高置信度 + 强成交量过滤
python backtest_after_filter.py \
    --csv backtest_money_log_quick.csv \
    --min-confidence 0.7 \
    --min-volume-ratio 2.0 \
    --max-consecutive-losses 2 \
    --output strict_filter
```

### 3. 使用配置文件

```bash
# 使用自定义配置文件
python backtest_after_filter.py \
    --csv backtest_money_log_quick.csv \
    --config my_filter_config.json \
    --output custom_filter
```

## 配置文件格式

创建 `filter_config.json` 文件：

```json
{
  "coin": "ETH",
  "interval": "15m",
  "market": "spot",
  "initial_capital": 1000,
  "confidence_filter": {
    "enabled": true,
    "min_confidence": 0.65,
    "max_confidence": 1.0
  },
  "ma_filter": {
    "enabled": true,
    "short_period": 5,
    "long_period": 20,
    "same_direction_only": true
  },
  "volume_filter": {
    "enabled": true,
    "volume_period": 20,
    "min_volume_ratio": 1.5
  },
  "time_filter": {
    "enabled": false,
    "allowed_hours": [9, 10, 11, 14, 15, 16, 20, 21, 22],
    "allowed_days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
  },
  "consecutive_filter": {
    "enabled": true,
    "max_consecutive_losses": 3
  }
}
```

## 过滤策略详解

### 1. 置信度过滤 (Confidence Filter)

过滤模型预测置信度不在指定范围内的信号。

**参数:**
- `min_confidence`: 最小置信度阈值 (0.0-1.0)
- `max_confidence`: 最大置信度阈值 (0.0-1.0)

**使用场景:**
- 过滤掉置信度过低的不确定信号
- 排除置信度异常高的可能过拟合信号

### 2. 双均线过滤 (Moving Average Filter)

使用短期和长期移动平均线判断趋势方向，只在趋势一致时交易。

**参数:**
- `short_period`: 短期均线周期
- `long_period`: 长期均线周期
- `same_direction_only`: 是否只允许与均线趋势一致的信号

**使用场景:**
- 在明确的趋势中交易，避免震荡市场
- 提高信号的趋势一致性

### 3. 成交量过滤 (Volume Filter)

基于成交量活跃度筛选交易信号，避免在流动性不足时交易。

**参数:**
- `volume_period`: 成交量均线计算周期
- `min_volume_ratio`: 最小成交量比率 (当前成交量/平均成交量)

**使用场景:**
- 确保在流动性充足时交易
- 避免在成交量异常低迷时开仓

### 4. 时间段过滤 (Time Filter)

限制在特定时间段进行交易，基于历史统计的高胜率时段。

**参数:**
- `allowed_hours`: 允许的小时列表 [0-23]
- `allowed_days`: 允许的星期列表

**使用场景:**
- 基于 `chushou.json` 的高胜率时段分析
- 避免在市场活跃度低的时段交易

### 5. 连续亏损过滤 (Consecutive Loss Filter)

在连续亏损达到上限后暂停交易，控制风险暴露。

**参数:**
- `max_consecutive_losses`: 最大连续亏损次数

**使用场景:**
- 防止在不利市场条件下持续亏损
- 实现动态风险管理

## 输出文件说明

运行过滤分析后，会生成以下文件：

1. **`{output}_filtered.csv`**: 过滤后的详细交易记录
2. **`{output}_metrics_comparison.csv`**: 过滤前后性能指标对比
3. **`{output}_config.json`**: 使用的过滤配置
4. **`{output}_analysis.png`**: 可视化分析图表

## 性能指标说明

### 基础指标
- **总收益率**: 整体投资回报率
- **胜率**: 盈利交易占总交易的比例
- **交易数量**: 过滤后剩余的交易次数

### 风险指标
- **最大回撤**: 资金曲线的最大下跌幅度
- **夏普比率**: 风险调整后的收益率
- **盈亏比**: 平均盈利与平均亏损的比率

## 实际使用示例

### 示例1: 保守策略
```bash
python backtest_after_filter.py \
    --csv backtest_money_log_quick.csv \
    --min-confidence 0.75 \
    --min-volume-ratio 2.0 \
    --max-consecutive-losses 2 \
    --output conservative_strategy
```

### 示例2: 趋势跟随策略
```json
{
  "ma_filter": {
    "enabled": true,
    "short_period": 3,
    "long_period": 10,
    "same_direction_only": true
  },
  "volume_filter": {
    "enabled": true,
    "min_volume_ratio": 1.8
  }
}
```

### 示例3: 时间优化策略
```json
{
  "time_filter": {
    "enabled": true,
    "allowed_hours": [10, 11, 14, 15, 16, 20, 21],
    "allowed_days": ["Monday", "Tuesday", "Wednesday", "Thursday"]
  },
  "confidence_filter": {
    "enabled": true,
    "min_confidence": 0.68
  }
}
```

## 最佳实践

### 1. 渐进式过滤
- 先应用单一过滤器观察效果
- 逐步组合多个过滤器
- 避免过度过滤导致交易机会过少

### 2. 参数优化
- 基于历史数据测试不同参数组合
- 关注过滤后的交易频率和收益稳定性
- 平衡收益提升与交易机会减少

### 3. 定期评估
- 定期重新评估过滤策略的有效性
- 根据市场环境调整过滤参数
- 保持策略的适应性

## 故障排除

### 常见问题

1. **"无法加载价格数据"**
   - 检查数据库文件路径是否正确
   - 确认币种和时间间隔参数匹配

2. **"过滤后无交易记录"**
   - 过滤条件可能过于严格
   - 尝试放宽过滤参数

3. **"性能指标异常"**
   - 检查初始资金设置是否正确
   - 确认CSV文件格式完整

### 调试模式
```bash
# 查看详细的过滤过程
python backtest_after_filter.py --csv your_file.csv --verbose
```

## 扩展功能

### 自定义过滤器
可以在 `BacktestFilter` 类中添加自定义过滤逻辑：

```python
def apply_custom_filter(self, custom_params):
    """自定义过滤逻辑"""
    # 实现你的过滤策略
    pass
```

### 批量测试
```python
# 批量测试不同参数组合
configs = [
    {"min_confidence": 0.6, "min_volume_ratio": 1.2},
    {"min_confidence": 0.7, "min_volume_ratio": 1.5},
    {"min_confidence": 0.8, "min_volume_ratio": 2.0},
]

for i, config in enumerate(configs):
    # 运行过滤分析
    pass
```

## 总结

回测结果过滤分析工具提供了一个高效、灵活的方式来优化交易策略性能。通过合理使用各种过滤策略，可以显著提高策略的风险调整收益，同时保持足够的交易频率。

记住，过滤的目标是提高信号质量而不是简单地提高胜率。最佳的过滤策略应该在收益提升、风险控制和交易频率之间找到平衡点。