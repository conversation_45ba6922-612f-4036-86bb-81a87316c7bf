#!/usr/bin/env python3
# 反向平仓功能使用示例

"""
反向平仓功能使用示例

这个脚本展示了如何使用新的反向平仓功能进行回测。
反向平仓功能可以在模型预测出现反向信号时自动平仓，
有助于风险控制和资金管理。
"""

import subprocess
import sys

def run_backtest_example(description, cmd_args):
    """运行回测示例"""
    print(f"\n=== {description} ===")
    print(f"命令: python backtest_money_quick.py {' '.join(cmd_args)}")
    
    try:
        result = subprocess.run(
            ["python", "backtest_money_quick.py"] + cmd_args,
            capture_output=True, text=True, timeout=120
        )
        
        if result.returncode == 0:
            print("✅ 回测成功完成")
            
            # 提取关键信息
            lines = result.stdout.split('\n')
            for line in lines:
                if any(keyword in line for keyword in [
                    '反向平仓: 已启用', '总预测数:', '最终资金:', '总收益率:', '胜率:'
                ]):
                    print(f"   {line.strip()}")
        else:
            print(f"❌ 回测失败: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("❌ 回测超时")
    except Exception as e:
        print(f"❌ 回测异常: {e}")

def main():
    """主函数"""
    print("反向平仓功能使用示例")
    print("=" * 50)
    
    # 基础参数
    base_args = [
        "--coin", "ETH",
        "--interval", "15m",
        "--quick",
        "--start-time", "2025-05-20",
        "--end-time", "2025-05-25",
        "--initial-capital", "1000",
        "--risk-per-trade", "1.0",
        "--max-active-predictions", "5"
    ]
    
    # 示例1：不启用反向平仓（基准测试）
    run_backtest_example(
        "基准测试（不启用反向平仓）",
        base_args
    )
    
    # 示例2：启用反向平仓，最小1笔触发
    run_backtest_example(
        "反向平仓（最小1笔同向单触发）",
        base_args + ["--enable-reverse-close", "--reverse-close-min-positions", "1"]
    )
    
    # 示例3：启用反向平仓，最小3笔触发
    run_backtest_example(
        "反向平仓（最小3笔同向单触发）",
        base_args + ["--enable-reverse-close", "--reverse-close-min-positions", "3"]
    )
    
    # 示例4：启用反向平仓，最小10笔触发（保守模式）
    run_backtest_example(
        "反向平仓（最小10笔同向单触发 - 保守模式）",
        base_args + ["--enable-reverse-close", "--reverse-close-min-positions", "10"]
    )
    
    print("\n" + "=" * 50)
    print("📋 反向平仓功能说明:")
    print("   1. --enable-reverse-close: 启用反向平仓功能")
    print("   2. --reverse-close-min-positions N: 设置最小同向仓位数阈值")
    print("   3. 当同向仓位数 ≥ N 且出现反向信号时，自动平仓")
    print("   4. 数值越大越保守，数值越小越激进")
    print("   5. 适用于风险控制，避免过度集中持仓")
    
    print("\n💡 使用建议:")
    print("   - 新手建议使用较大的阈值（如10）")
    print("   - 有经验的交易者可以使用较小的阈值（如1-3）")
    print("   - 可以结合止损功能一起使用")
    print("   - 建议先在小资金上测试效果")

if __name__ == "__main__":
    main()
