#!/usr/bin/env python3
# 测试 model_utils_905.py 中的特征计算功能

import sys
import os
sys.path.append('trade')

import pandas as pd
import numpy as np
from model_utils_905 import calculate_features, get_feature_list

def create_test_data(n_periods=100, timeframe=15):
    """创建测试数据"""
    dates = pd.date_range('2024-01-01', periods=n_periods, freq=f'{timeframe}min')
    
    # 创建模拟的OHLCV数据
    np.random.seed(42)  # 固定随机种子以便重现
    base_price = 2500
    
    data = {
        'open': [],
        'high': [],
        'low': [],
        'close': [],
        'volume': []
    }
    
    current_price = base_price
    for i in range(n_periods):
        # 模拟价格变动
        change = np.random.normal(0, 0.01)  # 1% 标准差
        new_price = current_price * (1 + change)
        
        # 生成OHLC
        open_price = current_price
        close_price = new_price
        
        # 确保high >= max(open, close), low <= min(open, close)
        high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, 0.005)))
        low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, 0.005)))
        
        data['open'].append(open_price)
        data['high'].append(high_price)
        data['low'].append(low_price)
        data['close'].append(close_price)
        data['volume'].append(np.random.uniform(1000, 5000))
        
        current_price = new_price
    
    df = pd.DataFrame(data, index=dates)
    return df

def test_feature_calculation():
    """测试特征计算功能"""
    print("=== 测试特征计算功能 ===")
    
    # 创建测试数据
    print("1. 创建测试数据...")
    test_data = create_test_data(n_periods=200, timeframe=15)
    print(f"   测试数据形状: {test_data.shape}")
    print(f"   时间范围: {test_data.index[0]} 到 {test_data.index[-1]}")
    
    # 计算特征
    print("\n2. 计算特征...")
    try:
        result = calculate_features(test_data, timeframe=15)
        print(f"   特征计算完成，结果形状: {result.shape}")
    except Exception as e:
        print(f"   特征计算失败: {e}")
        return False
    
    # 检查新的RSI特征
    print("\n3. 检查RSI特征...")
    rsi_features = [col for col in result.columns if 'rsi_' in col]
    print(f"   RSI特征 ({len(rsi_features)}个): {rsi_features}")
    
    # 检查上下影线特征
    print("\n4. 检查上下影线特征...")
    shadow_features = [col for col in result.columns if 'shadow' in col]
    print(f"   影线特征 ({len(shadow_features)}个): {shadow_features}")
    
    # 检查新的时间窗口特征
    print("\n5. 检查收益率特征...")
    return_features = [col for col in result.columns if 'return_' in col]
    print(f"   收益率特征 ({len(return_features)}个): {return_features}")
    
    # 检查波动率特征
    print("\n6. 检查波动率特征...")
    volatility_features = [col for col in result.columns if 'volatility_ratio_' in col]
    print(f"   波动率特征 ({len(volatility_features)}个): {volatility_features}")
    
    # 测试特征列表函数
    print("\n7. 测试特征列表函数...")
    try:
        feature_list = get_feature_list(result, time_frame=15)
        print(f"   特征列表长度: {len(feature_list)}")
        print(f"   前10个特征: {feature_list[:10]}")
    except Exception as e:
        print(f"   特征列表获取失败: {e}")
        return False
    
    # 检查数据质量
    print("\n8. 检查数据质量...")
    nan_counts = result.isnull().sum()
    features_with_nan = nan_counts[nan_counts > 0]
    if len(features_with_nan) > 0:
        print(f"   包含NaN的特征: {len(features_with_nan)}个")
        print(f"   前5个: {features_with_nan.head()}")
    else:
        print("   所有特征都没有NaN值")
    
    # 显示一些统计信息
    print("\n9. 特征统计信息...")
    print(f"   总特征数: {len(result.columns)}")
    print(f"   数值特征数: {len([col for col in result.columns if result[col].dtype in ['float64', 'int64']])}")
    
    # 显示RSI值范围
    for rsi_col in rsi_features:
        if rsi_col in result.columns:
            rsi_values = result[rsi_col].dropna()
            if len(rsi_values) > 0:
                print(f"   {rsi_col}: 范围 [{rsi_values.min():.2f}, {rsi_values.max():.2f}], 均值 {rsi_values.mean():.2f}")
    
    print("\n=== 测试完成 ===")
    return True

if __name__ == "__main__":
    success = test_feature_calculation()
    if success:
        print("✅ 所有测试通过！")
    else:
        print("❌ 测试失败！")
