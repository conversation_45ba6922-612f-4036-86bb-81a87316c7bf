#!/usr/bin/env python3
"""
Darts 详细回测系统快速开始脚本
自动检查环境、训练模型（如需要）、运行回测
"""

import os
import sys
import subprocess
import argparse
from datetime import datetime, timedelta

def print_banner():
    """打印欢迎横幅"""
    print("=" * 70)
    print("🚀 Darts 详细回测系统 - 快速开始")
    print("=" * 70)
    print("基于 backtest_money_quick.py 的完整功能")
    print("专门适配 darts_classification_trainer_fixed.py 模型")
    print("=" * 70)

def check_environment():
    """检查环境和依赖"""
    print("\n🔍 检查环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查必要的包
    required_packages = [
        'pandas', 'numpy', 'lightgbm', 'scikit-learn', 
        'joblib', 'matplotlib', 'pytz'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {missing_packages}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    return True

def check_data_files():
    """检查数据文件"""
    print("\n🔍 检查数据文件...")
    
    db_path = "../coin_data.db"
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        print("请确保已下载历史数据")
        return False
    
    print(f"✅ 数据库文件: {db_path}")
    return True

def check_model_files(coin="ETH"):
    """检查模型文件"""
    print(f"\n🔍 检查 {coin} 模型文件...")
    
    model_file = f"darts_fixed_eth_5m_model.joblib"
    config_file = f"darts_fixed_eth_5m_config.json"
    
    model_exists = os.path.exists(model_file)
    config_exists = os.path.exists(config_file)
    
    if model_exists:
        print(f"✅ 模型文件: {model_file}")
    else:
        print(f"❌ 模型文件不存在: {model_file}")
    
    if config_exists:
        print(f"✅ 配置文件: {config_file}")
    else:
        print(f"❌ 配置文件不存在: {config_file}")
    
    return model_exists and config_exists

def train_model_if_needed(coin="ETH"):
    """如果需要，训练模型"""
    if check_model_files(coin):
        print(f"\n✅ {coin} 模型已存在，跳过训练")
        return True
    
    print(f"\n🏋️ 开始训练 {coin} 模型...")
    print("这可能需要几分钟时间...")
    
    try:
        cmd = [
            "python", "darts_classification_trainer_fixed.py",
            "--coin", coin
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ 模型训练成功")
            return True
        else:
            print("❌ 模型训练失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 模型训练超时")
        return False
    except Exception as e:
        print(f"❌ 模型训练异常: {e}")
        return False

def run_basic_backtest(coin="ETH", days=7):
    """运行基础回测"""
    print(f"\n🚀 运行 {coin} 基础回测 (最近{days}天)...")
    
    # 计算时间范围
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)
    
    cmd = [
        "python", "backtest_darts_detailed.py",
        "--coin", coin,
        "--interval", "5m",
        "--initial-capital", "1000",
        "--risk-per-trade", "1.0",
        "--max-active-predictions", "10",
        "--start-time", start_time.strftime("%Y-%m-%d"),
        "--end-time", end_time.strftime("%Y-%m-%d")
    ]
    
    try:
        print("执行命令:", " ".join(cmd))
        result = subprocess.run(cmd, timeout=300)
        
        if result.returncode == 0:
            print("✅ 基础回测完成")
            return True
        else:
            print("❌ 基础回测失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 回测超时")
        return False
    except Exception as e:
        print(f"❌ 回测异常: {e}")
        return False

def run_advanced_backtest(coin="ETH", days=30):
    """运行高级功能回测"""
    print(f"\n🚀 运行 {coin} 高级功能回测 (最近{days}天)...")
    
    # 计算时间范围
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)
    
    cmd = [
        "python", "backtest_darts_detailed.py",
        "--coin", coin,
        "--interval", "5m",
        "--initial-capital", "10000",
        "--risk-per-trade", "2.0",
        "--max-active-predictions", "20",
        "--stop-loss", "2.5",
        "--use-supertrend",
        "--supertrend-interval", "30m",
        "--supertrend-atr-period", "13",
        "--supertrend-multiplier", "3.8",
        "--enable-reverse-close",
        "--reverse-close-min-positions", "2",
        "--better-price-pct", "0.1",
        "--start-time", start_time.strftime("%Y-%m-%d"),
        "--end-time", end_time.strftime("%Y-%m-%d")
    ]
    
    try:
        print("执行命令:", " ".join(cmd))
        result = subprocess.run(cmd, timeout=600)
        
        if result.returncode == 0:
            print("✅ 高级回测完成")
            return True
        else:
            print("❌ 高级回测失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 回测超时")
        return False
    except Exception as e:
        print(f"❌ 回测异常: {e}")
        return False

def show_results():
    """显示结果文件"""
    print("\n📊 检查输出文件...")
    
    output_files = [
        "darts_backtest_money_log_detailed.csv",
        "darts_backtest_performance_metrics.csv"
    ]
    
    for file in output_files:
        if os.path.exists(file):
            print(f"✅ 生成文件: {file}")
            
            # 显示文件大小
            size = os.path.getsize(file)
            if size > 1024:
                print(f"   📏 文件大小: {size/1024:.1f} KB")
            else:
                print(f"   📏 文件大小: {size} bytes")
        else:
            print(f"❌ 未找到文件: {file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Darts详细回测系统快速开始")
    parser.add_argument("--coin", default="ETH", help="币种 (默认: ETH)")
    parser.add_argument("--mode", choices=["basic", "advanced", "both"], default="both", 
                       help="回测模式 (默认: both)")
    parser.add_argument("--days", type=int, default=7, help="回测天数 (默认: 7)")
    parser.add_argument("--skip-training", action="store_true", help="跳过模型训练")
    parser.add_argument("--skip-checks", action="store_true", help="跳过环境检查")
    
    args = parser.parse_args()
    
    print_banner()
    
    # 环境检查
    if not args.skip_checks:
        if not check_environment():
            print("\n❌ 环境检查失败，请解决问题后重试")
            return False
        
        if not check_data_files():
            print("\n❌ 数据文件检查失败，请确保数据文件存在")
            return False
    
    # 模型检查和训练
    if not args.skip_training:
        if not train_model_if_needed(args.coin):
            print("\n❌ 模型准备失败")
            return False
    
    # 运行回测
    success = True
    
    if args.mode in ["basic", "both"]:
        if not run_basic_backtest(args.coin, args.days):
            success = False
    
    if args.mode in ["advanced", "both"]:
        if not run_advanced_backtest(args.coin, args.days * 4):  # 高级回测用更多天数
            success = False
    
    # 显示结果
    show_results()
    
    # 总结
    print("\n" + "=" * 70)
    if success:
        print("🎉 快速开始完成！")
        print("📊 请查看生成的CSV文件和图表")
        print("📖 详细使用方法请参考 DARTS_DETAILED_BACKTEST_GUIDE.md")
    else:
        print("❌ 部分步骤失败，请检查错误信息")
    print("=" * 70)
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
