#!/usr/bin/env python3
"""
使用模拟数据测试 Darts 训练器
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加父目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

def create_mock_data(n_samples=10000):
    """创建模拟的OHLCV数据"""
    print(f"创建 {n_samples} 条模拟数据...")
    
    # 生成时间序列
    start_time = datetime.now() - timedelta(days=n_samples//288)  # 假设5分钟数据
    timestamps = [start_time + timedelta(minutes=5*i) for i in range(n_samples)]
    
    # 生成价格数据 (更大的波动以产生更多有效标签)
    np.random.seed(42)
    price_changes = np.random.normal(0, 0.005, n_samples)  # 增加到0.5% 标准差
    prices = 3000 * np.exp(np.cumsum(price_changes))  # 从3000开始的价格
    
    # 生成OHLCV数据
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        # 简单的OHLC生成
        high = close * (1 + abs(np.random.normal(0, 0.002)))
        low = close * (1 - abs(np.random.normal(0, 0.002)))
        open_price = close + np.random.normal(0, close * 0.001)
        volume = np.random.uniform(100, 1000)
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': max(open_price, high, close),
            'low': min(open_price, low, close),
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    
    print(f"✅ 模拟数据创建完成: {len(df)} 条记录")
    print(f"时间范围: {df.index.min()} 到 {df.index.max()}")
    print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    return df

def test_darts_trainer_with_mock_data():
    """使用模拟数据测试 Darts 训练器"""
    print("=== 使用模拟数据测试 Darts 训练器 ===")
    
    try:
        from darts_lgbm_trainer import DartsLGBMTrainer
        from model_utils_815 import get_coin_config, calculate_features
        
        # 获取配置
        config_path = os.path.join(parent_dir, 'config.json')
        coin_config = get_coin_config("ETH", config_path)
        if coin_config is None:
            print("❌ 无法获取ETH配置")
            return False
        
        # 创建模拟数据
        df = create_mock_data(20000)  # 更大的数据集用于测试
        
        # 计算特征
        print("计算特征...")
        df_with_features = calculate_features(df.copy(), timeframe=coin_config['timeframe_minutes'])
        
        # 创建训练器并生成标签
        trainer = DartsLGBMTrainer({})
        target_labels = trainer.create_percentage_target(
            df, 
            coin_config['up_threshold'], 
            coin_config['down_threshold'], 
            coin_config['max_lookforward_minutes'], 
            coin_config['timeframe_minutes']
        )
        
        # 合并数据
        df_combined = df_with_features.join(target_labels.rename('label'), how='inner')
        df_clean = df_combined.dropna()
        
        if len(df_clean) < 100:
            print("❌ 清理后数据太少，无法进行训练测试")
            return False
        
        print(f"✅ 数据准备完成: {len(df_clean)} 条有效记录")
        
        # 转换为 Darts 格式
        target_series, feature_series, feature_cols = trainer.prepare_darts_timeseries(df_clean)
        
        print(f"✅ Darts 数据转换完成:")
        print(f"  - 目标序列长度: {len(target_series)}")
        print(f"  - 特征数量: {len(feature_cols)}")
        
        # 分割数据
        (train_target, val_target, test_target), (train_features, val_features, test_features) = trainer.split_darts_data(
            target_series, feature_series
        )
        
        # 快速训练（不优化）
        print("开始快速训练...")
        model = trainer.train_darts_model(
            train_target, train_features, val_target, val_features, best_params=None
        )
        
        # 评估
        print("评估模型...")
        metrics = trainer.evaluate_darts_model(test_target, test_features)
        
        print(f"✅ 训练和评估完成!")
        print(f"  - MAE: {metrics['mae']:.4f}")
        print(f"  - 准确率: {metrics['accuracy']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("Darts 训练器模拟数据测试")
    print("=" * 50)
    
    success = test_darts_trainer_with_mock_data()
    
    if success:
        print("\n✅ 模拟数据测试成功！Darts 训练器工作正常。")
        print("现在可以使用真实数据进行训练。")
    else:
        print("\n❌ 模拟数据测试失败，请检查代码。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)