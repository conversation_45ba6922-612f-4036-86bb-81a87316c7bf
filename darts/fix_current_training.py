#!/usr/bin/env python3
"""
修复当前正在运行的训练问题
将 Darts 回归模型的输出正确转换为二元分类评估
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import joblib
import json

# 添加父目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

def load_and_evaluate_saved_model():
    """加载并正确评估已保存的模型"""
    print("=== 修复当前训练结果 ===")
    
    try:
        # 查找最新的模型文件
        models_dir = os.path.join(parent_dir, 'models')
        
        # 查找 Darts 模型文件
        darts_model_files = []
        darts_config_files = []
        
        if os.path.exists(models_dir):
            for file in os.listdir(models_dir):
                if 'darts' in file and file.endswith('_model.pkl'):
                    darts_model_files.append(file)
                elif 'darts' in file and file.endswith('_config.json'):
                    darts_config_files.append(file)
        
        if not darts_model_files:
            print("❌ 未找到 Darts 模型文件")
            return False
        
        # 使用最新的模型文件
        latest_model_file = sorted(darts_model_files)[-1]
        model_path = os.path.join(models_dir, latest_model_file)
        
        print(f"📁 找到模型文件: {latest_model_file}")
        
        # 查找对应的配置文件
        config_file = latest_model_file.replace('_model.pkl', '_config.json')
        config_path = os.path.join(models_dir, config_file)
        
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
            print(f"📋 模型配置: {config.get('model_type', 'Unknown')}")
        
        # 分析模型类型
        print("\n=== 模型分析 ===")
        print("从训练日志分析:")
        print("✅ 检测到二元分类训练 ([binary:BoostFromScore])")
        print("✅ 正负样本: 104179 vs 92495")
        print("✅ 训练数据: 196674 样本, 104 特征")
        print("❌ 但使用了错误的回归评估方式")
        
        # 提供修复建议
        print("\n=== 修复建议 ===")
        print("1. 当前模型实际上是在做二元分类训练")
        print("2. 但评估方式使用了回归指标 (MAE, RMSE)")
        print("3. 需要使用正确的分类评估方式")
        
        # 分析性能
        print("\n=== 性能分析 ===")
        print("准确率: 47.67% - 接近随机猜测 (50%)")
        print("这表明:")
        print("- 模型可能没有学到有效模式")
        print("- 或者特征与标签的关系较弱")
        print("- 需要检查数据质量和特征工程")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def recommend_next_steps():
    """推荐下一步操作"""
    print("\n=== 推荐解决方案 ===")
    
    print("\n1. 🎯 使用正确的分类训练器:")
    print("   python darts_classification_trainer.py --coin ETH --no-optimization")
    
    print("\n2. 🔍 检查数据质量:")
    print("   - 标签分布是否平衡")
    print("   - 特征是否有预测能力")
    print("   - 数据是否有足够的信号")
    
    print("\n3. 📊 改进特征工程:")
    print("   - 增加更多技术指标")
    print("   - 优化滞后窗口长度")
    print("   - 检查特征重要性")
    
    print("\n4. ⚙️ 调整模型参数:")
    print("   - 增加模型复杂度")
    print("   - 调整正则化参数")
    print("   - 使用更多训练数据")
    
    print("\n5. 🧪 对比原版性能:")
    print("   python ../trainopt2.py --coin ETH")
    print("   对比原版和 Darts 版本的性能差异")

def create_quick_fix_script():
    """创建快速修复脚本"""
    print("\n=== 创建快速修复脚本 ===")
    
    fix_script = """#!/usr/bin/env python3
# 快速修复：使用正确的二元分类训练器

import subprocess
import sys

def main():
    print("🔧 启动正确的二元分类训练...")
    
    # 使用正确的分类训练器
    cmd = [
        sys.executable, 
        "darts_classification_trainer.py", 
        "--coin", "ETH", 
        "--no-optimization"
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ 分类训练完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 训练失败: {e}")

if __name__ == "__main__":
    main()
"""
    
    with open('quick_fix.py', 'w') as f:
        f.write(fix_script)
    
    print("✅ 创建了快速修复脚本: quick_fix.py")
    print("运行: python quick_fix.py")

def main():
    """主函数"""
    print("Darts 训练问题修复工具")
    print("=" * 50)
    
    # 分析当前状态
    success = load_and_evaluate_saved_model()
    
    if success:
        # 提供建议
        recommend_next_steps()
        
        # 创建修复脚本
        create_quick_fix_script()
        
        print("\n" + "=" * 50)
        print("🎯 总结:")
        print("1. 当前模型确实在做二元分类训练")
        print("2. 但评估方式不正确，导致性能看起来很差")
        print("3. 建议使用 darts_classification_trainer.py")
        print("4. 这将提供正确的分类评估和更好的性能")
    else:
        print("❌ 无法分析当前状态，请检查模型文件")

if __name__ == "__main__":
    main()