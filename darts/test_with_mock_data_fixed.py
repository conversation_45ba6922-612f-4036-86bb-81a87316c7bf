#!/usr/bin/env python3
"""
使用模拟数据测试修复后的分类训练器
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from darts_classification_trainer_fixed import FixedDartsClassificationTrainer, calculate_basic_features

def create_mock_data(n_samples=5000):
    """创建模拟的OHLCV数据"""
    print(f"创建 {n_samples} 条模拟数据...")
    
    # 创建时间序列
    start_time = datetime.now() - timedelta(days=30)
    timestamps = [start_time + timedelta(minutes=5*i) for i in range(n_samples)]
    
    # 生成模拟价格数据
    np.random.seed(42)
    base_price = 3000.0
    
    # 随机游走生成价格
    price_changes = np.random.normal(0, 0.01, n_samples)  # 1%标准差
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 100))  # 最低价格100
    
    # 创建OHLCV数据
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        # 模拟开高低价
        volatility = abs(np.random.normal(0, 0.005))  # 0.5%波动
        high = close * (1 + volatility)
        low = close * (1 - volatility)
        open_price = close + np.random.normal(0, close * 0.002)
        
        # 模拟成交量
        volume = np.random.uniform(1000, 10000)
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    
    print(f"✅ 模拟数据创建完成: {len(df)} 条记录")
    return df

def test_complete_workflow():
    """测试完整的训练流程"""
    print("=== 测试完整训练流程 ===")
    
    # 1. 创建模拟数据
    df = create_mock_data(5000)
    
    # 2. 计算特征
    print("\n计算技术指标特征...")
    df_with_features = calculate_basic_features(df.copy(), timeframe=5)
    
    # 3. 创建训练器并生成标签
    trainer = FixedDartsClassificationTrainer({})
    
    print("\n创建目标标签...")
    target_labels = trainer.create_percentage_target(
        df, 
        up_threshold=0.02,
        down_threshold=0.02,
        max_lookforward_minutes=240,
        timeframe=5
    )
    
    # 4. 合并特征和标签
    print("\n合并特征与标签...")
    df_combined = df_with_features.join(target_labels.rename('label'), how='inner')
    df_clean = df_combined.dropna()
    print(f"清理后数据: {len(df_clean)} 条记录")
    
    if len(df_clean) < 100:
        print("❌ 数据太少，无法训练")
        return
    
    # 5. 准备安全特征
    print("\n准备安全特征...")
    lagged_data, feature_names = trainer.prepare_safe_features(df_clean, lags=3)
    trainer.features = feature_names
    
    print(f"特征数量: {len(feature_names)}")
    print(f"样本数量: {len(lagged_data)}")
    
    # 6. 分割数据
    print("\n分割数据...")
    train_data, val_data, test_data = trainer.split_time_series_data_with_gap(lagged_data)
    
    # 7. 训练模型
    print("\n训练模型...")
    model = trainer.train_conservative_model(train_data, val_data)
    
    # 8. 评估模型
    print("\n评估模型...")
    val_metrics = trainer.evaluate_classification_model(val_data, save_detailed_results=False)
    test_metrics = trainer.evaluate_classification_model(test_data, save_detailed_results=True, model_name="test_fixed")
    
    # 9. 分析特征重要性
    print("\n分析特征重要性...")
    importance_df = trainer.analyze_feature_importance("test_fixed")
    
    # 10. 保存模型
    print("\n保存模型...")
    coin_config = {
        'model_basename': 'test_fixed',
        'up_threshold': 0.02,
        'down_threshold': 0.02,
        'max_lookforward_minutes': 240,
        'timeframe_minutes': 5
    }
    trainer.save_model(coin_config, "test_fixed")
    
    print(f"\n=== 测试完成 ===")
    print(f"验证集准确率: {val_metrics['accuracy']:.4f}")
    print(f"验证集AUC: {val_metrics['auc']:.4f}")
    print(f"测试集准确率: {test_metrics['accuracy']:.4f}")
    print(f"测试集AUC: {test_metrics['auc']:.4f}")
    print(f"最优阈值: {trainer.best_threshold:.3f}")
    
    return True

if __name__ == '__main__':
    try:
        success = test_complete_workflow()
        if success:
            print("✅ 所有测试通过！")
        else:
            print("❌ 测试失败")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()