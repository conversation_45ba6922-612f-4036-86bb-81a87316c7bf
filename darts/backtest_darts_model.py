#!/usr/bin/env python3
"""
Darts模型专用回测脚本
基于backtest_money_quick.py，适配darts分类模型的特殊需求
支持滞后特征和安全特征集
"""

import pandas as pd
import numpy as np
import joblib
import json
import argparse
import os
import sqlite3
from datetime import datetime, timedelta
import pytz
from typing import Dict, List, Optional
import sys

# 添加父目录到路径以导入工具函数
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 导入darts训练器用于特征计算
from darts_classification_trainer_fixed import FixedDartsClassificationTrainer, calculate_basic_features

try:
    from model_utils_815 import calculate_features
    from get_coin_history import get_table_name
    HAS_EXTERNAL_UTILS = True
except ImportError:
    HAS_EXTERNAL_UTILS = False
    print("⚠️ 未找到外部工具函数，使用内置版本")
    
    def get_table_name(coin, interval, market):
        """简化的表名生成函数"""
        return f"{coin}_{interval}_{market}"

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def to_beijing_time(timestamp):
    """转换为北京时间"""
    if isinstance(timestamp, pd.Timestamp):
        if timestamp.tz is None:
            timestamp = timestamp.tz_localize('UTC')
        return timestamp.tz_convert(BEIJING_TZ)
    elif isinstance(timestamp, datetime):
        if timestamp.tzinfo is None:
            timestamp = pytz.UTC.localize(timestamp)
        return timestamp.astimezone(BEIJING_TZ)
    return timestamp

def format_beijing_time(timestamp):
    """格式化北京时间"""
    beijing_time = to_beijing_time(timestamp)
    return beijing_time.strftime('%Y-%m-%d %H:%M:%S UTC+8')

def parse_time_input(time_str):
    """解析时间输入"""
    if not time_str:
        return None
    try:
        formats = ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M', '%Y-%m-%d', '%m-%d %H:%M', '%m-%d']
        dt = None
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                break
            except ValueError:
                continue
        if dt is None:
            raise ValueError(f"无法解析时间格式: {time_str}")
        if dt.year == 1900:
            dt = dt.replace(year=datetime.now().year)
        beijing_dt = BEIJING_TZ.localize(dt)
        utc_dt = beijing_dt.astimezone(pytz.UTC)
        return pd.Timestamp(utc_dt).tz_localize(None)
    except Exception as e:
        print(f"时间解析错误: {e}")
        return None

def load_data_from_sqlite(db_path, coin, interval, market, start_time=None, end_time=None):
    """从SQLite读取数据"""
    table_name = get_table_name(coin, interval, market)
    conn = sqlite3.connect(db_path)
    
    query = f"SELECT timestamp, open, high, low, close, volume FROM {table_name}"
    conditions = []
    params = []
    
    if start_time:
        conditions.append("timestamp >= ?")
        params.append(int(start_time.timestamp()))
    if end_time:
        conditions.append("timestamp <= ?")
        params.append(int(end_time.timestamp()))
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    query += " ORDER BY timestamp ASC"
    
    df = pd.read_sql_query(query, conn, params=params)
    conn.close()
    
    if df.empty:
        print("❌ 数据库中无符合条件的数据")
        return None
    
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
    df.set_index('timestamp', inplace=True)
    df = df.astype(float)
    
    return df

class DartsBacktester:
    """Darts模型专用回测器"""
    
    def __init__(self, model_file: str, config_file: str, initial_capital: float, 
                 risk_per_trade_pct: float, stop_loss_pct: float = None):
        self.model_file = model_file
        self.config_file = config_file
        self.stop_loss_pct = stop_loss_pct
        
        # 加载模型和配置
        self.model = joblib.load(model_file)
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        
        # 资金管理
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.risk_per_trade_pct = risk_per_trade_pct / 100.0
        
        # 预测管理
        self.active_predictions: Dict[str, dict] = {}
        self.completed_predictions: List[dict] = []
        self.prediction_counter = 0
        
        # 统计计数器
        self.total_predictions = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        self.timeout_predictions = 0
        self.stop_loss_predictions = 0
        
        # 初始化darts训练器用于特征计算
        self.trainer = FixedDartsClassificationTrainer({})
        
        print(f"Darts回测器初始化完成")
        print(f"模型阈值: {self.config['best_threshold']:.3f}")
        print(f"最大等待: {self.config['max_lookforward_minutes']}分钟")
        print(f"初始资金: ${initial_capital:,.2f}")
        print(f"单次风险比例: {risk_per_trade_pct:.2f}%")
        if stop_loss_pct:
            print(f"止损触发: {stop_loss_pct:.1f}%")

    def prepare_features_for_prediction(self, df_window, lags=5):
        """为预测准备特征，使用与训练时相同的方法"""
        try:
            # 1. 计算基础特征
            if HAS_EXTERNAL_UTILS:
                df_with_features = calculate_features(df_window.copy(), 
                                                    timeframe=self.config['timeframe_minutes'])
            else:
                df_with_features = calculate_basic_features(df_window.copy(), 
                                                          timeframe=self.config['timeframe_minutes'])
            
            # 2. 移除高相关性特征（与训练时保持一致）
            excluded_features = [
                'open', 'high', 'low', 'close',
                'sma_120', 'sma_360', 'sma_720',
                'vwap_360',
            ]
            
            feature_cols = [col for col in df_with_features.columns 
                          if col not in excluded_features]
            safe_features = feature_cols
            
            # 3. 创建滞后特征（与训练时保持一致）
            if len(df_with_features) < lags + 1:
                return None, None
            
            # 获取最后一个时间点的数据
            last_idx = len(df_with_features) - 1
            
            # 当前特征
            current_features = df_with_features[safe_features].iloc[last_idx].values
            
            # 滞后特征
            lag_feature_count = min(5, len(safe_features))
            lagged_features = []
            
            for lag in range(1, lags + 1):
                if last_idx - lag >= 0:
                    lag_features = df_with_features[safe_features[:lag_feature_count]].iloc[last_idx - lag].values
                    lagged_features.extend(lag_features)
                else:
                    # 如果没有足够的历史数据，用0填充
                    lagged_features.extend([0.0] * lag_feature_count)
            
            # 组合特征
            combined_features = np.concatenate([current_features, lagged_features])
            current_price = df_with_features['close'].iloc[last_idx] if 'close' in df_with_features.columns else df_window['close'].iloc[-1]
            
            return combined_features, current_price
            
        except Exception as e:
            print(f"特征准备错误: {e}")
            return None, None

    def make_prediction(self, features_array):
        """使用特征数组进行预测"""
        try:
            if features_array is None:
                return None, 0.0
            
            # 检查特征数量是否匹配
            expected_features = len(self.config.get('features', []))
            if len(features_array) != expected_features and expected_features > 0:
                print(f"⚠️ 特征数量不匹配: 期望{expected_features}, 实际{len(features_array)}")
                return None, 0.0
            
            # 重塑为2D数组
            features_2d = features_array.reshape(1, -1)
            
            # 预测概率
            probability = self.model.predict_proba(features_2d)[0, 1]
            
            # 根据阈值决定预测
            best_threshold = self.config['best_threshold']
            guess = None
            
            if probability > best_threshold:
                guess = 1  # 预测上涨
            elif probability < (1 - best_threshold):
                guess = 0  # 预测下跌
            
            return guess, probability
            
        except Exception as e:
            print(f"预测错误: {e}")
            return None, 0.0

    def add_prediction(self, guess: int, probability: float, price: float, 
                      timestamp: pd.Timestamp, current_idx: int):
        """添加新预测"""
        self.prediction_counter += 1
        prediction_id = f"darts_pred_{self.prediction_counter:06d}"
        
        trade_risk_capital = self.current_capital * self.risk_per_trade_pct
        max_wait_candles = self.config['max_lookforward_minutes'] // self.config['timeframe_minutes']
        
        prediction = {
            'id': prediction_id,
            'guess': guess,
            'probability': probability,
            'start_price': price,
            'start_timestamp': timestamp,
            'start_idx': current_idx,
            'expire_idx': current_idx + max_wait_candles,
            'up_target': price * (1 + self.config['up_threshold']),
            'down_target': price * (1 - self.config['down_threshold']),
            'trade_risk_capital': trade_risk_capital,
            'max_loss_pct': 0.0,
            'max_loss_price': price,
            'max_loss_timestamp': timestamp,
            'max_profit_pct': 0.0,
            'max_profit_price': price,
            'max_profit_timestamp': timestamp
        }
        
        self.active_predictions[prediction_id] = prediction
        self.total_predictions += 1
        
        direction_str = f"先涨{self.config['up_threshold']*100:.1f}%" if guess == 1 else f"先跌{self.config['down_threshold']*100:.1f}%"
        print(f"[{format_beijing_time(timestamp)}] 新预测: {direction_str}, "
              f"信心: {probability:.3f}, 价格: {price:.4f}, "
              f"风险暴露: ${trade_risk_capital:,.2f}")

    def check_predictions(self, current_price: float, current_timestamp: pd.Timestamp, 
                         current_idx: int, current_high: float = None, current_low: float = None):
        """检查现有预测状态"""
        completed_ids = []
        
        for pred_id, pred in self.active_predictions.items():
            # 计算价格变化百分比
            price_change_pct = (current_price - pred['start_price']) / pred['start_price'] * 100
            
            # 更新最大盈亏
            if (pred['guess'] == 1 and price_change_pct < 0) or \
               (pred['guess'] == 0 and price_change_pct > 0):
                loss_pct = abs(price_change_pct)
                if loss_pct > pred['max_loss_pct']:
                    pred.update({
                        'max_loss_pct': loss_pct,
                        'max_loss_price': current_price,
                        'max_loss_timestamp': current_timestamp
                    })
            
            if (pred['guess'] == 1 and price_change_pct > 0) or \
               (pred['guess'] == 0 and price_change_pct < 0):
                profit_pct = abs(price_change_pct)
                if profit_pct > pred['max_profit_pct']:
                    pred.update({
                        'max_profit_pct': profit_pct,
                        'max_profit_price': current_price,
                        'max_profit_timestamp': current_timestamp
                    })
            
            # 检查止损
            if self.stop_loss_pct is not None:
                should_stop_loss = (pred['guess'] == 1 and price_change_pct < -self.stop_loss_pct) or \
                                 (pred['guess'] == 0 and price_change_pct > self.stop_loss_pct)
                if should_stop_loss:
                    reason = f"止损(触发点:{-self.stop_loss_pct:.1f}%)"
                    self.complete_prediction(pred_id, -2, current_price, current_timestamp, current_idx, reason)
                    completed_ids.append(pred_id)
                    continue
            
            # 检查目标达成
            if current_price >= pred['up_target']:
                result = 1 if pred['guess'] == 1 else 0
                reason = "达到上涨目标" if pred['guess'] == 1 else "达到上涨目标(预测错误)"
                self.complete_prediction(pred_id, result, current_price, current_timestamp, current_idx, reason)
                completed_ids.append(pred_id)
            elif current_price <= pred['down_target']:
                result = 1 if pred['guess'] == 0 else 0
                reason = "达到下跌目标" if pred['guess'] == 0 else "达到下跌目标(预测错误)"
                self.complete_prediction(pred_id, result, current_price, current_timestamp, current_idx, reason)
                completed_ids.append(pred_id)
            elif current_idx >= pred['expire_idx']:
                self.complete_prediction(pred_id, -1, current_price, current_timestamp, current_idx, "超时")
                completed_ids.append(pred_id)
        
        # 移除已完成的预测
        for pred_id in completed_ids:
            if pred_id in self.active_predictions:
                del self.active_predictions[pred_id]

    def calculate_score(self, prediction: int, start_price: float, end_price: float, result: int):
        """计算得分"""
        price_change_pct = (end_price - start_price) / start_price
        threshold = self.config.get('up_threshold', 0.02) if prediction == 1 else self.config.get('down_threshold', 0.02)
        
        # 考虑手续费的得分计算
        score = (price_change_pct - 0.001) / threshold if prediction == 1 else (-price_change_pct + 0.001) / threshold
        return score

    def complete_prediction(self, pred_id: str, result: int, final_price: float, 
                          end_timestamp: pd.Timestamp, end_idx: int, reason: str):
        """完成预测"""
        if pred_id not in self.active_predictions:
            return
        
        pred = self.active_predictions[pred_id]
        
        # 计算得分和盈亏
        score = self.calculate_score(pred['guess'], pred['start_price'], final_price, result)
        profit_loss = pred['trade_risk_capital'] * score
        self.current_capital += profit_loss
        
        # 更新统计
        if result == 1:
            self.successful_predictions += 1
            status_str = "成功✅"
        elif result == 0:
            self.failed_predictions += 1
            status_str = "失败❌"
        elif result == -2:
            self.stop_loss_predictions += 1
            status_str = "止损🛑"
        else:
            self.timeout_predictions += 1
            status_str = "超时⏰"
        
        # 记录完成的预测
        completed_pred = {
            'PredictionID': pred['id'],
            'StartTimestamp': format_beijing_time(pred['start_timestamp']),
            'EndTimestamp': format_beijing_time(end_timestamp),
            'StartPrice': pred['start_price'],
            'EndPrice': final_price,
            'PriceChangePct': ((final_price - pred['start_price']) / pred['start_price']) * 100,
            'MaxLossPct': pred['max_loss_pct'],
            'MaxLossPrice': pred['max_loss_price'],
            'MaxLossTimestamp': format_beijing_time(pred['max_loss_timestamp']),
            'MaxProfitPct': pred['max_profit_pct'],
            'MaxProfitPrice': pred['max_profit_price'],
            'MaxProfitTimestamp': format_beijing_time(pred['max_profit_timestamp']),
            'Confidence': pred['probability'],
            'Prediction': pred['guess'],
            'Result': result,
            'Score': score,
            'ProfitLoss': profit_loss,
            'CapitalAfter': self.current_capital,
            'Status': status_str,
            'Reason': reason,
            'DurationMinutes': (end_idx - pred['start_idx']) * self.config['timeframe_minutes'],
            'UpTarget': pred['up_target'],
            'DownTarget': pred['down_target']
        }
        
        self.completed_predictions.append(completed_pred)
        
        direction_str = f"先涨..." if pred['guess'] == 1 else f"先跌..."
        print(f"[{format_beijing_time(end_timestamp)}] 预测完成: {direction_str} -> {status_str}, "
              f"得分: {score:+.2f}, 盈亏: ${profit_loss:+.2f}, "
              f"当前资金: ${self.current_capital:,.2f}")

def run_darts_backtest(model_file: str, config_file: str, df: pd.DataFrame, 
                      initial_capital: float, risk_per_trade_pct: float,
                      stop_loss_pct: float = None, max_active_predictions: int = 10,
                      prediction_window_size: int = 1000, lags: int = 5):
    """运行Darts模型回测"""
    print("=== 开始Darts模型回测 ===")
    
    backtester = DartsBacktester(model_file, config_file, initial_capital, 
                                risk_per_trade_pct, stop_loss_pct)
    
    # 确定开始位置（需要足够的历史数据）
    min_history = max(720 // backtester.config.get('timeframe_minutes', 5) + 50, prediction_window_size)
    actual_start_pos = min_history
    
    if actual_start_pos >= len(df):
        print(f"❌ 数据不足。需要至少 {actual_start_pos} 条数据，但只有 {len(df)} 条。")
        return
    
    print(f"\n从索引 {actual_start_pos} 开始预测")
    print(f"时间: {format_beijing_time(df.index[actual_start_pos])}")
    print(f"最多同时保持 {max_active_predictions} 笔活跃投资")
    
    # 开始回测循环
    for i in range(actual_start_pos, len(df)):
        current_timestamp = df.index[i]
        current_price = df.iloc[i]['close']
        current_high = df.iloc[i]['high']
        current_low = df.iloc[i]['low']
        
        # 检查现有预测
        backtester.check_predictions(current_price, current_timestamp, i, current_high, current_low)
        
        # 检查是否可以添加新预测
        active_count = len(backtester.active_predictions)
        if active_count < max_active_predictions:
            # 准备预测窗口
            start_slice_index = max(0, i - prediction_window_size + 1)
            current_window_df = df.iloc[start_slice_index:i+1].copy()
            
            # 准备特征并预测
            features_array, pred_price = backtester.prepare_features_for_prediction(current_window_df, lags)
            if features_array is not None:
                guess, probability = backtester.make_prediction(features_array)
                if guess is not None:
                    backtester.add_prediction(guess, probability, pred_price, current_timestamp, i)
    
    # 结束所有活跃预测
    final_timestamp = df.index[-1]
    final_price = df.iloc[-1]['close']
    final_idx = len(df) - 1
    
    for pred_id in list(backtester.active_predictions.keys()):
        backtester.complete_prediction(pred_id, -1, final_price, final_timestamp, final_idx, "数据结束-超时")
    
    # 生成报告
    print("\n=== Darts回测结果摘要 ===")
    print(f"总预测数: {backtester.total_predictions}")
    print(f"成功: {backtester.successful_predictions}")
    print(f"失败: {backtester.failed_predictions}")
    print(f"超时: {backtester.timeout_predictions}")
    if backtester.stop_loss_predictions > 0:
        print(f"止损: {backtester.stop_loss_predictions}")
    
    print(f"\n初始资金: ${backtester.initial_capital:,.2f}")
    print(f"最终资金: ${backtester.current_capital:,.2f}")
    total_return = (backtester.current_capital - backtester.initial_capital) / backtester.initial_capital * 100
    print(f"总收益率: {total_return:+.2f}%")
    
    if backtester.total_predictions > 0:
        win_rate = backtester.successful_predictions / backtester.total_predictions * 100
        print(f"胜率: {win_rate:.2f}%")
    
    # 保存详细结果
    if backtester.completed_predictions:
        results_df = pd.DataFrame(backtester.completed_predictions)
        output_filename = "darts_backtest_results.csv"
        results_df.to_csv(output_filename, index=False, float_format='%.4f')
        print(f"\n📊 详细回测结果已保存到: {output_filename}")
    
    return backtester

def main():
    parser = argparse.ArgumentParser(description="Darts模型专用回测脚本")
    parser.add_argument("--model-file", required=True, help="模型文件路径 (.joblib)")
    parser.add_argument("--config-file", required=True, help="配置文件路径 (.json)")
    parser.add_argument("--db", default="../coin_data.db", help="SQLite数据库路径")
    parser.add_argument("--coin", default="ETHUSDT", help="交易对")
    parser.add_argument("--interval", default="5m", help="K线间隔")
    parser.add_argument("--market", default="spot", help="市场类型")
    parser.add_argument("--start-time", help="回测开始时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--end-time", help="回测结束时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--initial-capital", type=float, default=1000, help="初始资金")
    parser.add_argument("--risk-per-trade", type=float, default=1.0, help="单次交易风险比例(%)")
    parser.add_argument("--stop-loss", type=float, help="止损百分比")
    parser.add_argument("--max-active", type=int, default=10, help="最大同时活跃预测数")
    parser.add_argument("--window-size", type=int, default=1000, help="预测窗口大小")
    parser.add_argument("--lags", type=int, default=5, help="滞后特征数量")
    
    args = parser.parse_args()
    
    # 解析时间
    start_time = parse_time_input(args.start_time) if args.start_time else None
    end_time = parse_time_input(args.end_time) if args.end_time else None
    
    # 检查文件存在
    if not os.path.exists(args.model_file):
        print(f"❌ 模型文件不存在: {args.model_file}")
        return
    
    if not os.path.exists(args.config_file):
        print(f"❌ 配置文件不存在: {args.config_file}")
        return
    
    # 加载数据
    print(f"从数据库加载数据: {args.coin} {args.interval} {args.market}")
    df = load_data_from_sqlite(args.db, args.coin, args.interval, args.market, start_time, end_time)
    
    if df is None or df.empty:
        print("❌ 未加载到任何数据")
        return
    
    print(f"✅ 加载 {len(df)} 条数据")
    print(f"时间范围: {format_beijing_time(df.index[0])} 到 {format_beijing_time(df.index[-1])}")
    
    # 运行回测
    backtester = run_darts_backtest(
        args.model_file, args.config_file, df,
        args.initial_capital, args.risk_per_trade, args.stop_loss,
        args.max_active, args.window_size, args.lags
    )

if __name__ == '__main__':
    main()