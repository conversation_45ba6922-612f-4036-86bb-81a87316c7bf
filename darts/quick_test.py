#!/usr/bin/env python3
"""
快速测试 Darts 训练器的修复版本
"""

import sys
import os

# 添加父目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

def quick_test():
    """快速测试修复后的功能"""
    print("=== Darts 训练器快速测试 ===")
    
    try:
        # 测试基本导入
        from darts_lgbm_trainer import DartsLGBMTrainer
        print("✅ 训练器导入成功")
        
        # 测试配置加载
        from model_utils_815 import get_coin_config
        config_path = os.path.join(parent_dir, 'config.json')
        coin_config = get_coin_config("ETH", config_path)
        
        if coin_config:
            print("✅ 配置加载成功")
            print(f"  币种: {coin_config['display_name']}")
            print(f"  时间框架: {coin_config['timeframe_minutes']}分钟")
        else:
            print("❌ 配置加载失败")
            return False
        
        # 测试 Darts 相关导入
        from darts import TimeSeries
        from darts.models import LightGBMModel
        from darts.metrics import mae, rmse
        print("✅ Darts 模块导入成功")
        
        # 创建简单的测试数据
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        
        # 创建小规模测试数据
        dates = pd.date_range('2024-01-01', periods=100, freq='5T')
        values = np.random.choice([0, 1], size=100)  # 二元标签
        
        df = pd.DataFrame({
            'label': values
        }, index=dates)
        
        # 测试 TimeSeries 创建
        ts = TimeSeries.from_dataframe(df, value_cols=['label'])
        print("✅ TimeSeries 创建成功")
        
        # 测试评估指标
        ts1 = ts[:50]
        ts2 = ts[50:]
        
        if len(ts2) > 0:
            mae_score = mae(ts1, ts2[:len(ts1)])
            print(f"✅ MAE 计算成功: {mae_score:.4f}")
            
            # 测试 MAPE 错误处理
            try:
                from darts.metrics import mape
                mape_score = mape(ts1, ts2[:len(ts1)])
                print(f"✅ MAPE 计算成功: {mape_score:.4f}")
            except ValueError as e:
                print(f"✅ MAPE 错误处理成功: {str(e)}")
        
        print("\n🎉 所有测试通过！Darts 训练器已准备就绪。")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_test()
    
    if success:
        print("\n下一步可以运行:")
        print("1. python example_usage.py  # 运行示例")
        print("2. python darts_lgbm_trainer.py --coin ETH --no-optimization  # 直接训练")
    
    sys.exit(0 if success else 1)