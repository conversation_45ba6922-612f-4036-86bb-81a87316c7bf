#!/usr/bin/env python3
# 快速修复：使用正确的二元分类训练器

import subprocess
import sys

def main():
    print("🔧 启动正确的二元分类训练...")
    
    # 使用正确的分类训练器
    cmd = [
        sys.executable, 
        "darts_classification_trainer.py", 
        "--coin", "ETH", 
        "--no-optimization"
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ 分类训练完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 训练失败: {e}")

if __name__ == "__main__":
    main()
