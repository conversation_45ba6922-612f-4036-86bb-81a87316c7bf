#!/usr/bin/env python3
"""
Darts LightGBM 训练器启动脚本
简化的用户界面
"""

import sys
import os
import subprocess

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🚀 Darts LightGBM 训练器")
    print("   基于 trainopt2.py 移植，使用 Darts 框架")
    print("=" * 60)

def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    
    try:
        # 检查基本导入
        import pandas as pd
        import numpy as np
        import lightgbm as lgb
        from darts import TimeSeries
        print("✅ 基础依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 依赖检查失败: {e}")
        print("请运行: pip install darts[lightgbm] optuna pandas numpy scikit-learn")
        return False

def show_menu():
    """显示主菜单"""
    print("\n📋 请选择操作:")
    print("1. 🧪 环境测试 - 验证所有功能正常")
    print("2. 🏃 快速训练 - ETH 5分钟，无优化（推荐新手）")
    print("3. 🎯 完整训练 - ETH 5分钟，带超参数优化")
    print("4. 🔧 诊断工具 - 解决训练问题")
    print("5. 📊 模拟数据测试 - 使用人工数据测试")
    print("6. ⚙️  自定义训练 - 手动指定参数")
    print("7. 📖 查看文档")
    print("0. 退出")
    print("-" * 40)

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n🔄 {description}...")
    print(f"命令: {cmd}")
    print("-" * 40)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=False, text=True)
        if result.returncode == 0:
            print(f"✅ {description} 完成")
        else:
            print(f"⚠️ {description} 完成，但可能有警告")
        return True
    except Exception as e:
        print(f"❌ {description} 失败: {e}")
        return False

def handle_choice(choice):
    """处理用户选择"""
    if choice == "1":
        # 环境测试
        run_command("python quick_test.py", "环境测试")
        
    elif choice == "2":
        # 快速训练
        print("\n🏃 开始快速训练...")
        print("这将使用 ETH 5分钟数据，不进行超参数优化")
        confirm = input("确认开始? (y/n): ").lower()
        if confirm.startswith('y'):
            run_command("python darts_lgbm_trainer.py --coin ETH --no-optimization", "快速训练")
        
    elif choice == "3":
        # 完整训练
        print("\n🎯 开始完整训练...")
        print("这将使用 ETH 5分钟数据，进行超参数优化（可能需要较长时间）")
        trials = input("输入优化试验次数 (默认50): ").strip()
        if not trials:
            trials = "50"
        
        confirm = input(f"确认开始 {trials} 次优化试验? (y/n): ").lower()
        if confirm.startswith('y'):
            run_command(f"python darts_lgbm_trainer.py --coin ETH --n-trials {trials}", "完整训练")
        
    elif choice == "4":
        # 诊断工具
        run_command("python diagnose_training.py", "训练诊断")
        
    elif choice == "5":
        # 模拟数据测试
        run_command("python test_optimized_training.py", "模拟数据测试")
        
    elif choice == "6":
        # 自定义训练
        print("\n⚙️ 自定义训练参数:")
        coin = input("币种 (默认 ETH): ").strip() or "ETH"
        
        print("选择训练模式:")
        print("1. 快速训练（无优化）")
        print("2. 优化训练")
        mode = input("选择 (1/2): ").strip()
        
        if mode == "1":
            cmd = f"python darts_lgbm_trainer.py --coin {coin} --no-optimization"
        else:
            trials = input("优化试验次数 (默认30): ").strip() or "30"
            cmd = f"python darts_lgbm_trainer.py --coin {coin} --n-trials {trials}"
        
        print(f"\n将执行: {cmd}")
        confirm = input("确认执行? (y/n): ").lower()
        if confirm.startswith('y'):
            run_command(cmd, f"{coin} 自定义训练")
        
    elif choice == "7":
        # 查看文档
        print("\n📖 可用文档:")
        print("1. README.md - 详细使用说明")
        print("2. FINAL_GUIDE.md - 最终使用指南")
        print("3. MIGRATION_SUMMARY.md - 移植总结")
        
        doc_choice = input("选择要查看的文档 (1-3): ").strip()
        docs = {"1": "README.md", "2": "FINAL_GUIDE.md", "3": "MIGRATION_SUMMARY.md"}
        
        if doc_choice in docs:
            doc_file = docs[doc_choice]
            if os.path.exists(doc_file):
                print(f"\n📄 {doc_file} 内容:")
                print("-" * 40)
                with open(doc_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 显示前50行
                    lines = content.split('\n')[:50]
                    print('\n'.join(lines))
                    if len(content.split('\n')) > 50:
                        print("\n... (文档较长，请直接打开文件查看完整内容)")
            else:
                print(f"❌ 文档文件 {doc_file} 不存在")
        
    elif choice == "0":
        print("👋 再见！")
        return False
        
    else:
        print("❌ 无效选择，请重试")
    
    return True

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请先安装依赖")
        return
    
    # 主循环
    while True:
        show_menu()
        choice = input("请输入选择 (0-7): ").strip()
        
        if not handle_choice(choice):
            break
        
        # 询问是否继续
        print("\n" + "=" * 40)
        continue_choice = input("按 Enter 继续，或输入 'q' 退出: ").strip().lower()
        if continue_choice == 'q':
            print("👋 再见！")
            break

if __name__ == "__main__":
    main()