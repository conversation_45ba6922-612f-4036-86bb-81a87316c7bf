"""
Darts框架集成的LightGBM训练器
基于trainopt2.py移植，使用darts进行时间序列预测
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import lightgbm as lgb
from sklearn.calibration import CalibratedClassifierCV
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import roc_auc_score, accuracy_score, precision_score, recall_score
import joblib
import json
import argparse
import os
import pickle
import optuna
import warnings
warnings.filterwarnings('ignore')

# Darts imports
from darts import TimeSeries
from darts.models import LightGBMModel
from darts.metrics import mape, mae, rmse
from darts.utils.timeseries_generation import datetime_attribute_timeseries
from darts.dataprocessing.transformers import Scaler

# 假设这些工具函数存在于您的项目中
from model_utils_815 import (
    calculate_features, get_coin_config, get_output_dir, get_feature_list,
    get_optimized_feature_list 
)
from data_loader import load_data_for_training, create_data_source_config, print_data_source_info

# 全局变量
TIMEFRAME_MINUTES = None
UP_THRESHOLD = None
DOWN_THRESHOLD = None
MAX_LOOKFORWARD_MINUTES = None
MODEL_BASENAME = None

class DartsLGBMTrainer:
    """基于Darts框架的LightGBM训练器"""
    
    def __init__(self, config):
        self.config = config
        self.model = None
        self.scaler = None
        self.features = None
        self.best_threshold = 0.5
        
    def create_percentage_target(self, df, up_threshold, down_threshold, max_lookforward_minutes, timeframe):
        """创建目标标签 - 与原版保持一致"""
        max_lookforward_candles = max_lookforward_minutes // timeframe
        print(f"创建目标标签：先涨{up_threshold*100:.1f}%还是先跌{down_threshold*100:.1f}% (最大等待 {max_lookforward_candles} 根K线)")
        labels = np.full(len(df), np.nan)
        
        close_prices = df['close'].to_numpy()
        up_targets = close_prices * (1 + up_threshold)
        down_targets = close_prices * (1 - down_threshold)

        for i in range(len(df) - max_lookforward_candles):
            if i % 20000 == 0:
                print(f"处理标签进度: {i}/{len(df)} ({i/len(df)*100:.1f}%)")
            
            future_window = close_prices[i+1 : i+1+max_lookforward_candles]
            
            up_hit_indices = np.where(future_window >= up_targets[i])[0]
            down_hit_indices = np.where(future_window <= down_targets[i])[0]

            first_up_hit = up_hit_indices[0] if len(up_hit_indices) > 0 else np.inf
            first_down_hit = down_hit_indices[0] if len(down_hit_indices) > 0 else np.inf

            if first_up_hit < first_down_hit:
                labels[i] = 1
            elif first_down_hit < first_up_hit:
                labels[i] = 0

        valid_mask = ~np.isnan(labels)
        valid_indices = df.index[valid_mask]
        valid_labels = labels[valid_mask].astype(int)
        
        print(f"有效标签数量: {len(valid_labels)}/{len(df)} ({len(valid_labels)/len(df)*100:.1f}%)")
        label_series = pd.Series(index=valid_indices, data=valid_labels)

        if len(valid_labels) > 0:
            up_count = np.sum(valid_labels)
            down_count = len(valid_labels) - up_count
            print(f"标签分布: 先涨 = {up_count} ({up_count/len(valid_labels)*100:.1f}%), 先跌 = {down_count} ({down_count/len(valid_labels)*100:.1f}%)")
        else:
            print("未生成任何有效标签。")
        return label_series

    def prepare_darts_timeseries(self, df_clean):
        """将数据转换为Darts TimeSeries格式"""
        print("准备Darts时间序列数据...")
        
        # 确保索引是datetime类型并设置频率
        if not isinstance(df_clean.index, pd.DatetimeIndex):
            df_clean.index = pd.to_datetime(df_clean.index)
        
        # 强制设置频率为5分钟
        df_clean = df_clean.asfreq('5T', method='ffill')
        
        # 分离特征和标签
        target_col = 'label'
        feature_cols = [col for col in df_clean.columns if col != target_col]
        
        # 先确保数据没有缺失值
        df_clean = df_clean.dropna()
        
        print(f"数据频率: {df_clean.index.freq}")
        print(f"数据形状: {df_clean.shape}")
        
        # 创建目标时间序列
        target_series = TimeSeries.from_dataframe(
            df_clean[[target_col]], 
            time_col=None,  # 使用索引作为时间
            value_cols=[target_col]
        )
        
        # 创建特征时间序列
        feature_series = TimeSeries.from_dataframe(
            df_clean[feature_cols],
            time_col=None,
            value_cols=feature_cols
        )
        
        return target_series, feature_series, feature_cols

    def split_darts_data(self, target_series, feature_series, train_ratio=0.7, val_ratio=0.15):
        """分割Darts时间序列数据"""
        total_len = len(target_series)
        train_len = int(total_len * train_ratio)
        val_len = int(total_len * val_ratio)
        
        # 分割目标序列
        train_target = target_series[:train_len]
        val_target = target_series[train_len:train_len + val_len]
        test_target = target_series[train_len + val_len:]
        
        # 分割特征序列
        train_features = feature_series[:train_len]
        val_features = feature_series[train_len:train_len + val_len]
        test_features = feature_series[train_len + val_len:]
        
        print(f"Darts数据分割:")
        print(f"训练集: {len(train_target)} 样本")
        print(f"验证集: {len(val_target)} 样本") 
        print(f"测试集: {len(test_target)} 样本")
        
        return (train_target, val_target, test_target), (train_features, val_features, test_features)

    def optimize_hyperparameters_darts(self, train_target, train_features, val_target, val_features, n_trials=50):
        """使用Optuna优化Darts LightGBM超参数"""
        print(f"\n=== 开始Darts LightGBM超参数优化 (n_trials={n_trials}) ===")
        
        def objective(trial):
            # LightGBM参数
            lgb_params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'num_leaves': trial.suggest_int('num_leaves', 10, 100),
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 50),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'random_state': 42
            }
            
            # Darts模型参数
            lags = trial.suggest_int('lags', 5, 50)
            lags_future_covariates = trial.suggest_int('lags_future_covariates', 1, 10)
            
            try:
                # 创建Darts LightGBM模型
                model = LightGBMModel(
                    lags=lags,
                    lags_future_covariates=[0, lags_future_covariates],
                    output_chunk_length=1,
                    **lgb_params
                )
                
                # 训练模型
                model.fit(
                    series=train_target,
                    future_covariates=train_features
                )
                
                # 预测验证集
                pred = model.predict(
                    n=len(val_target),
                    future_covariates=val_features
                )
                
                # 计算MAE作为优化目标
                mae_score = mae(val_target, pred)
                return mae_score
                
            except Exception as e:
                print(f"Trial failed: {e}")
                return float('inf')
        
        # 运行优化
        optuna.logging.set_verbosity(optuna.logging.WARNING)
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=n_trials)
        
        print(f"=== Darts超参数优化完成 ===")
        print(f"最佳MAE: {study.best_value:.4f}")
        print(f"最佳参数: {study.best_params}")
        
        return study.best_params, study.best_value

    def train_darts_model(self, train_target, train_features, val_target, val_features, best_params=None):
        """训练Darts LightGBM模型"""
        print("\n开始训练Darts LightGBM模型...")
        
        # 优化的默认参数 - 二元分类模型（与原版 trainopt2.py 一致）
        default_params = {
            'lags': 10,  # 减少滞后窗口
            'lags_future_covariates': [0, 3],  # 减少未来协变量滞后
            'objective': 'binary',  # 🔥 关键：二元分类目标函数
            'metric': 'binary_logloss',  # 二元分类指标
            'n_estimators': 100,  # 减少树的数量
            'learning_rate': 0.1,
            'max_depth': 6,
            'num_leaves': 31,
            'min_child_samples': 10,  # 降低最小样本数
            'min_child_weight': 0.001,  # 降低最小权重
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.0,  # 不使用L1正则化
            'reg_lambda': 0.0,  # 不使用L2正则化
            'min_split_gain': 0.0,  # 允许任何增益的分割
            'force_col_wise': True,  # 避免多线程开销警告
            'random_state': 42,
            'verbose': -1
        }
        
        if best_params:
            default_params.update(best_params)
        
        # 提取Darts特定参数
        lags = default_params.pop('lags')
        lags_future_covariates = default_params.pop('lags_future_covariates')
        verbose = default_params.pop('verbose', -1)
        
        # 创建模型
        self.model = LightGBMModel(
            lags=lags,
            lags_future_covariates=lags_future_covariates if isinstance(lags_future_covariates, list) else [0, lags_future_covariates],
            output_chunk_length=1,
            **default_params
        )
        
        # 训练模型
        self.model.fit(
            series=train_target,
            future_covariates=train_features,
            val_series=val_target,
            val_future_covariates=val_features
        )
        
        print("Darts模型训练完成")
        return self.model

    def evaluate_darts_model(self, test_target, test_features):
        """评估Darts模型"""
        print("\n=== Darts模型评估 ===")
        
        # 预测 - 使用历史预测避免协变量长度问题
        try:
            predictions = self.model.historical_forecasts(
                series=test_target,
                future_covariates=test_features,
                start=0.1,  # 从10%开始预测
                forecast_horizon=1,
                stride=1,
                retrain=False,
                verbose=False
            )
        except Exception as e:
            print(f"历史预测失败，尝试简单预测: {e}")
            # 如果历史预测失败，使用较小的预测长度
            n_predict = min(len(test_target), 100)
            predictions = self.model.predict(
                n=n_predict,
                future_covariates=test_features[:n_predict + 10]  # 添加一些缓冲
            )
            # 调整测试目标长度
            test_target = test_target[:len(predictions)]
        
        # 计算回归指标
        mae_score = mae(test_target, predictions)
        rmse_score = rmse(test_target, predictions)
        
        # MAPE 需要正值，对于0/1标签不适用，跳过
        try:
            mape_score = mape(test_target, predictions)
            print(f"MAPE: {mape_score:.4f}%")
        except ValueError as e:
            mape_score = float('nan')  # 对于0/1标签，MAPE不适用
            print(f"MAPE: 不适用 (原因: {str(e)})")
        
        print(f"MAE: {mae_score:.4f}")
        print(f"RMSE: {rmse_score:.4f}")
        
        # 转换为分类问题评估
        test_values = test_target.values().flatten()
        pred_values = predictions.values().flatten()
        
        # 确保长度一致
        min_len = min(len(test_values), len(pred_values))
        test_values = test_values[:min_len]
        pred_values = pred_values[:min_len]
        
        # 使用0.5作为分类阈值
        test_binary = (test_values > 0.5).astype(int)
        pred_binary = (pred_values > 0.5).astype(int)
        
        accuracy = accuracy_score(test_binary, pred_binary)
        precision = precision_score(test_binary, pred_binary, average='weighted', zero_division=0)
        recall = recall_score(test_binary, pred_binary, average='weighted', zero_division=0)
        
        print(f"\n分类指标:")
        print(f"准确率: {accuracy:.4f}")
        print(f"精确率: {precision:.4f}")
        print(f"召回率: {recall:.4f}")
        
        return {
            'mae': mae_score,
            'rmse': rmse_score,
            'mape': mape_score,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall
        }

    def save_darts_model(self, coin_config, model_name=None):
        """保存Darts模型和配置"""
        if model_name is None:
            model_name = f"darts_{coin_config['model_basename']}"
        
        output_dir = get_output_dir()
        model_file = os.path.join(output_dir, f'{model_name}_darts_model.pkl')
        config_file = os.path.join(output_dir, f'{model_name}_darts_config.json')
        
        # 保存模型
        self.model.save(model_file)
        
        # 保存配置
        config = {
            'model_type': 'Darts_LightGBM',
            'model_name': model_name,
            'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'up_threshold': coin_config['up_threshold'],
            'down_threshold': coin_config['down_threshold'],
            'max_lookforward_minutes': coin_config['max_lookforward_minutes'],
            'timeframe_minutes': coin_config['timeframe_minutes'],
            'features': self.features,
            'best_threshold': self.best_threshold
        }
        
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"\nDarts模型已保存:")
        print(f"模型文件: {model_file}")
        print(f"配置文件: {config_file}")

def prepare_features_and_labels(df, coin_config, symbol='ETHUSDT', market='spot'):
    """准备特征和标签 - 与原版保持一致"""
    print("在完整数据集上计算增强特征...")
    df_with_features = calculate_features(df.copy(), timeframe=coin_config['timeframe_minutes'])
    
    print("创建目标标签...")
    trainer = DartsLGBMTrainer({})
    target_labels = trainer.create_percentage_target(
        df, 
        coin_config['up_threshold'], 
        coin_config['down_threshold'], 
        coin_config['max_lookforward_minutes'], 
        coin_config['timeframe_minutes']
    )
    
    print("合并特征与标签...")
    df_combined = df_with_features.join(target_labels.rename('label'), how='inner')
    df_clean = df_combined.dropna()
    print(f"清理NaN后剩余 {len(df_clean)} 条记录")
    return df_clean

def train_darts_model(args):
    """主训练函数 - Darts版本"""
    # 获取币种配置 - 使用父目录的配置文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    config_path = os.path.join(parent_dir, 'config.json')
    
    coin_config = get_coin_config(args.coin, config_path)
    if coin_config is None:
        print(f"❌ 无法获取币种 {args.coin} 的配置")
        return
    
    # 设置全局变量
    global TIMEFRAME_MINUTES, UP_THRESHOLD, DOWN_THRESHOLD, MAX_LOOKFORWARD_MINUTES, MODEL_BASENAME
    TIMEFRAME_MINUTES = coin_config['timeframe_minutes']
    UP_THRESHOLD = coin_config['up_threshold']
    DOWN_THRESHOLD = coin_config['down_threshold']
    MAX_LOOKFORWARD_MINUTES = coin_config['max_lookforward_minutes']
    MODEL_BASENAME = coin_config['model_basename']
    
    print(f"开始训练Darts LightGBM模型 - {MODEL_BASENAME.replace('_', ' ').title()}")
    
    # 加载数据 - 直接构建数据源配置
    try:
        from data_loader import create_data_provider
        
        # 直接构建数据源配置，避免重复调用 get_coin_config
        data_source = {
            'type': 'sqlite',
            'db_path': args.db_path or coin_config.get('db_path', 'coin_data.db'),
            'symbol': args.symbol or coin_config.get('api_symbol'),
            'interval': args.interval or f"{coin_config['timeframe_minutes']}m",
            'market': args.market
        }
        
        print(f"数据源配置: {data_source}")
        
        # 创建数据提供者
        price_multiplier = coin_config.get('price_multiplier', 1.0)
        data_provider = create_data_provider(data_source, price_multiplier)
        
        # 加载数据
        df = data_provider.get_initial_data(initial_count=1000000)
        if df is None or len(df) == 0:
            print("❌ 无法加载数据")
            return
        
        print(f"✅ 成功加载 {len(df)} 条数据")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 准备特征和标签
    df_clean = prepare_features_and_labels(df, coin_config, args.symbol, args.market)
    
    # 创建训练器
    trainer = DartsLGBMTrainer(args.__dict__)
    
    # 转换为Darts格式
    target_series, feature_series, feature_cols = trainer.prepare_darts_timeseries(df_clean)
    trainer.features = feature_cols
    
    # 分割数据
    (train_target, val_target, test_target), (train_features, val_features, test_features) = trainer.split_darts_data(
        target_series, feature_series
    )
    
    # 超参数优化
    best_params = None
    if not args.no_optimization:
        best_params, best_score = trainer.optimize_hyperparameters_darts(
            train_target, train_features, val_target, val_features, 
            n_trials=args.n_trials
        )
    
    # 训练模型
    model = trainer.train_darts_model(
        train_target, train_features, val_target, val_features, best_params
    )
    
    # 评估模型
    metrics = trainer.evaluate_darts_model(test_target, test_features)
    
    # 保存模型
    trainer.save_darts_model(coin_config)
    
    print(f"\n=== Darts训练完成 ===")
    print(f"币种: {coin_config['display_name']}")
    print(f"模型类型: Darts LightGBM")
    print(f"特征数量: {len(feature_cols)}")
    print(f"最终MAE: {metrics['mae']:.4f}")
    print(f"最终准确率: {metrics['accuracy']:.4f}")

def main():
    parser = argparse.ArgumentParser(description="Darts框架LightGBM模型训练器")
    parser.add_argument("--coin", default="ETH", help="币种名称 (如: DOT, SEI)")
    parser.add_argument("--db-path", default='coin_data.db', help="SQLite数据库路径")
    parser.add_argument("--symbol", help="交易对符号")
    parser.add_argument("--interval", help="时间间隔")
    parser.add_argument("--market", choices=['spot', 'futures'], default='spot', help="市场类型")
    parser.add_argument("--start-time", help="数据开始时间 (YYYY-MM-DD)")
    parser.add_argument("--end-time", help="数据结束时间 (YYYY-MM-DD)")
    parser.add_argument("--no-optimization", action='store_true', help="禁用超参数优化")
    parser.add_argument("--n-trials", type=int, default=50, help="Optuna优化试验次数")
    
    args = parser.parse_args()
    
    print(f"=== Darts LightGBM 模型训练器 ===")
    print(f"币种: {args.coin}")
    train_darts_model(args)

if __name__ == '__main__':
    main()