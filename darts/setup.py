"""
Darts LightGBM 训练器安装和设置脚本
"""

import subprocess
import sys
import os

def install_dependencies():
    """安装必要的依赖包"""
    print("=== 安装 Darts LightGBM 依赖包 ===")
    
    dependencies = [
        'darts[lightgbm]',
        'optuna',
        'pandas',
        'numpy',
        'scikit-learn',
        'joblib',
        'matplotlib',
        'seaborn'
    ]
    
    for package in dependencies:
        print(f"安装 {package}...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    print("✅ 所有依赖包安装完成")
    return True

def check_dependencies():
    """检查依赖包是否正确安装"""
    print("=== 检查依赖包 ===")
    
    required_packages = {
        'darts': 'darts',
        'optuna': 'optuna', 
        'pandas': 'pd',
        'numpy': 'np',
        'sklearn': 'sklearn',
        'joblib': 'joblib',
        'lightgbm': 'lgb'
    }
    
    missing_packages = []
    
    for package_name, import_name in required_packages.items():
        try:
            if import_name == 'sklearn':
                import sklearn
            elif import_name == 'pd':
                import pandas as pd
            elif import_name == 'np':
                import numpy as np
            elif import_name == 'lgb':
                import lightgbm as lgb
            else:
                __import__(import_name)
            print(f"✅ {package_name} 可用")
        except ImportError:
            print(f"❌ {package_name} 未安装或不可用")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n缺少以下包: {', '.join(missing_packages)}")
        return False
    else:
        print("\n✅ 所有依赖包检查通过")
        return True

def create_test_script():
    """创建测试脚本"""
    print("=== 创建测试脚本 ===")
    
    test_script = """#!/usr/bin/env python3
\"\"\"
Darts LightGBM 快速测试脚本
\"\"\"

import sys
import os

def test_imports():
    \"\"\"测试导入\"\"\"
    print("测试导入...")
    try:
        import darts
        import optuna
        import pandas as pd
        import numpy as np
        import lightgbm as lgb
        from sklearn.metrics import accuracy_score
        print("✅ 所有导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_darts_basic():
    \"\"\"测试 Darts 基本功能\"\"\"
    print("测试 Darts 基本功能...")
    try:
        from darts import TimeSeries
        from darts.models import LightGBMModel
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        values = np.random.randn(100).cumsum()
        ts = TimeSeries.from_dataframe(
            pd.DataFrame({'date': dates, 'value': values}),
            time_col='date',
            value_cols=['value']
        )
        
        # 测试模型创建
        model = LightGBMModel(lags=5, output_chunk_length=1)
        print("✅ Darts 基本功能测试通过")
        return True
    except Exception as e:
        print(f"❌ Darts 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=== Darts LightGBM 环境测试 ===")
    
    success = True
    success &= test_imports()
    success &= test_darts_basic()
    
    if success:
        print("\\n✅ 所有测试通过，环境配置正确")
    else:
        print("\\n❌ 测试失败，请检查环境配置")
        sys.exit(1)
"""
    
    with open('test_environment.py', 'w') as f:
        f.write(test_script)
    
    print("✅ 测试脚本已创建: test_environment.py")

def setup_directory_structure():
    """设置目录结构"""
    print("=== 设置目录结构 ===")
    
    directories = [
        'models',
        'logs',
        'results',
        'configs'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"📁 目录已存在: {directory}")

def main():
    """主安装流程"""
    print("Darts LightGBM 训练器安装程序")
    print("="*50)
    
    # 检查 Python 版本
    if sys.version_info < (3, 7):
        print("❌ 需要 Python 3.7 或更高版本")
        sys.exit(1)
    
    print(f"✅ Python 版本: {sys.version}")
    
    # 设置目录结构
    setup_directory_structure()
    
    # 检查现有依赖
    if not check_dependencies():
        print("\n是否安装缺少的依赖包? (y/n): ", end="")
        if input().lower().startswith('y'):
            if not install_dependencies():
                print("❌ 依赖安装失败")
                sys.exit(1)
        else:
            print("⚠️ 跳过依赖安装，可能影响功能")
    
    # 创建测试脚本
    create_test_script()
    
    # 最终检查
    print("\n" + "="*50)
    print("=== 最终环境检查 ===")
    
    if check_dependencies():
        print("\n✅ 安装完成！")
        print("\n下一步:")
        print("1. 运行环境测试: python test_environment.py")
        print("2. 查看使用示例: python example_usage.py")
        print("3. 开始训练: python darts_lgbm_trainer.py --coin ETH")
        print("4. 性能比较: python compare_with_original.py")
    else:
        print("\n❌ 环境配置不完整，请手动安装缺少的依赖")

if __name__ == "__main__":
    main()