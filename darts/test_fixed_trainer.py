#!/usr/bin/env python3
"""
测试修复后的分类训练器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from darts_classification_trainer_fixed import train_fixed_model
import argparse

def test_fixed_trainer():
    """测试修复后的训练器"""
    print("=== 测试修复后的分类训练器 ===")
    
    # 创建测试参数
    class TestArgs:
        coin = "ETH"
        db_path = None
    
    args = TestArgs()
    
    try:
        # 运行训练
        train_fixed_model(args)
        print("✅ 训练完成，没有错误")
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_fixed_trainer()