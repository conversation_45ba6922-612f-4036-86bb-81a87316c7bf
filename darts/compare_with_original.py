"""
比较 Darts LightGBM 和原版 trainopt2.py 的性能
"""

import sys
import os
import time
import pandas as pd
import numpy as np
from datetime import datetime
import json

# 添加父目录到路径
sys.path.append('..')

def run_original_training(coin="ETH", quick_mode=True):
    """运行原版 trainopt2.py 训练"""
    print("=== 运行原版 trainopt2.py 训练 ===")
    
    try:
        # 导入原版训练函数
        from trainopt2 import train_model, get_coin_config
        
        # 模拟命令行参数
        class Args:
            def __init__(self):
                self.coin = coin
                self.mode = 'train'
                self.db_path = 'coin_data.db'
                self.symbol = None
                self.interval = None
                self.market = 'spot'
                self.start_time = None
                self.end_time = None
                self.save_data = False
                self.load_data = False
                self.data_file = None
                self.no_time_series_cv = quick_mode  # 快速模式禁用CV
                self.cv_splits = 3 if not quick_mode else 5
                self.cv_trials = 10 if quick_mode else 50
                self.optimize_features = False
                self.top_n = 50
                self.corr_threshold = 0.9
        
        args = Args()
        
        # 记录开始时间
        start_time = time.time()
        
        # 运行训练
        train_model(args)
        
        # 记录结束时间
        end_time = time.time()
        training_time = end_time - start_time
        
        print(f"✅ 原版训练完成，耗时: {training_time:.2f}秒")
        return {
            'success': True,
            'training_time': training_time,
            'model_type': 'Original_LightGBM'
        }
        
    except Exception as e:
        print(f"❌ 原版训练失败: {e}")
        return {
            'success': False,
            'error': str(e),
            'model_type': 'Original_LightGBM'
        }

def run_darts_training(coin="ETH", quick_mode=True):
    """运行 Darts LightGBM 训练"""
    print("=== 运行 Darts LightGBM 训练 ===")
    
    try:
        from darts_lgbm_trainer import train_darts_model
        
        # 模拟命令行参数
        class Args:
            def __init__(self):
                self.coin = coin
                self.db_path = 'coin_data.db'
                self.symbol = None
                self.interval = None
                self.market = 'spot'
                self.start_time = None
                self.end_time = None
                self.no_optimization = quick_mode  # 快速模式禁用优化
                self.n_trials = 10 if quick_mode else 50
        
        args = Args()
        
        # 记录开始时间
        start_time = time.time()
        
        # 运行训练
        train_darts_model(args)
        
        # 记录结束时间
        end_time = time.time()
        training_time = end_time - start_time
        
        print(f"✅ Darts训练完成，耗时: {training_time:.2f}秒")
        return {
            'success': True,
            'training_time': training_time,
            'model_type': 'Darts_LightGBM'
        }
        
    except Exception as e:
        print(f"❌ Darts训练失败: {e}")
        return {
            'success': False,
            'error': str(e),
            'model_type': 'Darts_LightGBM'
        }

def compare_models(coin="ETH", quick_mode=True):
    """比较两个模型的性能"""
    print(f"=== 开始比较 {coin} 模型性能 ===")
    print(f"快速模式: {'开启' if quick_mode else '关闭'}")
    
    results = []
    
    # 运行原版训练
    print("\n" + "="*50)
    original_result = run_original_training(coin, quick_mode)
    results.append(original_result)
    
    # 运行 Darts 训练
    print("\n" + "="*50)
    darts_result = run_darts_training(coin, quick_mode)
    results.append(darts_result)
    
    # 生成比较报告
    print("\n" + "="*50)
    print("=== 性能比较报告 ===")
    
    comparison_data = []
    for result in results:
        if result['success']:
            comparison_data.append({
                'Model': result['model_type'],
                'Success': '✅',
                'Training_Time(s)': f"{result['training_time']:.2f}",
                'Status': 'Completed'
            })
        else:
            comparison_data.append({
                'Model': result['model_type'],
                'Success': '❌',
                'Training_Time(s)': 'N/A',
                'Status': f"Failed: {result['error'][:50]}..."
            })
    
    # 创建比较表格
    df_comparison = pd.DataFrame(comparison_data)
    print(df_comparison.to_string(index=False))
    
    # 保存比较结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f'comparison_results_{coin}_{timestamp}.json'
    
    comparison_summary = {
        'timestamp': timestamp,
        'coin': coin,
        'quick_mode': quick_mode,
        'results': results,
        'summary': {
            'original_success': original_result['success'],
            'darts_success': darts_result['success'],
            'time_difference': None
        }
    }
    
    # 计算时间差异
    if original_result['success'] and darts_result['success']:
        time_diff = darts_result['training_time'] - original_result['training_time']
        comparison_summary['summary']['time_difference'] = time_diff
        print(f"\n时间差异: Darts比原版{'快' if time_diff < 0 else '慢'} {abs(time_diff):.2f}秒")
    
    # 保存结果
    with open(results_file, 'w') as f:
        json.dump(comparison_summary, f, indent=2)
    
    print(f"\n比较结果已保存到: {results_file}")
    return comparison_summary

def benchmark_multiple_coins():
    """对多个币种进行基准测试"""
    coins = ['ETH', 'BTC']  # 可以添加更多币种
    
    print("=== 多币种基准测试 ===")
    
    all_results = {}
    
    for coin in coins:
        print(f"\n{'='*20} 测试 {coin} {'='*20}")
        try:
            result = compare_models(coin, quick_mode=True)
            all_results[coin] = result
        except Exception as e:
            print(f"❌ {coin} 测试失败: {e}")
            all_results[coin] = {'error': str(e)}
    
    # 生成总结报告
    print("\n" + "="*50)
    print("=== 多币种测试总结 ===")
    
    for coin, result in all_results.items():
        if 'error' in result:
            print(f"{coin}: ❌ 失败 - {result['error']}")
        else:
            original_success = result['summary']['original_success']
            darts_success = result['summary']['darts_success']
            print(f"{coin}: 原版{'✅' if original_success else '❌'} | Darts{'✅' if darts_success else '❌'}")
    
    # 保存总结
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    summary_file = f'benchmark_summary_{timestamp}.json'
    with open(summary_file, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\n基准测试结果已保存到: {summary_file}")

if __name__ == "__main__":
    print("Darts vs Original LightGBM 性能比较工具")
    print("请选择测试模式:")
    print("1. 单币种比较 (ETH)")
    print("2. 单币种比较 (自定义)")
    print("3. 多币种基准测试")
    print("4. 快速测试 (ETH, 禁用优化)")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == "1":
        compare_models("ETH", quick_mode=False)
    elif choice == "2":
        coin = input("请输入币种名称 (如 BTC, ETH): ").strip().upper()
        compare_models(coin, quick_mode=False)
    elif choice == "3":
        benchmark_multiple_coins()
    elif choice == "4":
        compare_models("ETH", quick_mode=True)
    else:
        print("无效选择，运行快速测试...")
        compare_models("ETH", quick_mode=True)