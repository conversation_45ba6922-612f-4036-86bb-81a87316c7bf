# Darts 详细回测指南

## 概述

`backtest_darts_detailed.py` 是基于 `backtest_money_quick.py` 开发的专门用于 Darts 模型的详细回测脚本。它支持完整的资金管理、止损、SuperTrend过滤、反向平仓、挂单买入等高级功能。

## 主要特性

### 🎯 核心功能
- **完整资金管理**: 基于风险比例的仓位管理
- **Darts模型适配**: 专门适配 `darts_classification_trainer_fixed.py` 生成的模型
- **滞后特征支持**: 与训练时保持一致的特征工程
- **安全特征集**: 避免数据泄露的特征选择

### 🛡️ 风险控制
- **止损机制**: 可配置的止损百分比
- **SuperTrend过滤**: 趋势过滤，看涨时只做多，看跌时只做空
- **反向平仓**: 当信号反转时自动平仓
- **挂单买入**: 期望更好价格的限价单功能

### 📊 分析功能
- **详细性能指标**: 夏普比率、Sortino比率、Calmar比率、最大回撤等
- **时间统计**: 按小时、星期分析交易表现
- **可视化分析**: 自动生成图表和报告

## 使用方法

### 1. 基础回测

```bash
# 使用默认参数回测ETH 5分钟数据
cd darts
python backtest_darts_detailed.py --coin ETH
```

### 2. 指定时间范围

```bash
# 回测指定时间段
python backtest_darts_detailed.py \
    --coin ETH \
    --start-time "2024-01-01" \
    --end-time "2024-12-31"
```

### 3. 启用止损

```bash
# 启用2.5%止损
python backtest_darts_detailed.py \
    --coin ETH \
    --stop-loss 2.5
```

### 4. 启用SuperTrend过滤

```bash
# 启用SuperTrend过滤，使用30分钟周期
python backtest_darts_detailed.py \
    --coin ETH \
    --use-supertrend \
    --supertrend-interval 30m \
    --supertrend-atr-period 13 \
    --supertrend-multiplier 3.8
```

### 5. 启用反向平仓

```bash
# 启用反向平仓，当有1笔以上同向仓位时触发
python backtest_darts_detailed.py \
    --coin ETH \
    --enable-reverse-close \
    --reverse-close-min-positions 1
```

### 6. 启用挂单买入

```bash
# 启用挂单买入，期望0.1%更好的价格
python backtest_darts_detailed.py \
    --coin ETH \
    --better-price-pct 0.1
```

### 7. 启用时间过滤

```bash
# 仅在高胜率时间段交易
python backtest_darts_detailed.py \
    --coin ETH \
    --use-chushou \
    --chushou-file ../chushou.json
```

### 8. 完整配置示例

```bash
# 使用所有高级功能的完整回测
python backtest_darts_detailed.py \
    --coin ETH \
    --interval 5m \
    --start-time "2024-06-01" \
    --end-time "2024-12-31" \
    --initial-capital 10000 \
    --risk-per-trade 2.0 \
    --stop-loss 2.5 \
    --max-active-predictions 50 \
    --use-supertrend \
    --supertrend-interval 30m \
    --supertrend-atr-period 13 \
    --supertrend-multiplier 3.8 \
    --enable-reverse-close \
    --reverse-close-min-positions 2 \
    --better-price-pct 0.1 \
    --use-chushou \
    --chushou-file ../chushou.json
```

## 参数说明

### 基础参数
- `--coin`: 币种 (ETH, BTC)
- `--interval`: K线间隔 (5m, 15m, 1h等)
- `--market`: 市场类型 (spot, futures)
- `--db`: 数据库路径
- `--start-time`: 回测开始时间 (北京时间)
- `--end-time`: 回测结束时间 (北京时间)

### 资金管理
- `--initial-capital`: 初始资金 (默认: 1000)
- `--risk-per-trade`: 单次交易风险比例% (默认: 1.0)
- `--max-active-predictions`: 最大同时活跃预测数 (默认: 1000)

### 风险控制
- `--stop-loss`: 止损百分比
- `--use-supertrend`: 启用SuperTrend过滤
- `--supertrend-interval`: SuperTrend计算周期 (默认: 30m)
- `--supertrend-atr-period`: ATR周期 (默认: 13)
- `--supertrend-multiplier`: ATR倍数 (默认: 3.8)

### 高级功能
- `--enable-reverse-close`: 启用反向平仓
- `--reverse-close-min-positions`: 反向平仓最小仓位数 (默认: 1)
- `--better-price-pct`: 挂单期望更好价格百分比
- `--use-chushou`: 启用时间过滤
- `--chushou-file`: 时间过滤配置文件

### 模型文件
- `--model-file`: 指定模型文件路径
- `--config-file`: 指定配置文件路径

## 输出文件

### 1. 详细交易日志
- `darts_backtest_money_log_detailed.csv`: 包含每笔交易的详细信息

### 2. 性能指标
- `darts_backtest_performance_metrics.csv`: 详细的性能指标

### 3. 可视化图表
- 自动生成的分析图表和报告

## 性能指标说明

### 基础指标
- **总收益率**: 整体投资回报率
- **胜率**: 成功交易占总交易的比例
- **最大回撤**: 资金曲线的最大下跌幅度

### 风险调整指标
- **夏普比率**: 风险调整后的收益率
- **Sortino比率**: 仅考虑下行风险的收益率
- **Calmar比率**: 年化收益率与最大回撤的比值

### 交易统计
- **平均盈利**: 盈利交易的平均金额
- **平均亏损**: 亏损交易的平均金额
- **盈亏比**: 总盈利与总亏损的比值

## 注意事项

### 1. 模型要求
- 必须使用 `darts_classification_trainer_fixed.py` 训练的模型
- 模型文件和配置文件必须匹配

### 2. 数据要求
- 需要足够的历史数据用于特征计算
- 建议至少有1000条以上的K线数据

### 3. 特征一致性
- 回测时的特征计算与训练时保持完全一致
- 自动处理滞后特征和安全特征集

### 4. 性能考虑
- 预计算特征模式，回测速度较快
- 大数据集可能需要较长时间

## 故障排除

### 常见问题

1. **模型文件不存在**
   - 确保已使用 `darts_classification_trainer_fixed.py` 训练模型
   - 检查文件路径是否正确

2. **特征数量不匹配**
   - 确保使用相同的特征工程方法
   - 检查模型配置文件

3. **数据不足**
   - 增加历史数据量
   - 调整开始时间

4. **内存不足**
   - 减少数据量或时间范围
   - 调整预测窗口大小

## 最佳实践

1. **参数调优**: 先用小数据集测试参数
2. **风险控制**: 合理设置止损和仓位大小
3. **时间过滤**: 使用历史胜率数据优化交易时间
4. **趋势过滤**: 结合SuperTrend提高胜率
5. **分批测试**: 分不同时间段验证策略稳定性
