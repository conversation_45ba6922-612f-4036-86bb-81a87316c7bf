#!/usr/bin/env python3
"""
Darts回测结果分析脚本
分析回测结果并生成可视化图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import argparse
import os

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

def load_results(csv_file):
    """加载回测结果"""
    if not os.path.exists(csv_file):
        print(f"❌ 结果文件不存在: {csv_file}")
        return None
    
    df = pd.read_csv(csv_file)
    
    # 转换时间列
    df['StartTimestamp'] = pd.to_datetime(df['StartTimestamp'].str.replace(' UTC\\+8', '', regex=True))
    df['EndTimestamp'] = pd.to_datetime(df['EndTimestamp'].str.replace(' UTC\\+8', '', regex=True))
    
    print(f"✅ 加载 {len(df)} 条回测记录")
    return df

def calculate_performance_metrics(df):
    """计算详细的性能指标"""
    if df.empty:
        return {}
    
    metrics = {}
    
    # 基础统计
    total_trades = len(df)
    winning_trades = len(df[df['Result'] == 1])
    losing_trades = len(df[df['Result'] == 0])
    timeout_trades = len(df[df['Result'] == -1])
    stop_loss_trades = len(df[df['Result'] == -2])
    
    metrics['total_trades'] = total_trades
    metrics['winning_trades'] = winning_trades
    metrics['losing_trades'] = losing_trades
    metrics['timeout_trades'] = timeout_trades
    metrics['stop_loss_trades'] = stop_loss_trades
    
    # 胜率
    if total_trades > 0:
        metrics['win_rate'] = winning_trades / total_trades * 100
        metrics['loss_rate'] = losing_trades / total_trades * 100
        metrics['timeout_rate'] = timeout_trades / total_trades * 100
        metrics['stop_loss_rate'] = stop_loss_trades / total_trades * 100
    
    # 资金曲线
    initial_capital = df.iloc[0]['CapitalAfter'] - df.iloc[0]['ProfitLoss']
    final_capital = df.iloc[-1]['CapitalAfter']
    total_return = (final_capital - initial_capital) / initial_capital * 100
    
    metrics['initial_capital'] = initial_capital
    metrics['final_capital'] = final_capital
    metrics['total_return'] = total_return
    
    # 盈亏统计
    profits = df[df['ProfitLoss'] > 0]['ProfitLoss']
    losses = df[df['ProfitLoss'] < 0]['ProfitLoss']
    
    if len(profits) > 0:
        metrics['avg_profit'] = profits.mean()
        metrics['max_profit'] = profits.max()
        metrics['total_profits'] = profits.sum()
    else:
        metrics['avg_profit'] = 0
        metrics['max_profit'] = 0
        metrics['total_profits'] = 0
    
    if len(losses) > 0:
        metrics['avg_loss'] = losses.mean()
        metrics['max_loss'] = losses.min()
        metrics['total_losses'] = losses.sum()
    else:
        metrics['avg_loss'] = 0
        metrics['max_loss'] = 0
        metrics['total_losses'] = 0
    
    # 盈亏比
    if metrics['avg_loss'] != 0:
        metrics['profit_loss_ratio'] = abs(metrics['avg_profit'] / metrics['avg_loss'])
    else:
        metrics['profit_loss_ratio'] = float('inf')
    
    # 最大回撤
    capital_series = df['CapitalAfter'].values
    rolling_max = np.maximum.accumulate(capital_series)
    drawdown = (capital_series - rolling_max) / rolling_max * 100
    metrics['max_drawdown'] = np.min(drawdown)
    
    # 夏普比率（简化版）
    returns = df['ProfitLoss'] / (df['CapitalAfter'] - df['ProfitLoss'])
    if len(returns) > 1 and returns.std() > 0:
        metrics['sharpe_ratio'] = returns.mean() / returns.std() * np.sqrt(len(returns))
    else:
        metrics['sharpe_ratio'] = 0
    
    # 交易持续时间统计
    metrics['avg_duration'] = df['DurationMinutes'].mean()
    metrics['max_duration'] = df['DurationMinutes'].max()
    metrics['min_duration'] = df['DurationMinutes'].min()
    
    return metrics

def print_performance_summary(metrics):
    """打印性能摘要"""
    print("\n" + "="*50)
    print("           DARTS回测结果分析")
    print("="*50)
    
    print(f"\n📊 交易统计:")
    print(f"  总交易数: {metrics['total_trades']}")
    print(f"  盈利交易: {metrics['winning_trades']} ({metrics.get('win_rate', 0):.2f}%)")
    print(f"  亏损交易: {metrics['losing_trades']} ({metrics.get('loss_rate', 0):.2f}%)")
    print(f"  超时交易: {metrics['timeout_trades']} ({metrics.get('timeout_rate', 0):.2f}%)")
    if metrics.get('stop_loss_trades', 0) > 0:
        print(f"  止损交易: {metrics['stop_loss_trades']} ({metrics.get('stop_loss_rate', 0):.2f}%)")
    
    print(f"\n💰 资金表现:")
    print(f"  初始资金: ${metrics['initial_capital']:,.2f}")
    print(f"  最终资金: ${metrics['final_capital']:,.2f}")
    print(f"  总收益率: {metrics['total_return']:+.2f}%")
    print(f"  最大回撤: {metrics['max_drawdown']:.2f}%")
    
    print(f"\n📈 盈亏分析:")
    print(f"  平均盈利: ${metrics['avg_profit']:+.2f}")
    print(f"  平均亏损: ${metrics['avg_loss']:+.2f}")
    print(f"  最大盈利: ${metrics['max_profit']:+.2f}")
    print(f"  最大亏损: ${metrics['max_loss']:+.2f}")
    if metrics['profit_loss_ratio'] != float('inf'):
        print(f"  盈亏比: {metrics['profit_loss_ratio']:.2f}")
    else:
        print(f"  盈亏比: ∞ (无亏损)")
    
    print(f"\n⏱️ 时间分析:")
    print(f"  平均持仓: {metrics['avg_duration']:.1f} 分钟")
    print(f"  最长持仓: {metrics['max_duration']:.0f} 分钟")
    print(f"  最短持仓: {metrics['min_duration']:.0f} 分钟")
    
    print(f"\n📊 风险指标:")
    print(f"  夏普比率: {metrics['sharpe_ratio']:.3f}")

def create_visualizations(df, output_dir="darts_analysis"):
    """创建可视化图表"""
    if df.empty:
        print("❌ 无数据可视化")
        return
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置图表样式
    plt.style.use('seaborn-v0_8')
    
    # 1. 资金曲线图
    plt.figure(figsize=(12, 6))
    plt.plot(range(len(df)), df['CapitalAfter'], linewidth=2, color='blue')
    plt.title('Darts模型资金曲线', fontsize=16, fontweight='bold')
    plt.xlabel('交易序号')
    plt.ylabel('资金 ($)')
    plt.grid(True, alpha=0.3)
    
    # 添加收益率标注
    initial_capital = df.iloc[0]['CapitalAfter'] - df.iloc[0]['ProfitLoss']
    final_capital = df.iloc[-1]['CapitalAfter']
    total_return = (final_capital - initial_capital) / initial_capital * 100
    
    plt.text(0.02, 0.98, f'总收益率: {total_return:+.2f}%', 
             transform=plt.gca().transAxes, fontsize=12, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/capital_curve.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 盈亏分布图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 盈亏直方图
    profits = df[df['ProfitLoss'] > 0]['ProfitLoss']
    losses = df[df['ProfitLoss'] < 0]['ProfitLoss']
    
    ax1.hist(profits, bins=20, alpha=0.7, color='green', label=f'盈利 ({len(profits)}笔)')
    ax1.hist(losses, bins=20, alpha=0.7, color='red', label=f'亏损 ({len(losses)}笔)')
    ax1.set_title('盈亏分布', fontweight='bold')
    ax1.set_xlabel('盈亏金额 ($)')
    ax1.set_ylabel('频次')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 累计盈亏图
    cumulative_pnl = df['ProfitLoss'].cumsum()
    ax2.plot(range(len(df)), cumulative_pnl, linewidth=2, color='purple')
    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax2.set_title('累计盈亏', fontweight='bold')
    ax2.set_xlabel('交易序号')
    ax2.set_ylabel('累计盈亏 ($)')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/pnl_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 交易结果饼图
    plt.figure(figsize=(10, 8))
    
    result_counts = df['Result'].value_counts()
    result_labels = []
    result_values = []
    colors = []
    
    for result, count in result_counts.items():
        if result == 1:
            result_labels.append(f'成功 ({count})')
            colors.append('green')
        elif result == 0:
            result_labels.append(f'失败 ({count})')
            colors.append('red')
        elif result == -1:
            result_labels.append(f'超时 ({count})')
            colors.append('orange')
        elif result == -2:
            result_labels.append(f'止损 ({count})')
            colors.append('purple')
        result_values.append(count)
    
    plt.pie(result_values, labels=result_labels, colors=colors, autopct='%1.1f%%', startangle=90)
    plt.title('交易结果分布', fontsize=16, fontweight='bold')
    plt.axis('equal')
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/result_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. 信心度与结果关系
    plt.figure(figsize=(12, 6))
    
    # 按结果分组绘制信心度分布
    for result in df['Result'].unique():
        subset = df[df['Result'] == result]
        if result == 1:
            label, color = '成功', 'green'
        elif result == 0:
            label, color = '失败', 'red'
        elif result == -1:
            label, color = '超时', 'orange'
        elif result == -2:
            label, color = '止损', 'purple'
        else:
            label, color = f'结果{result}', 'gray'
        
        plt.scatter(subset.index, subset['Confidence'], 
                   alpha=0.6, label=f'{label} ({len(subset)})', color=color)
    
    plt.title('模型信心度与交易结果', fontsize=16, fontweight='bold')
    plt.xlabel('交易序号')
    plt.ylabel('模型信心度')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/confidence_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 5. 时间分析
    if 'StartTimestamp' in df.columns:
        df['Hour'] = df['StartTimestamp'].dt.hour
        df['DayOfWeek'] = df['StartTimestamp'].dt.dayofweek
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 按小时统计
        hourly_stats = df.groupby('Hour').agg({
            'ProfitLoss': ['count', 'mean', 'sum']
        }).round(2)
        
        ax1.bar(hourly_stats.index, hourly_stats[('ProfitLoss', 'count')], 
                alpha=0.7, color='skyblue')
        ax1.set_title('按小时交易分布', fontweight='bold')
        ax1.set_xlabel('小时')
        ax1.set_ylabel('交易次数')
        ax1.grid(True, alpha=0.3)
        
        # 按星期统计
        weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        daily_stats = df.groupby('DayOfWeek').agg({
            'ProfitLoss': ['count', 'mean', 'sum']
        }).round(2)
        
        ax2.bar(range(len(daily_stats)), daily_stats[('ProfitLoss', 'count')], 
                alpha=0.7, color='lightcoral')
        ax2.set_title('按星期交易分布', fontweight='bold')
        ax2.set_xlabel('星期')
        ax2.set_ylabel('交易次数')
        ax2.set_xticks(range(len(weekday_names)))
        ax2.set_xticklabels(weekday_names)
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{output_dir}/time_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    print(f"📊 图表已保存到 {output_dir}/ 目录")

def analyze_trading_patterns(df):
    """分析交易模式"""
    print(f"\n📋 交易模式分析:")
    
    # 预测方向分析
    if 'Prediction' in df.columns:
        pred_stats = df.groupby('Prediction').agg({
            'Result': lambda x: (x == 1).sum(),
            'ProfitLoss': ['count', 'mean', 'sum']
        }).round(2)
        
        print(f"\n按预测方向统计:")
        for pred in pred_stats.index:
            direction = "看涨" if pred == 1 else "看跌"
            total = pred_stats.loc[pred, ('ProfitLoss', 'count')]
            wins = pred_stats.loc[pred, 'Result']
            avg_pnl = pred_stats.loc[pred, ('ProfitLoss', 'mean')]
            win_rate = wins / total * 100 if total > 0 else 0
            
            print(f"  {direction}: {total}笔, 胜率{win_rate:.1f}%, 平均盈亏${avg_pnl:+.2f}")
    
    # 信心度区间分析
    if 'Confidence' in df.columns:
        df['ConfidenceRange'] = pd.cut(df['Confidence'], 
                                     bins=[0, 0.6, 0.7, 0.8, 0.9, 1.0], 
                                     labels=['0.5-0.6', '0.6-0.7', '0.7-0.8', '0.8-0.9', '0.9-1.0'])
        
        conf_stats = df.groupby('ConfidenceRange').agg({
            'Result': lambda x: (x == 1).sum(),
            'ProfitLoss': ['count', 'mean']
        }).round(2)
        
        print(f"\n按信心度区间统计:")
        for conf_range in conf_stats.index:
            if pd.isna(conf_range):
                continue
            total = conf_stats.loc[conf_range, ('ProfitLoss', 'count')]
            wins = conf_stats.loc[conf_range, 'Result']
            avg_pnl = conf_stats.loc[conf_range, ('ProfitLoss', 'mean')]
            win_rate = wins / total * 100 if total > 0 else 0
            
            print(f"  {conf_range}: {total}笔, 胜率{win_rate:.1f}%, 平均盈亏${avg_pnl:+.2f}")

def main():
    parser = argparse.ArgumentParser(description="分析Darts回测结果")
    parser.add_argument("--csv-file", default="darts_backtest_results.csv", 
                       help="回测结果CSV文件路径")
    parser.add_argument("--output-dir", default="darts_analysis", 
                       help="输出目录")
    parser.add_argument("--no-plots", action='store_true', 
                       help="不生成图表")
    
    args = parser.parse_args()
    
    # 加载结果
    df = load_results(args.csv_file)
    if df is None:
        return
    
    # 计算性能指标
    metrics = calculate_performance_metrics(df)
    
    # 打印摘要
    print_performance_summary(metrics)
    
    # 分析交易模式
    analyze_trading_patterns(df)
    
    # 生成图表
    if not args.no_plots:
        create_visualizations(df, args.output_dir)
    
    # 保存详细指标
    metrics_df = pd.DataFrame([metrics])
    metrics_file = os.path.join(args.output_dir, 'performance_metrics.csv')
    os.makedirs(args.output_dir, exist_ok=True)
    metrics_df.to_csv(metrics_file, index=False)
    print(f"\n📈 详细指标已保存到: {metrics_file}")

if __name__ == '__main__':
    main()