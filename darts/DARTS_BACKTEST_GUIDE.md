# Darts模型回测指南

## 概述

这个回测系统专门为darts分类模型设计，支持滞后特征和安全特征集，确保与训练时的特征工程保持一致。

## 文件结构

```
darts/
├── darts_classification_trainer_fixed.py  # 修复版训练器
├── backtest_darts_model.py               # 专用回测脚本
├── run_darts_backtest.py                 # 简化运行脚本
├── test_results_darts_fixed_eth_5m.csv   # 训练时的测试结果
└── DARTS_BACKTEST_GUIDE.md              # 本指南
```

## 快速开始

### 1. 训练模型

首先训练一个darts模型：

```bash
cd darts
python darts_classification_trainer_fixed.py --coin ETH --db-path ../coin_data.db
```

这会生成：
- `darts_fixed_eth_5m_model.joblib` - 模型文件
- `darts_fixed_eth_5m_config.json` - 配置文件

### 2. 运行回测

使用简化脚本：

```bash
python run_darts_backtest.py
```

或使用完整参数：

```bash
python backtest_darts_model.py \
    --model-file darts_fixed_eth_5m_model.joblib \
    --config-file darts_fixed_eth_5m_config.json \
    --db ../coin_data.db \
    --coin ETHUSDT \
    --interval 5m \
    --market spot \
    --initial-capital 1000 \
    --risk-per-trade 1.0 \
    --stop-loss 2.0 \
    --max-active 5 \
    --start-time "2024-01-01" \
    --end-time "2024-02-01"
```

## 参数说明

### 必需参数
- `--model-file`: 模型文件路径 (.joblib)
- `--config-file`: 配置文件路径 (.json)

### 数据参数
- `--db`: SQLite数据库路径 (默认: ../coin_data.db)
- `--coin`: 交易对 (默认: ETHUSDT)
- `--interval`: K线间隔 (默认: 5m)
- `--market`: 市场类型 (默认: spot)
- `--start-time`: 回测开始时间 (北京时间)
- `--end-time`: 回测结束时间 (北京时间)

### 交易参数
- `--initial-capital`: 初始资金 (默认: 1000)
- `--risk-per-trade`: 单次交易风险比例% (默认: 1.0)
- `--stop-loss`: 止损百分比 (可选)
- `--max-active`: 最大同时活跃预测数 (默认: 10)

### 技术参数
- `--window-size`: 预测窗口大小 (默认: 1000)
- `--lags`: 滞后特征数量 (默认: 5)

## 特征工程一致性

回测系统确保与训练时的特征工程完全一致：

### 1. 安全特征集
- 移除高相关性特征：`open`, `high`, `low`, `close`
- 移除长期移动平均：`sma_120`, `sma_360`, `sma_720`
- 移除高相关VWAP：`vwap_360`

### 2. 滞后特征
- 使用与训练时相同的滞后窗口
- 只对前5个安全特征创建滞后
- 组合当前特征 + 滞后特征

### 3. 特征计算
- 支持外部`model_utils_815.calculate_features`
- 内置安全的`calculate_basic_features`作为备选

## 输出结果

### 控制台输出
```
=== 开始Darts模型回测 ===
Darts回测器初始化完成
模型阈值: 0.650
最大等待: 240分钟
初始资金: $1,000.00
单次风险比例: 1.00%

从索引 1000 开始预测
时间: 2024-01-15 10:30:00 UTC+8
最多同时保持 5 笔活跃投资

[2024-01-15 10:35:00 UTC+8] 新预测: 先涨2.0%, 信心: 0.678, 价格: 2456.7800, 风险暴露: $10.00
[2024-01-15 14:20:00 UTC+8] 预测完成: 先涨... -> 成功✅, 得分: +0.85, 盈亏: $+8.50, 当前资金: $1,008.50
```

### CSV结果文件
生成 `darts_backtest_results.csv` 包含：

| 字段 | 说明 |
|------|------|
| PredictionID | 预测唯一标识 |
| StartTimestamp | 开始时间 |
| EndTimestamp | 结束时间 |
| StartPrice | 开始价格 |
| EndPrice | 结束价格 |
| PriceChangePct | 价格变化百分比 |
| MaxLossPct | 最大亏损百分比 |
| MaxProfitPct | 最大盈利百分比 |
| Confidence | 模型信心度 |
| Prediction | 预测方向 (0=跌, 1=涨) |
| Result | 结果 (1=成功, 0=失败, -1=超时, -2=止损) |
| Score | 得分 |
| ProfitLoss | 盈亏金额 |
| CapitalAfter | 交易后资金 |
| Status | 状态描述 |
| Reason | 完成原因 |

## 与原版backtest_money_quick.py的区别

### 1. 特征工程适配
- **原版**: 使用完整特征集，可能包含数据泄露
- **Darts版**: 使用安全特征集 + 滞后特征，避免数据泄露

### 2. 预测方法
- **原版**: 直接使用`feature_list`中的特征
- **Darts版**: 重新构建滞后特征，确保与训练时一致

### 3. 模型兼容性
- **原版**: 适用于传统LightGBM模型
- **Darts版**: 专门适配darts分类模型的特殊结构

## 性能优化建议

### 1. 窗口大小调整
```bash
# 快速回测（较小窗口）
--window-size 500

# 精确回测（较大窗口）
--window-size 1500
```

### 2. 滞后特征优化
```bash
# 减少计算量
--lags 3

# 提高精度
--lags 5
```

### 3. 活跃预测数控制
```bash
# 保守策略
--max-active 3

# 激进策略
--max-active 10
```

## 故障排除

### 1. 特征数量不匹配
```
⚠️ 特征数量不匹配: 期望150, 实际145
```
**解决方案**: 检查训练时的滞后参数，确保回测时使用相同的`--lags`值

### 2. 数据不足
```
❌ 数据不足。需要至少 1000 条数据，但只有 500 条。
```
**解决方案**: 
- 减少`--window-size`
- 扩大时间范围
- 检查数据库中的数据完整性

### 3. 模型加载失败
```
❌ 模型文件不存在: darts_fixed_eth_5m_model.joblib
```
**解决方案**: 先运行训练脚本生成模型文件

## 最佳实践

### 1. 回测前检查
- 确认模型和配置文件存在
- 验证数据库连接和数据完整性
- 检查时间范围是否合理

### 2. 参数调优
- 从小资金开始测试
- 逐步调整风险比例
- 观察止损效果

### 3. 结果分析
- 关注胜率和平均盈亏
- 分析最大回撤
- 检查时间分布特征

## 示例用法

### 基础回测
```bash
python backtest_darts_model.py \
    --model-file darts_fixed_eth_5m_model.joblib \
    --config-file darts_fixed_eth_5m_config.json
```

### 带止损的回测
```bash
python backtest_darts_model.py \
    --model-file darts_fixed_eth_5m_model.joblib \
    --config-file darts_fixed_eth_5m_config.json \
    --stop-loss 2.5 \
    --risk-per-trade 0.5
```

### 指定时间范围
```bash
python backtest_darts_model.py \
    --model-file darts_fixed_eth_5m_model.joblib \
    --config-file darts_fixed_eth_5m_config.json \
    --start-time "2024-01-01" \
    --end-time "2024-01-31" \
    --initial-capital 5000
```