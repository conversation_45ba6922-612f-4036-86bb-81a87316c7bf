#!/usr/bin/env python3
"""
诊断和优化 Darts LightGBM 训练问题
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# 添加父目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

def diagnose_data_quality(df_clean):
    """诊断数据质量"""
    print("=== 数据质量诊断 ===")
    
    # 基本统计
    print(f"数据形状: {df_clean.shape}")
    print(f"缺失值: {df_clean.isnull().sum().sum()}")
    
    # 标签分布
    if 'label' in df_clean.columns:
        label_counts = df_clean['label'].value_counts()
        print(f"标签分布:")
        for label, count in label_counts.items():
            print(f"  {label}: {count} ({count/len(df_clean)*100:.1f}%)")
        
        # 检查标签平衡性
        if len(label_counts) == 2:
            ratio = min(label_counts) / max(label_counts)
            if ratio < 0.1:
                print(f"⚠️ 标签严重不平衡，比例: {ratio:.3f}")
            elif ratio < 0.3:
                print(f"⚠️ 标签轻微不平衡，比例: {ratio:.3f}")
            else:
                print(f"✅ 标签分布相对平衡，比例: {ratio:.3f}")
    
    # 特征统计
    feature_cols = [col for col in df_clean.columns if col != 'label']
    print(f"特征数量: {len(feature_cols)}")
    
    # 检查特征方差
    low_variance_features = []
    for col in feature_cols:
        if df_clean[col].var() < 1e-8:
            low_variance_features.append(col)
    
    if low_variance_features:
        print(f"⚠️ 低方差特征 ({len(low_variance_features)}): {low_variance_features[:5]}...")
    else:
        print("✅ 所有特征都有足够的方差")
    
    # 检查常数特征
    constant_features = []
    for col in feature_cols:
        if df_clean[col].nunique() <= 1:
            constant_features.append(col)
    
    if constant_features:
        print(f"⚠️ 常数特征 ({len(constant_features)}): {constant_features}")
    else:
        print("✅ 没有常数特征")
    
    return {
        'low_variance_features': low_variance_features,
        'constant_features': constant_features,
        'label_balance': ratio if 'label' in df_clean.columns and len(label_counts) == 2 else None
    }

def get_optimized_lgbm_params(data_info):
    """根据数据特征获取优化的 LightGBM 参数"""
    print("\n=== 参数优化建议 ===")
    
    base_params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'random_state': 42,
        'verbose': -1,
        'force_col_wise': True  # 避免多线程开销警告
    }
    
    # 根据数据质量调整参数
    if data_info.get('label_balance', 1) < 0.3:
        print("📊 检测到标签不平衡，调整参数...")
        base_params.update({
            'is_unbalance': True,
            'scale_pos_weight': 1.0 / data_info['label_balance']
        })
    
    # 更宽松的参数设置，避免 "no further splits" 问题
    optimized_params = {
        **base_params,
        'n_estimators': 100,  # 减少树的数量
        'learning_rate': 0.1,  # 适中的学习率
        'max_depth': 6,  # 适中的深度
        'num_leaves': 31,  # 默认叶子数
        'min_child_samples': 10,  # 降低最小样本数
        'min_child_weight': 1e-3,  # 降低最小权重
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'reg_alpha': 0.0,  # 不使用L1正则化
        'reg_lambda': 0.0,  # 不使用L2正则化
        'min_split_gain': 0.0,  # 允许任何增益的分割
    }
    
    print("优化后的参数:")
    for key, value in optimized_params.items():
        if key not in base_params:
            print(f"  {key}: {value}")
    
    return optimized_params

def create_simple_test_data():
    """创建简单的测试数据来验证模型"""
    print("\n=== 创建简单测试数据 ===")
    
    np.random.seed(42)
    n_samples = 1000
    
    # 创建有明显模式的特征
    feature1 = np.random.normal(0, 1, n_samples)
    feature2 = np.random.normal(0, 1, n_samples)
    feature3 = feature1 * 0.5 + np.random.normal(0, 0.1, n_samples)  # 与feature1相关
    
    # 创建有意义的标签
    # 当 feature1 > 0 且 feature2 > 0 时，更可能是1
    labels = ((feature1 > 0) & (feature2 > 0)).astype(int)
    
    # 添加一些噪声
    noise_indices = np.random.choice(n_samples, size=int(n_samples * 0.1), replace=False)
    labels[noise_indices] = 1 - labels[noise_indices]
    
    # 创建时间索引
    dates = pd.date_range('2024-01-01', periods=n_samples, freq='5T')
    
    df = pd.DataFrame({
        'feature1': feature1,
        'feature2': feature2,
        'feature3': feature3,
        'label': labels
    }, index=dates)
    
    print(f"测试数据创建完成: {df.shape}")
    print(f"标签分布: {df['label'].value_counts().to_dict()}")
    
    return df

def test_simple_lgbm_training():
    """测试简单的 LightGBM 训练"""
    print("\n=== 测试简单 LightGBM 训练 ===")
    
    try:
        import lightgbm as lgb
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score, roc_auc_score
        
        # 创建测试数据
        df = create_simple_test_data()
        
        # 分离特征和标签
        feature_cols = ['feature1', 'feature2', 'feature3']
        X = df[feature_cols]
        y = df['label']
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 诊断数据
        train_df = pd.concat([X_train, y_train], axis=1)
        data_info = diagnose_data_quality(train_df)
        
        # 获取优化参数
        params = get_optimized_lgbm_params(data_info)
        
        # 训练模型
        print("\n开始训练 LightGBM...")
        model = lgb.LGBMClassifier(**params)
        model.fit(X_train, y_train)
        
        # 预测和评估
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1]
        
        accuracy = accuracy_score(y_test, y_pred)
        auc = roc_auc_score(y_test, y_pred_proba)
        
        print(f"✅ 训练成功!")
        print(f"准确率: {accuracy:.4f}")
        print(f"AUC: {auc:.4f}")
        
        return True, params
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def create_optimized_darts_trainer():
    """创建优化的 Darts 训练器配置"""
    print("\n=== 创建优化的 Darts 配置 ===")
    
    # 测试简单训练
    success, optimal_params = test_simple_lgbm_training()
    
    if not success:
        print("❌ 无法确定最优参数")
        return None
    
    # 为 Darts 调整参数
    darts_params = {
        'lags': 10,  # 减少滞后窗口
        'lags_future_covariates': [0, 3],  # 减少未来协变量滞后
        'output_chunk_length': 1,
        **{k: v for k, v in optimal_params.items() 
           if k not in ['objective', 'metric', 'verbose']}
    }
    
    print("Darts 优化参数:")
    for key, value in darts_params.items():
        print(f"  {key}: {value}")
    
    return darts_params

def main():
    """主诊断函数"""
    print("Darts LightGBM 训练问题诊断工具")
    print("=" * 50)
    
    # 测试基本 LightGBM 功能
    success, params = test_simple_lgbm_training()
    
    if success:
        print("\n✅ 基本 LightGBM 功能正常")
        
        # 创建优化配置
        darts_params = create_optimized_darts_trainer()
        
        if darts_params:
            # 保存优化配置
            import json
            config_file = 'optimized_darts_config.json'
            with open(config_file, 'w') as f:
                json.dump({
                    'model_params': darts_params,
                    'training_tips': [
                        "使用较小的数据集进行初始测试",
                        "检查特征质量和标签分布",
                        "使用更宽松的正则化参数",
                        "确保有足够的训练样本"
                    ]
                }, f, indent=2)
            
            print(f"\n✅ 优化配置已保存到: {config_file}")
            
            print("\n📋 建议:")
            print("1. 使用优化后的参数重新训练")
            print("2. 检查数据质量，移除低质量特征")
            print("3. 确保标签分布相对平衡")
            print("4. 从小数据集开始测试")
    else:
        print("\n❌ 基本 LightGBM 功能异常，请检查环境")

if __name__ == "__main__":
    main()