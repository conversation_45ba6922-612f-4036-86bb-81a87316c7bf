#!/usr/bin/env python3
"""
测试 Darts 是否支持分类模型
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加父目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

def test_darts_classification():
    """测试 Darts 分类功能"""
    print("=== 测试 Darts LightGBM 分类功能 ===")
    
    try:
        from darts import TimeSeries
        from darts.models import LightGBMModel
        import lightgbm as lgb
        
        # 创建简单的二元分类数据
        np.random.seed(42)
        n_samples = 200
        
        # 生成时间序列
        dates = pd.date_range('2024-01-01', periods=n_samples, freq='5T')
        
        # 生成二元标签 (0 或 1)
        labels = np.random.choice([0, 1], size=n_samples, p=[0.4, 0.6])
        
        # 生成相关特征
        feature1 = np.random.normal(0, 1, n_samples) + labels * 0.5  # 与标签相关
        feature2 = np.random.normal(0, 1, n_samples)
        
        # 创建 DataFrame
        df = pd.DataFrame({
            'target': labels,
            'feature1': feature1,
            'feature2': feature2
        }, index=dates)
        
        print(f"数据形状: {df.shape}")
        print(f"标签分布: {df['target'].value_counts().to_dict()}")
        
        # 转换为 TimeSeries
        target_series = TimeSeries.from_dataframe(df[['target']])
        feature_series = TimeSeries.from_dataframe(df[['feature1', 'feature2']])
        
        # 分割数据
        split_point = int(len(target_series) * 0.8)
        train_target = target_series[:split_point]
        test_target = target_series[split_point:]
        train_features = feature_series[:split_point]
        test_features = feature_series[split_point:]
        
        print(f"训练集: {len(train_target)} 样本")
        print(f"测试集: {len(test_target)} 样本")
        
        # 测试1: 尝试使用 LightGBMModel 进行分类
        print("\n--- 测试1: Darts LightGBMModel 分类 ---")
        try:
            model = LightGBMModel(
                lags=5,
                lags_future_covariates=[0, 2],
                output_chunk_length=1,
                objective='binary',
                metric='binary_logloss',
                n_estimators=50,
                random_state=42,
                verbose=-1
            )
            
            model.fit(train_target, future_covariates=train_features)
            predictions = model.predict(n=len(test_target), future_covariates=test_features)
            
            print("✅ Darts LightGBMModel 分类成功")
            print(f"预测形状: {predictions.values().shape}")
            print(f"预测范围: {predictions.values().min():.3f} - {predictions.values().max():.3f}")
            
            return True, "darts_native"
            
        except Exception as e:
            print(f"❌ Darts LightGBMModel 分类失败: {e}")
        
        # 测试2: 使用原生 LightGBM 分类器
        print("\n--- 测试2: 原生 LightGBM 分类器 ---")
        try:
            # 准备数据
            X_train = []
            y_train = []
            
            # 创建滞后特征
            for i in range(5, len(train_target)):
                # 滞后目标值
                lag_target = train_target[i-5:i].values().flatten()
                # 当前特征值
                current_features = train_features[i].values().flatten()
                # 组合特征
                X_train.append(np.concatenate([lag_target, current_features]))
                y_train.append(train_target[i].values().flatten()[0])
            
            X_train = np.array(X_train)
            y_train = np.array(y_train)
            
            print(f"训练数据形状: X={X_train.shape}, y={y_train.shape}")
            
            # 训练分类器
            classifier = lgb.LGBMClassifier(
                objective='binary',
                metric='binary_logloss',
                n_estimators=50,
                random_state=42,
                verbose=-1
            )
            
            classifier.fit(X_train, y_train)
            
            # 预测
            X_test = []
            for i in range(5, len(test_target)):
                lag_target = test_target[i-5:i].values().flatten()
                current_features = test_features[i].values().flatten()
                X_test.append(np.concatenate([lag_target, current_features]))
            
            X_test = np.array(X_test)
            y_pred = classifier.predict(X_test)
            y_pred_proba = classifier.predict_proba(X_test)[:, 1]
            
            print("✅ 原生 LightGBM 分类成功")
            print(f"预测形状: {y_pred.shape}")
            print(f"预测分布: {np.bincount(y_pred)}")
            print(f"概率范围: {y_pred_proba.min():.3f} - {y_pred_proba.max():.3f}")
            
            return True, "native_lgb"
            
        except Exception as e:
            print(f"❌ 原生 LightGBM 分类失败: {e}")
            import traceback
            traceback.print_exc()
        
        return False, None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """主测试函数"""
    print("Darts 分类模型支持测试")
    print("=" * 50)
    
    success, method = test_darts_classification()
    
    if success:
        print(f"\n✅ 分类功能测试成功！")
        print(f"推荐方法: {method}")
        
        if method == "darts_native":
            print("🎉 Darts 原生支持分类，可以直接使用")
        elif method == "native_lgb":
            print("🔧 需要使用原生 LightGBM，然后包装为 Darts 兼容格式")
    else:
        print("\n❌ 分类功能测试失败")
        print("需要重新设计分类方案")

if __name__ == "__main__":
    main()