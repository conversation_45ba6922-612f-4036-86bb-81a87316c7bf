#!/usr/bin/env python3
"""
Darts模型回测运行脚本
简化的使用示例
"""

import os
import sys
from backtest_darts_model import main as backtest_main

def run_example_backtest():
    """运行示例回测"""
    print("=== Darts模型回测示例 ===")
    
    # 检查必要文件是否存在
    model_file = "darts_fixed_eth_5m_model.joblib"
    config_file = "darts_fixed_eth_5m_config.json"
    
    if not os.path.exists(model_file):
        print(f"❌ 模型文件不存在: {model_file}")
        print("请先运行训练脚本生成模型")
        return
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        print("请先运行训练脚本生成配置")
        return
    
    # 设置命令行参数
    sys.argv = [
        'run_darts_backtest.py',
        '--model-file', model_file,
        '--config-file', config_file,
        '--db', '../coin_data.db',
        '--coin', 'ETHUSDT',
        '--interval', '5m',
        '--market', 'spot',
        '--initial-capital', '1000',
        '--risk-per-trade', '1.0',
        '--stop-loss', '2.0',
        '--max-active', '5',
        '--window-size', '500',
        '--lags', '5',
        # '--start-time', '2024-01-01',
        # '--end-time', '2024-02-01'
    ]
    
    # 运行回测
    try:
        backtest_main()
        print("\n✅ 回测完成！")
        print("📊 查看 darts_backtest_results.csv 获取详细结果")
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    run_example_backtest()