"""
修复数据泄露问题的二元分类训练器
移除滞后目标值和高相关特征
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import lightgbm as lgb
from sklearn.calibration import CalibratedClassifierCV
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import roc_auc_score, accuracy_score, precision_score, recall_score, classification_report
import joblib
import json
import argparse
import os
import pickle
import optuna
import warnings
warnings.filterwarnings('ignore')

# 添加父目录到路径
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 尝试导入外部工具函数
try:
    from model_utils_815 import calculate_features
    from data_loader import create_data_provider
    HAS_EXTERNAL_UTILS = True
except ImportError:
    HAS_EXTERNAL_UTILS = False
    print("⚠️ 未找到外部工具函数，使用内置简化版本")

class FixedDartsClassificationTrainer:
    """修复数据泄露的二元分类训练器"""
    
    def __init__(self, config):
        self.config = config
        self.model = None
        self.features = None
        self.best_threshold = 0.5
        
    def create_percentage_target(self, df, up_threshold, down_threshold, max_lookforward_minutes, timeframe):
        """创建目标标签 - 与原版保持一致"""
        max_lookforward_candles = max_lookforward_minutes // timeframe
        print(f"创建目标标签：先涨{up_threshold*100:.1f}%还是先跌{down_threshold*100:.1f}% (最大等待 {max_lookforward_candles} 根K线)")
        labels = np.full(len(df), np.nan)
        
        close_prices = df['close'].to_numpy()
        up_targets = close_prices * (1 + up_threshold)
        down_targets = close_prices * (1 - down_threshold)

        for i in range(len(df) - max_lookforward_candles):
            if i % 20000 == 0:
                print(f"处理标签进度: {i}/{len(df)} ({i/len(df)*100:.1f}%)")
            
            future_window = close_prices[i+1 : i+1+max_lookforward_candles]
            
            up_hit_indices = np.where(future_window >= up_targets[i])[0]
            down_hit_indices = np.where(future_window <= down_targets[i])[0]

            first_up_hit = up_hit_indices[0] if len(up_hit_indices) > 0 else np.inf
            first_down_hit = down_hit_indices[0] if len(down_hit_indices) > 0 else np.inf

            if first_up_hit < first_down_hit:
                labels[i] = 1
            elif first_down_hit < first_up_hit:
                labels[i] = 0

        valid_mask = ~np.isnan(labels)
        valid_indices = df.index[valid_mask]
        valid_labels = labels[valid_mask].astype(int)
        
        print(f"有效标签数量: {len(valid_labels)}/{len(df)} ({len(valid_labels)/len(df)*100:.1f}%)")
        label_series = pd.Series(index=valid_indices, data=valid_labels)

        if len(valid_labels) > 0:
            up_count = np.sum(valid_labels)
            down_count = len(valid_labels) - up_count
            print(f"标签分布: 先涨 = {up_count} ({up_count/len(valid_labels)*100:.1f}%), 先跌 = {down_count} ({down_count/len(valid_labels)*100:.1f}%)")
        else:
            print("未生成任何有效标签。")
        return label_series

    def prepare_safe_features(self, df_clean, lags=5):
        """准备安全的特征，避免数据泄露"""
        print(f"准备安全特征，滞后窗口: {lags}")
        
        # 确保索引是datetime类型
        if not isinstance(df_clean.index, pd.DatetimeIndex):
            df_clean.index = pd.to_datetime(df_clean.index)
        
        # 分离特征和标签
        target_col = 'label'
        feature_cols = [col for col in df_clean.columns if col != target_col]
        
        # 🔥 关键修复：移除高相关性特征，避免数据泄露
        excluded_features = [
            'open', 'high', 'low', 'close',  # 移除原始价格
            'sma_120', 'sma_360', 'sma_720',  # 移除高相关移动平均
            'vwap_360',  # 移除高相关VWAP
        ]
        
        safe_features = [col for col in feature_cols if col not in excluded_features]
        print(f"移除 {len(excluded_features)} 个高风险特征")
        print(f"保留 {len(safe_features)} 个安全特征")
        
        # 🔥 修复：创建完整的特征名称列表，包括滞后特征
        lag_feature_count = min(5, len(safe_features))  # 最多使用前5个特征做滞后
        all_feature_names = []
        
        # 当前特征名称
        all_feature_names.extend(safe_features)
        
        # 滞后特征名称
        for lag in range(1, lags + 1):
            for feat_name in safe_features[:lag_feature_count]:
                all_feature_names.append(f"{feat_name}_lag_{lag}")
        
        print(f"总特征数量: {len(all_feature_names)} (当前: {len(safe_features)}, 滞后: {lags * lag_feature_count})")
        
        # 创建滞后特征 - 🔥 关键修复：只使用特征的滞后，不使用目标的滞后
        lagged_data = []
        
        for i in range(lags, len(df_clean)):
            # 当前时间点的安全特征
            current_features = df_clean[safe_features].iloc[i].values
            
            # 🔥 修复：只使用特征的滞后值，不使用目标的滞后值
            lagged_features = []
            for lag in range(1, lags + 1):
                # 使用部分安全特征的滞后值
                lag_features = df_clean[safe_features[:lag_feature_count]].iloc[i-lag].values
                lagged_features.extend(lag_features)
            
            # 组合特征：当前特征 + 滞后特征
            combined_features = np.concatenate([
                current_features,
                lagged_features
            ])
            
            # 当前标签
            current_label = df_clean[target_col].iloc[i]
            
            lagged_data.append({
                'features': combined_features,
                'label': current_label,
                'timestamp': df_clean.index[i]
            })
        
        print(f"生成 {len(lagged_data)} 个安全时间序列样本")
        return lagged_data, all_feature_names

    def split_time_series_data_with_gap(self, lagged_data, train_ratio=0.6, val_ratio=0.2, gap_ratio=0.05):
        """分割时间序列数据，添加时间间隔避免泄露"""
        total_len = len(lagged_data)
        
        # 🔥 修复：添加时间间隔
        gap_len = int(total_len * gap_ratio)
        train_len = int(total_len * train_ratio)
        val_len = int(total_len * val_ratio)
        
        # 训练集
        train_data = lagged_data[:train_len]
        
        # 间隔（丢弃）
        gap_start = train_len
        gap_end = gap_start + gap_len
        
        # 验证集
        val_start = gap_end
        val_end = val_start + val_len
        val_data = lagged_data[val_start:val_end]
        
        # 第二个间隔
        gap2_start = val_end
        gap2_end = gap2_start + gap_len
        
        # 测试集
        test_data = lagged_data[gap2_end:]
        
        print(f"数据分割 (带时间间隔):")
        print(f"训练集: {len(train_data)} 样本")
        print(f"间隔1: {gap_len} 样本 (丢弃)")
        print(f"验证集: {len(val_data)} 样本")
        print(f"间隔2: {gap_len} 样本 (丢弃)")
        print(f"测试集: {len(test_data)} 样本")
        
        return train_data, val_data, test_data

    def prepare_sklearn_data(self, data_list):
        """将时间序列数据转换为sklearn格式"""
        if not data_list:
            return np.array([]), np.array([])
        
        X = np.array([item['features'] for item in data_list])
        y = np.array([item['label'] for item in data_list])
        
        # 检查并处理 NaN 值
        if np.isnan(X).any():
            print(f"⚠️ 发现 {np.isnan(X).sum()} 个 NaN 值，进行填充")
            X = np.nan_to_num(X, nan=0.0)
        
        if np.isnan(y).any():
            print(f"⚠️ 标签中发现 {np.isnan(y).sum()} 个 NaN 值，进行填充")
            y = np.nan_to_num(y, nan=0)
        
        return X, y

    def find_best_threshold(self, val_data):
        """寻找最优预测阈值，类似trainopt2.py的方法"""
        print("\n开始阈值优化...")
        
        X_val, y_val = self.prepare_sklearn_data(val_data)
        val_probabilities = self.model.predict_proba(X_val)[:, 1]
        
        thresholds_to_test = np.arange(0.52, 0.80, 0.01)
        best_score = -np.inf
        best_threshold = 0.5
        
        for threshold in thresholds_to_test:
            predictions = np.where(val_probabilities > threshold, 1, 
                                 np.where(val_probabilities < (1 - threshold), 0, -1))
            
            correct_trades = (predictions == y_val) & (predictions != -1)
            incorrect_trades = (predictions != y_val) & (predictions != -1)
            current_score = correct_trades.sum() - incorrect_trades.sum()
            
            if current_score > best_score:
                best_score = current_score
                best_threshold = threshold
        
        self.best_threshold = best_threshold
        print(f"验证集上的最优得分: {best_score:.0f}, 最优信心阈值: {best_threshold:.3f}")
        return best_score, best_threshold

    def train_conservative_model(self, train_data, val_data):
        """训练保守的二元分类模型"""
        print("\n开始训练保守的二元分类LightGBM模型...")
        
        # 准备数据
        X_train, y_train = self.prepare_sklearn_data(train_data)
        X_val, y_val = self.prepare_sklearn_data(val_data)
        
        print(f"训练数据形状: X={X_train.shape}, y={y_train.shape}")
        print(f"验证数据形状: X={X_val.shape}, y={y_val.shape}")
        
        # 🔥 修复：使用更保守的参数，避免过拟合
        conservative_params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'n_estimators': 100,  # 减少树的数量
            'learning_rate': 0.05,  # 降低学习率
            'max_depth': 4,  # 减少深度
            'num_leaves': 15,  # 减少叶子数
            'min_child_samples': 50,  # 增加最小样本数
            'subsample': 0.7,  # 减少采样
            'colsample_bytree': 0.7,  # 减少特征采样
            'reg_alpha': 0.5,  # 增加L1正则化
            'reg_lambda': 0.5,  # 增加L2正则化
            'random_state': 42,
            'verbose': -1
        }
        
        # 创建并训练分类器
        self.model = lgb.LGBMClassifier(**conservative_params)
        
        # 训练
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            eval_metric='binary_logloss',
            callbacks=[lgb.early_stopping(stopping_rounds=20, verbose=False)]
        )
        
        print("保守二元分类模型训练完成")
        
        # 优化阈值
        self.find_best_threshold(val_data)
        
        return self.model

    def evaluate_classification_model(self, test_data, save_detailed_results=True, model_name=None):
        """评估二元分类模型并生成详细CSV结果"""
        print("\n=== 二元分类模型评估 ===")
        
        X_test, y_test = self.prepare_sklearn_data(test_data)
        
        # 预测
        y_pred = self.model.predict(X_test)
        y_pred_proba = self.model.predict_proba(X_test)[:, 1]
        
        # 计算指标
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
        recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
        auc = roc_auc_score(y_test, y_pred_proba)
        
        print(f"准确率: {accuracy:.4f}")
        print(f"精确率: {precision:.4f}")
        print(f"召回率: {recall:.4f}")
        print(f"AUC: {auc:.4f}")
        
        # 详细分类报告
        print("\n分类报告:")
        print(classification_report(y_test, y_pred))
        
        # 生成详细测试结果CSV
        if save_detailed_results:
            self.save_detailed_test_results(test_data, y_pred_proba, y_test, model_name)
        
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'auc': auc,
            'predictions': y_pred,
            'probabilities': y_pred_proba
        }

    def save_detailed_test_results(self, test_data, y_pred_proba, y_test, model_name=None):
        """保存详细的测试结果到CSV文件，类似trainopt2.py的格式"""
        print(f"\n--- 生成详细测试结果 (使用阈值: {self.best_threshold:.3f}) ---")
        
        results_log = []
        
        for i in range(len(test_data)):
            data_item = test_data[i]
            prob = y_pred_proba[i]
            actual_result = y_test[i]
            timestamp = data_item['timestamp']
            
            # 根据阈值做预测决策
            guess = -1  # 默认不交易
            if prob > self.best_threshold:
                guess = 1  # 预测上涨
            elif prob < (1 - self.best_threshold):
                guess = 0  # 预测下跌
            
            # 计算得分
            if guess == actual_result and guess != -1:
                score = 1  # 正确预测
            elif guess != actual_result and guess != -1:
                score = -1  # 错误预测
            else:
                score = 0  # 不交易
            
            log_entry = {
                'Timestamp': timestamp,
                'ConfidenceUp': prob,
                'ConfidenceDown': 1 - prob,
                'Prediction': guess,
                'ActualResult': actual_result,
                'Score': score,
                'IsTraded': 1 if guess != -1 else 0
            }
            results_log.append(log_entry)
        
        # 转换为DataFrame
        results_df = pd.DataFrame(results_log)
        
        # 计算统计信息
        trades_made = (results_df['Prediction'] != -1).sum()
        wins = (results_df['Score'] == 1).sum()
        losses = (results_df['Score'] == -1).sum()
        total_score = wins - losses
        
        print(f"总样本数: {len(test_data)}")
        print(f"交易次数: {trades_made} ({trades_made/len(test_data)*100:.2f}%)")
        if trades_made > 0:
            win_rate = wins / trades_made * 100
            print(f"胜率: {win_rate:.2f}% ({wins}/{trades_made})")
        print(f"总得分: {total_score:+d} (胜:{wins}, 负:{losses})")
        
        # 保存CSV文件
        if model_name is None:
            model_name = "darts_fixed_classification"
        
        current_dir = os.path.dirname(os.path.abspath(__file__))
        results_filename = os.path.join(current_dir, f'test_results_{model_name}.csv')
        
        results_df.to_csv(results_filename, index=False, float_format='%.4f')
        print(f"\n详细测试结果已保存到: {results_filename}")
        
        return results_df

    def save_model(self, coin_config, model_name=None):
        """保存模型"""
        if model_name is None:
            model_name = f"darts_fixed_{coin_config['model_basename']}"
        
        # 保存在 darts 目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        model_file = os.path.join(current_dir, f'{model_name}_model.joblib')
        config_file = os.path.join(current_dir, f'{model_name}_config.json')
        
        # 保存模型
        joblib.dump(self.model, model_file)
        
        # 保存配置
        config = {
            'model_type': 'Fixed_Binary_Classification_LightGBM',
            'model_name': model_name,
            'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'up_threshold': coin_config['up_threshold'],
            'down_threshold': coin_config['down_threshold'],
            'max_lookforward_minutes': coin_config['max_lookforward_minutes'],
            'timeframe_minutes': coin_config['timeframe_minutes'],
            'features': self.features,
            'best_threshold': self.best_threshold,
            'objective': 'binary',
            'data_leakage_fixed': True,
            'target_description': f'predict_first_{coin_config["up_threshold"]*100}%_move_within_{coin_config["max_lookforward_minutes"]}_minutes'
        }
        
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"\n修复版模型已保存:")
        print(f"模型文件: {model_file}")
        print(f"配置文件: {config_file}")

    def analyze_feature_importance(self, model_name=None):
        """分析特征重要性，类似trainopt2.py"""
        if self.model is None or self.features is None:
            print("⚠️ 模型或特征列表未初始化")
            return
        
        # 获取特征重要性
        importance_scores = self.model.feature_importances_
        
        # 🔥 修复：检查特征名称和重要性数组长度是否匹配
        print(f"特征名称数量: {len(self.features)}")
        print(f"重要性分数数量: {len(importance_scores)}")
        
        if len(self.features) != len(importance_scores):
            print(f"⚠️ 特征数量不匹配，创建通用特征名称")
            # 创建通用特征名称
            feature_names = [f"feature_{i}" for i in range(len(importance_scores))]
        else:
            feature_names = self.features
        
        # 创建特征重要性DataFrame
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': importance_scores
        }).sort_values('importance', ascending=False)
        
        print("\n" + "="*20 + " 特征重要性 (Top 20) " + "="*20)
        print(importance_df.head(20).to_string(index=False))
        
        # 保存特征重要性
        if model_name is None:
            model_name = "darts_fixed_classification"
        
        current_dir = os.path.dirname(os.path.abspath(__file__))
        importance_file = os.path.join(current_dir, f'feature_importance_{model_name}.csv')
        importance_df.to_csv(importance_file, index=False)
        print(f"\n完整特征重要性已保存到: {importance_file}")
        
        return importance_df

def calculate_basic_features(df, timeframe=5):
    """计算基础技术指标特征 - 避免高相关性"""
    print("计算安全的基础技术指标...")
    
    # 🔥 修复：只计算低相关性的特征
    
    # 价格变化特征（相对安全）
    df['price_change'] = df['close'].pct_change()
    df['price_change_abs'] = df['price_change'].abs()
    
    # 短期移动平均（避免长期MA）
    df['sma_5'] = df['close'].rolling(5).mean()
    df['sma_10'] = df['close'].rolling(10).mean()
    
    # 价格位置（相对值，降低相关性）
    df['price_vs_sma5'] = df['close'] / df['sma_5'] - 1
    df['price_vs_sma10'] = df['close'] / df['sma_10'] - 1
    
    # 短期波动率
    df['volatility_5'] = df['close'].rolling(5).std()
    df['volatility_10'] = df['close'].rolling(10).std()
    
    # 成交量特征
    df['volume_change'] = df['volume'].pct_change()
    df['volume_sma'] = df['volume'].rolling(5).mean()
    df['volume_ratio'] = df['volume'] / df['volume_sma']
    
    # 简化RSI
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    df['rsi_change'] = df['rsi'].diff()
    
    # 时间特征
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    
    return df

def get_builtin_coin_config(coin_name):
    """内置币种配置"""
    configs = {
        "ETH": {
            "db_path": "coin_data.db",
            "api_symbol": "ETHUSDT",
            "display_name": "ETH/USDT",
            "timeframe_minutes": 5,
            "up_threshold": 0.02,
            "down_threshold": 0.02,
            "max_lookforward_minutes": 240,
            "model_basename": "eth_5m",
            "price_multiplier": 1.0
        }
    }
    return configs.get(coin_name)

def prepare_features_and_labels(df, coin_config):
    """准备特征和标签"""
    if HAS_EXTERNAL_UTILS:
        print("使用外部特征工程...")
        df_with_features = calculate_features(df.copy(), timeframe=coin_config['timeframe_minutes'])
    else:
        print("使用安全的内置特征工程...")
        df_with_features = calculate_basic_features(df.copy(), timeframe=coin_config['timeframe_minutes'])
    
    print("创建目标标签...")
    trainer = FixedDartsClassificationTrainer({})
    target_labels = trainer.create_percentage_target(
        df, 
        coin_config['up_threshold'], 
        coin_config['down_threshold'], 
        coin_config['max_lookforward_minutes'], 
        coin_config['timeframe_minutes']
    )
    
    print("合并特征与标签...")
    df_combined = df_with_features.join(target_labels.rename('label'), how='inner')
    df_clean = df_combined.dropna()
    print(f"清理NaN后剩余 {len(df_clean)} 条记录")
    return df_clean

def train_fixed_model(args):
    """训练修复版模型"""
    coin_config = get_builtin_coin_config(args.coin)
    if coin_config is None:
        print(f"❌ 不支持的币种 {args.coin}")
        return
    
    print(f"开始训练修复版二元分类模型 - {coin_config['model_basename']}")
    
    # 加载数据
    try:
        if HAS_EXTERNAL_UTILS:
            data_source = {
                'type': 'sqlite',
                'db_path': args.db_path or '../coin_data.db',
                'symbol': coin_config.get('api_symbol'),
                'interval': f"{coin_config['timeframe_minutes']}m",
                'market': 'spot'
            }
            data_provider = create_data_provider(data_source, 1.0)
            df = data_provider.get_initial_data(initial_count=300000)  # 减少数据量
        else:
            import sqlite3
            db_path = args.db_path or '../coin_data.db'
            conn = sqlite3.connect(db_path)
            query = f"""
            SELECT timestamp, open, high, low, close, volume 
            FROM ETHUSDT_5min_spot 
            ORDER BY timestamp DESC 
            LIMIT 300000
            """
            df = pd.read_sql_query(query, conn)
            conn.close()
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            df = df.sort_index()
        
        print(f"✅ 加载 {len(df)} 条数据")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 准备特征和标签
    df_clean = prepare_features_and_labels(df, coin_config)
    
    # 创建训练器
    trainer = FixedDartsClassificationTrainer({})
    
    # 准备安全特征
    lagged_data, feature_names = trainer.prepare_safe_features(df_clean, lags=5)  # 减少滞后
    trainer.features = feature_names
    
    if len(lagged_data) < 100:
        print("❌ 数据太少")
        return
    
    # 分割数据（带间隔）
    train_data, val_data, test_data = trainer.split_time_series_data_with_gap(lagged_data)
    
    # 训练保守模型
    model = trainer.train_conservative_model(train_data, val_data)
    
    # 评估
    print("\n=== 验证集评估 ===")
    val_metrics = trainer.evaluate_classification_model(val_data, save_detailed_results=False)
    
    print("\n=== 测试集评估 ===")
    model_name = f"darts_fixed_{coin_config['model_basename']}"
    test_metrics = trainer.evaluate_classification_model(test_data, save_detailed_results=True, model_name=model_name)
    
    # 保存模型
    trainer.save_model(coin_config, model_name)
    
    # 分析特征重要性
    trainer.analyze_feature_importance(model_name)
    
    print(f"\n=== 修复版训练完成 ===")
    print(f"验证集准确率: {val_metrics['accuracy']:.4f}")
    print(f"验证集AUC: {val_metrics['auc']:.4f}")
    print(f"测试集准确率: {test_metrics['accuracy']:.4f}")
    print(f"测试集AUC: {test_metrics['auc']:.4f}")
    print(f"最优阈值: {trainer.best_threshold:.3f}")

def main():
    parser = argparse.ArgumentParser(description="修复数据泄露的二元分类训练器")
    parser.add_argument("--coin", default="ETH", help="币种名称")
    parser.add_argument("--db-path", help="数据库路径")
    
    args = parser.parse_args()
    
    print("=== 修复版 Darts 二元分类训练器 ===")
    print("🔧 已修复数据泄露问题")
    train_fixed_model(args)

if __name__ == '__main__':
    main()