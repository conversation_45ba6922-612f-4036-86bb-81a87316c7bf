#!/usr/bin/env python3
"""
测试 Darts 详细回测系统
验证回测脚本的基本功能
"""

import os
import sys
import subprocess
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加父目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

def check_model_files():
    """检查模型文件是否存在"""
    print("🔍 检查模型文件...")
    
    model_files = [
        "darts_fixed_eth_5m_model.joblib",
        "darts_fixed_eth_5m_config.json"
    ]
    
    missing_files = []
    for file in model_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"✅ 找到: {file}")
    
    if missing_files:
        print(f"❌ 缺少模型文件: {missing_files}")
        print("请先运行 darts_classification_trainer_fixed.py 训练模型")
        return False
    
    return True

def check_data_file():
    """检查数据文件是否存在"""
    print("\n🔍 检查数据文件...")
    
    db_path = "../coin_data.db"
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    print(f"✅ 找到数据库: {db_path}")
    return True

def run_basic_test():
    """运行基础回测测试"""
    print("\n🚀 运行基础回测测试...")
    
    cmd = [
        "python", "backtest_darts_detailed.py",
        "--coin", "ETH",
        "--interval", "5m",
        "--initial-capital", "1000",
        "--risk-per-trade", "1.0",
        "--max-active-predictions", "10",
        "--start-time", "2024-11-01",
        "--end-time", "2024-11-07"  # 一周的数据
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 基础回测测试成功")
            print("输出预览:")
            print(result.stdout[-500:])  # 显示最后500字符
            return True
        else:
            print("❌ 基础回测测试失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 回测测试超时")
        return False
    except Exception as e:
        print(f"❌ 回测测试异常: {e}")
        return False

def run_advanced_test():
    """运行高级功能测试"""
    print("\n🚀 运行高级功能测试...")
    
    cmd = [
        "python", "backtest_darts_detailed.py",
        "--coin", "ETH",
        "--interval", "5m",
        "--initial-capital", "1000",
        "--risk-per-trade", "1.0",
        "--max-active-predictions", "5",
        "--stop-loss", "2.5",
        "--use-supertrend",
        "--supertrend-interval", "15m",
        "--supertrend-atr-period", "10",
        "--supertrend-multiplier", "3.0",
        "--enable-reverse-close",
        "--reverse-close-min-positions", "1",
        "--better-price-pct", "0.1",
        "--start-time", "2024-11-01",
        "--end-time", "2024-11-03"  # 3天的数据
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 高级功能测试成功")
            print("输出预览:")
            print(result.stdout[-500:])  # 显示最后500字符
            return True
        else:
            print("❌ 高级功能测试失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 高级功能测试超时")
        return False
    except Exception as e:
        print(f"❌ 高级功能测试异常: {e}")
        return False

def check_output_files():
    """检查输出文件是否生成"""
    print("\n🔍 检查输出文件...")
    
    expected_files = [
        "darts_backtest_money_log_detailed.csv",
        "darts_backtest_performance_metrics.csv"
    ]
    
    found_files = []
    for file in expected_files:
        if os.path.exists(file):
            found_files.append(file)
            print(f"✅ 找到输出文件: {file}")
            
            # 检查文件内容
            if file.endswith('.csv'):
                try:
                    df = pd.read_csv(file)
                    print(f"   📊 文件包含 {len(df)} 行数据")
                    if len(df.columns) > 0:
                        print(f"   📋 列数: {len(df.columns)}")
                except Exception as e:
                    print(f"   ⚠️ 读取文件时出错: {e}")
        else:
            print(f"❌ 未找到输出文件: {file}")
    
    return len(found_files) == len(expected_files)

def validate_results():
    """验证回测结果的合理性"""
    print("\n🔍 验证回测结果...")
    
    try:
        # 检查详细日志
        if os.path.exists("darts_backtest_money_log_detailed.csv"):
            df = pd.read_csv("darts_backtest_money_log_detailed.csv")
            
            if len(df) > 0:
                print(f"✅ 生成了 {len(df)} 笔交易记录")
                
                # 检查必要的列
                required_columns = [
                    'PredictionID', 'StartTimestamp', 'EndTimestamp', 
                    'StartPrice', 'EndPrice', 'Prediction', 'Result', 
                    'Score', 'ProfitLoss', 'CapitalAfter'
                ]
                
                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    print(f"❌ 缺少必要列: {missing_columns}")
                    return False
                else:
                    print("✅ 所有必要列都存在")
                
                # 检查数据合理性
                if df['CapitalAfter'].iloc[-1] > 0:
                    print(f"✅ 最终资金: ${df['CapitalAfter'].iloc[-1]:.2f}")
                else:
                    print(f"⚠️ 最终资金异常: ${df['CapitalAfter'].iloc[-1]:.2f}")
                
                # 检查预测分布
                predictions = df['Prediction'].value_counts()
                print(f"📊 预测分布: {predictions.to_dict()}")
                
                # 检查结果分布
                results = df['Result'].value_counts()
                print(f"📊 结果分布: {results.to_dict()}")
                
                return True
            else:
                print("❌ 没有生成交易记录")
                return False
        else:
            print("❌ 详细日志文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 验证结果时出错: {e}")
        return False

def cleanup_test_files():
    """清理测试生成的文件"""
    print("\n🧹 清理测试文件...")
    
    test_files = [
        "darts_backtest_money_log_detailed.csv",
        "darts_backtest_performance_metrics.csv"
    ]
    
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"🗑️ 删除: {file}")
            except Exception as e:
                print(f"⚠️ 删除文件失败 {file}: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 Darts 详细回测系统测试")
    print("=" * 60)
    
    # 检查前置条件
    if not check_model_files():
        print("\n❌ 测试失败: 缺少模型文件")
        return False
    
    if not check_data_file():
        print("\n❌ 测试失败: 缺少数据文件")
        return False
    
    # 运行测试
    tests_passed = 0
    total_tests = 4
    
    # 测试1: 基础回测
    if run_basic_test():
        tests_passed += 1
    
    # 测试2: 高级功能
    if run_advanced_test():
        tests_passed += 1
    
    # 测试3: 输出文件检查
    if check_output_files():
        tests_passed += 1
    
    # 测试4: 结果验证
    if validate_results():
        tests_passed += 1
    
    # 清理文件
    cleanup_test_files()
    
    # 总结
    print("\n" + "=" * 60)
    print(f"🏁 测试完成: {tests_passed}/{total_tests} 通过")
    print("=" * 60)
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！Darts详细回测系统工作正常。")
        return True
    else:
        print("❌ 部分测试失败，请检查问题。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
