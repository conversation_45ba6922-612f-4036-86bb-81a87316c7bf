"""
Darts LightGBM训练器使用示例
"""

import sys
import os

# 添加父目录到路径以导入依赖模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from darts_lgbm_trainer import train_darts_model, DartsLGBMTrainer
import argparse

def example_eth_5m_training():
    """ETH 5分钟数据训练示例"""
    print("=== ETH 5分钟Darts LightGBM训练示例 ===")
    
    # 模拟命令行参数
    class Args:
        def __init__(self):
            self.coin = "ETH"
            self.db_path = "coin_data.db"
            self.symbol = "ETHUSDT"
            self.interval = "5m"
            self.market = "spot"
            self.start_time = None
            self.end_time = None
            self.no_optimization = False
            self.n_trials = 30  # 减少试验次数以加快示例运行
    
    args = Args()
    
    try:
        train_darts_model(args)
        print("\n✅ ETH 5分钟Darts训练完成")
    except Exception as e:
        print(f"❌ 训练失败: {e}")

def example_quick_training():
    """快速训练示例（禁用优化）"""
    print("=== 快速Darts训练示例（无优化） ===")
    
    class Args:
        def __init__(self):
            self.coin = "ETH"
            self.db_path = "coin_data.db"
            self.symbol = "ETHUSDT"
            self.interval = "5m"
            self.market = "spot"
            self.start_time = None
            self.end_time = None
            self.no_optimization = True  # 禁用优化以加快训练
            self.n_trials = 10
    
    args = Args()
    
    try:
        train_darts_model(args)
        print("\n✅ 快速Darts训练完成")
    except Exception as e:
        print(f"❌ 训练失败: {e}")

def example_custom_date_range():
    """自定义日期范围训练示例"""
    print("=== 自定义日期范围Darts训练示例 ===")
    
    class Args:
        def __init__(self):
            self.coin = "ETH"
            self.db_path = "coin_data.db"
            self.symbol = "ETHUSDT"
            self.interval = "5m"
            self.market = "spot"
            self.start_time = "2024-01-01"
            self.end_time = "2024-12-31"
            self.no_optimization = False
            self.n_trials = 20
    
    args = Args()
    
    try:
        train_darts_model(args)
        print("\n✅ 自定义日期范围Darts训练完成")
    except Exception as e:
        print(f"❌ 训练失败: {e}")

if __name__ == "__main__":
    print("Darts LightGBM训练器使用示例")
    print("请选择要运行的示例:")
    print("1. ETH 5分钟完整训练")
    print("2. 快速训练（无优化）")
    print("3. 自定义日期范围训练")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        example_eth_5m_training()
    elif choice == "2":
        example_quick_training()
    elif choice == "3":
        example_custom_date_range()
    else:
        print("无效选择，运行快速训练示例...")
        example_quick_training()