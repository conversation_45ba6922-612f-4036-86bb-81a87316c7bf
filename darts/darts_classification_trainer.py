"""
Darts 框架的二元分类 LightGBM 训练器
正确实现与原版 trainopt2.py 一致的二元分类功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import lightgbm as lgb
from sklearn.calibration import CalibratedClassifierCV
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import roc_auc_score, accuracy_score, precision_score, recall_score, classification_report
import joblib
import json
import argparse
import os
import pickle
import optuna
import warnings
warnings.filterwarnings('ignore')

# Darts imports for data handling
from darts import TimeSeries
from darts.metrics import mae, rmse

# 添加父目录到路径
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 尝试导入外部工具函数，如果失败则使用内置版本
try:
    from model_utils_815 import calculate_features
    from data_loader import create_data_provider
    HAS_EXTERNAL_UTILS = True
except ImportError:
    HAS_EXTERNAL_UTILS = False
    print("⚠️ 未找到外部工具函数，使用内置简化版本")

# 全局变量
TIMEFRAME_MINUTES = None
UP_THRESHOLD = None
DOWN_THRESHOLD = None
MAX_LOOKFORWARD_MINUTES = None
MODEL_BASENAME = None

class DartsClassificationTrainer:
    """基于Darts数据处理的二元分类LightGBM训练器"""
    
    def __init__(self, config):
        self.config = config
        self.model = None
        self.scaler = None
        self.features = None
        self.best_threshold = 0.5
        
    def create_percentage_target(self, df, up_threshold, down_threshold, max_lookforward_minutes, timeframe):
        """创建目标标签 - 与原版保持一致"""
        max_lookforward_candles = max_lookforward_minutes // timeframe
        print(f"创建目标标签：先涨{up_threshold*100:.1f}%还是先跌{down_threshold*100:.1f}% (最大等待 {max_lookforward_candles} 根K线)")
        labels = np.full(len(df), np.nan)
        
        close_prices = df['close'].to_numpy()
        up_targets = close_prices * (1 + up_threshold)
        down_targets = close_prices * (1 - down_threshold)

        for i in range(len(df) - max_lookforward_candles):
            if i % 20000 == 0:
                print(f"处理标签进度: {i}/{len(df)} ({i/len(df)*100:.1f}%)")
            
            future_window = close_prices[i+1 : i+1+max_lookforward_candles]
            
            up_hit_indices = np.where(future_window >= up_targets[i])[0]
            down_hit_indices = np.where(future_window <= down_targets[i])[0]

            first_up_hit = up_hit_indices[0] if len(up_hit_indices) > 0 else np.inf
            first_down_hit = down_hit_indices[0] if len(down_hit_indices) > 0 else np.inf

            if first_up_hit < first_down_hit:
                labels[i] = 1
            elif first_down_hit < first_up_hit:
                labels[i] = 0

        valid_mask = ~np.isnan(labels)
        valid_indices = df.index[valid_mask]
        valid_labels = labels[valid_mask].astype(int)
        
        print(f"有效标签数量: {len(valid_labels)}/{len(df)} ({len(valid_labels)/len(df)*100:.1f}%)")
        label_series = pd.Series(index=valid_indices, data=valid_labels)

        if len(valid_labels) > 0:
            up_count = np.sum(valid_labels)
            down_count = len(valid_labels) - up_count
            print(f"标签分布: 先涨 = {up_count} ({up_count/len(valid_labels)*100:.1f}%), 先跌 = {down_count} ({down_count/len(valid_labels)*100:.1f}%)")
        else:
            print("未生成任何有效标签。")
        return label_series

    def prepare_time_series_features(self, df_clean, lags=10):
        """准备时间序列特征（滞后特征）"""
        print(f"准备时间序列特征，滞后窗口: {lags}")
        
        # 确保索引是datetime类型
        if not isinstance(df_clean.index, pd.DatetimeIndex):
            df_clean.index = pd.to_datetime(df_clean.index)
        
        # 分离特征和标签
        target_col = 'label'
        feature_cols = [col for col in df_clean.columns if col != target_col]
        
        # 创建滞后特征
        lagged_data = []
        
        for i in range(lags, len(df_clean)):
            # 当前时间点的特征
            current_features = df_clean[feature_cols].iloc[i].values
            
            # 滞后的目标值
            lagged_targets = df_clean[target_col].iloc[i-lags:i].values
            
            # 滞后的特征值（可选，这里只使用滞后目标）
            # lagged_features = df_clean[feature_cols].iloc[i-lags:i].values.flatten()
            
            # 组合特征
            combined_features = np.concatenate([
                current_features,
                lagged_targets
            ])
            
            # 当前标签
            current_label = df_clean[target_col].iloc[i]
            
            lagged_data.append({
                'features': combined_features,
                'label': current_label,
                'timestamp': df_clean.index[i]
            })
        
        print(f"生成 {len(lagged_data)} 个时间序列样本")
        return lagged_data, feature_cols

    def split_time_series_data(self, lagged_data, train_ratio=0.7, val_ratio=0.15):
        """分割时间序列数据"""
        total_len = len(lagged_data)
        train_len = int(total_len * train_ratio)
        val_len = int(total_len * val_ratio)
        
        train_data = lagged_data[:train_len]
        val_data = lagged_data[train_len:train_len + val_len]
        test_data = lagged_data[train_len + val_len:]
        
        print(f"数据分割:")
        print(f"训练集: {len(train_data)} 样本")
        print(f"验证集: {len(val_data)} 样本") 
        print(f"测试集: {len(test_data)} 样本")
        
        return train_data, val_data, test_data

    def prepare_sklearn_data(self, data_list):
        """将时间序列数据转换为sklearn格式"""
        if not data_list:
            return np.array([]), np.array([])
        
        X = np.array([item['features'] for item in data_list])
        y = np.array([item['label'] for item in data_list])
        
        # 检查并处理 NaN 值
        if np.isnan(X).any():
            print(f"⚠️ 发现 {np.isnan(X).sum()} 个 NaN 值，进行填充")
            X = np.nan_to_num(X, nan=0.0)
        
        if np.isnan(y).any():
            print(f"⚠️ 标签中发现 {np.isnan(y).sum()} 个 NaN 值，进行填充")
            y = np.nan_to_num(y, nan=0)
        
        return X, y

    def optimize_hyperparameters(self, train_data, val_data, n_trials=50):
        """使用Optuna优化超参数 - 二元分类版本"""
        print(f"\n=== 开始二元分类超参数优化 (n_trials={n_trials}) ===")
        
        X_train, y_train = self.prepare_sklearn_data(train_data)
        X_val, y_val = self.prepare_sklearn_data(val_data)
        
        def objective(trial):
            # LightGBM 二元分类参数
            params = {
                'objective': 'binary',  # 🔥 关键：二元分类
                'metric': 'binary_logloss',
                'n_estimators': trial.suggest_int('n_estimators', 50, 500),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'num_leaves': trial.suggest_int('num_leaves', 10, 100),
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 50),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 1.0),
                'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 1.0),
                'random_state': 42,
                'verbose': -1
            }
            
            try:
                # 创建分类器
                model = lgb.LGBMClassifier(**params)
                
                # 训练模型
                model.fit(X_train, y_train)
                
                # 预测验证集
                y_pred_proba = model.predict_proba(X_val)[:, 1]
                
                # 计算AUC作为优化目标
                auc_score = roc_auc_score(y_val, y_pred_proba)
                return auc_score
                
            except Exception as e:
                print(f"Trial failed: {e}")
                return 0.0
        
        # 运行优化
        optuna.logging.set_verbosity(optuna.logging.WARNING)
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=n_trials)
        
        print(f"=== 超参数优化完成 ===")
        print(f"最佳AUC: {study.best_value:.4f}")
        print(f"最佳参数: {study.best_params}")
        
        return study.best_params, study.best_value

    def train_classification_model(self, train_data, val_data, best_params=None):
        """训练二元分类模型"""
        print("\n开始训练二元分类LightGBM模型...")
        
        # 准备数据
        X_train, y_train = self.prepare_sklearn_data(train_data)
        X_val, y_val = self.prepare_sklearn_data(val_data)
        
        print(f"训练数据形状: X={X_train.shape}, y={y_train.shape}")
        print(f"验证数据形状: X={X_val.shape}, y={y_val.shape}")
        
        # 默认参数 - 二元分类
        default_params = {
            'objective': 'binary',  # 🔥 关键：二元分类
            'metric': 'binary_logloss',
            'n_estimators': 200,
            'learning_rate': 0.1,
            'max_depth': 6,
            'num_leaves': 31,
            'min_child_samples': 20,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 0.1,
            'random_state': 42,
            'verbose': -1
        }
        
        if best_params:
            default_params.update(best_params)
        
        # 创建并训练分类器
        self.model = lgb.LGBMClassifier(**default_params)
        
        # 训练
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            eval_metric='binary_logloss',
            callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False)]
        )
        
        print("二元分类模型训练完成")
        return self.model

    def evaluate_classification_model(self, test_data):
        """评估二元分类模型"""
        print("\n=== 二元分类模型评估 ===")
        
        X_test, y_test = self.prepare_sklearn_data(test_data)
        
        # 预测
        y_pred = self.model.predict(X_test)
        y_pred_proba = self.model.predict_proba(X_test)[:, 1]
        
        # 计算指标
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
        recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
        auc = roc_auc_score(y_test, y_pred_proba)
        
        print(f"准确率: {accuracy:.4f}")
        print(f"精确率: {precision:.4f}")
        print(f"召回率: {recall:.4f}")
        print(f"AUC: {auc:.4f}")
        
        # 详细分类报告
        print("\n分类报告:")
        print(classification_report(y_test, y_pred))
        
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'auc': auc,
            'predictions': y_pred,
            'probabilities': y_pred_proba
        }

    def save_classification_model(self, coin_config, model_name=None):
        """保存二元分类模型和配置"""
        if model_name is None:
            model_name = f"darts_classification_{coin_config['model_basename']}"
        
        # 保存在 darts 目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        output_dir = current_dir
        model_file = os.path.join(output_dir, f'{model_name}_model.joblib')
        config_file = os.path.join(output_dir, f'{model_name}_config.json')
        
        # 保存模型
        joblib.dump(self.model, model_file)
        
        # 保存配置
        config = {
            'model_type': 'Binary_Classification_LightGBM',
            'model_name': model_name,
            'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'up_threshold': coin_config['up_threshold'],
            'down_threshold': coin_config['down_threshold'],
            'max_lookforward_minutes': coin_config['max_lookforward_minutes'],
            'timeframe_minutes': coin_config['timeframe_minutes'],
            'features': self.features,
            'best_threshold': self.best_threshold,
            'objective': 'binary',  # 明确标记为二元分类
            'target_description': f'predict_first_{coin_config["up_threshold"]*100}%_move_within_{coin_config["max_lookforward_minutes"]}_minutes'
        }
        
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"\n二元分类模型已保存:")
        print(f"模型文件: {model_file}")
        print(f"配置文件: {config_file}")

def calculate_basic_features(df, timeframe=5):
    """计算基础技术指标特征"""
    print("计算基础技术指标...")
    
    # 价格特征
    df['price_change'] = df['close'].pct_change()
    df['price_change_abs'] = df['price_change'].abs()
    
    # 移动平均
    df['sma_5'] = df['close'].rolling(5).mean()
    df['sma_10'] = df['close'].rolling(10).mean()
    df['sma_20'] = df['close'].rolling(20).mean()
    
    # 价格位置
    df['price_vs_sma5'] = df['close'] / df['sma_5'] - 1
    df['price_vs_sma20'] = df['close'] / df['sma_20'] - 1
    
    # 波动率
    df['volatility_5'] = df['close'].rolling(5).std()
    df['volatility_20'] = df['close'].rolling(20).std()
    
    # 成交量特征
    df['volume_sma'] = df['volume'].rolling(10).mean()
    df['volume_ratio'] = df['volume'] / df['volume_sma']
    
    # RSI 简化版
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # MACD 简化版
    ema12 = df['close'].ewm(span=12).mean()
    ema26 = df['close'].ewm(span=26).mean()
    df['macd'] = ema12 - ema26
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    
    return df

def prepare_features_and_labels(df, coin_config, symbol='ETHUSDT', market='spot'):
    """准备特征和标签"""
    if HAS_EXTERNAL_UTILS:
        print("使用外部特征工程...")
        df_with_features = calculate_features(df.copy(), timeframe=coin_config['timeframe_minutes'])
    else:
        print("使用内置特征工程...")
        df_with_features = calculate_basic_features(df.copy(), timeframe=coin_config['timeframe_minutes'])
    
    print("创建目标标签...")
    trainer = DartsClassificationTrainer({})
    target_labels = trainer.create_percentage_target(
        df, 
        coin_config['up_threshold'], 
        coin_config['down_threshold'], 
        coin_config['max_lookforward_minutes'], 
        coin_config['timeframe_minutes']
    )
    
    print("合并特征与标签...")
    df_combined = df_with_features.join(target_labels.rename('label'), how='inner')
    df_clean = df_combined.dropna()
    print(f"清理NaN后剩余 {len(df_clean)} 条记录")
    return df_clean

def get_builtin_coin_config(coin_name):
    """内置币种配置，避免依赖外部文件"""
    configs = {
        "ETH": {
            "db_path": "coin_data.db",
            "api_symbol": "ETHUSDT",
            "display_name": "ETH/USDT",
            "timeframe_minutes": 5,
            "up_threshold": 0.02,
            "down_threshold": 0.02,
            "max_lookforward_minutes": 240,
            "model_basename": "eth_5m",
            "price_multiplier": 1.0
        },
        "BTC": {
            "db_path": "coin_data.db",
            "api_symbol": "BTCUSDT",
            "display_name": "BTC/USDT",
            "timeframe_minutes": 5,
            "up_threshold": 0.02,
            "down_threshold": 0.02,
            "max_lookforward_minutes": 240,
            "model_basename": "btc_5m",
            "price_multiplier": 1.0
        }
    }
    return configs.get(coin_name)

def train_classification_model(args):
    """主训练函数 - 二元分类版本"""
    # 使用内置配置
    coin_config = get_builtin_coin_config(args.coin)
    if coin_config is None:
        print(f"❌ 不支持的币种 {args.coin}，支持的币种: ETH, BTC")
        return
    
    print(f"开始训练二元分类模型 - {coin_config['model_basename'].replace('_', ' ').title()}")
    
    # 加载数据
    try:
        if HAS_EXTERNAL_UTILS:
            # 使用外部数据加载器
            data_source = {
                'type': 'sqlite',
                'db_path': args.db_path or coin_config.get('db_path', 'coin_data.db'),
                'symbol': args.symbol or coin_config.get('api_symbol'),
                'interval': args.interval or f"{coin_config['timeframe_minutes']}m",
                'market': args.market
            }
            
            print(f"数据源配置: {data_source}")
            
            price_multiplier = coin_config.get('price_multiplier', 1.0)
            data_provider = create_data_provider(data_source, price_multiplier)
            df = data_provider.get_initial_data(initial_count=1000000)
        else:
            # 使用简化的数据加载
            import sqlite3
            db_path = args.db_path or '../coin_data.db'
            symbol = args.symbol or coin_config.get('api_symbol')
            interval = args.interval or f"{coin_config['timeframe_minutes']}m"
            market = args.market
            
            # 构建表名
            table_name = f"{symbol}_{interval.replace('m', 'min')}_{market}"
            
            print(f"从数据库加载: {db_path}, 表: {table_name}")
            
            conn = sqlite3.connect(db_path)
            query = f"""
            SELECT timestamp, open, high, low, close, volume 
            FROM {table_name} 
            ORDER BY timestamp DESC 
            LIMIT 300000
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            # 转换时间戳
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            df = df.sort_index()
        
        if df is None or len(df) == 0:
            print("❌ 无法加载数据")
            return
        
        print(f"✅ 成功加载 {len(df)} 条数据")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 准备特征和标签
    df_clean = prepare_features_and_labels(df, coin_config, args.symbol, args.market)
    
    # 创建训练器
    trainer = DartsClassificationTrainer(args.__dict__)
    
    # 准备时间序列特征
    lagged_data, feature_cols = trainer.prepare_time_series_features(df_clean, lags=10)
    trainer.features = feature_cols
    
    if len(lagged_data) < 100:
        print("❌ 时间序列数据太少，无法训练")
        return
    
    # 分割数据
    train_data, val_data, test_data = trainer.split_time_series_data(lagged_data)
    
    # 超参数优化
    best_params = None
    if not args.no_optimization:
        best_params, best_score = trainer.optimize_hyperparameters(
            train_data, val_data, n_trials=args.n_trials
        )
    
    # 训练模型
    model = trainer.train_classification_model(train_data, val_data, best_params)
    
    # 评估验证集
    print("\n=== 验证集评估 ===")
    val_metrics = trainer.evaluate_classification_model(val_data)
    
    # 评估测试集
    print("\n=== 测试集评估 ===")
    test_metrics = trainer.evaluate_classification_model(test_data)
    
    # 保存模型
    try:
        trainer.save_classification_model(coin_config)
    except Exception as e:
        print(f"⚠️ 模型保存失败: {e}")
        print("但训练和评估已成功完成")
    
    print(f"\n=== 二元分类训练完成 ===")
    print(f"币种: {coin_config['display_name']}")
    print(f"模型类型: 二元分类 LightGBM")
    print(f"特征数量: {len(feature_cols)}")
    print(f"验证集准确率: {val_metrics['accuracy']:.4f}")
    print(f"验证集AUC: {val_metrics['auc']:.4f}")
    print(f"测试集准确率: {test_metrics['accuracy']:.4f}")
    print(f"测试集AUC: {test_metrics['auc']:.4f}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Darts框架二元分类LightGBM训练器")
    parser.add_argument("--coin", default="ETH", help="币种名称 (如: DOT, SEI)")
    parser.add_argument("--db-path", default='coin_data.db', help="SQLite数据库路径")
    parser.add_argument("--symbol", help="交易对符号")
    parser.add_argument("--interval", help="时间间隔")
    parser.add_argument("--market", choices=['spot', 'futures'], default='spot', help="市场类型")
    parser.add_argument("--start-time", help="数据开始时间 (YYYY-MM-DD)")
    parser.add_argument("--end-time", help="数据结束时间 (YYYY-MM-DD)")
    parser.add_argument("--no-optimization", action='store_true', help="禁用超参数优化")
    parser.add_argument("--n-trials", type=int, default=50, help="Optuna优化试验次数")
    
    args = parser.parse_args()
    
    print(f"=== Darts 二元分类 LightGBM 训练器 ===")
    print(f"币种: {args.coin}")
    train_classification_model(args)

if __name__ == '__main__':
    main()