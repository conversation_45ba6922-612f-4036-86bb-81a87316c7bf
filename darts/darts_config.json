{"model_config": {"model_type": "LightGBM", "framework": "darts", "default_params": {"lags": 20, "lags_future_covariates": 5, "n_estimators": 500, "learning_rate": 0.1, "max_depth": 6, "num_leaves": 31, "min_child_samples": 20, "subsample": 0.8, "colsample_bytree": 0.8, "random_state": 42}}, "optimization_config": {"enabled": true, "n_trials": 50, "parameter_ranges": {"lags": [5, 50], "lags_future_covariates": [1, 10], "n_estimators": [100, 1000], "learning_rate": [0.01, 0.3], "max_depth": [3, 10], "num_leaves": [10, 100], "min_child_samples": [5, 50], "subsample": [0.6, 1.0], "colsample_bytree": [0.6, 1.0]}}, "data_config": {"train_ratio": 0.7, "val_ratio": 0.15, "test_ratio": 0.15, "scaling": true}, "evaluation_metrics": ["mae", "rmse", "mape", "accuracy", "precision", "recall"]}