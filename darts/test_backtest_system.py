#!/usr/bin/env python3
"""
测试Darts回测系统
验证特征工程一致性和回测功能
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from darts_classification_trainer_fixed import FixedDartsClassificationTrainer, calculate_basic_features
from backtest_darts_model import DartsBacktester

def create_test_data(n_samples=2000):
    """创建测试数据"""
    print(f"创建 {n_samples} 条测试数据...")
    
    # 创建时间序列
    start_time = datetime.now() - timedelta(days=10)
    timestamps = [start_time + timedelta(minutes=5*i) for i in range(n_samples)]
    
    # 生成模拟价格数据
    np.random.seed(42)
    base_price = 3000.0
    
    # 随机游走生成价格
    price_changes = np.random.normal(0, 0.01, n_samples)
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 100))
    
    # 创建OHLCV数据
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        volatility = abs(np.random.normal(0, 0.005))
        high = close * (1 + volatility)
        low = close * (1 - volatility)
        open_price = close + np.random.normal(0, close * 0.002)
        volume = np.random.uniform(1000, 10000)
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    
    print(f"✅ 测试数据创建完成: {len(df)} 条记录")
    return df

def test_feature_consistency():
    """测试特征工程一致性"""
    print("\n=== 测试特征工程一致性 ===")
    
    # 创建测试数据
    df = create_test_data(1000)
    
    # 创建训练器
    trainer = FixedDartsClassificationTrainer({})
    
    # 计算特征
    df_with_features = calculate_basic_features(df.copy(), timeframe=5)
    
    # 创建虚拟标签
    df_with_features['label'] = np.random.choice([0, 1], size=len(df_with_features))
    df_clean = df_with_features.dropna()
    
    print(f"特征计算完成，数据形状: {df_clean.shape}")
    
    # 准备安全特征（训练方式）
    lagged_data, feature_names = trainer.prepare_safe_features(df_clean, lags=5)
    
    print(f"训练方式特征数量: {len(feature_names)}")
    print(f"样本数量: {len(lagged_data)}")
    
    # 模拟回测方式的特征准备
    from backtest_darts_model import DartsBacktester
    
    # 创建虚拟配置
    config = {
        'best_threshold': 0.65,
        'max_lookforward_minutes': 240,
        'timeframe_minutes': 5,
        'up_threshold': 0.02,
        'down_threshold': 0.02,
        'features': feature_names
    }
    
    # 保存虚拟配置用于测试
    import json
    with open('test_config.json', 'w') as f:
        json.dump(config, f)
    
    # 创建虚拟模型（简单的随机分类器）
    from sklearn.ensemble import RandomForestClassifier
    import joblib
    
    # 准备训练数据
    X_train = np.array([item['features'] for item in lagged_data[:500]])
    y_train = np.array([item['label'] for item in lagged_data[:500]])
    
    # 训练虚拟模型
    dummy_model = RandomForestClassifier(n_estimators=10, random_state=42)
    dummy_model.fit(X_train, y_train)
    
    # 保存虚拟模型
    joblib.dump(dummy_model, 'test_model.joblib')
    
    print("✅ 特征一致性测试准备完成")
    
    # 测试回测器的特征准备
    try:
        backtester = DartsBacktester('test_model.joblib', 'test_config.json', 1000, 1.0)
        
        # 测试特征准备
        window_df = df.iloc[-200:].copy()  # 使用最后200条数据作为窗口
        features_array, pred_price = backtester.prepare_features_for_prediction(window_df, lags=5)
        
        if features_array is not None:
            print(f"回测方式特征数量: {len(features_array)}")
            print(f"预测价格: {pred_price:.4f}")
            
            # 测试预测
            guess, probability = backtester.make_prediction(features_array)
            print(f"预测结果: {guess}, 概率: {probability:.3f}")
            
            print("✅ 特征一致性测试通过")
        else:
            print("❌ 特征准备失败")
            
    except Exception as e:
        print(f"❌ 回测器测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 清理测试文件
    for file in ['test_model.joblib', 'test_config.json']:
        if os.path.exists(file):
            os.remove(file)

def test_backtest_workflow():
    """测试完整回测流程"""
    print("\n=== 测试完整回测流程 ===")
    
    # 检查是否有真实的模型文件
    model_file = "darts_fixed_eth_5m_model.joblib"
    config_file = "darts_fixed_eth_5m_config.json"
    
    if not os.path.exists(model_file) or not os.path.exists(config_file):
        print("⚠️ 未找到真实模型文件，跳过完整流程测试")
        print("请先运行 darts_classification_trainer_fixed.py 生成模型")
        return
    
    # 创建测试数据
    df = create_test_data(1500)
    
    try:
        # 导入回测函数
        from backtest_darts_model import run_darts_backtest
        
        print("开始小规模回测测试...")
        
        # 运行小规模回测
        backtester = run_darts_backtest(
            model_file=model_file,
            config_file=config_file,
            df=df,
            initial_capital=1000,
            risk_per_trade_pct=1.0,
            stop_loss_pct=2.0,
            max_active_predictions=3,
            prediction_window_size=300,
            lags=5
        )
        
        print("✅ 完整回测流程测试通过")
        
        # 检查结果文件
        if os.path.exists("darts_backtest_results.csv"):
            results_df = pd.read_csv("darts_backtest_results.csv")
            print(f"📊 生成了 {len(results_df)} 条回测记录")
            
            if len(results_df) > 0:
                print("前5条记录预览:")
                print(results_df[['StartTimestamp', 'Prediction', 'Result', 'Score', 'ProfitLoss']].head())
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_parameter_validation():
    """测试参数验证"""
    print("\n=== 测试参数验证 ===")
    
    # 测试不存在的文件
    try:
        backtester = DartsBacktester('nonexistent.joblib', 'nonexistent.json', 1000, 1.0)
        print("❌ 应该检测到文件不存在")
    except Exception as e:
        print(f"✅ 正确检测到文件不存在: {type(e).__name__}")
    
    # 测试其他参数验证...
    print("✅ 参数验证测试完成")

def run_all_tests():
    """运行所有测试"""
    print("=== Darts回测系统测试套件 ===")
    
    try:
        # 1. 特征一致性测试
        test_feature_consistency()
        
        # 2. 参数验证测试
        test_parameter_validation()
        
        # 3. 完整流程测试
        test_backtest_workflow()
        
        print("\n=== 测试总结 ===")
        print("✅ 所有测试完成")
        print("📋 如需运行真实回测，请使用:")
        print("   python run_darts_backtest.py")
        print("📖 详细说明请查看:")
        print("   DARTS_BACKTEST_GUIDE.md")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    run_all_tests()