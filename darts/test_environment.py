#!/usr/bin/env python3
"""
Darts LightGBM 快速测试脚本
"""

import sys
import os

def test_imports():
    """测试导入"""
    print("测试导入...")
    try:
        import darts
        import optuna
        import pandas as pd
        import numpy as np
        import lightgbm as lgb
        from sklearn.metrics import accuracy_score
        print("✅ 所有导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_darts_basic():
    """测试 Darts 基本功能"""
    print("测试 Darts 基本功能...")
    try:
        from darts import TimeSeries
        from darts.models import LightGBMModel
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        values = np.random.randn(100).cumsum()
        ts = TimeSeries.from_dataframe(
            pd.DataFrame({'date': dates, 'value': values}),
            time_col='date',
            value_cols=['value']
        )
        
        # 测试模型创建
        model = LightGBMModel(lags=5, output_chunk_length=1)
        print("✅ Darts 基本功能测试通过")
        return True
    except Exception as e:
        print(f"❌ Darts 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=== Darts LightGBM 环境测试 ===")
    
    success = True
    success &= test_imports()
    success &= test_darts_basic()
    
    if success:
        print("\n✅ 所有测试通过，环境配置正确")
    else:
        print("\n❌ 测试失败，请检查环境配置")
        sys.exit(1)
