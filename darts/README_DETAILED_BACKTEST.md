# Darts 详细回测系统

## 概述

基于 `backtest_money_quick.py` 开发的专门用于 Darts 模型的详细回测脚本，支持完整的资金管理、止损、SuperTrend过滤、反向平仓、挂单买入等高级功能。

## 🚀 快速开始

### 1. 一键启动
```bash
cd darts
python quick_start_darts_backtest.py
```

### 2. 手动步骤

#### 训练模型（如果还没有）
```bash
python darts_classification_trainer_fixed.py --coin ETH
```

#### 运行基础回测
```bash
python backtest_darts_detailed.py --coin ETH
```

#### 运行完整功能回测
```bash
python backtest_darts_detailed.py \
    --coin ETH \
    --initial-capital 10000 \
    --risk-per-trade 2.0 \
    --stop-loss 2.5 \
    --use-supertrend \
    --enable-reverse-close \
    --better-price-pct 0.1
```

## 📁 主要文件

| 文件 | 描述 |
|------|------|
| `backtest_darts_detailed.py` | 主要回测脚本 |
| `darts_classification_trainer_fixed.py` | 模型训练脚本 |
| `quick_start_darts_backtest.py` | 快速开始脚本 |
| `test_detailed_backtest.py` | 功能测试脚本 |
| `DARTS_DETAILED_BACKTEST_GUIDE.md` | 详细使用指南 |

## 🎯 核心特性

### ✅ Darts模型专用适配
- 与训练时保持一致的特征工程
- 滞后特征自动处理
- 安全特征集，避免数据泄露

### ✅ 完整功能移植
- 资金管理和风险控制
- 止损、SuperTrend过滤
- 反向平仓、挂单买入
- 详细性能指标分析

### ✅ 智能特征处理
- 自动移除高相关性特征
- 特征数量自动匹配
- 滞后特征生成（5个滞后期）

## 🛠️ 使用示例

### 基础回测
```bash
# 最简单的用法
python backtest_darts_detailed.py --coin ETH

# 指定时间范围
python backtest_darts_detailed.py \
    --coin ETH \
    --start-time "2024-01-01" \
    --end-time "2024-12-31"
```

### 风险控制
```bash
# 启用止损
python backtest_darts_detailed.py \
    --coin ETH \
    --stop-loss 2.5

# 启用SuperTrend过滤
python backtest_darts_detailed.py \
    --coin ETH \
    --use-supertrend \
    --supertrend-interval 30m
```

### 高级功能
```bash
# 反向平仓
python backtest_darts_detailed.py \
    --coin ETH \
    --enable-reverse-close \
    --reverse-close-min-positions 2

# 挂单买入
python backtest_darts_detailed.py \
    --coin ETH \
    --better-price-pct 0.1

# 时间过滤
python backtest_darts_detailed.py \
    --coin ETH \
    --use-chushou \
    --chushou-file ../chushou.json
```

## 📊 输出文件

### 主要输出
- `darts_backtest_money_log_detailed.csv` - 详细交易日志
- `darts_backtest_performance_metrics.csv` - 性能指标
- 自动生成的分析图表

### 日志字段
- 基础信息：预测ID、时间戳、价格、预测结果
- 盈亏信息：得分、盈亏金额、资金变化
- 风险信息：最大盈利、最大亏损
- 高级信息：SuperTrend信号、挂单信息

## 🧪 测试验证

```bash
# 运行完整测试
python test_detailed_backtest.py

# 测试包括：
# - 模型文件检查
# - 基础回测功能
# - 高级功能测试
# - 输出文件验证
```

## ⚙️ 参数说明

### 基础参数
- `--coin`: 币种 (ETH, BTC)
- `--interval`: K线间隔 (5m, 15m, 1h)
- `--initial-capital`: 初始资金 (默认: 1000)
- `--risk-per-trade`: 单次风险比例% (默认: 1.0)

### 风险控制
- `--stop-loss`: 止损百分比
- `--use-supertrend`: 启用SuperTrend过滤
- `--supertrend-interval`: SuperTrend周期 (默认: 30m)

### 高级功能
- `--enable-reverse-close`: 启用反向平仓
- `--better-price-pct`: 挂单期望更好价格%
- `--use-chushou`: 启用时间过滤

## 🔧 故障排除

### 常见问题

1. **模型文件不存在**
   ```bash
   # 解决方案：训练模型
   python darts_classification_trainer_fixed.py --coin ETH
   ```

2. **特征数量不匹配**
   - 确保使用相同的特征工程方法
   - 检查模型配置文件

3. **数据不足**
   - 检查数据库文件是否存在
   - 确保有足够的历史数据

4. **回测速度慢**
   - 减少时间范围
   - 降低最大活跃预测数

### 性能优化

- 使用预计算特征模式
- 合理设置时间范围
- 调整预测窗口大小

## 📈 性能指标

### 基础指标
- 总收益率、胜率、最大回撤

### 风险调整指标
- 夏普比率、Sortino比率、Calmar比率

### 交易统计
- 平均盈利、平均亏损、盈亏比

## 🔗 相关文档

- [详细使用指南](DARTS_DETAILED_BACKTEST_GUIDE.md)
- [Darts训练器文档](darts_classification_trainer_fixed.py)
- [项目总体文档](../project.md)

## 💡 最佳实践

1. **模型准备**: 使用 `darts_classification_trainer_fixed.py` 训练
2. **参数调优**: 先用小数据集测试
3. **风险控制**: 合理设置止损和仓位
4. **功能组合**: 根据策略选择功能
5. **结果验证**: 使用测试脚本验证

## 🆚 与原版对比

| 功能 | backtest_money_quick.py | backtest_darts_detailed.py |
|------|------------------------|----------------------------|
| 模型支持 | 通用LightGBM | Darts专用 |
| 特征工程 | 实时计算 | 训练时一致 |
| 滞后特征 | ❌ | ✅ |
| 数据泄露防护 | 基础 | 高级 |
| 配置管理 | 通用 | 专用 |

---

**开始使用**: `python quick_start_darts_backtest.py`

**获取帮助**: `python backtest_darts_detailed.py --help`
