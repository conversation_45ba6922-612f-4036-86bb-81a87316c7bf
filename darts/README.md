# Darts LightGBM 训练器

基于 `trainopt2.py` 移植到 Darts 框架的 LightGBM 训练器，提供时间序列预测功能。

## 功能特性

- **Darts 框架集成**: 使用 Darts 的 LightGBMModel 进行时间序列建模
- **超参数优化**: 集成 Optuna 进行自动超参数调优
- **多种评估指标**: 支持回归和分类指标评估
- **灵活配置**: 支持命令行参数和配置文件
- **模型保存**: 自动保存训练好的模型和配置

## 安装依赖

```bash
pip install darts[lightgbm] optuna pandas numpy scikit-learn joblib
```

## 文件结构

```
darts/
├── darts_lgbm_trainer.py    # 主训练脚本
├── darts_config.json        # 配置文件
├── example_usage.py         # 使用示例
└── README.md               # 说明文档
```

## 使用方法

### 1. 命令行使用

```bash
# 基本训练
python darts_lgbm_trainer.py --coin ETH

# 指定参数训练
python darts_lgbm_trainer.py --coin ETH --symbol ETHUSDT --interval 5m --n-trials 100

# 禁用超参数优化（快速训练）
python darts_lgbm_trainer.py --coin ETH --no-optimization

# 指定日期范围
python darts_lgbm_trainer.py --coin ETH --start-time 2024-01-01 --end-time 2024-12-31
```

### 2. 程序化使用

```python
from darts_lgbm_trainer import DartsLGBMTrainer, train_darts_model

# 创建参数对象
class Args:
    def __init__(self):
        self.coin = "ETH"
        self.db_path = "coin_data.db"
        self.symbol = "ETHUSDT"
        self.interval = "5m"
        self.market = "spot"
        self.no_optimization = False
        self.n_trials = 50

args = Args()
train_darts_model(args)
```

### 3. 运行示例

```bash
python example_usage.py
```

## 主要参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--coin` | 币种名称 | ETH |
| `--symbol` | 交易对符号 | 从配置获取 |
| `--interval` | 时间间隔 | 从配置获取 |
| `--market` | 市场类型 (spot/futures) | spot |
| `--db-path` | 数据库路径 | coin_data.db |
| `--start-time` | 开始时间 (YYYY-MM-DD) | None |
| `--end-time` | 结束时间 (YYYY-MM-DD) | None |
| `--no-optimization` | 禁用超参数优化 | False |
| `--n-trials` | Optuna试验次数 | 50 |

## 与原版 trainopt2.py 的区别

### 相同功能
- ✅ 特征工程流程
- ✅ 目标标签创建逻辑
- ✅ 数据分割策略
- ✅ 超参数优化
- ✅ 模型评估

### Darts 框架优势
- 🚀 **时间序列专用**: 专为时间序列设计的框架
- 📊 **丰富的指标**: 内置多种时间序列评估指标
- 🔄 **数据处理**: 自动处理时间序列数据格式
- 🎯 **预测功能**: 更强大的预测和回测功能
- 📈 **可视化**: 内置可视化工具

### 新增功能
- TimeSeries 数据格式转换
- Darts 特定的超参数优化
- 时间序列回归和分类双重评估
- 更灵活的滞后特征配置

## 输出文件

训练完成后会生成以下文件：
- `{model_name}_darts_model.pkl`: Darts模型文件
- `{model_name}_darts_config.json`: 模型配置文件

## 评估指标

### 回归指标
- MAE (Mean Absolute Error)
- RMSE (Root Mean Square Error)  
- MAPE (Mean Absolute Percentage Error)

### 分类指标
- Accuracy (准确率)
- Precision (精确率)
- Recall (召回率)

## 配置文件说明

`darts_config.json` 包含：
- 模型默认参数
- 优化参数范围
- 数据分割配置
- 评估指标设置

## 注意事项

1. **数据格式**: 确保数据索引为 datetime 类型
2. **内存使用**: Darts 可能比原版使用更多内存
3. **依赖版本**: 确保 Darts 版本兼容性
4. **特征数量**: 过多特征可能影响 Darts 性能

## 故障排除

### 常见问题

1. **导入错误**: 确保安装了所有依赖
2. **内存不足**: 减少数据量或特征数量
3. **优化失败**: 使用 `--no-optimization` 跳过优化
4. **数据格式**: 检查时间索引格式

### 性能优化建议

1. 使用 `--no-optimization` 进行快速测试
2. 减少 `--n-trials` 数量
3. 限制特征数量
4. 使用较小的数据集进行初始测试