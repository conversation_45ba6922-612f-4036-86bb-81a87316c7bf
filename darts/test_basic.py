#!/usr/bin/env python3
"""
基本功能测试脚本
"""

import sys
import os

# 添加父目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

def test_imports():
    """测试基本导入"""
    print("=== 测试导入 ===")
    try:
        # 测试基本库导入
        import pandas as pd
        import numpy as np
        print("✅ pandas, numpy 导入成功")
        
        # 测试 model_utils 导入
        from model_utils_815 import get_coin_config, calculate_features, get_output_dir
        print("✅ model_utils_815 导入成功")
        
        # 测试 data_loader 导入
        from data_loader import load_data_for_training
        print("✅ data_loader 导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_config():
    """测试配置加载"""
    print("\n=== 测试配置加载 ===")
    try:
        from model_utils_815 import get_coin_config
        
        # 测试 ETH 配置 - 使用父目录的配置文件
        config_path = os.path.join(parent_dir, 'config.json')
        config = get_coin_config("ETH", config_path)
        if config is None:
            print("❌ ETH 配置加载失败")
            return False
        
        print(f"✅ ETH 配置加载成功:")
        print(f"  - 显示名称: {config.get('display_name', 'N/A')}")
        print(f"  - 时间框架: {config.get('timeframe_minutes', 'N/A')} 分钟")
        print(f"  - 模型基名: {config.get('model_basename', 'N/A')}")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_darts_imports():
    """测试 Darts 相关导入"""
    print("\n=== 测试 Darts 导入 ===")
    try:
        # 测试 darts 导入
        from darts import TimeSeries
        from darts.models import LightGBMModel
        print("✅ Darts 核心模块导入成功")
        
        # 测试 optuna 导入
        import optuna
        print("✅ Optuna 导入成功")
        
        # 测试 lightgbm 导入
        import lightgbm as lgb
        print("✅ LightGBM 导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ Darts 相关导入失败: {e}")
        print("请运行: pip install darts[lightgbm] optuna")
        return False

def test_trainer_import():
    """测试训练器导入"""
    print("\n=== 测试训练器导入 ===")
    try:
        from darts_lgbm_trainer import DartsLGBMTrainer, train_darts_model
        print("✅ Darts 训练器导入成功")
        
        # 创建训练器实例
        trainer = DartsLGBMTrainer({})
        print("✅ 训练器实例创建成功")
        
        return True
    except Exception as e:
        print(f"❌ 训练器导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Darts LightGBM 基本功能测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_darts_imports,
        test_trainer_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print("⚠️ 测试失败，可能影响后续功能")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过，可以开始使用 Darts 训练器")
        return True
    else:
        print("❌ 部分测试失败，请检查环境配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)