#!/usr/bin/env python3
"""
测试可选退出机制功能的脚本
"""

import subprocess
import sys
import os

def run_backtest_with_options(exit_options, description):
    """运行带有特定退出选项的回测"""
    print(f"\n{'='*60}")
    print(f"测试: {description}")
    print(f"{'='*60}")
    
    base_cmd = [
        sys.executable, "backtest_money_quick.py",
        "--coin", "ETH",
        "--interval", "15m", 
        "--market", "spot",
        "--db", "coin_data.db",
        "--initial-capital", "10000",
        "--risk-per-trade", "1.0",
        "--max-active-predictions", "3",
        "--quick",
        "--start-time", "2024-01-01",
        "--end-time", "2024-01-05"
    ]
    
    # 添加退出机制选项
    cmd = base_cmd + exit_options
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ 测试成功")
            
            # 提取关键信息
            lines = result.stdout.split('\n')
            for line in lines:
                if "启用的退出机制:" in line:
                    print(f"📋 {line.strip()}")
                elif "回测结果摘要" in line:
                    # 找到摘要部分
                    idx = lines.index(line)
                    if idx + 1 < len(lines):
                        print(f"📊 {lines[idx + 1].strip()}")
                elif "总收益率:" in line:
                    print(f"💰 {line.strip()}")
                elif "⚠️  警告:" in line:
                    print(f"⚠️  {line.strip()}")
            
            return True
        else:
            print(f"❌ 测试失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_exit_mechanisms():
    """测试各种退出机制组合"""
    print("=== 测试可选退出机制功能 ===")
    
    # 检查必要文件
    required_files = ["models/eth_15m_model.joblib", "models/eth_15m_config.json", "coin_data.db"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print("✅ 所有必要文件都存在")
    
    # 测试用例
    test_cases = [
        {
            "options": [],
            "description": "默认模式 (所有退出机制都启用)"
        },
        {
            "options": ["--disable-take-profit"],
            "description": "禁用止盈 (只有止损、超时、反向平仓)"
        },
        {
            "options": ["--disable-stop-loss"],
            "description": "禁用止损 (只有止盈、超时、反向平仓)"
        },
        {
            "options": ["--disable-timeout"],
            "description": "禁用超时 (只有止盈、止损、反向平仓)"
        },
        {
            "options": ["--enable-reverse-close"],
            "description": "启用反向平仓 (所有退出机制)"
        },
        {
            "options": ["--disable-take-profit", "--disable-stop-loss"],
            "description": "只启用超时和反向平仓"
        },
        {
            "options": ["--disable-take-profit", "--disable-timeout"],
            "description": "只启用止损和反向平仓"
        },
        {
            "options": ["--disable-stop-loss", "--disable-timeout"],
            "description": "只启用止盈和反向平仓"
        },
        {
            "options": ["--enable-reverse-close", "--disable-take-profit", "--disable-stop-loss", "--disable-timeout"],
            "description": "只启用反向平仓 (极端测试)"
        },
        {
            "options": ["--disable-take-profit", "--disable-stop-loss", "--disable-timeout"],
            "description": "禁用所有退出机制 (危险测试)"
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n[{i}/{total_count}] ", end="")
        if run_backtest_with_options(test_case["options"], test_case["description"]):
            success_count += 1
    
    print(f"\n{'='*60}")
    print(f"测试完成: {success_count}/{total_count} 成功")
    print(f"{'='*60}")
    
    if success_count == total_count:
        print("🎉 所有测试都通过了！")
        print("\n📖 使用说明:")
        print("1. 默认情况下，所有退出机制都启用")
        print("2. 使用 --disable-* 参数可以禁用特定的退出机制")
        print("3. 使用 --enable-reverse-close 启用反向平仓")
        print("4. 可以自由组合不同的退出策略")
        print("5. 如果禁用所有退出机制，仓位将永不平仓（危险）")
        
        print("\n🔧 常用组合示例:")
        print("# 只使用止盈和反向平仓")
        print("python backtest_money_quick.py --disable-stop-loss --disable-timeout --enable-reverse-close")
        print("\n# 只使用传统的止盈止损")
        print("python backtest_money_quick.py --disable-timeout --stop-loss 2.5")
        print("\n# 激进模式：只使用反向平仓")
        print("python backtest_money_quick.py --disable-take-profit --disable-stop-loss --disable-timeout --enable-reverse-close")
        
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = test_exit_mechanisms()
    sys.exit(0 if success else 1)
