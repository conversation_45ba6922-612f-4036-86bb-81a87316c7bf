# model_utils_pv.py
# 基于量价分析原理的特征工程模块
# 专注于价格和成交量关系的核心特征

import pandas as pd
import numpy as np
import json
import os

def load_config(config_file='config.json'):
    """加载配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except Exception as e:
        print(f"加载配置文件失败:{config_file}{e} ")
        return None

def get_coin_config(coin_name, config_file='config.json'):
    """获取指定币种的配置"""
    config = load_config(config_file)
    if config is None:
        return None

    if coin_name not in config['coin_configs']:
        print(f"错误: 配置中未找到币种 '{coin_name}'")
        print(f"可用币种: {list(config['coin_configs'].keys())}")
        return None

    return config['coin_configs'][coin_name]

def get_output_dir():
    """获取指定币种的输出目录"""
    config = load_config("config.json")
    if config is None:
        return None

    return config['output_dir']

def create_percentage_target(df, up_threshold, down_threshold, max_lookforward_minutes, timeframe):
    """为回测数据生成真实标签"""
    max_lookforward_candles = max_lookforward_minutes // timeframe
    print(f"为回测数据生成真实标签 (先涨{up_threshold*100:.1f}% vs 先跌{down_threshold*100:.1f}%)")

    labels = []
    valid_indices = []
    for i in range(len(df)):
        current_price = df.iloc[i]['close']
        up_target = current_price * (1 + up_threshold)
        down_target = current_price * (1 - down_threshold)
        label = None
        for j in range(1, min(max_lookforward_candles + 1, len(df) - i)):
            future_price = df.iloc[i + j]['close']
            if future_price >= up_target:
                label = 1
                break
            elif future_price <= down_target:
                label = 0
                break
        if label is not None:
            labels.append(label)
            valid_indices.append(i)

    label_series = pd.Series(index=df.index[valid_indices], data=labels, name='ActualResult')
    print(f"生成了 {len(label_series)} 个有效标签用于回测比对。")
    return label_series

def calculate_resistance_support_levels(df, window=20):
    """计算阻力位和支撑位"""
    # 使用滚动窗口计算局部高点和低点
    df['local_high'] = df['high'].rolling(window=window, center=True).max()
    df['local_low'] = df['low'].rolling(window=window, center=True).min()
    
    # 判断是否为阻力位或支撑位
    df['is_resistance'] = (df['high'] == df['local_high']).astype(int)
    df['is_support'] = (df['low'] == df['local_low']).astype(int)
    
    return df

def calculate_volume_price_features(df, timeframe):
    """
    计算基于量价分析原理的核心特征
    基于四个核心原则：
    1. 突破阻力后，价涨量缩是最佳看涨信号
    2. 量增价不涨时应该撤退  
    3. 突破阻力时成交量应该增加
    4. 突破前量从低到高，突破后量从高到低
    """
    epsilon = 1e-9
    df = df.copy()
    
    # 基础时间特征
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    
    # ===== 核心量价特征 =====
    
    # 1. 价格变化率
    df['price_change_1'] = df['close'].pct_change(1)
    df['price_change_3'] = df['close'].pct_change(3)
    df['price_change_5'] = df['close'].pct_change(5)
    
    # 2. 成交量变化率
    df['volume_change_1'] = df['volume'].pct_change(1)
    df['volume_change_3'] = df['volume'].pct_change(3)
    df['volume_change_5'] = df['volume'].pct_change(5)
    
    # 3. 量价背离指标 (原则2: 量增价不涨)
    # 当成交量增加但价格不涨或下跌时的背离程度
    df['volume_price_divergence_1'] = df['volume_change_1'] - df['price_change_1']
    df['volume_price_divergence_3'] = df['volume_change_3'] - df['price_change_3']
    
    # 4. 成交量相对强度
    for window in [5, 10, 20]:
        df[f'volume_ratio_{window}'] = df['volume'] / (df['volume'].rolling(window=window).mean() + epsilon)
    
    # 5. 价格相对位置 (用于判断阻力位和支撑位)
    for window in [10, 20, 50]:
        df[f'price_position_{window}'] = (df['close'] - df['close'].rolling(window=window).min()) / \
                                        (df['close'].rolling(window=window).max() - df['close'].rolling(window=window).min() + epsilon)
    
    # 6. 突破信号检测
    # 计算阻力位和支撑位
    df = calculate_resistance_support_levels(df, window=20)
    
    # 7. 突破时的量价配合度 (原则3: 突破阻力时成交量应该增加)
    df['breakout_volume_support'] = 0.0
    for i in range(1, len(df)):
        # 检测价格突破
        if df.iloc[i]['close'] > df.iloc[i-1]['local_high']:  # 向上突破
            volume_increase = df.iloc[i]['volume'] > df.iloc[i-1]['volume']
            df.iloc[i, df.columns.get_loc('breakout_volume_support')] = 1.0 if volume_increase else -1.0
        elif df.iloc[i]['close'] < df.iloc[i-1]['local_low']:  # 向下突破
            volume_increase = df.iloc[i]['volume'] > df.iloc[i-1]['volume']
            df.iloc[i, df.columns.get_loc('breakout_volume_support')] = -1.0 if volume_increase else 1.0
    
    # 8. 量价趋势一致性 (原则1: 价涨量缩是好信号)
    # 计算价格和成交量的趋势方向
    df['price_trend_5'] = np.where(df['close'] > df['close'].shift(5), 1, -1)
    df['volume_trend_5'] = np.where(df['volume'] > df['volume'].shift(5), 1, -1)
    
    # 价涨量缩信号 (最佳看涨信号)
    df['price_up_volume_down'] = np.where(
        (df['price_trend_5'] == 1) & (df['volume_trend_5'] == -1), 1, 0
    )
    
    # 价跌量增信号 (看跌信号)
    df['price_down_volume_up'] = np.where(
        (df['price_trend_5'] == -1) & (df['volume_trend_5'] == 1), 1, 0
    )
    
    # 9. 成交量集群分析 (原则4: 突破前量从低到高，突破后量从高到低)
    # 计算成交量的变化趋势
    df['volume_acceleration'] = df['volume_change_1'] - df['volume_change_1'].shift(1)
    
    # 10. VWAP相关特征 (成交量加权平均价格)
    for window in [10, 20, 50]:
        vwap_num = (df['close'] * df['volume']).rolling(window=window).sum()
        vwap_den = df['volume'].rolling(window=window).sum()
        df[f'vwap_{window}'] = vwap_num / (vwap_den + epsilon)
        df[f'price_vs_vwap_{window}'] = (df['close'] - df[f'vwap_{window}']) / (df[f'vwap_{window}'] + epsilon)
    
    # 11. 成交量波动率
    for window in [5, 10, 20]:
        df[f'volume_volatility_{window}'] = df['volume'].rolling(window=window).std() / (df['volume'].rolling(window=window).mean() + epsilon)
    
    # 12. 价格波动率
    for window in [5, 10, 20]:
        df[f'price_volatility_{window}'] = df['close'].rolling(window=window).std() / (df['close'].rolling(window=window).mean() + epsilon)
    
    # 13. 量价相关性
    for window in [10, 20]:
        df[f'volume_price_corr_{window}'] = df['volume'].rolling(window=window).corr(df['close'])
    
    # 清理临时列
    temp_cols = ['local_high', 'local_low', 'is_resistance', 'is_support']
    df = df.drop(columns=[col for col in temp_cols if col in df.columns], errors='ignore')
    
    return df

def calculate_features(df, timeframe):
    """主要特征计算函数 - 专注于量价分析"""
    print(f"开始计算量价分析特征 ({timeframe}-min K线)...")
    
    # 计算量价特征
    df = calculate_volume_price_features(df, timeframe)
    
    print("量价分析特征计算完成。")
    return df

def get_feature_list(df_clean, time_frame=15):
    """获取量价分析特征列表"""
    pv_features = [
        # 基础特征
        'hour', 'day_of_week',
        
        # 价格变化特征
        'price_change_1', 'price_change_3', 'price_change_5',
        
        # 成交量变化特征  
        'volume_change_1', 'volume_change_3', 'volume_change_5',
        
        # 量价背离特征
        'volume_price_divergence_1', 'volume_price_divergence_3',
        
        # 成交量相对强度
        'volume_ratio_5', 'volume_ratio_10', 'volume_ratio_20',
        
        # 价格相对位置
        'price_position_10', 'price_position_20', 'price_position_50',
        
        # 突破信号
        'breakout_volume_support',
        
        # 量价趋势信号
        'price_trend_5', 'volume_trend_5', 'price_up_volume_down', 'price_down_volume_up',
        
        # 成交量分析
        'volume_acceleration',
        
        # VWAP特征
        'price_vs_vwap_10', 'price_vs_vwap_20', 'price_vs_vwap_50',
        
        # 波动率特征
        'volume_volatility_5', 'volume_volatility_10', 'volume_volatility_20',
        'price_volatility_5', 'price_volatility_10', 'price_volatility_20',
        
        # 量价相关性
        'volume_price_corr_10', 'volume_price_corr_20'
    ]
    
    available_features = [col for col in pv_features if col in df_clean.columns]
    missing = [col for col in pv_features if col not in df_clean.columns]
    
    if missing: 
        print(f"警告：以下特征在数据中不存在: {missing}")
    
    print(f"使用的量价分析特征数量: {len(available_features)}")
    return available_features
