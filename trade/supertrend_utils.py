# supertrend_utils.py

import pandas as pd
import numpy as np
import requests
import logging
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any
import pytz

# Binance API endpoints for resilience
BINANCE_API_ENDPOINTS = [
    "https://api.binance.com",
    "https://api-gcp.binance.com",
    "https://api1.binance.com",
    "https://api2.binance.com",
    "https://api3.binance.com",
    "https://api4.binance.com",
]

def calculate_supertrend(df: pd.DataFrame, atr_period: int = 10, multiplier: float = 3.0) -> pd.DataFrame:
    """
    计算SuperTrend指标
    参数:
    - df: 包含 high, low, close 列的DataFrame
    - atr_period: ATR计算周期，默认10
    - multiplier: ATR倍数，默认3.0
    
    返回:
    - 包含 supertrend 和 trend 列的DataFrame
    """
    df = df.copy()
    
    # 计算True Range
    df['prev_close'] = df['close'].shift(1)
    df['tr1'] = df['high'] - df['low']
    df['tr2'] = abs(df['high'] - df['prev_close'])
    df['tr3'] = abs(df['low'] - df['prev_close'])
    df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
    
    # 计算ATR (使用SMA而不是EMA，与Pine脚本保持一致)
    df['atr'] = df['tr'].rolling(window=atr_period, min_periods=1).mean()
    
    # 计算HL2 (High + Low) / 2
    df['hl2'] = (df['high'] + df['low']) / 2
    
    # 计算基础上下轨
    df['basic_upper'] = df['hl2'] + (multiplier * df['atr'])
    df['basic_lower'] = df['hl2'] - (multiplier * df['atr'])
    
    # 初始化最终上下轨
    df['final_upper'] = df['basic_upper']
    df['final_lower'] = df['basic_lower']
    
    # 初始化趋势方向
    df['trend'] = 1
    
    # 逐行计算最终上下轨和趋势
    for i in range(1, len(df)):
        # 计算最终上轨
        if df.iloc[i]['basic_upper'] < df.iloc[i-1]['final_upper'] or df.iloc[i-1]['close'] > df.iloc[i-1]['final_upper']:
            df.iloc[i, df.columns.get_loc('final_upper')] = df.iloc[i]['basic_upper']
        else:
            df.iloc[i, df.columns.get_loc('final_upper')] = df.iloc[i-1]['final_upper']
        
        # 计算最终下轨
        if df.iloc[i]['basic_lower'] > df.iloc[i-1]['final_lower'] or df.iloc[i-1]['close'] < df.iloc[i-1]['final_lower']:
            df.iloc[i, df.columns.get_loc('final_lower')] = df.iloc[i]['basic_lower']
        else:
            df.iloc[i, df.columns.get_loc('final_lower')] = df.iloc[i-1]['final_lower']
        
        # 确定趋势方向
        if (df.iloc[i-1]['trend'] == -1) and (df.iloc[i]['close'] > df.iloc[i]['final_lower']):
            df.iloc[i, df.columns.get_loc('trend')] = 1
        elif (df.iloc[i-1]['trend'] == 1) and (df.iloc[i]['close'] < df.iloc[i]['final_upper']):
            df.iloc[i, df.columns.get_loc('trend')] = -1
        else:
            df.iloc[i, df.columns.get_loc('trend')] = df.iloc[i-1]['trend']
    
    # 计算SuperTrend值
    df['supertrend'] = np.where(df['trend'] == 1, df['final_lower'], df['final_upper'])
    
    # 清理临时列
    columns_to_drop = ['prev_close', 'tr1', 'tr2', 'tr3', 'tr', 'hl2', 'basic_upper', 'basic_lower', 'final_upper', 'final_lower', 'atr']
    df = df.drop(columns=[col for col in columns_to_drop if col in df.columns], axis=1)
    
    return df[['supertrend', 'trend']]

def fetch_klines(api_symbol: str, interval: str = "30m", limit: int = 1000, logger: Optional[logging.Logger] = None) -> Optional[pd.DataFrame]:
    """
    获取指定时间间隔的K线数据用于SuperTrend计算
    """
    params = {'symbol': api_symbol, 'interval': interval, 'limit': limit}
    
    for base_url in BINANCE_API_ENDPOINTS:
        try:
            url = f"{base_url}/api/v3/klines"
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            klines = response.json()
            
            if not klines:
                return None
                
            df = pd.DataFrame(klines, columns=[
                'Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume', 
                'CloseTime', 'QuoteVolume', 'TradeCount', 'TakerBuyBaseVolume', 
                'TakerBuyQuoteVolume', 'Ignore'
            ])
            
            df = df.astype({
                'Open': float, 'High': float, 'Low': float, 
                'Close': float, 'Volume': float
            })
            
            df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='ms', utc=True)
            df.set_index('Timestamp', inplace=True)
            
            return df[['Open', 'High', 'Low', 'Close', 'Volume']].rename(columns=str.lower)
            
        except requests.exceptions.RequestException as e:
            # if logger:
            #     logger.warning(f"从 {base_url} 获取{interval} k线数据失败: {e}")
            continue
    
    if logger:
        logger.error(f"所有Binance API节点均无法连接，获取{interval} SuperTrend K线数据失败。")
    return None

class SuperTrendManager:
    """
    SuperTrend 管理器 - 负责实时计算和缓存SuperTrend数据
    """
    def __init__(self, api_symbol: str, interval: str = "30m", atr_period: int = 13, multiplier: float = 3.8,
                 logger: Optional[logging.Logger] = None):
        self.api_symbol = api_symbol
        self.interval = interval
        self.atr_period = atr_period
        self.multiplier = multiplier
        self.logger = logger or logging.getLogger(__name__)

        # 缓存相关
        self.cached_data: Optional[pd.DataFrame] = None
        self.last_update_time: Optional[datetime] = None

        # 根据主信号频率设置更新频率 - 主信号5分钟出现一次，SuperTrend需要更频繁更新
        # 设置为3分钟更新一次，确保每次主信号出现时SuperTrend都是最新的
        self.update_interval = timedelta(minutes=3)

        self.logger.info(f"SuperTrend管理器初始化: {interval}K线, ATR周期{atr_period}, 倍数{multiplier}, 每3分钟更新")

        # 初始化数据
        self._update_supertrend_data()

    def _parse_interval_to_minutes(self, interval: str) -> int:
        """
        解析时间间隔字符串为分钟数
        """
        if interval.endswith('m'):
            return int(interval[:-1])
        elif interval.endswith('h'):
            return int(interval[:-1]) * 60
        elif interval.endswith('d'):
            return int(interval[:-1]) * 24 * 60
        else:
            # 默认按分钟处理
            return int(interval)

    def _update_supertrend_data(self) -> bool:
        """
        更新SuperTrend数据 - 每次重新获取最新1000条K线并计算
        """
        try:
            # 获取最新的1000条K线数据
            df_klines = fetch_klines(self.api_symbol, self.interval, limit=1000, logger=self.logger)
            if df_klines is None or df_klines.empty:
                self.logger.error(f"无法获取{self.interval}K线数据用于SuperTrend计算")
                return False

            # 直接在原数据上计算SuperTrend指标，避免合并操作
            supertrend_result = calculate_supertrend(df_klines, self.atr_period, self.multiplier)

            # 将SuperTrend列添加到原始数据
            df_klines['supertrend'] = supertrend_result['supertrend']
            df_klines['trend'] = supertrend_result['trend']

            # 直接替换缓存数据
            self.cached_data = df_klines
            self.last_update_time = datetime.now(timezone.utc)

            self.logger.info(f"SuperTrend数据更新成功，共 {len(self.cached_data)} 条{self.interval}K线记录 (每3分钟更新，确保主信号时数据最新)")
            return True

        except Exception as e:
            self.logger.error(f"更新SuperTrend数据失败: {e}")
            return False
    
    def get_supertrend_signal(self, timestamp: Optional[datetime] = None) -> Optional[int]:
        """
        获取指定时间点的SuperTrend信号
        返回: 1表示看涨，-1表示看跌，None表示无数据
        """
        # 检查是否需要更新数据
        now = datetime.now(timezone.utc)
        if (self.last_update_time is None or
            (now - self.last_update_time) > self.update_interval or
            self.cached_data is None):
            if not self._update_supertrend_data():
                return None

        if self.cached_data is None or self.cached_data.empty:
            return None

        # 如果没有指定时间，使用当前时间
        if timestamp is None:
            timestamp = now

        # 确保timestamp有时区信息
        if timestamp.tzinfo is None:
            timestamp = timestamp.replace(tzinfo=timezone.utc)

        # 找到最接近但不超过当前时间的SuperTrend数据点
        valid_data = self.cached_data[self.cached_data.index <= timestamp]
        if valid_data.empty:
            return None

        # 重要修复：使用倒数第二根K线，因为最后一根可能还在进行中
        if len(valid_data) < 2:
            self.logger.warning("SuperTrend数据不足，至少需要2根已确定的K线")
            return None
        # 使用倒数第二根已确定的K线
        confirmed_supertrend = valid_data.iloc[-2]
        self.logger.info(f"使用已确定的SuperTrend数据: {confirmed_supertrend.name}, 趋势: {confirmed_supertrend['trend']}, 价格: {confirmed_supertrend['close']}")
        return int(confirmed_supertrend['trend'])
    
    def should_trade_with_supertrend(self, prediction: int, timestamp: Optional[datetime] = None) -> bool:
        """
        根据SuperTrend信号决定是否应该交易
        
        参数:
        - prediction: 模型预测 (1=看涨, 0=看跌)
        - timestamp: 时间戳，如果为None则使用当前时间
        
        返回: True表示可以交易，False表示应该跳过
        """
        supertrend_signal = self.get_supertrend_signal(timestamp)

        if supertrend_signal is None:
            # 如果无法获取SuperTrend信号，允许交易（降级处理）
            self.logger.warning("无法获取SuperTrend信号，允许交易")
            return True
        
        # SuperTrend看涨时只做多，看跌时只做空
        if prediction == 1 and supertrend_signal == 1:  # 模型预测涨 + SuperTrend看涨
            return True
        elif prediction == 0 and supertrend_signal == -1:  # 模型预测跌 + SuperTrend看跌
            return True
        else:
            return False
    
    def get_supertrend_info(self, timestamp: Optional[datetime] = None) -> Dict[str, Any]:
        """
        获取SuperTrend详细信息用于日志记录
        """
        signal = self.get_supertrend_signal(timestamp)

        if self.cached_data is None or self.cached_data.empty:
            return {'signal': None, 'error': '无SuperTrend数据'}

        if timestamp is None:
            timestamp = datetime.now(timezone.utc)

        if timestamp.tzinfo is None:
            timestamp = timestamp.replace(tzinfo=timezone.utc)

        valid_data = self.cached_data[self.cached_data.index <= timestamp]
        if valid_data.empty or len(valid_data) < 2:
            return {'signal': None, 'error': '无足够的有效SuperTrend数据'}

        # 使用倒数第二根已确定的K线数据
        confirmed_data = valid_data.iloc[-2]

        return {
            'signal': signal,
            'supertrend_value': float(confirmed_data['supertrend']),
            'current_price': float(confirmed_data['close']),
            'data_timestamp': confirmed_data.name.isoformat(),
            'atr_period': self.atr_period,
            'multiplier': self.multiplier,
            'note': '使用倒数第二根已确定的K线数据'
        }
