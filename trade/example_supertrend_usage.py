#!/usr/bin/env python3
# example_supertrend_usage.py

"""
SuperTrend实时交易功能使用示例

这个脚本演示了如何使用新增的 --use-supertrend 功能
"""

import subprocess
import sys
import os
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_command_info(description, command):
    """打印命令信息"""
    print(f"\n📋 {description}")
    print(f"💻 命令: {command}")
    print("-" * 50)

def main():
    """主函数"""
    print_header("SuperTrend实时交易功能使用指南")
    
    print("""
🎯 SuperTrend功能说明:
   - 使用1000条30分钟K线数据实时计算SuperTrend指标
   - ATR周期: 13, 倍数: 3.8 (优化后的参数)
   - SuperTrend看涨时只允许做多，看跌时只允许做空
   - 每3分钟自动更新一次SuperTrend数据 (确保5分钟主信号时数据最新)
   - 与现有的时间过滤功能完全兼容
    """)
    
    # 基础使用示例
    print_header("基础使用示例")
    
    basic_commands = [
        {
            "desc": "启用SuperTrend过滤 (使用默认参数)",
            "cmd": "python real_main.py --coin ETH --use-supertrend"
        },
        {
            "desc": "自定义SuperTrend参数",
            "cmd": "python real_main.py --coin ETH --use-supertrend --supertrend-interval 15m --supertrend-atr-period 10 --supertrend-multiplier 3.0"
        },
        {
            "desc": "结合时间过滤使用",
            "cmd": "python real_main.py --coin ETH --use-supertrend --use-chushou --chushou-file chushou.json"
        }
    ]
    
    for i, cmd_info in enumerate(basic_commands, 1):
        print_command_info(f"{i}. {cmd_info['desc']}", cmd_info['cmd'])
    
    # 高级配置示例
    print_header("高级配置示例")
    
    advanced_commands = [
        {
            "desc": "保守策略 (1小时K线)",
            "cmd": "python real_main.py --coin BTC --use-supertrend --supertrend-interval 1h --supertrend-atr-period 20 --supertrend-multiplier 4.0"
        },
        {
            "desc": "激进策略 (15分钟K线)",
            "cmd": "python real_main.py --coin ETH --use-supertrend --supertrend-interval 15m --supertrend-atr-period 8 --supertrend-multiplier 2.5"
        },
        {
            "desc": "完整配置示例",
            "cmd": "python real_main.py --coin DOT --use-supertrend --supertrend-interval 30m --supertrend-atr-period 13 --supertrend-multiplier 3.8 --use-chushou --update-interval 30 --no-sound"
        }
    ]
    
    for i, cmd_info in enumerate(advanced_commands, 1):
        print_command_info(f"{i}. {cmd_info['desc']}", cmd_info['cmd'])
    
    # 参数说明
    print_header("参数详细说明")
    
    params_info = """
🔧 SuperTrend相关参数:

--use-supertrend
    启用SuperTrend过滤功能

--supertrend-interval <时间间隔>
    K线时间间隔，默认: 30m
    - 支持: 15m, 30m, 1h, 4h 等
    - 较短间隔: 更敏感，适合短线交易
    - 较长间隔: 更稳定，适合中长线交易

--supertrend-atr-period <数值>
    ATR计算周期，默认: 13
    - 较小值(8-10): 对价格变化更敏感，信号更频繁
    - 较大值(15-20): 对价格变化不太敏感，信号更稳定

--supertrend-multiplier <数值>
    ATR倍数，默认: 3.8
    - 较小值(2.5-3.0): 更敏感，更多交易信号
    - 较大值(4.0-4.5): 更保守，更少但更可靠的信号

📊 SuperTrend工作原理:
1. 每3分钟自动获取最新的1000条30分钟K线数据
2. 实时计算SuperTrend指标和趋势方向 (ATR周期13, 倍数3.8)
3. 使用倒数第二根已确定的K线数据，确保信号稳定
4. 当模型生成交易信号时(每5分钟)，检查SuperTrend趋势:
   - 模型预测涨 + SuperTrend看涨 → 允许做多
   - 模型预测跌 + SuperTrend看跌 → 允许做空
   - 其他情况 → 跳过交易

⚠️  注意事项:
- SuperTrend基于30分钟数据，适合中短期趋势过滤
- 3分钟更新频率确保主信号出现时SuperTrend数据是最新的
- 在强烈震荡市场中可能产生较多假信号
- 建议结合其他过滤条件(如时间过滤)一起使用
- 首次启动时需要联网获取历史数据
- 使用已确定的K线数据，信号会有最多30分钟的延迟
    """
    
    print(params_info)
    
    # 测试建议
    print_header("测试建议")
    
    test_suggestions = """
🧪 建议的测试流程:

1. 首先运行测试脚本验证功能:
   python test_supertrend_real.py

2. 使用纸上交易模式测试:
   python real_main.py --coin ETH --use-supertrend
   (确保配置文件中 live_trading = false)

3. 观察日志输出，关注:
   - SuperTrend数据更新信息
   - 信号过滤情况
   - 交易决策逻辑

4. 根据测试结果调整参数:
   - 如果过滤太严格，减小multiplier
   - 如果信号太频繁，增大atr_period

5. 确认无误后再启用实盘交易
    """
    
    print(test_suggestions)
    
    # 故障排除
    print_header("常见问题排除")
    
    troubleshooting = """
🔧 常见问题及解决方案:

Q: 启动时提示"无法获取1小时K线数据"
A: 检查网络连接，确保能访问Binance API

Q: SuperTrend信号始终为None
A: 可能是数据不足，等待几分钟后重试

Q: 所有信号都被过滤
A: 当前SuperTrend趋势与模型预测方向不一致，这是正常的过滤行为

Q: 想要查看SuperTrend详细信息
A: 查看日志文件，包含详细的SuperTrend计算和过滤信息

Q: 如何调整SuperTrend敏感度
A: 调整 --supertrend-multiplier 参数:
   - 减小值 → 更敏感，更多信号
   - 增大值 → 更保守，更少信号
    """
    
    print(troubleshooting)
    
    print_header("开始使用")
    print("""
🚀 现在你可以开始使用SuperTrend功能了！

建议从以下命令开始:
python real_main.py --coin ETH --use-supertrend

祝交易顺利！ 📈
    """)

if __name__ == "__main__":
    main()
