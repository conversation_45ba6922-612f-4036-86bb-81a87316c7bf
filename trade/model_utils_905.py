# model_utils.py
# 公共函数库，用于数据加载、特征计算和标签生成

import pandas as pd
import numpy as np
import json
import os
from typing import List, Dict, Optional, Tuple
def load_config(config_file='config.json'):
    """加载配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except Exception as e:
        print(f"加载配置文件失败:{config_file}{e} ")
        return None

def get_coin_config(coin_name, config_file='config.json'):
    """获取指定币种的配置"""
    config = load_config(config_file)
    if config is None:
        return None

    if coin_name not in config['coin_configs']:
        print(f"错误: 配置中未找到币种 '{coin_name}'")
        print(f"可用币种: {list(config['coin_configs'].keys())}")
        return None

    return config['coin_configs'][coin_name]

def get_output_dir():
    """获取指定币种的输出目录"""
    config = load_config("config.json")
    if config is None:
        return None

    return config['output_dir']

def load_and_prepare_data(csv_file, price_multiplier=1.0):
    """加载和预处理指定的CSV文件的数据"""
    print(f"加载和预处理数据: {csv_file}")
    if price_multiplier != 1.0:
        print(f"价格缩放倍数: {price_multiplier}")

    try:
        df = pd.read_csv(csv_file)
        if 'Timestamp' not in df.columns:
            raise ValueError("CSV文件缺少 'Timestamp' 列")

        df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
        df.set_index('Timestamp', inplace=True)
        df.rename(columns={'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}, inplace=True)

        required_cols = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_cols):
            raise ValueError("CSV文件缺少必要的OHLCV列")

        df = df[required_cols]

        # 应用价格缩放
        if price_multiplier != 1.0:
            price_cols = ['open', 'high', 'low', 'close']
            for col in price_cols:
                df[col] = df[col] * price_multiplier
            print(f"价格列已乘以 {price_multiplier}")

        df.dropna(inplace=True)
        df.sort_index(inplace=True)
        print(f"数据预处理完成，共有 {len(df)} 条记录")
        return df
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None

def create_percentage_target(df, up_threshold, down_threshold, max_lookforward_minutes, timeframe):
    """为回测数据生成真实标签"""
    max_lookforward_candles = max_lookforward_minutes // timeframe
    print(f"为回测数据生成真实标签 (先涨{up_threshold*100:.1f}% vs 先跌{down_threshold*100:.1f}%)")

    labels = []
    valid_indices = []
    for i in range(len(df)):
        current_price = df.iloc[i]['close']
        up_target = current_price * (1 + up_threshold)
        down_target = current_price * (1 - down_threshold)
        label = None
        for j in range(1, min(max_lookforward_candles + 1, len(df) - i)):
            future_price = df.iloc[i + j]['close']
            if future_price >= up_target:
                label = 1
                break
            elif future_price <= down_target:
                label = 0
                break
        if label is not None:
            labels.append(label)
            valid_indices.append(i)

    label_series = pd.Series(index=df.index[valid_indices], data=labels, name='ActualResult')
    print(f"生成了 {len(label_series)} 个有效标签用于回测比对。")
    return label_series
# --- 新增的特征优化函数 ---
def get_optimized_feature_list(
    df_features: pd.DataFrame, 
    importances_df: pd.DataFrame, 
    top_n: int = 50,
    corr_threshold: float = 0.9) -> List[str]:
    """
    根据特征重要性和相关性，从一个大的特征集中筛选出最优的特征列表。

    Args:
        df_features (pd.DataFrame): 包含了所有特征和基础价格数据的DataFrame。
        importances_df (pd.DataFrame): 包含两列'feature'和'importance'的DataFrame。
        top_n (int): 初步筛选时保留的最重要特征的数量。
        corr_threshold (float): 用于移除高度相关特征的阈值。

    Returns:
        list: 优化后的特征名称列表。
    """
    print(f"\n--- 开始特征优化 ---")
    print(f"原始特征数量: {len(importances_df)}")

    # --- 第1步：基于重要性选择Top N特征 ---
    if len(importances_df) < top_n:
        print(f"警告: 可用特征数量({len(importances_df)}) 小于请求的 top_n({top_n})。将使用所有可用特征。")
        top_n = len(importances_df)
        
    top_features = importances_df.nlargest(top_n, 'importance')['feature'].tolist()
    print(f"步骤 1: 已根据重要性选择前 {top_n} 个特征。")

    # --- 第2步：移除高度相关的特征 ---
    top_features_df = df_features[top_features]
    corr_matrix = top_features_df.corr().abs()
    
    upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
    
    # 获取每个特征的重要性，以便比较
    importances_map = importances_df.set_index('feature')['importance'].to_dict()
    
    features_to_keep = list(top_features)
    removed_count = 0
    
    # 遍历相关性矩阵
    for i in range(len(upper.columns)):
        for j in range(i):
            if upper.iloc[j, i] > corr_threshold:
                feat1 = upper.columns[i]
                feat2 = upper.index[j]
                
                # 如果两个特征都还在我们的保留列表中
                if feat1 in features_to_keep and feat2 in features_to_keep:
                    # 决定移除哪个：移除重要性较低的那个
                    if importances_map.get(feat1, 0) >= importances_map.get(feat2, 0):
                        feature_to_remove = feat2
                    else:
                        feature_to_remove = feat1
                    
                    features_to_keep.remove(feature_to_remove)
                    removed_count += 1
                    # print(f"  - 移除 '{feature_to_remove}' (因与 '{feat1 if feature_to_remove==feat2 else feat2}' 相关性高)")

    print(f"步骤 2: 已移除 {removed_count} 个高度相关 (阈值>{corr_threshold}) 的特征。")
    print(f"最终优化后的特征数量: {len(features_to_keep)}")
    print(f"--- 特征优化完成 ---\n")
    
    return features_to_keep
def calculate_features(df, timeframe):
    """计算技术指标特征 (与训练时完全一致) - 新增RSI、BBW及其动态特征"""
    # print(f"开始计算特征 ({timeframe}-min K线)...")
    epsilon = 1e-9
    FEATURE_WINDOWS_MINUTES = [15,60,120, 360, 720]

    df['hour'], df['day_of_week'] = df.index.hour, df.index.dayofweek

    # K线基础特征计算
    df = get_standardized_kline_features(df, timeframe_suffix=f'_{timeframe}m', epsilon=epsilon)

    # 动量特征
    for n_minutes in FEATURE_WINDOWS_MINUTES:
        n_candles = n_minutes // timeframe
        if n_candles >= 1: df[f'return_{n_minutes}min'] = df['close'].pct_change(n_candles)

    # 波动率和趋势特征
    for n_minutes in FEATURE_WINDOWS_MINUTES:
        n_candles = n_minutes // timeframe
        if n_candles >= 1:
            df[f'volatility_ratio_{n_minutes}'] = df['close'].rolling(window=n_candles).std() / (df['close'] + epsilon)
            df[f'sma_{n_minutes}'] = df['close'].rolling(window=n_candles).mean()
            df[f'price_div_sma_{n_minutes}'] = df['close'] / (df[f'sma_{n_minutes}'] + epsilon)

    if (120 // timeframe > 0 and 720 // timeframe > 0) and f'sma_120' in df.columns and f'sma_720' in df.columns:
         df['sma_120_div_sma_720'] = df['sma_120'] / (df['sma_720'] + epsilon)

    # ================================================================= #
    # ===== 新增：震荡器和波动带特征 (Oscillators & Volatility Bands) ===== #
    # ================================================================= #

    # 1. RSI (相对强弱指数) - 多周期版本 (包含原有的14周期以保持兼容性)
    rsi_periods = [6, 12, 14, 24]  # 保留14以兼容现有模型
    delta = df['close'].diff(1)
    for rsi_period in rsi_periods:
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / (loss + epsilon)
        df[f'rsi_{rsi_period}'] = 100 - (100 / (1 + rs))

    # 2. Bollinger Bands Width (布林带宽度)
    bb_period = 20
    bb_std_dev = 2
    middle_band = df['close'].rolling(window=bb_period).mean()
    std_dev = df['close'].rolling(window=bb_period).std()
    upper_band = middle_band + bb_std_dev * std_dev
    lower_band = middle_band - bb_std_dev * std_dev
    df[f'bb_width_{bb_period}'] = (upper_band - lower_band) / (middle_band + epsilon)

    # --- 新增：捕捉RSI和BBW动态变化的特征 ---
    # 计算RSI差分和移动平均 (多周期，包含14以保持兼容性)
    indicator_ma_period = 5
    for rsi_period in rsi_periods:
        df[f'rsi_{rsi_period}_diff_1'] = df[f'rsi_{rsi_period}'].diff(1)
        df[f'rsi_{rsi_period}_ma_{indicator_ma_period}'] = df[f'rsi_{rsi_period}'].rolling(window=indicator_ma_period).mean()

    # 计算BBW差分和移动平均
    df[f'bb_width_{bb_period}_diff_1'] = df[f'bb_width_{bb_period}'].diff(1)
    df[f'bb_width_{bb_period}_ma_{indicator_ma_period}'] = df[f'bb_width_{bb_period}'].rolling(window=indicator_ma_period).mean()

    # VWAP特征
    vwap_candles = 360 // timeframe
    if vwap_candles > 0:
        df['price_x_volume'] = df['close'] * df['volume']
        vwap_numerator = df['price_x_volume'].rolling(window=vwap_candles).sum()
        vwap_denominator = df['volume'].rolling(window=vwap_candles).sum()
        df['vwap_360'] = vwap_numerator / (vwap_denominator + epsilon)
        df['price_div_vwap_360'] = df['close'] / (df['vwap_360'] + epsilon)
        df.drop('price_x_volume', axis=1, inplace=True)

    # VMA特征
    for timePeriod in [60,120,360, 720, 1440]:
        vma_candles = timePeriod // timeframe
        vma_col = f'vma_{timePeriod}'
        if vma_candles > 0:
            df[vma_col] = df['volume'].rolling(window=vma_candles).mean()
            df[f'volume_div_{vma_col}'] = df['volume'] / (df[vma_col] + epsilon)

    # 滚动K线特征 - 更健壮的版本
    for window in [5, 12, 24]:
        range_col = f'range_norm_by_atr_{timeframe}m'
        body_col = f'body_percent_of_range_{timeframe}m'

        if range_col in df.columns:
            df[f'range_norm_by_atr_mean_{window}'] = df[range_col].rolling(window=window).mean()

        if body_col in df.columns:
             df[f'body_percent_mean_{window}'] = df[body_col].rolling(window=window).mean()

    # print("特征计算完成 (已新增RSI、BBW及其动态变化特征)。")
    return df

def get_standardized_kline_features(df, timeframe_suffix='', epsilon=1e-9):
    """
    计算标准化的K线特征的辅助函数 (修正版)
    """
    try:
        # 1. 计算ATR
        high_low = df['high'] - df['low']
        high_prev_close = abs(df['high'] - df['close'].shift(1))
        low_prev_close = abs(df['low'] - df['close'].shift(1))
        tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
        atr_col = f'atr_14{timeframe_suffix}'
        df[atr_col] = tr.rolling(window=14).mean()
        # 新增：标准化ATR，消除价格绝对值影响
        df[f'{atr_col}_pct'] = df[atr_col] / (df['close'] + epsilon)

        # 2. 计算K线形态
        body_size = abs(df['close'] - df['open'])
        price_range = df['high'] - df['low']

        # 计算上下影线
        upper_shadow = df['high'] - df[['open', 'close']].max(axis=1)  # 上影线
        lower_shadow = df[['open', 'close']].min(axis=1) - df['low']   # 下影线

        # 3. 标准化 - 确保这两个关键特征都被创建
        df[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)
        df[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (df[atr_col] + epsilon)

        # 4. 新增：上下影线特征
        df[f'upper_shadow_pct{timeframe_suffix}'] = upper_shadow / (price_range + epsilon)
        df[f'lower_shadow_pct{timeframe_suffix}'] = lower_shadow / (price_range + epsilon)
        df[f'shadow_ratio{timeframe_suffix}'] = (upper_shadow + lower_shadow) / (body_size + epsilon)

    except Exception as e:
        print(f"K线特征计算出错: {e}")

    return df
def get_feature_list(df_clean,time_frame=15):
    long_term_features = [
        'hour', 'day_of_week', f'atr_14_{time_frame}m_pct', f'range_norm_by_atr_{time_frame}m',
        'return_15min', 'return_60min', 'return_120min', 'return_360min', 'return_720min',
        'volatility_ratio_15', 'volatility_ratio_60', 'volatility_ratio_120', 'volatility_ratio_360', 'volatility_ratio_720',
        'price_div_sma_15', 'price_div_sma_60', 'price_div_sma_120', 'price_div_sma_360', 'price_div_sma_720', 'sma_120_div_sma_720',
        'price_div_vwap_360', 'volume_div_vma_360', 'volume_div_vma_720','volume_div_vma_1440','range_norm_by_atr_mean_12',
        'range_norm_by_atr_mean_24', 'body_percent_mean_12', 'body_percent_mean_24',
        # 多周期RSI特征 (包含原有的14周期以保持兼容性)
        'rsi_6', 'rsi_12', 'rsi_14', 'rsi_24',
        'rsi_6_diff_1', 'rsi_12_diff_1', 'rsi_14_diff_1', 'rsi_24_diff_1',
        'rsi_6_ma_5', 'rsi_12_ma_5', 'rsi_14_ma_5', 'rsi_24_ma_5',
        # 布林带特征
        'bb_width_20', 'bb_width_20_diff_1', 'bb_width_20_ma_5',
        # 上下影线特征
        f'upper_shadow_pct_{time_frame}m', f'lower_shadow_pct_{time_frame}m', f'shadow_ratio_{time_frame}m'
    ]
    available_features = [col for col in long_term_features if col in df_clean.columns]
    missing = [col for col in long_term_features if col not in df_clean.columns]
    if missing: print(f"警告：以下特征在数据中不存在: {missing}")
    print(f"使用的特征数量: {len(available_features)}")
    return available_features

# 在 model_utils.py 文件末尾添加以下函数