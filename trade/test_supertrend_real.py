#!/usr/bin/env python3
# test_supertrend_real.py

"""
测试实时SuperTrend功能
"""

import sys
import os
import logging
from datetime import datetime, timezone

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from supertrend_utils import SuperTrendManager, calculate_supertrend, fetch_klines

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def test_supertrend_calculation():
    """测试SuperTrend计算功能"""
    logger = setup_logging()
    logger.info("=== 测试SuperTrend计算功能 ===")
    
    # 测试获取30分钟K线数据
    api_symbol = "ETHUSDT"
    interval = "30m"
    logger.info(f"获取 {api_symbol} {interval}K线数据...")

    df_klines = fetch_klines(api_symbol, interval, limit=100, logger=logger)
    if df_klines is None or df_klines.empty:
        logger.error(f"无法获取{interval}K线数据")
        return False

    logger.info(f"成功获取 {len(df_klines)} 条{interval}K线数据")
    logger.info(f"数据时间范围: {df_klines.index[0]} 到 {df_klines.index[-1]}")

    # 测试SuperTrend计算
    logger.info("计算SuperTrend指标...")
    supertrend_result = calculate_supertrend(df_klines, atr_period=13, multiplier=3.8)
    
    if supertrend_result is None or supertrend_result.empty:
        logger.error("SuperTrend计算失败")
        return False
    
    logger.info(f"SuperTrend计算成功，共 {len(supertrend_result)} 条记录")
    
    # 显示最近几条数据
    logger.info("最近5条SuperTrend数据:")
    recent_data = supertrend_result.tail(5)
    for idx, row in recent_data.iterrows():
        trend_str = "看涨" if row['trend'] == 1 else "看跌"
        logger.info(f"  {idx}: SuperTrend={row['supertrend']:.4f}, 趋势={trend_str}")

    # 验证倒数第二根K线逻辑
    logger.info(f"最后一根{interval}K线(进行中): {recent_data.index[-1]}")
    logger.info(f"倒数第二根{interval}K线(已确定): {recent_data.index[-2]}")
    logger.info(f"✅ 应该使用倒数第二根{interval}K线的SuperTrend信号")
    
    return True

def test_supertrend_manager():
    """测试SuperTrend管理器"""
    logger = setup_logging()
    logger.info("\n=== 测试SuperTrend管理器 ===")
    
    # 创建SuperTrend管理器
    api_symbol = "ETHUSDT"
    interval = "30m"
    manager = SuperTrendManager(
        api_symbol=api_symbol,
        interval=interval,
        atr_period=13,
        multiplier=3.8,
        logger=logger
    )
    
    # 测试获取当前信号
    logger.info("获取当前SuperTrend信号...")
    current_signal = manager.get_supertrend_signal()
    
    if current_signal is None:
        logger.error("无法获取SuperTrend信号")
        return False
    
    signal_str = "看涨" if current_signal == 1 else "看跌"
    logger.info(f"当前SuperTrend信号: {signal_str} ({current_signal})")
    
    # 测试交易决策
    logger.info("测试交易决策逻辑...")
    
    # 测试看涨预测
    should_trade_bull = manager.should_trade_with_supertrend(1)  # 预测看涨
    logger.info(f"模型预测看涨时是否交易: {should_trade_bull}")
    
    # 测试看跌预测
    should_trade_bear = manager.should_trade_with_supertrend(0)  # 预测看跌
    logger.info(f"模型预测看跌时是否交易: {should_trade_bear}")
    
    # 获取详细信息
    supertrend_info = manager.get_supertrend_info()
    logger.info("SuperTrend详细信息:")
    for key, value in supertrend_info.items():
        logger.info(f"  {key}: {value}")
    
    return True

def test_integration_scenario():
    """测试集成场景"""
    logger = setup_logging()
    logger.info("\n=== 测试集成场景 ===")
    
    api_symbol = "ETHUSDT"
    interval = "30m"
    manager = SuperTrendManager(
        api_symbol=api_symbol,
        interval=interval,
        atr_period=13,
        multiplier=3.8,
        logger=logger
    )
    
    # 模拟不同的预测场景
    test_scenarios = [
        {"prediction": 1, "description": "模型预测看涨"},
        {"prediction": 0, "description": "模型预测看跌"}
    ]
    
    current_time = datetime.now(timezone.utc)
    
    for scenario in test_scenarios:
        prediction = scenario["prediction"]
        description = scenario["description"]
        
        should_trade = manager.should_trade_with_supertrend(prediction, current_time)
        supertrend_signal = manager.get_supertrend_signal(current_time)
        
        signal_str = "看涨" if supertrend_signal == 1 else "看跌" if supertrend_signal == -1 else "无信号"
        trade_str = "允许交易" if should_trade else "跳过交易"
        
        logger.info(f"{description} + SuperTrend{signal_str} = {trade_str}")
    
    return True

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始测试实时SuperTrend功能")
    
    try:
        # 测试SuperTrend计算
        if not test_supertrend_calculation():
            logger.error("SuperTrend计算测试失败")
            return False
        
        # 测试SuperTrend管理器
        if not test_supertrend_manager():
            logger.error("SuperTrend管理器测试失败")
            return False
        
        # 测试集成场景
        if not test_integration_scenario():
            logger.error("集成场景测试失败")
            return False
        
        logger.info("\n✅ 所有测试通过！SuperTrend功能正常工作")
        return True
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
