"""
Microstructure-Based Trading Signals
Generate trading signals using market microstructure indicators
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
import warnings
from dataclasses import dataclass
from enum import Enum

class SignalType(Enum):
    BUY = 1
    SELL = -1
    HOLD = 0

@dataclass
class TradingSignal:
    timestamp: str
    signal_type: SignalType
    confidence: float
    indicators: Dict[str, float]
    reason: str

class MicrostructureTradingSignals:
    """
    Generate trading signals based on market microstructure indicators
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or self._default_config()
        self.signal_history = []
        
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration for signal generation"""
        return {
            # Spread thresholds
            'spread_high_threshold': 0.05,  # 5 bps
            'spread_low_threshold': 0.01,   # 1 bp
            
            # Liquidity imbalance thresholds
            'imbalance_buy_threshold': 0.3,
            'imbalance_sell_threshold': -0.3,
            
            # Order flow thresholds
            'order_flow_buy_threshold': 0.4,
            'order_flow_sell_threshold': -0.4,
            
            # VPIN thresholds
            'vpin_high_threshold': 0.7,
            'vpin_low_threshold': 0.3,
            
            # Kyle's lambda thresholds
            'kyle_lambda_high_threshold': 1e-6,
            
            # Depth slope thresholds
            'depth_slope_threshold': 1000,
            
            # Signal confidence weights
            'weights': {
                'spread': 0.2,
                'liquidity_imbalance': 0.25,
                'order_flow': 0.3,
                'vpin': 0.15,
                'depth_slope': 0.1
            },
            
            # Minimum confidence for signal generation
            'min_confidence': 0.6,
            
            # Signal persistence (seconds)
            'signal_cooldown': 30
        }
    
    def calculate_spread_signal(self, spread_pct: float, spread_history: List[float]) -> Tuple[float, str]:
        """
        Calculate signal based on bid-ask spread
        
        Args:
            spread_pct: Current spread percentage
            spread_history: Historical spread values
            
        Returns:
            Tuple of (signal_strength, reason)
        """
        if np.isnan(spread_pct):
            return 0.0, "No spread data"
        
        # Low spread suggests good liquidity, favorable for trading
        if spread_pct < self.config['spread_low_threshold']:
            return 0.5, f"Low spread ({spread_pct:.4f}%) - good liquidity"
        
        # High spread suggests poor liquidity, unfavorable for trading
        elif spread_pct > self.config['spread_high_threshold']:
            return -0.5, f"High spread ({spread_pct:.4f}%) - poor liquidity"
        
        # Check if spread is widening/narrowing
        if len(spread_history) >= 5:
            recent_trend = np.polyfit(range(5), spread_history[-5:], 1)[0]
            if recent_trend > 0:
                return -0.2, "Spread widening"
            elif recent_trend < 0:
                return 0.2, "Spread narrowing"
        
        return 0.0, "Neutral spread"
    
    def calculate_liquidity_imbalance_signal(self, imbalance: float) -> Tuple[float, str]:
        """
        Calculate signal based on liquidity imbalance
        
        Args:
            imbalance: Liquidity imbalance (-1 to 1)
            
        Returns:
            Tuple of (signal_strength, reason)
        """
        if np.isnan(imbalance):
            return 0.0, "No imbalance data"
        
        if imbalance > self.config['imbalance_buy_threshold']:
            return 0.8, f"Strong bid liquidity ({imbalance:.3f})"
        elif imbalance < self.config['imbalance_sell_threshold']:
            return -0.8, f"Strong ask liquidity ({imbalance:.3f})"
        else:
            return imbalance * 0.5, f"Moderate imbalance ({imbalance:.3f})"
    
    def calculate_order_flow_signal(self, order_flow_1min: float, order_flow_5min: float) -> Tuple[float, str]:
        """
        Calculate signal based on order flow imbalance
        
        Args:
            order_flow_1min: 1-minute order flow imbalance
            order_flow_5min: 5-minute order flow imbalance
            
        Returns:
            Tuple of (signal_strength, reason)
        """
        if np.isnan(order_flow_1min) and np.isnan(order_flow_5min):
            return 0.0, "No order flow data"
        
        # Use 1-minute if available, otherwise 5-minute
        flow = order_flow_1min if not np.isnan(order_flow_1min) else order_flow_5min
        timeframe = "1min" if not np.isnan(order_flow_1min) else "5min"
        
        if flow > self.config['order_flow_buy_threshold']:
            return 0.9, f"Strong buy flow {timeframe} ({flow:.3f})"
        elif flow < self.config['order_flow_sell_threshold']:
            return -0.9, f"Strong sell flow {timeframe} ({flow:.3f})"
        else:
            return flow * 0.6, f"Moderate flow {timeframe} ({flow:.3f})"
    
    def calculate_vpin_signal(self, vpin: float) -> Tuple[float, str]:
        """
        Calculate signal based on VPIN
        
        Args:
            vpin: Volume-Synchronized Probability of Informed Trading
            
        Returns:
            Tuple of (signal_strength, reason)
        """
        if np.isnan(vpin):
            return 0.0, "No VPIN data"
        
        if vpin > self.config['vpin_high_threshold']:
            return -0.7, f"High informed trading ({vpin:.3f}) - avoid"
        elif vpin < self.config['vpin_low_threshold']:
            return 0.3, f"Low informed trading ({vpin:.3f}) - favorable"
        else:
            return -0.2, f"Moderate informed trading ({vpin:.3f})"
    
    def calculate_depth_slope_signal(self, bid_slope: float, ask_slope: float) -> Tuple[float, str]:
        """
        Calculate signal based on order book depth slopes
        
        Args:
            bid_slope: Bid side depth slope
            ask_slope: Ask side depth slope
            
        Returns:
            Tuple of (signal_strength, reason)
        """
        if np.isnan(bid_slope) and np.isnan(ask_slope):
            return 0.0, "No depth slope data"
        
        # Steeper slopes indicate better liquidity at that side
        if not np.isnan(bid_slope) and not np.isnan(ask_slope):
            if abs(bid_slope) > abs(ask_slope) * 1.5:
                return 0.4, f"Strong bid depth (slope: {bid_slope:.0f})"
            elif abs(ask_slope) > abs(bid_slope) * 1.5:
                return -0.4, f"Strong ask depth (slope: {ask_slope:.0f})"
            else:
                return 0.0, "Balanced depth"
        
        return 0.0, "Insufficient depth data"
    
    def generate_signal(self, indicators: Dict[str, float]) -> Optional[TradingSignal]:
        """
        Generate trading signal based on all indicators
        
        Args:
            indicators: Dictionary of calculated indicators
            
        Returns:
            TradingSignal or None if no signal
        """
        try:
            timestamp = indicators.get('timestamp', datetime.now().isoformat())
            
            # Check cooldown period
            if self._in_cooldown(timestamp):
                return None
            
            # Calculate individual signals
            signals = {}
            reasons = []
            
            # Spread signal
            spread_signal, spread_reason = self.calculate_spread_signal(
                indicators.get('bid_ask_spread_pct', np.nan),
                []  # Would need historical data in real implementation
            )
            signals['spread'] = spread_signal
            if spread_signal != 0:
                reasons.append(spread_reason)
            
            # Liquidity imbalance signal
            imbalance_signal, imbalance_reason = self.calculate_liquidity_imbalance_signal(
                indicators.get('liquidity_imbalance_volume', np.nan)
            )
            signals['liquidity_imbalance'] = imbalance_signal
            if imbalance_signal != 0:
                reasons.append(imbalance_reason)
            
            # Order flow signal
            flow_signal, flow_reason = self.calculate_order_flow_signal(
                indicators.get('order_flow_imbalance_1min', np.nan),
                indicators.get('order_flow_imbalance_5min', np.nan)
            )
            signals['order_flow'] = flow_signal
            if flow_signal != 0:
                reasons.append(flow_reason)
            
            # VPIN signal
            vpin_signal, vpin_reason = self.calculate_vpin_signal(
                indicators.get('vpin', np.nan)
            )
            signals['vpin'] = vpin_signal
            if vpin_signal != 0:
                reasons.append(vpin_reason)
            
            # Depth slope signal
            depth_signal, depth_reason = self.calculate_depth_slope_signal(
                indicators.get('bid_slope', np.nan),
                indicators.get('ask_slope', np.nan)
            )
            signals['depth_slope'] = depth_signal
            if depth_signal != 0:
                reasons.append(depth_reason)
            
            # Calculate weighted signal
            weighted_signal = 0.0
            total_weight = 0.0
            
            for signal_name, signal_value in signals.items():
                if not np.isnan(signal_value) and signal_name in self.config['weights']:
                    weight = self.config['weights'][signal_name]
                    weighted_signal += signal_value * weight
                    total_weight += weight
            
            if total_weight == 0:
                return None
            
            # Normalize by actual weights used
            final_signal = weighted_signal / total_weight
            confidence = min(abs(final_signal), 1.0)
            
            # Check minimum confidence
            if confidence < self.config['min_confidence']:
                return None
            
            # Determine signal type
            if final_signal > 0:
                signal_type = SignalType.BUY
            elif final_signal < 0:
                signal_type = SignalType.SELL
            else:
                signal_type = SignalType.HOLD
            
            # Create signal
            trading_signal = TradingSignal(
                timestamp=timestamp,
                signal_type=signal_type,
                confidence=confidence,
                indicators=indicators.copy(),
                reason="; ".join(reasons[:3])  # Limit to top 3 reasons
            )
            
            self.signal_history.append(trading_signal)
            return trading_signal
            
        except Exception as e:
            warnings.warn(f"Error generating signal: {e}")
            return None
    
    def _in_cooldown(self, timestamp: str) -> bool:
        """
        Check if we're in cooldown period since last signal
        
        Args:
            timestamp: Current timestamp
            
        Returns:
            True if in cooldown
        """
        if not self.signal_history:
            return False
        
        try:
            current_time = datetime.fromisoformat(timestamp)
            last_signal_time = datetime.fromisoformat(self.signal_history[-1].timestamp)
            
            time_diff = (current_time - last_signal_time).total_seconds()
            return time_diff < self.config['signal_cooldown']
            
        except Exception:
            return False
    
    def get_signal_performance(self, price_data: List[Tuple[str, float]], 
                             holding_period: int = 300) -> Dict[str, Any]:
        """
        Analyze signal performance
        
        Args:
            price_data: List of (timestamp, price) tuples
            holding_period: Holding period in seconds
            
        Returns:
            Performance statistics
        """
        if not self.signal_history or not price_data:
            return {}
        
        # Convert price data to DataFrame
        price_df = pd.DataFrame(price_data, columns=['timestamp', 'price'])
        price_df['timestamp'] = pd.to_datetime(price_df['timestamp'])
        price_df.set_index('timestamp', inplace=True)
        
        returns = []
        
        for signal in self.signal_history:
            try:
                signal_time = pd.to_datetime(signal.timestamp)
                exit_time = signal_time + timedelta(seconds=holding_period)
                
                # Find entry and exit prices
                entry_price = price_df.loc[price_df.index >= signal_time, 'price'].iloc[0]
                exit_prices = price_df.loc[price_df.index >= exit_time, 'price']
                
                if len(exit_prices) > 0:
                    exit_price = exit_prices.iloc[0]
                    
                    if signal.signal_type == SignalType.BUY:
                        ret = (exit_price - entry_price) / entry_price
                    elif signal.signal_type == SignalType.SELL:
                        ret = (entry_price - exit_price) / entry_price
                    else:
                        continue
                    
                    returns.append(ret)
                    
            except Exception:
                continue
        
        if not returns:
            return {}
        
        returns = np.array(returns)
        
        return {
            'total_signals': len(self.signal_history),
            'analyzed_signals': len(returns),
            'win_rate': np.mean(returns > 0),
            'avg_return': np.mean(returns),
            'std_return': np.std(returns),
            'sharpe_ratio': np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0,
            'max_return': np.max(returns),
            'min_return': np.min(returns)
        }


def example_signal_generation():
    """Example of signal generation"""
    
    # Create signal generator
    signal_generator = MicrostructureTradingSignals()
    
    # Simulate some market conditions
    scenarios = [
        {
            'name': 'High bid liquidity + low spread',
            'indicators': {
                'timestamp': '2025-01-01T12:00:00',
                'bid_ask_spread_pct': 0.008,
                'liquidity_imbalance_volume': 0.4,
                'order_flow_imbalance_1min': 0.5,
                'vpin': 0.2,
                'bid_slope': -1500,
                'ask_slope': -800
            }
        },
        {
            'name': 'High ask liquidity + sell pressure',
            'indicators': {
                'timestamp': '2025-01-01T12:01:00',
                'bid_ask_spread_pct': 0.015,
                'liquidity_imbalance_volume': -0.4,
                'order_flow_imbalance_1min': -0.6,
                'vpin': 0.3,
                'bid_slope': -600,
                'ask_slope': -1200
            }
        },
        {
            'name': 'High informed trading',
            'indicators': {
                'timestamp': '2025-01-01T12:02:00',
                'bid_ask_spread_pct': 0.025,
                'liquidity_imbalance_volume': 0.1,
                'order_flow_imbalance_1min': 0.2,
                'vpin': 0.8,
                'bid_slope': -800,
                'ask_slope': -900
            }
        }
    ]
    
    print("Microstructure Trading Signals Example:")
    print("=" * 50)
    
    for scenario in scenarios:
        print(f"\nScenario: {scenario['name']}")
        print("-" * 30)
        
        signal = signal_generator.generate_signal(scenario['indicators'])
        
        if signal:
            print(f"Signal: {signal.signal_type.name}")
            print(f"Confidence: {signal.confidence:.3f}")
            print(f"Reason: {signal.reason}")
        else:
            print("No signal generated")
    
    print(f"\nTotal signals generated: {len(signal_generator.signal_history)}")


if __name__ == "__main__":
    example_signal_generation()