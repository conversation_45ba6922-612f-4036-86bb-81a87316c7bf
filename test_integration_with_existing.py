#!/usr/bin/env python3
"""
测试新的主动买卖量特征与现有训练脚本的集成
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目根目录到路径
sys.path.append('/Users/<USER>/project/ai/vibe/daily/eth-trade')

def test_model_utils_compatibility():
    """测试 model_utils_815.py 与现有代码的兼容性"""
    
    print("=" * 60)
    print("测试主动买卖量特征与现有代码的兼容性")
    print("=" * 60)
    
    try:
        # 1. 测试导入
        print("1. 测试模块导入...")
        from model_utils_815 import (
            load_and_prepare_data_from_db,
            calculate_features,
            get_feature_list,
            create_percentage_target,
            get_coin_config,
            get_output_dir
        )
        print("✅ 所有函数导入成功")
        
        # 2. 测试配置加载
        print("\n2. 测试配置加载...")
        config = get_coin_config("ETH")
        if config:
            print(f"✅ ETH配置加载成功: {config.get('model_basename', 'N/A')}")
        else:
            print("⚠️ ETH配置未找到，但这不影响功能")
        
        output_dir = get_output_dir()
        print(f"✅ 输出目录: {output_dir}")
        
        # 3. 测试数据加载
        print("\n3. 测试数据加载...")
        db_path = "/Users/<USER>/project/ai/vibe/daily/eth-trade/coin_data.db"
        table_name = "ETHUSDT_5min_spot"
        
        df = load_and_prepare_data_from_db(db_path, table_name, limit=1000)
        if df is not None:
            print(f"✅ 数据加载成功: {df.shape}")
            print(f"   包含主动买卖量: {'taker_buy_base_volume' in df.columns}")
        else:
            print("❌ 数据加载失败")
            return False
        
        # 4. 测试特征计算
        print("\n4. 测试特征计算...")
        df_features = calculate_features(df, timeframe=5)
        print(f"✅ 特征计算成功: {df_features.shape}")
        
        # 5. 测试特征列表
        print("\n5. 测试特征列表...")
        feature_list = get_feature_list(df_features, time_frame=5)
        print(f"✅ 特征列表生成成功: {len(feature_list)} 个特征")
        
        # 统计特征类型
        taker_features = [f for f in feature_list if any(keyword in f for keyword in ['taker', 'buy_sell', 'extreme', 'balanced'])]
        traditional_features = [f for f in feature_list if f not in taker_features]
        
        print(f"   传统特征: {len(traditional_features)}")
        print(f"   主动买卖量特征: {len(taker_features)}")
        
        # 6. 测试标签生成
        print("\n6. 测试标签生成...")
        labels = create_percentage_target(df_features, 0.02, 0.02, 240, 5)
        print(f"✅ 标签生成成功: {len(labels)} 个标签")
        
        # 7. 测试向后兼容性
        print("\n7. 测试向后兼容性...")
        
        # 模拟没有主动买卖量数据的情况
        df_no_taker = df.drop(['taker_buy_base_volume', 'taker_buy_quote_volume'], axis=1, errors='ignore')
        df_features_no_taker = calculate_features(df_no_taker, timeframe=5)
        feature_list_no_taker = get_feature_list(df_features_no_taker, time_frame=5)
        
        print(f"✅ 无主动买卖量数据时特征数: {len(feature_list_no_taker)}")
        print("✅ 向后兼容性测试通过")
        
        # 8. 性能对比
        print("\n8. 性能对比...")
        print(f"包含主动买卖量特征: {len(feature_list)} 个特征")
        print(f"仅传统特征: {len(feature_list_no_taker)} 个特征")
        print(f"新增特征数: {len(feature_list) - len(feature_list_no_taker)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_existing_script_compatibility():
    """测试与现有训练脚本的兼容性"""
    
    print("\n" + "=" * 60)
    print("测试与现有训练脚本的兼容性")
    print("=" * 60)
    
    try:
        # 检查是否可以导入现有的训练模块
        print("1. 检查现有模块导入...")
        
        # 检查是否存在 train.py
        if os.path.exists('/Users/<USER>/project/ai/vibe/daily/eth-trade/train.py'):
            print("✅ train.py 存在")
        else:
            print("⚠️ train.py 不存在")
        
        # 检查是否存在 data_loader.py
        if os.path.exists('/Users/<USER>/project/ai/vibe/daily/eth-trade/data_loader.py'):
            print("✅ data_loader.py 存在")
            
            # 测试 data_loader 是否能使用新功能
            from data_loader import load_data_for_training
            
            # 这应该能够工作，因为 data_loader 会调用 model_utils_815
            df = load_data_for_training(
                coin_name="ETH",
                db_path="/Users/<USER>/project/ai/vibe/daily/eth-trade/coin_data.db",
                symbol="ETHUSDT",
                interval="5min",
                initial_count=1000
            )
            
            if df is not None:
                print(f"✅ data_loader 集成测试成功: {df.shape}")
                print(f"   包含主动买卖量: {'taker_buy_base_volume' in df.columns}")
            else:
                print("⚠️ data_loader 集成测试失败")
        else:
            print("⚠️ data_loader.py 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    
    print("🚀 开始集成测试...")
    
    # 测试1: 基本功能兼容性
    success1 = test_model_utils_compatibility()
    
    # 测试2: 现有脚本兼容性
    success2 = test_existing_script_compatibility()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success1 and success2:
        print("🎉 所有测试通过！")
        print("✅ 新的主动买卖量特征已成功集成")
        print("✅ 与现有代码完全兼容")
        print("✅ 可以直接在现有训练脚本中使用")
        
        print("\n📋 使用建议:")
        print("1. 现有训练脚本无需修改即可使用新特征")
        print("2. 新特征会自动检测数据库中的主动买卖量字段")
        print("3. 如果没有主动买卖量数据，会自动回退到传统特征")
        print("4. 建议在重要模型训练前先运行此测试脚本验证")
        
        return True
    else:
        print("❌ 部分测试失败")
        print("请检查错误信息并修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
