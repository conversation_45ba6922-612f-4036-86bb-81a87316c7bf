#!/bin/bash

# 测试不使用 --better-price-pct 参数时的向后兼容性

echo "=== 测试向后兼容性：不使用 --better-price-pct 参数 ==="

# 测试基本功能（应该不报错）
echo "测试1: 基本回测（不使用挂单功能）"
python backtest_money_quick.py \
    --coin ETH \
    --interval 15m \
    --initial-capital 1000 \
    --risk-per-trade 1.0 \
    --quick \
    --start-time "2024-01-01" \
    --end-time "2024-01-02" \
    --max-active-predictions 1

if [ $? -eq 0 ]; then
    echo "✅ 测试1通过：基本回测功能正常"
else
    echo "❌ 测试1失败：基本回测功能异常"
    exit 1
fi

echo ""
echo "测试2: 带止损的回测（不使用挂单功能）"
python backtest_money_quick.py \
    --coin ETH \
    --interval 15m \
    --initial-capital 1000 \
    --risk-per-trade 1.0 \
    --stop-loss 2.0 \
    --quick \
    --start-time "2024-01-01" \
    --end-time "2024-01-02" \
    --max-active-predictions 1

if [ $? -eq 0 ]; then
    echo "✅ 测试2通过：带止损的回测功能正常"
else
    echo "❌ 测试2失败：带止损的回测功能异常"
    exit 1
fi

echo ""
echo "🎉 所有向后兼容性测试通过！"
echo "✅ 不使用 --better-price-pct 参数时，所有原有功能正常工作"
