#!/usr/bin/env python3
# 测试向后兼容性：不使用 --better-price-pct 参数时的行为

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pytz

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_prediction_structure():
    """测试预测结构的兼容性"""
    print("=== 测试预测结构兼容性 ===")
    
    # 模拟不同的 better_price_pct 设置
    test_cases = [
        {"better_price_pct": 0.0, "name": "不使用挂单 (默认行为)"},
        {"better_price_pct": 0.1, "name": "使用0.1%挂单"},
    ]
    
    for case in test_cases:
        print(f"\n测试案例: {case['name']}")
        better_price_pct = case['better_price_pct'] / 100.0  # 转换为小数
        
        # 模拟预测创建逻辑
        price = 2000.0
        guess = 1  # 做多
        
        # 计算挂单价格
        if better_price_pct > 0:
            if guess == 1:  # 做多
                order_price = price * (1 - better_price_pct)
            else:  # 做空
                order_price = price * (1 + better_price_pct)
            status = 'pending'  # 挂单状态
            start_price = None  # 挂单状态下暂时没有成交价
        else:
            order_price = price
            status = 'active'  # 直接成交
            start_price = price  # 直接成交时设置起始价格
        
        prediction = {
            'id': 'test_001',
            'guess': guess,
            'signal_price': price,
            'order_price': order_price,
            'start_price': start_price,
            'status': status,
        }
        
        print(f"  信号价格: {prediction['signal_price']}")
        print(f"  挂单价格: {prediction['order_price']}")
        print(f"  起始价格: {prediction['start_price']}")
        print(f"  状态: {prediction['status']}")
        
        # 测试获取起始价格的逻辑（与实际代码逻辑一致）
        effective_start_price = prediction.get('start_price') or prediction.get('order_price', 0)
        print(f"  有效起始价格: {effective_start_price}")

        # 验证逻辑正确性
        if better_price_pct == 0:
            assert prediction['status'] == 'active', "默认情况下应该是active状态"
            assert prediction['start_price'] == price, "默认情况下start_price应该等于信号价格"
            assert effective_start_price == price, "有效起始价格应该等于信号价格"
        else:
            assert prediction['status'] == 'pending', "使用挂单时应该是pending状态"
            assert prediction['start_price'] is None, "挂单状态下start_price应该为None"
            assert effective_start_price == prediction['order_price'], "有效起始价格应该等于挂单价格"
        
        print(f"  ✅ 结构验证通过")

def test_price_change_calculation():
    """测试价格变化计算的兼容性"""
    print("\n=== 测试价格变化计算兼容性 ===")
    
    # 模拟不同状态的预测
    predictions = [
        {
            'name': '直接成交 (better_price_pct = 0)',
            'pred': {
                'start_price': 2000.0,
                'order_price': 2000.0,
                'status': 'active'
            },
            'current_price': 2020.0
        },
        {
            'name': '挂单成交后 (better_price_pct > 0)',
            'pred': {
                'start_price': 1998.0,  # 成交后设置
                'order_price': 1998.0,
                'status': 'active'
            },
            'current_price': 2020.0
        },
        {
            'name': '挂单未成交 (better_price_pct > 0)',
            'pred': {
                'start_price': None,
                'order_price': 1998.0,
                'status': 'pending'
            },
            'current_price': 2020.0
        }
    ]
    
    for case in predictions:
        print(f"\n测试案例: {case['name']}")
        pred = case['pred']
        current_price = case['current_price']
        
        # 使用修复后的逻辑获取起始价格
        start_price = pred.get('start_price', pred.get('order_price', 0))
        
        if pred['status'] == 'active':
            # 只有active状态才计算价格变化
            if start_price > 0:
                price_change_pct = (current_price - start_price) / start_price * 100
                print(f"  起始价格: {start_price}")
                print(f"  当前价格: {current_price}")
                print(f"  价格变化: {price_change_pct:+.2f}%")
                print(f"  ✅ 计算成功")
            else:
                print(f"  ❌ 起始价格无效: {start_price}")
        else:
            print(f"  状态: {pred['status']} - 跳过价格变化计算")
            print(f"  ✅ 正确跳过")

if __name__ == "__main__":
    print("=== 向后兼容性测试 ===")
    
    try:
        test_prediction_structure()
        test_price_change_calculation()
        
        print("\n🎉 所有兼容性测试通过！")
        print("✅ 不使用 --better-price-pct 参数时行为正常")
        print("✅ 使用 --better-price-pct 参数时功能正常")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
