#!/usr/bin/env python3
"""
测试阈值参数修复
"""

import json
import os

def test_config_files():
    """检查生成的配置文件是否包含threshold参数"""
    print("=== 检查配置文件中的threshold参数 ===")
    
    # 查找所有配置文件
    config_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('_config.json'):
                config_files.append(os.path.join(root, file))
    
    if not config_files:
        print("未找到任何配置文件")
        return True
    
    for config_file in config_files:
        print(f"\n检查文件: {config_file}")
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            # 检查是否有threshold参数在不应该出现的地方
            if 'best_cv_params' in config:
                cv_params = config['best_cv_params']
                if 'threshold' in cv_params:
                    print(f"❌ 发现问题: {config_file} 的 best_cv_params 中包含 threshold 参数")
                    print(f"   threshold 值: {cv_params['threshold']}")
                    return False
                else:
                    print(f"✅ 正常: best_cv_params 中没有 threshold 参数")
            
            # 检查是否有正确的best_threshold参数
            if 'best_threshold' in config:
                print(f"✅ 正常: 找到 best_threshold 参数: {config['best_threshold']}")
            else:
                print(f"⚠️  警告: 未找到 best_threshold 参数")
                
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
            return False
    
    print("\n✅ 所有配置文件检查完成")
    return True

def show_lightgbm_params():
    """显示LightGBM的有效参数列表"""
    print("\n=== LightGBM 有效参数参考 ===")
    valid_params = [
        'objective', 'metric', 'random_state', 'verbose', 'n_jobs',
        'n_estimators', 'learning_rate', 'max_depth', 'num_leaves',
        'min_child_samples', 'subsample', 'colsample_bytree',
        'reg_alpha', 'reg_lambda', 'min_split_gain', 'subsample_freq'
    ]
    
    print("常用的有效参数:")
    for param in valid_params:
        print(f"  - {param}")
    
    print("\n❌ 无效参数 (会产生警告):")
    print("  - threshold (这是我们自定义的预测阈值，不应传递给LightGBM)")

if __name__ == '__main__':
    show_lightgbm_params()
    success = test_config_files()
    
    if success:
        print("\n🎉 测试通过！threshold 参数已正确处理")
    else:
        print("\n❌ 测试失败！需要进一步修复")