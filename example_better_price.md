# 挂单买入功能使用说明

## 功能概述

新增的 `--better-price-pct` 参数允许你在回测中使用挂单买入策略，而不是直接按信号价格买入。这个功能模拟了真实交易中的挂单行为，可以获得更好的入场价格。

## 工作原理

### 挂单价格计算
- **做多信号**: 挂单价格 = 信号价格 × (1 - better_price_pct%)
- **做空信号**: 挂单价格 = 信号价格 × (1 + better_price_pct%)

### 成交判断
程序会检查下一根K线的高低价来判断挂单是否成交：
- **做多挂单**: 当K线最低价 ≤ 挂单价格时成交
- **做空挂单**: 当K线最高价 ≥ 挂单价格时成交

### 超时处理
如果挂单在预设的等待时间内未成交，该单会被标记为"挂单失败"并取消。

## 使用示例

### 基本用法
```bash
# 使用0.1%的更好价格期望
python backtest_money_quick.py --coin ETH --better-price-pct 0.1 --quick

# 使用0.2%的更好价格期望，并启用止损
python backtest_money_quick.py --coin ETH --better-price-pct 0.2 --stop-loss 2.0 --quick
```

### 完整示例
```bash
python backtest_money_quick.py \
    --coin ETH \
    --interval 15m \
    --better-price-pct 0.1 \
    --initial-capital 10000 \
    --risk-per-trade 2.0 \
    --stop-loss 2.5 \
    --quick \
    --start-time "2024-01-01" \
    --end-time "2024-12-31"
```

## 参数说明

- `--better-price-pct`: 期望更好价格的百分比
  - 例如: `0.1` 表示期望0.1%更好的价格
  - 做多时会挂更低0.1%的价格
  - 做空时会挂更高0.1%的价格

## 实际案例

假设ETH价格为$2000，收到做多信号：

### 不使用挂单 (better-price-pct = 0)
- 直接以$2000买入

### 使用0.1%挂单 (better-price-pct = 0.1)
- 挂单价格: $2000 × (1 - 0.001) = $1998
- 如果下一根K线最低价触及$1998或更低，则成交
- 如果没有触及，挂单失败

## 统计信息

回测结果会显示额外的统计信息：
- **挂单失败**: 显示有多少挂单因为价格未触及而失败
- **成交信息**: 在详细日志中显示挂单成交的具体价格和时间

## 注意事项

1. **真实性**: 这个功能更接近真实交易，因为实际交易中很难保证按信号价格立即成交
2. **成交率**: 设置的better-price-pct越大，挂单失败的概率越高
3. **收益影响**: 成功的挂单会获得更好的入场价格，但失败的挂单会错过交易机会
4. **资金管理**: 挂单失败时不会产生任何盈亏，资金保持不变（因为没有实际交易发生）
5. **等待时间**: 挂单等待时间与模型的max_lookforward_minutes相同，从信号时间开始计算
6. **建议值**: 建议从0.05%-0.2%开始测试，根据历史数据的波动性调整

## 输出文件

回测结果CSV文件中会包含新的字段：
- `SignalPrice`: 原始信号价格
- `OrderPrice`: 挂单价格
- `FilledPrice`: 实际成交价格
- `FilledTimestamp`: 成交时间
