#!/usr/bin/env python3
# 测试 model_utils_pv.py 中的量价分析特征

import sys
import os
sys.path.append('trade')

import pandas as pd
import numpy as np
from model_utils_pv import calculate_features, get_feature_list

def create_realistic_test_data(n_periods=200, timeframe=15):
    """创建更真实的测试数据，模拟量价关系"""
    dates = pd.date_range('2024-01-01', periods=n_periods, freq=f'{timeframe}min')
    
    np.random.seed(42)
    base_price = 2500
    base_volume = 1000
    
    data = {
        'open': [],
        'high': [],
        'low': [],
        'close': [],
        'volume': []
    }
    
    current_price = base_price
    trend_direction = 1  # 1为上涨，-1为下跌
    trend_strength = 0.5
    
    for i in range(n_periods):
        # 模拟趋势变化
        if i % 50 == 0:  # 每50个周期可能改变趋势
            trend_direction *= np.random.choice([-1, 1], p=[0.3, 0.7])
            trend_strength = np.random.uniform(0.3, 0.8)
        
        # 价格变化
        trend_change = trend_direction * trend_strength * np.random.uniform(0.001, 0.01)
        noise = np.random.normal(0, 0.005)
        price_change = trend_change + noise
        
        new_price = current_price * (1 + price_change)
        
        # 生成OHLC
        open_price = current_price
        close_price = new_price
        
        high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, 0.003)))
        low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, 0.003)))
        
        # 模拟量价关系
        # 突破时放量，盘整时缩量
        price_momentum = abs(price_change)
        if price_momentum > 0.01:  # 大幅变动时
            volume_multiplier = np.random.uniform(1.5, 3.0)  # 放量
        elif price_momentum < 0.002:  # 小幅变动时
            volume_multiplier = np.random.uniform(0.3, 0.8)  # 缩量
        else:
            volume_multiplier = np.random.uniform(0.8, 1.5)  # 正常量
        
        volume = base_volume * volume_multiplier * np.random.uniform(0.5, 2.0)
        
        data['open'].append(open_price)
        data['high'].append(high_price)
        data['low'].append(low_price)
        data['close'].append(close_price)
        data['volume'].append(volume)
        
        current_price = new_price
    
    df = pd.DataFrame(data, index=dates)
    return df

def test_pv_features():
    """测试量价分析特征"""
    print("=== 测试量价分析特征 ===")
    
    # 创建测试数据
    print("1. 创建模拟量价数据...")
    test_data = create_realistic_test_data(n_periods=200, timeframe=15)
    print(f"   测试数据形状: {test_data.shape}")
    print(f"   价格范围: {test_data['close'].min():.2f} - {test_data['close'].max():.2f}")
    print(f"   成交量范围: {test_data['volume'].min():.0f} - {test_data['volume'].max():.0f}")
    
    # 计算特征
    print("\n2. 计算量价分析特征...")
    try:
        result = calculate_features(test_data, timeframe=15)
        print(f"   特征计算完成，结果形状: {result.shape}")
    except Exception as e:
        print(f"   特征计算失败: {e}")
        return False
    
    # 检查核心量价特征
    print("\n3. 检查核心量价特征...")
    
    # 价格变化特征
    price_features = [col for col in result.columns if 'price_change' in col]
    print(f"   价格变化特征 ({len(price_features)}个): {price_features}")
    
    # 成交量变化特征
    volume_features = [col for col in result.columns if 'volume_change' in col]
    print(f"   成交量变化特征 ({len(volume_features)}个): {volume_features}")
    
    # 量价背离特征
    divergence_features = [col for col in result.columns if 'divergence' in col]
    print(f"   量价背离特征 ({len(divergence_features)}个): {divergence_features}")
    
    # 突破信号特征
    breakout_features = [col for col in result.columns if 'breakout' in col]
    print(f"   突破信号特征 ({len(breakout_features)}个): {breakout_features}")
    
    # 量价趋势特征
    trend_features = [col for col in result.columns if any(x in col for x in ['price_up_volume_down', 'price_down_volume_up', 'trend'])]
    print(f"   量价趋势特征 ({len(trend_features)}个): {trend_features}")
    
    # VWAP特征
    vwap_features = [col for col in result.columns if 'vwap' in col]
    print(f"   VWAP特征 ({len(vwap_features)}个): {vwap_features}")
    
    # 测试特征列表函数
    print("\n4. 测试特征列表函数...")
    try:
        feature_list = get_feature_list(result, time_frame=15)
        print(f"   特征列表长度: {len(feature_list)}")
        print(f"   前10个特征: {feature_list[:10]}")
    except Exception as e:
        print(f"   特征列表获取失败: {e}")
        return False
    
    # 检查数据质量
    print("\n5. 检查数据质量...")
    nan_counts = result.isnull().sum()
    features_with_nan = nan_counts[nan_counts > 0]
    if len(features_with_nan) > 0:
        print(f"   包含NaN的特征: {len(features_with_nan)}个")
        print(f"   前5个: {features_with_nan.head()}")
    else:
        print("   所有特征都没有NaN值")
    
    # 显示量价分析核心指标的统计
    print("\n6. 量价分析核心指标统计...")
    
    # 量价背离指标
    if 'volume_price_divergence_1' in result.columns:
        divergence = result['volume_price_divergence_1'].dropna()
        print(f"   量价背离(1期): 均值 {divergence.mean():.4f}, 标准差 {divergence.std():.4f}")
    
    # 价涨量缩信号
    if 'price_up_volume_down' in result.columns:
        bullish_signals = result['price_up_volume_down'].sum()
        print(f"   价涨量缩信号出现次数: {bullish_signals}")
    
    # 价跌量增信号  
    if 'price_down_volume_up' in result.columns:
        bearish_signals = result['price_down_volume_up'].sum()
        print(f"   价跌量增信号出现次数: {bearish_signals}")
    
    # 突破信号
    if 'breakout_volume_support' in result.columns:
        breakout_signals = result['breakout_volume_support'].abs().sum()
        print(f"   突破信号总数: {breakout_signals}")
    
    # 成交量相对强度
    if 'volume_ratio_20' in result.columns:
        vol_ratio = result['volume_ratio_20'].dropna()
        high_volume_periods = (vol_ratio > 1.5).sum()
        print(f"   高成交量期间(>1.5倍均值): {high_volume_periods}次")
    
    print(f"\n7. 特征总结...")
    print(f"   总特征数: {len(result.columns)}")
    print(f"   可用特征数: {len(feature_list)}")
    print(f"   特征覆盖率: {len(feature_list)/len(result.columns)*100:.1f}%")
    
    print("\n=== 测试完成 ===")
    return True

if __name__ == "__main__":
    success = test_pv_features()
    if success:
        print("✅ 量价分析特征测试通过！")
        print("\n📊 量价分析特征说明:")
        print("   - 基于四大量价原则设计")
        print("   - 专注于价格和成交量的关系")
        print("   - 包含突破信号、背离检测、趋势确认等核心功能")
        print("   - 适用于短期交易信号识别")
    else:
        print("❌ 测试失败！")
