#!/usr/bin/env python3
"""
测试更新后的时间窗口特征计算
验证所有特征都使用统一的时间窗口 [30, 60, 120, 360, 720]
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目根目录到路径
sys.path.append('/Users/<USER>/project/ai/vibe/daily/eth-trade')

from model_utils_815 import load_and_prepare_data_from_db, calculate_features, get_feature_list

def test_updated_time_windows():
    """测试更新后的时间窗口特征"""
    
    print("=" * 60)
    print("测试更新后的时间窗口特征")
    print("=" * 60)
    
    # 定义期望的时间窗口
    EXPECTED_WINDOWS = [30, 60, 120, 360, 720]
    
    # 1. 加载数据
    db_path = "/Users/<USER>/project/ai/vibe/daily/eth-trade/coin_data.db"
    table_name = "ETHUSDT_5min_spot"
    
    print(f"从数据库加载数据: {table_name}")
    df = load_and_prepare_data_from_db(db_path, table_name, limit=5000)
    
    if df is None:
        print("❌ 数据加载失败")
        return False
    
    print(f"✅ 数据加载成功，共 {len(df)} 条记录")
    
    # 2. 计算特征
    print("\n计算特征...")
    timeframe = 5
    df_with_features = calculate_features(df, timeframe)
    print(f"✅ 特征计算完成，数据形状: {df_with_features.shape}")
    
    # 3. 获取特征列表
    feature_list = get_feature_list(df_with_features, time_frame=timeframe)
    print(f"✅ 特征列表生成成功: {len(feature_list)} 个特征")
    
    # 4. 验证时间窗口特征
    print("\n" + "=" * 40)
    print("验证时间窗口特征")
    print("=" * 40)
    
    # 检查收益率特征
    print("1. 收益率特征:")
    return_features = [f for f in feature_list if f.startswith('return_') and f.endswith('min')]
    expected_return_features = [f'return_{w}min' for w in EXPECTED_WINDOWS]
    
    print(f"   期望的收益率特征: {expected_return_features}")
    print(f"   实际的收益率特征: {return_features}")
    
    missing_return = set(expected_return_features) - set(return_features)
    extra_return = set(return_features) - set(expected_return_features)
    
    if missing_return:
        print(f"   ❌ 缺少收益率特征: {missing_return}")
    if extra_return:
        print(f"   ⚠️ 额外的收益率特征: {extra_return}")
    if not missing_return and not extra_return:
        print("   ✅ 收益率特征完全匹配")
    
    # 检查波动率特征
    print("\n2. 波动率特征:")
    volatility_features = [f for f in feature_list if f.startswith('volatility_ratio_')]
    expected_volatility_features = [f'volatility_ratio_{w}' for w in EXPECTED_WINDOWS]
    
    print(f"   期望的波动率特征: {expected_volatility_features}")
    print(f"   实际的波动率特征: {volatility_features}")
    
    missing_vol = set(expected_volatility_features) - set(volatility_features)
    extra_vol = set(volatility_features) - set(expected_volatility_features)
    
    if missing_vol:
        print(f"   ❌ 缺少波动率特征: {missing_vol}")
    if extra_vol:
        print(f"   ⚠️ 额外的波动率特征: {extra_vol}")
    if not missing_vol and not extra_vol:
        print("   ✅ 波动率特征完全匹配")
    
    # 检查SMA特征
    print("\n3. SMA特征:")
    sma_features = [f for f in feature_list if f.startswith('price_div_sma_')]
    expected_sma_features = [f'price_div_sma_{w}' for w in EXPECTED_WINDOWS]
    
    print(f"   期望的SMA特征: {expected_sma_features}")
    print(f"   实际的SMA特征: {sma_features}")
    
    missing_sma = set(expected_sma_features) - set(sma_features)
    extra_sma = set(sma_features) - set(expected_sma_features)
    
    if missing_sma:
        print(f"   ❌ 缺少SMA特征: {missing_sma}")
    if extra_sma:
        print(f"   ⚠️ 额外的SMA特征: {extra_sma}")
    if not missing_sma and not extra_sma:
        print("   ✅ SMA特征完全匹配")
    
    # 检查VMA特征
    print("\n4. VMA特征:")
    vma_features = [f for f in feature_list if f.startswith('volume_div_vma_')]
    expected_vma_features = [f'volume_div_vma_{w}' for w in EXPECTED_WINDOWS + [1440]]
    
    print(f"   期望的VMA特征: {expected_vma_features}")
    print(f"   实际的VMA特征: {vma_features}")
    
    missing_vma = set(expected_vma_features) - set(vma_features)
    extra_vma = set(vma_features) - set(expected_vma_features)
    
    if missing_vma:
        print(f"   ❌ 缺少VMA特征: {missing_vma}")
    if extra_vma:
        print(f"   ⚠️ 额外的VMA特征: {extra_vma}")
    if not missing_vma and not extra_vma:
        print("   ✅ VMA特征完全匹配")
    
    # 检查主动买卖量特征
    print("\n5. 主动买卖量特征:")
    taker_ma_features = [f for f in feature_list if 'taker_buy_ratio_ma_' in f or 'buy_sell_ratio_ma_' in f]
    taker_std_features = [f for f in feature_list if 'taker_buy_ratio_std_' in f or 'buy_sell_ratio_std_' in f]
    taker_dev_features = [f for f in feature_list if 'taker_buy_ratio_dev_' in f or 'buy_sell_ratio_dev_' in f]
    
    expected_taker_ma = []
    expected_taker_std = []
    expected_taker_dev = []
    
    for w in EXPECTED_WINDOWS:
        expected_taker_ma.extend([f'taker_buy_ratio_ma_{w}', f'buy_sell_ratio_ma_{w}'])
        expected_taker_std.extend([f'taker_buy_ratio_std_{w}', f'buy_sell_ratio_std_{w}'])
        expected_taker_dev.extend([f'taker_buy_ratio_dev_{w}', f'buy_sell_ratio_dev_{w}'])
    
    print(f"   主动买卖量MA特征: {len(taker_ma_features)}/{len(expected_taker_ma)}")
    print(f"   主动买卖量STD特征: {len(taker_std_features)}/{len(expected_taker_std)}")
    print(f"   主动买卖量DEV特征: {len(taker_dev_features)}/{len(expected_taker_dev)}")
    
    if len(taker_ma_features) == len(expected_taker_ma):
        print("   ✅ 主动买卖量MA特征数量正确")
    else:
        print(f"   ❌ 主动买卖量MA特征数量不匹配")
    
    # 6. 数据质量检查
    print("\n" + "=" * 40)
    print("数据质量检查")
    print("=" * 40)
    
    # 检查新时间窗口特征的数据质量
    new_features = [f'return_30min', f'return_60min', f'volatility_ratio_30', f'volatility_ratio_60']
    
    for feature in new_features:
        if feature in df_with_features.columns:
            values = df_with_features[feature].dropna()
            if len(values) > 0:
                print(f"   {feature}: {len(values)} 有效值, 范围: [{values.min():.4f}, {values.max():.4f}]")
            else:
                print(f"   {feature}: 全部为NaN")
        else:
            print(f"   {feature}: 特征不存在")
    
    # 7. 特征统计
    print("\n" + "=" * 40)
    print("特征统计")
    print("=" * 40)
    
    feature_categories = {
        'return': [f for f in feature_list if f.startswith('return_')],
        'volatility': [f for f in feature_list if f.startswith('volatility_ratio_')],
        'sma': [f for f in feature_list if f.startswith('price_div_sma_')],
        'vma': [f for f in feature_list if f.startswith('volume_div_vma_')],
        'taker_ma': [f for f in feature_list if 'taker_buy_ratio_ma_' in f or 'buy_sell_ratio_ma_' in f],
        'taker_std': [f for f in feature_list if 'taker_buy_ratio_std_' in f or 'buy_sell_ratio_std_' in f],
        'taker_dev': [f for f in feature_list if 'taker_buy_ratio_dev_' in f or 'buy_sell_ratio_dev_' in f],
        'other': [f for f in feature_list if not any(cat in f for cat in ['return_', 'volatility_ratio_', 'price_div_sma_', 'volume_div_vma_', 'taker_buy_ratio_', 'buy_sell_ratio_'])]
    }
    
    total_features = 0
    for category, features in feature_categories.items():
        print(f"   {category}: {len(features)} 个特征")
        total_features += len(features)
    
    print(f"   总计: {total_features} 个特征")
    
    return True

if __name__ == "__main__":
    success = test_updated_time_windows()
    if success:
        print("\n🎉 时间窗口更新测试完成！")
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)
