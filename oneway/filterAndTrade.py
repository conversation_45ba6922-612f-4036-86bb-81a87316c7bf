# -*- coding: utf-8 -*-
import pandas as pd

def process_and_merge_csvs_final(fhl_path, bmlq_path, output_path):
    """
    根据最终确认的正确逻辑处理和合并CSV文件：
    1. 显式地将 backtest 文件中的北京时间转换为 UTC 时间（减去8小时）。
    2. 基于此结果与 filterHighLow 文件中的 UTC 时间进行精确匹配。

    参数:
        fhl_path (str): filterHighLow.csv 的文件路径 (时间戳为 UTC)。
        bmlq_path (str): backtest_money_log_quick.csv 的文件路径 (时间戳为北京时间)。
        output_path (str): 输出合并后的CSV文件的路径。
    """
    try:
        fhl_df = pd.read_csv(fhl_path)
        bmlq_df = pd.read_csv(bmlq_path)
        print("✅ 成功加载 CSV 文件。")
    except FileNotFoundError as e:
        print(f"❌ 错误: {e}。请确保文件与脚本在同一个目录下。")
        return

    # 步骤 1: 预处理 `backtest_money_log_quick.csv` - 采用全新的、更可靠的转换方法
    print("\n⏳ 正在处理 'backtest_money_log_quick.csv' (采用更可靠的转换方法)...")
    try:
        # A. 将时间戳列转换为字符串，以防有混合类型
        timestamp_str = bmlq_df['StartTimestamp'].astype(str)
        
        # B. 移除 ' UTC+8' 后缀，得到一个纯净、无时区信息的时间字符串
        #    使用 .str.strip() 顺便移除可能存在的首尾空格
        naive_time_str = timestamp_str.str.replace(' UTC\+8', '', regex=True).str.strip()

        # C. 将这个纯净的字符串转换为“天真”的 datetime 对象
        naive_datetime = pd.to_datetime(naive_time_str)

        # D. 关键步骤：显式地将这个“天真”的时间“本地化”为北京时区
        beijing_time_aware = naive_datetime.dt.tz_localize('Asia/Shanghai', ambiguous='infer')

        # E. 最终转换：将已经正确标记为北京时区的时间，转换为 UTC。
        #    这一步现在会正确地执行“减去8小时”的操作。
        bmlq_df['backtest_utc_time'] = beijing_time_aware.dt.tz_convert('UTC')
        
        print("✅ 成功新增 'backtest_utc_time' 列。")
        print("   诊断信息 (前3行):")
        for i, row in bmlq_df.head(3).iterrows():
            print(f"   原始北京时间: {row['StartTimestamp']} -> 转换后UTC时间: {row['backtest_utc_time']}")

    except Exception as e:
        print(f"❌ 在处理 'backtest_money_log_quick.csv' 时出错: {e}")
        return

    # 步骤 2: 预处理 `filterHighLow.csv`，确保时间为 UTC
    print("\n⏳ 正在处理 'filterHighLow.csv'...")
    try:
        fhl_df['Timestamp_UTC'] = pd.to_datetime(fhl_df['Timestamp']).dt.tz_localize('UTC')
        print("✅ 成功将 'Timestamp' 列处理为标准的 UTC 时间。")
    except Exception as e:
        print(f"❌ 在处理 'filterHighLow.csv' 时出错: {e}")
        return
        
    # 步骤 3: 按条件筛选
    print("\n⏳ 正在根据条件筛选数据...")
    fhl_filtered = fhl_df[fhl_df['OriginalPredictedLabel'] == '最低点'].copy()
    bmlq_filtered = bmlq_df[bmlq_df['Prediction'] == 1].copy()
    print(f"📊 在 'filterHighLow.csv' 中找到 {len(fhl_filtered)} 条 '最低点' 记录。")
    print(f"📊 在 'backtest_money_log_quick.csv' 中找到 {len(bmlq_filtered)} 条 'Prediction' 为 1 的记录。")
    
    # 步骤 4: 基于标准化的 UTC 时间进行合并
    print("\n⏳ 正在合并数据...")
    merged_df = pd.merge(
        fhl_filtered,
        bmlq_filtered,
        left_on='Timestamp_UTC',
        right_on='backtest_utc_time',
        how='inner'
    )

    if merged_df.empty:
        print("⏹️ 合并结果为空。没有找到在同一 UTC 时间点同时满足筛选条件的记录。")
        return
    
    print(f"✅ 合并成功！共找到 {len(merged_df)} 条匹配记录。")

    # 步骤 5: 清理并保存结果
    merged_df = merged_df.drop(columns=['Timestamp_UTC'])
    merged_df.to_csv(output_path, index=False, encoding='utf-8-sig')
    print(f"🎉 处理完成！文件已保存为 '{output_path}'")


# --- 脚本主执行区 ---
if __name__ == "__main__":
    filter_high_low_file = 'filterHighLow.csv'
    backtest_log_file = 'backtest_money_log_quick.csv'
    output_file = 'filtered_and_merged_data_corrected.csv'

    process_and_merge_csvs_final(filter_high_low_file, backtest_log_file, output_file)