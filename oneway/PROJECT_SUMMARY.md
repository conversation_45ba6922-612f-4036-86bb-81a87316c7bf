# 📊 连续移动预测模型项目总结

## 🎯 项目目标

创建一个能够预测加密货币**连续不回头百分比移动**的机器学习模型，与传统的"先涨先跌"二元分类不同，这个模型预测价格能够连续移动的最大百分比。

## 🔬 核心算法

### 连续移动定义
- **连续性**: 价格朝一个方向移动，中途回撤不超过设定阈值
- **最大回撤**: 允许的最大回撤百分比（如1%）
- **前瞻窗口**: 计算连续移动的时间范围

### 算法流程
1. 对每个时间点，向前查看指定时间窗口
2. 分别计算上涨和下跌方向的最大连续移动
3. 选择绝对值更大的移动作为标签
4. 使用LightGBM回归模型学习预测

## 📁 项目结构

```
oneway/
├── 🚀 核心文件
│   ├── train_standalone.py          # 独立训练脚本 (推荐)
│   ├── predict_standalone.py        # 独立预测脚本
│   ├── standalone_utils.py          # 独立工具函数
│   └── continuous_config.json       # 配置文件
│
├── 🧪 测试和示例
│   ├── test_standalone.py           # 功能测试
│   ├── run_example.py              # 一键运行示例
│   └── setup.py                    # 环境设置
│
├── 📚 文档
│   ├── README.md                   # 详细文档
│   ├── QUICKSTART.md              # 快速启动指南
│   └── PROJECT_SUMMARY.md         # 项目总结 (本文件)
│
└── 🔧 完整版本 (需要外部依赖)
    ├── train_continuous_movement.py
    ├── train_continuous_movement_v2.py
    ├── predict_continuous_movement.py
    ├── batch_train_continuous.py
    └── continuous_utils.py
```

## 🎨 设计特点

### 1. 独立性
- **无外部依赖**: 独立版本不依赖父目录文件
- **自包含**: 所有必要功能都集成在oneway文件夹中
- **易部署**: 整个文件夹可以独立运行

### 2. 灵活性
- **多币种支持**: ETH, BTC, DOT, SUI等
- **参数可调**: 最大回撤、前瞻窗口等
- **模式选择**: 快速训练或完整优化

### 3. 实用性
- **回归预测**: 预测具体的百分比而非方向
- **信心评估**: 根据预测幅度评估信心水平
- **性能监控**: 详细的评估指标和特征重要性

## 📊 模型性能

### 评估指标
- **RMSE**: 均方根误差，衡量预测精度
- **MAE**: 平均绝对误差，衡量平均偏差
- **R²**: 决定系数，衡量模型解释能力
- **方向准确率**: 预测涨跌方向的准确性

### 特征工程
- **技术指标**: RSI, 布林带宽度, VWAP等
- **价格特征**: 收益率, 波动率, 移动平均等
- **K线特征**: 实体比例, 影线长度, ATR标准化等
- **时间特征**: 小时, 星期等

## 🚀 使用场景

### 1. 量化交易
- **止盈设置**: 根据预测设置合理的止盈点
- **仓位管理**: 根据预测信心调整仓位大小
- **风险控制**: 结合最大回撤进行风险管理

### 2. 市场分析
- **趋势预测**: 预测短期价格移动幅度
- **波动性评估**: 评估市场波动程度
- **时机选择**: 选择合适的入场时机

### 3. 研究开发
- **特征研究**: 分析哪些特征对预测最重要
- **参数优化**: 测试不同参数组合的效果
- **模型改进**: 基于结果改进算法

## 🔧 技术栈

### 核心库
- **pandas**: 数据处理和分析
- **numpy**: 数值计算
- **lightgbm**: 梯度提升模型
- **scikit-learn**: 机器学习工具
- **optuna**: 超参数优化

### 数据存储
- **SQLite**: 历史数据存储
- **CSV**: 结果导出
- **JSON**: 配置管理
- **joblib**: 模型序列化

## 📈 性能优化

### 训练优化
- **时序交叉验证**: 避免数据泄露
- **早停机制**: 防止过拟合
- **超参数优化**: 使用Optuna自动调优
- **特征选择**: 移除无用特征

### 预测优化
- **向量化计算**: 使用NumPy加速
- **批量预测**: 一次处理多个样本
- **内存管理**: 优化内存使用
- **缓存机制**: 缓存计算结果

## 🎯 未来改进

### 算法改进
1. **多时间框架**: 结合不同时间框架的信息
2. **集成学习**: 使用多个模型的集成
3. **深度学习**: 尝试LSTM或Transformer
4. **在线学习**: 实时更新模型参数

### 功能扩展
1. **实时预测**: 连接实时数据源
2. **多资产**: 支持股票、期货等其他资产
3. **策略回测**: 集成完整的回测框架
4. **可视化**: 添加图表和仪表板

### 工程优化
1. **分布式训练**: 支持多机训练
2. **模型服务**: 部署为API服务
3. **监控告警**: 模型性能监控
4. **A/B测试**: 模型版本对比

## 📋 使用检查清单

### 环境准备
- [ ] Python 3.8+ 已安装
- [ ] 必需包已安装 (`pip install pandas numpy lightgbm scikit-learn optuna joblib`)
- [ ] 配置文件存在 (`continuous_config.json`)

### 数据准备
- [ ] 历史数据可用 (SQLite数据库或CSV文件)
- [ ] 数据质量检查 (无缺失值、异常值)
- [ ] 时间范围合适 (至少几个月的数据)

### 模型训练
- [ ] 运行功能测试 (`python test_standalone.py`)
- [ ] 选择合适参数 (币种、回撤阈值等)
- [ ] 执行训练 (`python train_standalone.py`)
- [ ] 检查结果 (RMSE、R²等指标)

### 模型使用
- [ ] 加载训练好的模型
- [ ] 准备新数据进行预测
- [ ] 解释预测结果
- [ ] 制定交易策略

## ⚠️ 风险提示

1. **模型局限性**: 模型基于历史数据，无法预测突发事件
2. **市场风险**: 加密货币市场波动极大，存在重大损失风险
3. **技术风险**: 模型可能存在bug或过拟合问题
4. **使用责任**: 用户需要自行承担使用模型的风险

## 📞 支持和贡献

### 获取帮助
- 查看文档: `README.md`, `QUICKSTART.md`
- 运行测试: `python test_standalone.py`
- 查看示例: `python run_example.py --help`

### 贡献代码
- 报告bug和问题
- 提出改进建议
- 贡献新功能
- 完善文档

---

**免责声明**: 本项目仅供学习和研究使用，不构成投资建议。使用者需要自行承担投资风险。