#!/usr/bin/env python3
# setup.py
# 连续移动预测模型环境设置脚本

import subprocess
import sys
import os
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_packages():
    """安装必需的Python包"""
    packages = [
        'pandas>=1.3.0',
        'numpy>=1.21.0',
        'lightgbm>=3.3.0',
        'scikit-learn>=1.0.0',
        'optuna>=3.0.0',
        'joblib>=1.1.0'
    ]
    
    print("📦 安装Python包...")
    
    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', package, '--quiet'
            ])
            print(f"✅ {package}")
        except subprocess.CalledProcessError:
            print(f"❌ {package} 安装失败")
            return False
    
    return True

def verify_installation():
    """验证安装"""
    print("\n🔍 验证安装...")
    
    test_imports = [
        ('pandas', 'pd'),
        ('numpy', 'np'),
        ('lightgbm', 'lgb'),
        ('sklearn', None),
        ('optuna', None),
        ('joblib', None)
    ]
    
    for module, alias in test_imports:
        try:
            if alias:
                exec(f"import {module} as {alias}")
            else:
                exec(f"import {module}")
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            return False
    
    return True

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建目录...")
    
    directories = [
        '../models',
        'data',
        'logs'
    ]
    
    for dir_path in directories:
        try:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            print(f"✅ {dir_path}")
        except Exception as e:
            print(f"❌ {dir_path}: {e}")
            return False
    
    return True

def check_config_files():
    """检查配置文件"""
    print("\n⚙️ 检查配置文件...")
    
    config_files = [
        'continuous_config.json',
        'standalone_utils.py',
        'train_standalone.py',
        'predict_standalone.py'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ {config_file}")
        else:
            print(f"❌ {config_file} (缺失)")
            return False
    
    return True

def run_basic_test():
    """运行基础测试"""
    print("\n🧪 运行基础测试...")
    
    try:
        # 测试配置加载
        from standalone_utils import get_continuous_config
        config = get_continuous_config("ETH_CONTINUOUS_5M")
        if config:
            print("✅ 配置加载测试")
        else:
            print("❌ 配置加载测试")
            return False
        
        # 测试特征计算
        import pandas as pd
        import numpy as np
        from standalone_utils import calculate_features
        
        # 创建简单测试数据
        dates = pd.date_range('2024-01-01', periods=100, freq='5T')
        df = pd.DataFrame({
            'open': np.random.uniform(100, 110, 100),
            'high': np.random.uniform(110, 120, 100),
            'low': np.random.uniform(90, 100, 100),
            'close': np.random.uniform(100, 110, 100),
            'volume': np.random.uniform(1000, 10000, 100)
        }, index=dates)
        
        df_features = calculate_features(df, timeframe=5)
        if len(df_features.columns) > 5:
            print("✅ 特征计算测试")
        else:
            print("❌ 特征计算测试")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 基础测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 连续移动预测模型环境设置")
    print("=" * 50)
    
    steps = [
        ("检查Python版本", check_python_version),
        ("安装Python包", install_packages),
        ("验证安装", verify_installation),
        ("创建目录", create_directories),
        ("检查配置文件", check_config_files),
        ("运行基础测试", run_basic_test)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"\n❌ 设置失败: {step_name}")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 环境设置完成！")
    print("\n📚 下一步:")
    print("1. 运行测试: python test_standalone.py")
    print("2. 快速训练: python train_standalone.py --coin ETH --no-time-series-cv")
    print("3. 查看指南: cat QUICKSTART.md")
    print("4. 一键运行: python run_example.py")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)