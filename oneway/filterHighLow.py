import pandas as pd

# 1. 准备数据
# 替换为您的CSV文件路径
file_path = 'backtest_standalone_eth_5m.csv'

try:
    df = pd.read_csv(file_path)
    print(f"成功从文件 '{file_path}' 中加载数据。")
except FileNotFoundError:
    print(f"错误：文件 '{file_path}' 未找到。请检查文件路径是否正确。")
    # 如果文件未找到，程序将退出或您可以添加其他错误处理逻辑
    exit()
except Exception as e:
    print(f"读取CSV文件时发生错误：{e}")
    exit()

# 2. 核心筛选逻辑
# 条件1: 当前行的 'ProbaHighest' > 0.4
# condition1 = df['ProbaHighest'] > 0.3
condition1 = df['FilteredPredictedLabel'] == "最高点"

# 条件2: 下一行的 'ProbaLowest' > 0.4
# 我们使用 .shift(-1) 将 'ProbaLowest' 列的数据向上移动一行。
# 这样，当前行的 .shift(-1) 值就对应着下一行的原始值。
condition2 = df['ProbaLowest'].shift(-1) > 0.39
# condition2 = df['FilteredPredictedLabel'].shift(-1) == "最低点"
condition3 = df['ProbaHighest']+  df['ProbaLowest'].shift(-1)> 0.8
condition4 = df['ClosePrice'] > df['ClosePrice'].shift(-1)
# 找到满足条件的“前一条”所在的行
# 使用 & (逻辑与) 将两个条件组合起来
is_first_in_pair = condition1 & condition2 

# 找到满足条件的“后一条”所在的行
# “后一条”就是“前一条”的下一行。我们可以通过将上面的布尔索引向下移动一位来找到它们。
# .shift(1) 将布尔值向下移动一行，第一个位置会是NaN，我们用 fill_value=False 填充。
is_second_in_pair = is_first_in_pair.shift(1, fill_value=False)

# 合并两个条件，只要满足其中一个（是“前一条”或“后一条”），就保留
# 使用 | (逻辑或)
final_mask = is_first_in_pair | is_second_in_pair

# 应用最终的布尔掩码（mask）来筛选出 DataFrame
result_df = df[final_mask]


# 3. 显示结果
print("\n----------- 筛选后的数据 (前5行) -----------")
print(result_df.head()) # 显示前5行，因为数据可能很多

print(f"\n筛选前总行数：{len(df)}")
print(f"筛选后总行数：{len(result_df)}")

# 如果想将结果保存到新的CSV文件
result_df.to_csv('filterHighLow.csv', index=False)

print("\n筛选完成。如果需要保存结果，请取消注释最后一行代码并指定文件名。 ./filterHighLow.csv")