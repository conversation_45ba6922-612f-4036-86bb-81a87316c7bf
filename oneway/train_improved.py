#!/usr/bin/env python3
# train_improved.py
# 改进版连续移动预测模型训练脚本

import pandas as pd
import numpy as np
from datetime import datetime
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler, RobustScaler
import joblib
import json
import argparse
import os
import pickle
import optuna

from standalone_utils import (
    get_continuous_config, get_continuous_output_dir, 
    load_data_for_training, calculate_movement_statistics, 
    print_movement_statistics, print_continuous_training_summary
)

# 全局变量
TIMEFRAME_MINUTES = None
MAX_RETRACEMENT = None
MAX_LOOKFORWARD_MINUTES = None
MODEL_BASENAME = None

def calculate_enhanced_features(df, timeframe=5):
    """计算增强的技术指标特征"""
    print(f"开始计算增强特征 ({timeframe}-min K线)...")
    epsilon = 1e-9
    
    # 确保索引是datetime类型
    if not isinstance(df.index, pd.DatetimeIndex):
        if 'timestamp' in df.columns:
            df.index = pd.to_datetime(df['timestamp'])
        elif 'Timestamp' in df.columns:
            df.index = pd.to_datetime(df['Timestamp'])
    
    # 基础价格特征
    df['returns'] = df['close'].pct_change()
    df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
    df['price_change'] = df['close'] - df['open']
    df['price_change_pct'] = df['price_change'] / df['open']
    
    # 波动率特征 (多个时间窗口)
    for window in [5, 10, 20, 50]:
        if window <= len(df):
            df[f'volatility_{window}'] = df['returns'].rolling(window).std()
            df[f'price_std_{window}'] = df['close'].rolling(window).std()
    
    # 趋势特征
    for window in [5, 10, 20, 50]:
        if window <= len(df):
            df[f'sma_{window}'] = df['close'].rolling(window).mean()
            df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
            df[f'price_sma_ratio_{window}'] = df['close'] / (df[f'sma_{window}'] + epsilon)
            df[f'price_ema_ratio_{window}'] = df['close'] / (df[f'ema_{window}'] + epsilon)
    
    # 动量指标
    for window in [5, 10, 20]:
        if window <= len(df):
            df[f'momentum_{window}'] = df['close'] / df['close'].shift(window) - 1
            df[f'roc_{window}'] = (df['close'] - df['close'].shift(window)) / df['close'].shift(window)
    
    # RSI
    for window in [14, 21]:
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / (loss + epsilon)
        df[f'rsi_{window}'] = 100 - (100 / (1 + rs))
    
    # 布林带
    for window in [20, 50]:
        if window <= len(df):
            sma = df['close'].rolling(window).mean()
            std = df['close'].rolling(window).std()
            df[f'bb_upper_{window}'] = sma + 2 * std
            df[f'bb_lower_{window}'] = sma - 2 * std
            df[f'bb_width_{window}'] = (df[f'bb_upper_{window}'] - df[f'bb_lower_{window}']) / (sma + epsilon)
            df[f'bb_position_{window}'] = (df['close'] - df[f'bb_lower_{window}']) / (df[f'bb_upper_{window}'] - df[f'bb_lower_{window}'] + epsilon)
    
    # MACD
    ema12 = df['close'].ewm(span=12).mean()
    ema26 = df['close'].ewm(span=26).mean()
    df['macd'] = ema12 - ema26
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    df['macd_histogram'] = df['macd'] - df['macd_signal']
    
    # 成交量特征
    df['volume_sma_5'] = df['volume'].rolling(5).mean()
    df['volume_sma_20'] = df['volume'].rolling(20).mean()
    df['volume_ratio'] = df['volume'] / (df['volume_sma_20'] + epsilon)
    df['price_volume'] = df['close'] * df['volume']
    df['vwap_20'] = df['price_volume'].rolling(20).sum() / (df['volume'].rolling(20).sum() + epsilon)
    df['price_vwap_ratio'] = df['close'] / (df['vwap_20'] + epsilon)
    
    # K线形态特征
    df['body'] = abs(df['close'] - df['open'])
    df['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])
    df['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']
    df['total_range'] = df['high'] - df['low']
    df['body_ratio'] = df['body'] / (df['total_range'] + epsilon)
    df['upper_shadow_ratio'] = df['upper_shadow'] / (df['total_range'] + epsilon)
    df['lower_shadow_ratio'] = df['lower_shadow'] / (df['total_range'] + epsilon)
    
    # 时间特征
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
    
    # 滞后特征
    for lag in [1, 2, 3, 5]:
        df[f'returns_lag_{lag}'] = df['returns'].shift(lag)
        df[f'volume_lag_{lag}'] = df['volume'].shift(lag)
    
    # 未来收益率特征 (用于验证，不用于预测)
    for lead in [1, 2, 3]:
        df[f'future_return_{lead}'] = df['returns'].shift(-lead)
    
    print("增强特征计算完成。")
    return df

def calculate_improved_continuous_movement(df, max_retracement=0.01, max_lookforward_minutes=240, timeframe=5):
    """改进的连续移动计算 - 更精确的算法"""
    max_lookforward_candles = max_lookforward_minutes // timeframe
    print(f"计算改进的连续移动：最大回撤{max_retracement*100:.1f}%，最大等待{max_lookforward_candles}根K线")
    
    continuous_movements = np.full(len(df), np.nan)
    
    close_prices = df['close'].to_numpy()
    high_prices = df['high'].to_numpy()
    low_prices = df['low'].to_numpy()
    
    for i in range(len(df) - max_lookforward_candles):
        if i % 20000 == 0:
            print(f"处理进度: {i}/{len(df)} ({i/len(df)*100:.1f}%)")
        
        start_price = close_prices[i]
        
        # 更精确的连续移动计算
        max_up_move, max_down_move = calculate_precise_movement(
            i, start_price, high_prices, low_prices, close_prices,
            max_lookforward_candles, max_retracement
        )
        
        # 选择绝对值更大的移动
        if abs(max_up_move) >= abs(max_down_move):
            continuous_movements[i] = max_up_move
        else:
            continuous_movements[i] = -max_down_move
    
    # 创建有效的标签序列
    valid_mask = ~np.isnan(continuous_movements)
    valid_indices = df.index[valid_mask]
    valid_movements = continuous_movements[valid_mask]
    
    print(f"有效标签数量: {len(valid_movements)}/{len(df)} ({len(valid_movements)/len(df)*100:.1f}%)")
    
    if len(valid_movements) > 0:
        stats = calculate_movement_statistics(valid_movements)
        print_movement_statistics(stats)
    else:
        print("未生成任何有效标签。")
    
    return pd.Series(index=valid_indices, data=valid_movements)

def calculate_precise_movement(start_idx, start_price, high_prices, low_prices, close_prices,
                             max_lookforward, max_retracement):
    """更精确的方向性移动计算"""
    max_up_move = 0.0
    max_down_move = 0.0
    
    # 跟踪价格路径
    current_high = start_price
    current_low = start_price
    
    for j in range(1, max_lookforward + 1):
        idx = start_idx + j
        if idx >= len(high_prices):
            break
        
        period_high = high_prices[idx]
        period_low = low_prices[idx]
        period_close = close_prices[idx]
        
        # 更新极值
        if period_high > current_high:
            # 检查上涨路径的连续性
            up_move = (period_high - start_price) / start_price
            
            # 检查从新高点的最大回撤
            max_drawdown = 0.0
            for k in range(j, min(j + 5, max_lookforward + 1)):
                check_idx = start_idx + k
                if check_idx >= len(low_prices):
                    break
                check_low = min(low_prices[start_idx + k:check_idx + 1])
                drawdown = (period_high - check_low) / period_high
                max_drawdown = max(max_drawdown, drawdown)
            
            if max_drawdown <= max_retracement:
                max_up_move = max(max_up_move, up_move)
                current_high = period_high
        
        if period_low < current_low:
            # 检查下跌路径的连续性
            down_move = (start_price - period_low) / start_price
            
            # 检查从新低点的最大反弹
            max_bounce = 0.0
            for k in range(j, min(j + 5, max_lookforward + 1)):
                check_idx = start_idx + k
                if check_idx >= len(high_prices):
                    break
                check_high = max(high_prices[start_idx + k:check_idx + 1])
                bounce = (check_high - period_low) / period_low
                max_bounce = max(max_bounce, bounce)
            
            if max_bounce <= max_retracement:
                max_down_move = max(max_down_move, down_move)
                current_low = period_low
    
    return max_up_move, max_down_move

def get_enhanced_feature_list(df):
    """获取增强的特征列表"""
    exclude_columns = [
        'open', 'high', 'low', 'close', 'volume', 'timestamp', 'Timestamp',
        'continuous_movement', 'label', 'close_time', 'quote_volume', 
        'trade_count', 'taker_buy_base_volume', 'taker_buy_quote_volume', 'created_at',
        # 排除未来信息
        'future_return_1', 'future_return_2', 'future_return_3',
        # 排除中间计算列
        'price_volume', 'bb_upper_20', 'bb_lower_20', 'bb_upper_50', 'bb_lower_50'
    ]
    
    feature_columns = [col for col in df.columns if col not in exclude_columns and not pd.isna(df[col]).all()]
    print(f"增强特征数量: {len(feature_columns)}")
    return feature_columns

def prepare_enhanced_features_and_labels(df, symbol='ETHUSDT', market='spot'):
    """准备增强的特征和标签"""
    print("计算增强特征...")
    df_with_features = calculate_enhanced_features(df.copy(), timeframe=TIMEFRAME_MINUTES)
    
    print("创建改进的连续移动标签...")
    continuous_labels = calculate_improved_continuous_movement(
        df, MAX_RETRACEMENT, MAX_LOOKFORWARD_MINUTES, TIMEFRAME_MINUTES
    )
    
    print("合并特征与标签...")
    df_combined = df_with_features.join(continuous_labels.rename('continuous_movement'), how='inner')
    df_clean = df_combined.dropna()
    print(f"清理NaN后剩余 {len(df_clean)} 条记录")
    return df_clean

def enhanced_time_series_cv(df_clean, features, target='continuous_movement', n_splits=3, n_trials=50):
    """增强的时序交叉验证"""
    print(f"\n=== 增强时序交叉验证 (n_splits={n_splits}, n_trials={n_trials}) ===")
    tscv = TimeSeriesSplit(n_splits=n_splits)
    X = df_clean[features]
    y = df_clean[target]

    def objective(trial):
        params = {
            'objective': 'regression',
            'metric': 'rmse',
            'random_state': 42,
            'verbose': -1,
            'n_jobs': -1,
            'boosting_type': 'gbdt',
            'n_estimators': trial.suggest_int('n_estimators', 500, 2000),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.2, log=True),
            'max_depth': trial.suggest_int('max_depth', 3, 10),
            'num_leaves': trial.suggest_int('num_leaves', 10, 200),
            'min_child_samples': trial.suggest_int('min_child_samples', 10, 100),
            'subsample': trial.suggest_float('subsample', 0.6, 1.0),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
            'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 10.0),
            'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 10.0),
            'min_split_gain': trial.suggest_float('min_split_gain', 0.0, 1.0),
        }
        
        fold_scores = []
        for train_idx, val_idx in tscv.split(X):
            X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
            y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]
            
            try:
                lgbm = lgb.LGBMRegressor(**params)
                lgbm.fit(X_train_fold, y_train_fold, 
                        eval_set=[(X_val_fold, y_val_fold)], 
                        eval_metric='rmse',
                        callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False)])
                
                y_pred = lgbm.predict(X_val_fold)
                rmse = np.sqrt(mean_squared_error(y_val_fold, y_pred))
                fold_scores.append(rmse)
            except Exception as e:
                continue
        
        return np.mean(fold_scores) if fold_scores else float('inf')

    optuna.logging.set_verbosity(optuna.logging.WARNING)
    study = optuna.create_study(direction='minimize')
    study.optimize(objective, n_trials=n_trials)

    print(f"=== 优化完成 ===")
    if study.best_value == float('inf'):
        print("⚠️ 未找到有效参数，使用默认参数")
        return {}, float('inf')
    else:
        print(f"最佳RMSE: {study.best_value:.6f}")
        print(f"最佳参数: {study.best_params}")
    
    return study.best_params, study.best_value

def train_improved_model(args):
    """改进的模型训练函数"""
    print(f"开始训练改进的连续移动预测模型 - {MODEL_BASENAME}")
    
    # 加载数据
    df = load_data_for_training(
        args.coin, args.db_path, args.symbol, args.interval, args.market, 
        start_time=args.start_time, end_time=args.end_time
    )
    if df is None:
        print("❌ 数据加载失败，退出训练。")
        return
    
    # 限制数据量以提高训练速度
    if len(df) > args.max_samples:
        print(f"数据量过大，使用最新的 {args.max_samples} 条记录")
        df = df.tail(args.max_samples)
    
    df_clean = prepare_enhanced_features_and_labels(df, args.symbol, args.market)
    
    # 数据分割
    train_size = int(len(df_clean) * 0.70)
    val_size = int(len(df_clean) * 0.15)
    train_df = df_clean.iloc[:train_size]
    val_df = df_clean.iloc[train_size:train_size + val_size]
    test_df = df_clean.iloc[train_size + val_size:]
    
    print(f"训练集: {len(train_df)}, 验证集: {len(val_df)}, 测试集: {len(test_df)}")

    target = 'continuous_movement'
    X_train, y_train = train_df.drop(columns=[target]), train_df[target]
    X_val, y_val = val_df.drop(columns=[target]), val_df[target]
    X_test, y_test = test_df.drop(columns=[target]), test_df[target]

    # 获取特征
    features = get_enhanced_feature_list(df_clean)
    print(f"使用 {len(features)} 个增强特征")

    # 特征缩放
    scaler = RobustScaler()
    X_train_scaled = pd.DataFrame(
        scaler.fit_transform(X_train[features]), 
        columns=features, 
        index=X_train.index
    )
    X_val_scaled = pd.DataFrame(
        scaler.transform(X_val[features]), 
        columns=features, 
        index=X_val.index
    )
    X_test_scaled = pd.DataFrame(
        scaler.transform(X_test[features]), 
        columns=features, 
        index=X_test.index
    )

    # 超参数优化
    best_params = {}
    if not args.no_optuna:
        train_val_df = pd.concat([train_df, val_df])
        train_val_scaled = pd.concat([X_train_scaled, X_val_scaled])
        train_val_scaled['continuous_movement'] = train_val_df['continuous_movement']
        
        best_params, _ = enhanced_time_series_cv(
            train_val_scaled, features, target, 
            n_splits=args.cv_splits, n_trials=args.cv_trials
        )
    
    # 最终参数
    final_params = {
        'objective': 'regression',
        'metric': 'rmse',
        'n_jobs': -1,
        'random_state': 42,
        'verbose': -1,
        'n_estimators': 1500,
        'learning_rate': 0.05,
        'max_depth': 6,
        'num_leaves': 50,
        'min_child_samples': 20,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'reg_alpha': 1.0,
        'reg_lambda': 1.0,
    }
    final_params.update(best_params)

    print(f"\n训练最终模型...")
    lgbm = lgb.LGBMRegressor(**final_params)
    lgbm.fit(X_train_scaled, y_train, 
             eval_set=[(X_val_scaled, y_val)], 
             eval_metric='rmse',
             callbacks=[lgb.early_stopping(stopping_rounds=100, verbose=False)])

    # 评估
    val_pred = lgbm.predict(X_val_scaled)
    test_pred = lgbm.predict(X_test_scaled)
    
    val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))
    val_r2 = r2_score(y_val, val_pred)
    test_rmse = np.sqrt(mean_squared_error(y_test, test_pred))
    test_r2 = r2_score(y_test, test_pred)
    
    # 方向准确率
    val_direction_acc = ((val_pred > 0) == (y_val > 0)).mean() * 100
    test_direction_acc = ((test_pred > 0) == (y_test > 0)).mean() * 100
    
    print(f"\n=== 模型性能 ===")
    print(f"验证集 - RMSE: {val_rmse:.6f}, R²: {val_r2:.4f}, 方向准确率: {val_direction_acc:.2f}%")
    print(f"测试集 - RMSE: {test_rmse:.6f}, R²: {test_r2:.4f}, 方向准确率: {test_direction_acc:.2f}%")

    # 保存模型
    output_dir = get_continuous_output_dir()
    model_file = os.path.join(output_dir, f'improved_{MODEL_BASENAME}_model.joblib')
    scaler_file = os.path.join(output_dir, f'improved_{MODEL_BASENAME}_scaler.joblib')
    
    joblib.dump(lgbm, model_file)
    joblib.dump(scaler, scaler_file)
    
    # 保存配置
    config = {
        'model_type': f'LGBM_Improved_{MODEL_BASENAME}',
        'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'features': features,
        'val_rmse': float(val_rmse),
        'val_r2': float(val_r2),
        'test_rmse': float(test_rmse),
        'test_r2': float(test_r2),
        'val_direction_acc': float(val_direction_acc),
        'test_direction_acc': float(test_direction_acc),
        'params': final_params,
        'max_retracement': MAX_RETRACEMENT,
        'timeframe_minutes': TIMEFRAME_MINUTES
    }
    
    config_file = os.path.join(output_dir, f'improved_{MODEL_BASENAME}_config.json')
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"\n✅ 改进模型保存完成")
    print(f"模型: {model_file}")
    print(f"缩放器: {scaler_file}")
    print(f"配置: {config_file}")

def main():
    parser = argparse.ArgumentParser(description="改进版连续移动预测模型训练器")
    parser.add_argument("--coin", default="ETH", help="币种名称")
    parser.add_argument("--max-retracement", type=float, default=0.01, help="最大回撤")
    parser.add_argument("--max-samples", type=int, default=100000, help="最大样本数")
    parser.add_argument("--no-optuna", action='store_true', help="禁用Optuna优化")
    parser.add_argument("--cv-splits", type=int, default=3, help="CV分割数")
    parser.add_argument("--cv-trials", type=int, default=30, help="优化试验次数")
    parser.add_argument("--db-path", default="../coin_data.db", help="数据库路径")
    parser.add_argument("--symbol", help="交易对符号")
    parser.add_argument("--interval", help="时间间隔")
    parser.add_argument("--market", default='spot', help="市场类型")
    parser.add_argument("--start-time", help="开始时间")
    parser.add_argument("--end-time", help="结束时间")

    args = parser.parse_args()

    # 获取配置
    coin_config = get_continuous_config(f"{args.coin}_CONTINUOUS_5M")
    if coin_config is None:
        coin_config = get_continuous_config(args.coin)
        if coin_config is None:
            print(f"❌ 未找到币种 {args.coin} 的配置")
            exit(1)

    global TIMEFRAME_MINUTES, MAX_RETRACEMENT, MAX_LOOKFORWARD_MINUTES, MODEL_BASENAME
    TIMEFRAME_MINUTES = coin_config['timeframe_minutes']
    MAX_RETRACEMENT = args.max_retracement
    MAX_LOOKFORWARD_MINUTES = coin_config['max_lookforward_minutes']
    MODEL_BASENAME = coin_config['model_basename']

    print_continuous_training_summary(coin_config, MAX_RETRACEMENT)
    print(f"算法版本: 改进版 (增强特征 + 优化算法)")
    
    train_improved_model(args)

if __name__ == '__main__':
    main()