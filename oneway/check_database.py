#!/usr/bin/env python3
# check_database.py
# 检查SQLite数据库中的表和数据

import sqlite3
import os
import sys
from datetime import datetime

def check_database(db_path):
    """检查数据库内容"""
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        if not tables:
            print(f"📊 数据库 {db_path} 中没有表")
            return False
        
        print(f"📊 数据库: {db_path}")
        print("=" * 60)
        
        for (table_name,) in tables:
            print(f"\n📋 表: {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"列: {[col[1] for col in columns]}")
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"记录数: {count:,}")
            
            if count > 0:
                # 获取时间范围 (检查是否有timestamp列)
                column_names = [col[1] for col in columns]
                if 'timestamp' in column_names:
                    cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table_name}")
                    min_ts, max_ts = cursor.fetchone()
                else:
                    min_ts, max_ts = None, None
                
                if min_ts and max_ts:
                    min_time = datetime.fromtimestamp(min_ts).strftime('%Y-%m-%d %H:%M:%S')
                    max_time = datetime.fromtimestamp(max_ts).strftime('%Y-%m-%d %H:%M:%S')
                    print(f"时间范围: {min_time} 到 {max_time}")
                
                # 显示前几条记录 (只对有timestamp的表)
                if 'timestamp' in column_names:
                    cursor.execute(f"SELECT * FROM {table_name} ORDER BY timestamp LIMIT 3")
                    sample_data = cursor.fetchall()
                    print("样本数据:")
                    for i, row in enumerate(sample_data, 1):
                        timestamp = datetime.fromtimestamp(row[0]).strftime('%Y-%m-%d %H:%M:%S')
                        print(f"  {i}. {timestamp} | O:{row[1]:.2f} H:{row[2]:.2f} L:{row[3]:.2f} C:{row[4]:.2f} V:{row[5]:.0f}")
                else:
                    print("(非K线数据表)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="检查SQLite数据库内容")
    parser.add_argument("--db", default="../coin_data.db", help="数据库文件路径")
    
    args = parser.parse_args()
    
    success = check_database(args.db)
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()