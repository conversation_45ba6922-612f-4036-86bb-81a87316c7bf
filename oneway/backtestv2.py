#!/usr/bin/env python3
# backtest_standalone.py
# 使用训练好的模型进行回测

import pandas as pd
import numpy as np
import joblib
import json
import argparse
import os
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.font_manager import FontProperties

# 导入独立工具函数
from standalone_utils import (
    get_continuous_output_dir, calculate_features, load_data_for_training
)

def load_model_and_config(model_basename):
    """加载训练好的模型和配置"""
    output_dir = get_continuous_output_dir()
    model_file = os.path.join(output_dir, f'standalone_{model_basename}_model.joblib')
    config_file = os.path.join(output_dir, f'standalone_{model_basename}_config.json')
    
    if not os.path.exists(model_file):
        print(f"❌ 模型文件不存在: {model_file}")
        return None, None
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return None, None
    
    # 加载模型
    model = joblib.load(model_file)
    
    # 加载配置
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    print(f"✅ 成功加载模型: {model_file}")
    print(f"✅ 成功加载配置: {config_file}")
    print(f"模型类型: {config.get('model_type', 'Unknown')}")
    print(f"训练日期: {config.get('training_date', 'Unknown')}")
    print(f"特征数量: {len(config.get('feature_list', []))}")
    
    return model, config

def prepare_backtest_data(df, config):
    """准备回测数据"""
    timeframe_minutes = config['timeframe_minutes']
    
    print("计算特征...")
    df_with_features = calculate_features(df.copy(), timeframe=timeframe_minutes)
    
    # 获取模型需要的特征
    required_features = config['feature_list']
    
    # 检查特征是否存在
    missing_features = [f for f in required_features if f not in df_with_features.columns]
    if missing_features:
        print(f"⚠️ 警告: 缺少以下特征: {missing_features}")
        # 移除缺少的特征
        available_features = [f for f in required_features if f in df_with_features.columns]
        print(f"使用可用特征: {len(available_features)}/{len(required_features)}")
    else:
        available_features = required_features
    
    # 准备特征数据
    X = df_with_features[available_features].dropna()
    
    print(f"回测数据准备完成: {len(X)} 条记录")
    return X, available_features, df_with_features

def check_trading_conditions_hardcoded(proba_lowest, proba_neutral, proba_highest, original_predicted_class, prev_proba_highest):
    """
    修改后的交易条件检查:
    先只做多，做多逻辑是，前一条的ProbaHighest大于0.4，当前的ProbaLowest 大于0.4。则做多。
    其他情况（包括原始预测做空）均不交易 (Hold)。
    """
    
    # 只有当 prev_proba_highest 存在且满足条件，并且当前的 proba_lowest 满足条件时，才做多
    if (prev_proba_highest is not None and
        prev_proba_highest > 0.4 and
        proba_lowest > 0.4):
        return 0  # 满足做多条件，返回最低点 (Long)
    
    # 其他所有情况，包括原始预测为中性、最高点，或不满足做多条件，都设置为持有 (Hold)
    return 1 # 中性点 (Hold)

def check_trading_conditions(proba_lowest, proba_neutral, proba_highest, original_predicted_class, config, prev_proba_highest):
    """检查是否满足交易条件 - 使用修改后的硬编码版本"""
    # config (trading_thresholds) is passed for compatibility but not used by this function directly.
    return check_trading_conditions_hardcoded(proba_lowest, proba_neutral, proba_highest, original_predicted_class, prev_proba_highest)

def run_backtest(model, X, df_with_features, config, lookforward_minutes):
    """运行回测"""
    print(f"\n--- 开始回测 ---")
    
    print("✅ 使用新的做多逻辑: 上一条ProbaHighest > 0.4 且 当前条ProbaLowest > 0.4")
    print("❌ 仅考虑做多交易，其他情况均不交易 (Hold)")
    
    # 进行预测
    predictions = model.predict(X)
    probabilities = model.predict_proba(X)
    
    # 准备结果
    results_log = []
    label_names = {0: '最低点', 1: '中性点', 2: '最高点'}
    timeframe_minutes = config['timeframe_minutes']
    lookforward_candles = lookforward_minutes // timeframe_minutes
    
    print(f"计算交易收益（前瞻{lookforward_minutes}分钟）...")
    
    filtered_signals_count = 0  # 统计被过滤的原始交易信号数量
    prev_proba_highest = None # 初始化上一条的ProbaHighest
    
    for i in range(len(predictions)):
        current_idx = X.index[i]
        
        # 获取当前价格信息
        # 确保 current_idx 在 df_with_features 中
        if current_idx not in df_with_features.index:
            # 如果特征计算导致索引丢失，这里会跳过
            print(f"⚠️ 警告: 索引 {current_idx} 在特征数据中未找到。跳过。")
            prev_proba_highest = None # 如果出现中断，重置前值
            continue
            
        current_row = df_with_features.loc[current_idx]
        current_price = current_row['close']
        original_predicted_class = predictions[i]
        
        # 获取概率
        proba_lowest = probabilities[i][0]
        proba_neutral = probabilities[i][1]
        proba_highest = probabilities[i][2]
        
        # 应用新的交易条件过滤
        filtered_predicted_class = check_trading_conditions(
            proba_lowest, proba_neutral, proba_highest, original_predicted_class, config, prev_proba_highest
        )
        
        # 统计被过滤的信号
        # 如果原始预测是交易信号 (0或2)，但新逻辑使其变为持有 (1)
        if original_predicted_class != 1 and filtered_predicted_class == 1:
            filtered_signals_count += 1
        
        # 计算未来价格
        future_price = None
        price_change_pct = 0.0
        trading_return = 0.0
        trading_action = 'Hold' # 默认设置为 Hold
        
        # 找到未来的价格点
        future_idx = None
        current_pos = df_with_features.index.get_loc(current_idx)
        future_pos = current_pos + lookforward_candles
        
        if future_pos < len(df_with_features):
            future_idx = df_with_features.index[future_pos]
            future_price = df_with_features.loc[future_idx, 'close']
            price_change_pct = (future_price - current_price) / current_price
            
            # 根据过滤后的预测执行交易策略 (只做多)
            if filtered_predicted_class == 0:  # 预测最低点，做多
                trading_return = price_change_pct
                trading_action = 'Long'
            # 否则 (filtered_predicted_class == 1)，保持默认的 'Hold' 和 0.0 收益
        
        results_log.append({
            'Timestamp': current_idx,
            'ClosePrice': current_price,
            'FuturePrice': future_price,
            'PriceChangePct': price_change_pct * 100,
            'OriginalPredictedClass': original_predicted_class,
            'FilteredPredictedClass': filtered_predicted_class,
            'ProbaLowest': proba_lowest,
            'ProbaNeutral': proba_neutral,
            'ProbaHighest': proba_highest,
            'OriginalPredictedLabel': label_names[original_predicted_class],
            'FilteredPredictedLabel': label_names[filtered_predicted_class],
            'TradingAction': trading_action,
            'TradingReturnPct': trading_return * 100,
            'WasFiltered': original_predicted_class != filtered_predicted_class
        })
        
        # 更新 prev_proba_highest 为当前条的 ProbaHighest，供下一条使用
        prev_proba_highest = proba_highest
    
    print(f"概率过滤统计: {filtered_signals_count} 个原始交易信号被过滤为持有。")
    
    return pd.DataFrame(results_log)

def calculate_backtest_statistics(results_df):
    """计算回测统计信息"""
    # 过滤出实际交易的记录
    trades_df = results_df[results_df['TradingAction'] != 'Hold'].copy()
    
    # 过滤统计
    total_signals = len(results_df[results_df['OriginalPredictedClass'] != 1])  # 原始非中性信号
    filtered_signals = results_df['WasFiltered'].sum()  # 被过滤的信号 (原始预测与过滤后不同)
    filter_rate = filtered_signals / total_signals if total_signals > 0 else 0
    
    if len(trades_df) == 0:
        return {
            'total_trades': 0,
            'total_signals': total_signals,
            'filtered_signals': filtered_signals,
            'filter_rate': filter_rate
        }
    
    # 基本统计
    total_trades = len(trades_df)
    long_trades = len(trades_df[trades_df['TradingAction'] == 'Long'])
    short_trades = len(trades_df[trades_df['TradingAction'] == 'Short'])
    
    # 收益统计
    total_return = trades_df['TradingReturnPct'].sum()
    avg_return = trades_df['TradingReturnPct'].mean()
    win_trades = len(trades_df[trades_df['TradingReturnPct'] > 0])
    lose_trades = len(trades_df[trades_df['TradingReturnPct'] < 0])
    win_rate = win_trades / total_trades if total_trades > 0 else 0
    
    # 风险指标
    returns = trades_df['TradingReturnPct'].values
    max_win = returns.max()
    max_loss = returns.min()
    std_return = returns.std()
    sharpe_ratio = avg_return / std_return if std_return > 0 else 0
    
    # 最大回撤
    cumulative_returns = (1 + trades_df['TradingReturnPct'] / 100).cumprod()
    running_max = cumulative_returns.expanding().max()
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = drawdown.min() * 100
    
    # 按交易类型分组统计
    long_stats = trades_df[trades_df['TradingAction'] == 'Long']['TradingReturnPct']
    short_stats = trades_df[trades_df['TradingAction'] == 'Short']['TradingReturnPct'] # 理论上这里 short_stats 会是空的
    
    return {
        'total_trades': total_trades,
        'long_trades': long_trades,
        'short_trades': short_trades,
        'total_return_pct': total_return,
        'avg_return_pct': avg_return,
        'win_trades': win_trades,
        'lose_trades': lose_trades,
        'win_rate': win_rate,
        'max_win_pct': max_win,
        'max_loss_pct': max_loss,
        'std_return': std_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown_pct': max_drawdown,
        'long_avg_return': long_stats.mean() if len(long_stats) > 0 else 0,
        'short_avg_return': short_stats.mean() if len(short_stats) > 0 else 0,
        'long_win_rate': len(long_stats[long_stats > 0]) / len(long_stats) if len(long_stats) > 0 else 0,
        'short_win_rate': len(short_stats[short_stats > 0]) / len(short_stats) if len(short_stats) > 0 else 0,
        'total_periods': len(results_df),
        'trading_frequency': total_trades / len(results_df) if len(results_df) > 0 else 0,
        'total_signals': total_signals,
        'filtered_signals': filtered_signals,
        'filter_rate': filter_rate
    }

def plot_returns_chart(results_df, model_name, timestamp, output_dir):
    """绘制收益图表"""
    try:
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 过滤出有交易的记录
        trades_df = results_df[results_df['TradingAction'] != 'Hold'].copy()
        
        if len(trades_df) == 0:
            print("⚠️ 没有交易记录，跳过绘图")
            return
        
        # 计算累计收益
        trades_df = trades_df.sort_values('Timestamp')
        trades_df['CumulativeReturn'] = (1 + trades_df['TradingReturnPct'] / 100).cumprod() - 1
        trades_df['CumulativeReturnPct'] = trades_df['CumulativeReturn'] * 100
        
        # 创建图表
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
        
        # 图1: 累计收益曲线
        ax1.plot(trades_df['Timestamp'], trades_df['CumulativeReturnPct'], 
                linewidth=2, color='blue', label='累计收益')
        ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        ax1.set_title(f'{model_name} 回测累计收益曲线', fontsize=14, fontweight='bold')
        ax1.set_ylabel('累计收益 (%)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 格式化x轴日期
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax1.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(trades_df)//10)))
        
        # 图2: 单次交易收益分布
        ax2.hist(trades_df['TradingReturnPct'], bins=50, alpha=0.7, color='green', edgecolor='black')
        ax2.axvline(x=0, color='red', linestyle='--', alpha=0.7)
        ax2.set_title('单次交易收益分布', fontsize=14, fontweight='bold')
        ax2.set_xlabel('单次收益 (%)', fontsize=12)
        ax2.set_ylabel('频次', fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        # 图3: 交易动作时间分布
        long_trades = trades_df[trades_df['TradingAction'] == 'Long']
        short_trades = trades_df[trades_df['TradingAction'] == 'Short']
        
        if len(long_trades) > 0:
            ax3.scatter(long_trades['Timestamp'], long_trades['TradingReturnPct'], 
                       c='green', marker='^', s=50, alpha=0.7, label=f'做多 ({len(long_trades)}次)')
        
        if len(short_trades) > 0:
            # 理论上在此策略下不会有做空交易，但为了通用性保留
            ax3.scatter(short_trades['Timestamp'], short_trades['TradingReturnPct'], 
                       c='red', marker='v', s=50, alpha=0.7, label=f'做空 ({len(short_trades)}次)')
        
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax3.set_title('交易时间点与收益', fontsize=14, fontweight='bold')
        ax3.set_xlabel('时间', fontsize=12)
        ax3.set_ylabel('单次收益 (%)', fontsize=12)
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        # 格式化x轴日期
        ax3.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax3.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(trades_df)//10)))
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        chart_filename = os.path.join(output_dir, f'backtest_chart_{model_name}_{timestamp}.png')
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        print(f"📊 收益图表已保存到: {chart_filename}")
        
        # 显示图表（如果在交互环境中）
        try:
            plt.show()
        except:
            pass
        
        plt.close()
        
    except Exception as e:
        print(f"⚠️ 绘图失败: {e}")

def print_backtest_statistics(stats, start_time, end_time):
    """打印回测统计信息"""
    print(f"\n{'='*60}")
    print(f"回测结果统计 ({start_time} 到 {end_time})")
    print(f"{'='*60}")
    
    if stats['total_trades'] == 0:
        print("❌ 没有执行任何交易")
        return
    
    print(f"📊 基本统计:")
    print(f"  回测周期: {stats['total_periods']} 个时间点")
    print(f"  原始交易信号 (非中性): {stats.get('total_signals', 0)} 个")
    print(f"  过滤信号数 (原始信号被修正): {stats.get('filtered_signals', 0)} 个")
    print(f"  过滤率: {stats.get('filter_rate', 0)*100:.1f}%")
    print(f"  实际交易次数: {stats['total_trades']}")
    print(f"  交易频率: {stats['trading_frequency']*100:.1f}%")
    print(f"  做多次数: {stats['long_trades']}")
    print(f"  做空次数: {stats['short_trades']} (在此策略下应为0)")
    
    print(f"\n💰 收益统计:")
    print(f"  总收益: {stats['total_return_pct']:.2f}%")
    print(f"  平均收益: {stats['avg_return_pct']:.3f}%")
    print(f"  胜率: {stats['win_rate']*100:.1f}% ({stats['win_trades']}/{stats['total_trades']})")
    print(f"  最大单次收益: {stats['max_win_pct']:.2f}%")
    print(f"  最大单次损失: {stats['max_loss_pct']:.2f}%")
    
    print(f"\n📈 风险指标:")
    print(f"  收益标准差: {stats['std_return']:.3f}%")
    print(f"  夏普比率: {stats['sharpe_ratio']:.3f}")
    print(f"  最大回撤: {stats['max_drawdown_pct']:.2f}%")
    
    print(f"\n🔄 按交易类型分析:")
    print(f"  做多平均收益: {stats['long_avg_return']:.3f}%")
    print(f"  做空平均收益: {stats['short_avg_return']:.3f}% (在此策略下应为0)")
    print(f"  做多胜率: {stats['long_win_rate']*100:.1f}%")
    print(f"  做空胜率: {stats['short_win_rate']*100:.1f}% (在此策略下应为0)")

def main():
    parser = argparse.ArgumentParser(
        description="使用训练好的模型进行回测",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python backtest_standalone.py --model eth_5m --start-time 2024-01-01 --end-time 2024-02-01
  python backtest_standalone.py --model eth_5m --coin ETH --days 30
        """
    )
    
    parser.add_argument("--model", required=True, help="模型基名（如: eth_5m）")
    parser.add_argument("--coin", default="ETH", help="币种名称")
    parser.add_argument("--start-time", help="回测开始时间 (YYYY-MM-DD)")
    parser.add_argument("--end-time", help="回测结束时间 (YYYY-MM-DD)")
    parser.add_argument("--days", type=int, help="回测天数（从最新数据往前）")
    parser.add_argument("--db-path", default='../coin_data.db', help="数据库路径")
    parser.add_argument("--symbol", help="交易对符号")
    parser.add_argument("--interval", help="时间间隔")
    parser.add_argument("--market", choices=['spot', 'futures'], default='spot', help="市场类型")
    parser.add_argument("--lookforward-hours", type=float, default=4.0, help="前瞻时间（小时）")
    
    args = parser.parse_args()
    
    # 加载模型和配置
    model, config = load_model_and_config(args.model)
    if model is None or config is None:
        return
    
    # 确定时间范围
    if args.days:
        end_time = datetime.now().strftime('%Y-%m-%d')
        start_time = (datetime.now() - timedelta(days=args.days)).strftime('%Y-%m-%d')
    else:
        start_time = args.start_time
        end_time = args.end_time
    
    if not start_time or not end_time:
        print("❌ 请指定时间范围（--start-time 和 --end-time，或者 --days）")
        return
    
    print(f"回测时间范围: {start_time} 到 {end_time}")
    
    # 加载回测数据
    df = load_data_for_training(
        args.coin, args.db_path, args.symbol, args.interval, args.market,
        start_time=start_time, end_time=end_time
    )
    
    if df is None or len(df) == 0:
        print("❌ 无法加载回测数据")
        return
    
    print(f"加载回测数据: {len(df)} 条记录")
    
    # 准备回测数据
    X, features, df_with_features = prepare_backtest_data(df, config)
    
    if len(X) == 0:
        print("❌ 没有可用的回测数据")
        return
    
    # 运行回测
    lookforward_minutes = int(args.lookforward_hours * 60)
    results_df = run_backtest(model, X, df_with_features, config, lookforward_minutes)
    
    # 计算统计信息
    stats = calculate_backtest_statistics(results_df)
    print_backtest_statistics(stats, start_time, end_time)
    
    # 保存回测结果
    output_dir = get_continuous_output_dir()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_filename = os.path.join(output_dir, f'backtest_{args.model}_{timestamp}.csv')
    results_df.to_csv(results_filename, index=False, float_format='%.6f')
    
    print(f"\n✅ 回测结果已保存到: {results_filename}")
    
    # 绘制收益图表
    plot_returns_chart(results_df, args.model, timestamp, output_dir)

if __name__ == '__main__':
    main()