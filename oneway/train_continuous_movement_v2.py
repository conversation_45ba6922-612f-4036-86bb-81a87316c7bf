# train_continuous_movement_v2.py
# 改进版连续移动预测模型 - 使用所有数据进行回归训练

import pandas as pd
import numpy as np
from datetime import datetime
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
import json
import argparse
import os
import pickle
import optuna
import sys
sys.path.append('..')

# 导入父目录的工具函数
from model_utils_815 import (
    calculate_features, get_feature_list
)
from data_loader import load_data_for_training
from continuous_utils import (
    get_continuous_config, get_continuous_output_dir, 
    calculate_expected_signals, print_continuous_training_summary, 
    calculate_movement_statistics, print_movement_statistics
)

# 全局变量
TIMEFRAME_MINUTES = None
MAX_RETRACEMENT = None
MAX_LOOKFORWARD_MINUTES = None
MODEL_BASENAME = None

def calculate_max_continuous_movement(df, max_retracement=0.01, max_lookforward_minutes=240, timeframe=5):
    """
    计算每个时间点的最大连续移动百分比（改进版）
    
    参数:
    - max_retracement: 最大回撤百分比 (如 0.01 表示 1%)
    - max_lookforward_minutes: 最大前瞻时间
    - timeframe: K线时间间隔
    
    返回:
    - 连续移动百分比 (正数表示上涨，负数表示下跌)
    
    算法说明:
    1. 对每个时间点，向前查看指定时间窗口
    2. 计算在该窗口内能够达到的最大连续移动
    3. 连续性定义：从起始点开始，价格朝一个方向移动，中途回撤不超过阈值
    4. 返回上涨和下跌方向中绝对值更大的移动
    """
    max_lookforward_candles = max_lookforward_minutes // timeframe
    print(f"计算最大连续移动：最大回撤{max_retracement*100:.1f}%，最大等待{max_lookforward_candles}根K线")
    
    continuous_movements = np.full(len(df), np.nan)
    
    # 使用NumPy进行向量化操作
    close_prices = df['close'].to_numpy()
    high_prices = df['high'].to_numpy()
    low_prices = df['low'].to_numpy()
    
    for i in range(len(df) - max_lookforward_candles):
        if i % 10000 == 0:
            print(f"处理进度: {i}/{len(df)} ({i/len(df)*100:.1f}%)")
        
        start_price = close_prices[i]
        
        # 计算上涨方向的最大连续移动
        max_up_movement = calculate_directional_movement(
            i, start_price, high_prices, low_prices, max_lookforward_candles, 
            max_retracement, direction='up'
        )
        
        # 计算下跌方向的最大连续移动
        max_down_movement = calculate_directional_movement(
            i, start_price, high_prices, low_prices, max_lookforward_candles, 
            max_retracement, direction='down'
        )
        
        # 选择绝对值更大的移动
        if abs(max_up_movement) >= abs(max_down_movement):
            continuous_movements[i] = max_up_movement
        else:
            continuous_movements[i] = -max_down_movement
    
    # 创建有效的标签序列
    valid_mask = ~np.isnan(continuous_movements)
    valid_indices = df.index[valid_mask]
    valid_movements = continuous_movements[valid_mask]
    
    print(f"有效标签数量: {len(valid_movements)}/{len(df)} ({len(valid_movements)/len(df)*100:.1f}%)")
    
    if len(valid_movements) > 0:
        stats = calculate_movement_statistics(valid_movements)
        print_movement_statistics(stats)
    else:
        print("未生成任何有效标签。")
    
    return pd.Series(index=valid_indices, data=valid_movements)

def calculate_directional_movement(start_idx, start_price, high_prices, low_prices, 
                                 max_lookforward, max_retracement, direction='up'):
    """
    计算指定方向的最大连续移动
    
    参数:
    - start_idx: 起始索引
    - start_price: 起始价格
    - high_prices, low_prices: 价格数组
    - max_lookforward: 最大前瞻K线数
    - max_retracement: 最大回撤比例
    - direction: 'up' 或 'down'
    
    返回:
    - 该方向的最大连续移动百分比
    """
    max_movement = 0.0
    current_extreme = start_price  # 当前极值点
    
    for j in range(1, max_lookforward + 1):
        current_idx = start_idx + j
        if current_idx >= len(high_prices):
            break
        
        current_high = high_prices[current_idx]
        current_low = low_prices[current_idx]
        
        if direction == 'up':
            # 检查是否创新高
            if current_high > current_extreme:
                # 计算从起始点的涨幅
                movement = (current_high - start_price) / start_price
                
                # 检查从新高点开始是否有过大回撤
                max_retracement_from_high = 0.0
                for k in range(j, min(j + 10, max_lookforward + 1)):  # 检查后续几根K线
                    check_idx = start_idx + k
                    if check_idx >= len(low_prices):
                        break
                    check_low = low_prices[check_idx]
                    retracement = (current_high - check_low) / current_high
                    max_retracement_from_high = max(max_retracement_from_high, retracement)
                
                # 如果回撤在允许范围内，更新最大移动和极值点
                if max_retracement_from_high <= max_retracement:
                    max_movement = max(max_movement, movement)
                    current_extreme = current_high
                else:
                    # 回撤过大，停止在这个方向的搜索
                    break
        
        else:  # direction == 'down'
            # 检查是否创新低
            if current_low < current_extreme:
                # 计算从起始点的跌幅
                movement = (start_price - current_low) / start_price
                
                # 检查从新低点开始是否有过大反弹
                max_bounce_from_low = 0.0
                for k in range(j, min(j + 10, max_lookforward + 1)):  # 检查后续几根K线
                    check_idx = start_idx + k
                    if check_idx >= len(high_prices):
                        break
                    check_high = high_prices[check_idx]
                    bounce = (check_high - current_low) / current_low
                    max_bounce_from_low = max(max_bounce_from_low, bounce)
                
                # 如果反弹在允许范围内，更新最大移动和极值点
                if max_bounce_from_low <= max_retracement:
                    max_movement = max(max_movement, movement)
                    current_extreme = current_low
                else:
                    # 反弹过大，停止在这个方向的搜索
                    break
    
    return max_movement

def prepare_features_and_labels(df, symbol='ETHUSDT', market='spot'):
    """准备特征和标签"""
    print("在完整数据集上计算增强特征...")
    df_with_features = calculate_features(df.copy(), timeframe=TIMEFRAME_MINUTES)
    
    print("创建连续移动标签...")
    continuous_labels = calculate_max_continuous_movement(
        df, MAX_RETRACEMENT, MAX_LOOKFORWARD_MINUTES, TIMEFRAME_MINUTES
    )
    
    print("合并特征与标签...")
    df_combined = df_with_features.join(continuous_labels.rename('continuous_movement'), how='inner')
    df_clean = df_combined.dropna()
    print(f"清理NaN后剩余 {len(df_clean)} 条记录")
    return df_clean

def split_data(df_clean):
    """按时间顺序分割数据"""
    train_size = int(len(df_clean) * 0.70)
    val_size = int(len(df_clean) * 0.15)
    train_df = df_clean.iloc[:train_size]
    val_df = df_clean.iloc[train_size:train_size + val_size]
    test_df = df_clean.iloc[train_size + val_size:]
    
    print(f"训练集: {len(train_df)} ({train_df.index.min()} to {train_df.index.max()})")
    print(f"验证集: {len(val_df)} ({val_df.index.min()} to {val_df.index.max()})")
    print(f"测试集: {len(test_df)} ({test_df.index.min()} to {test_df.index.max()})")
    return train_df, val_df, test_df

def time_series_cross_validation(df_clean, features, target='continuous_movement', n_splits=5, n_trials=100):
    """使用Optuna进行时序交叉验证 - 回归版本"""
    print(f"\n=== 开始使用 Optuna 进行时序交叉验证 (回归) (n_splits={n_splits}, n_trials={n_trials}) ===")
    tscv = TimeSeriesSplit(n_splits=n_splits)
    X = df_clean[features]
    y = df_clean[target]

    def objective(trial):
        params = {
            'objective': 'regression',
            'metric': 'rmse',
            'random_state': 42,
            'verbose': -1,
            'n_jobs': -1,
            'n_estimators': trial.suggest_int('n_estimators', 800, 2500),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.1, log=True),
            'max_depth': trial.suggest_int('max_depth', 5, 12),
            'num_leaves': trial.suggest_int('num_leaves', 20, 150),
            'min_child_samples': trial.suggest_int('min_child_samples', 20, 100),
            'subsample': trial.suggest_float('subsample', 0.7, 1.0),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.7, 1.0),
        }
        
        fold_scores = []
        for train_idx, val_idx in tscv.split(X):
            X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
            y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]
            
            try:
                lgbm = lgb.LGBMRegressor(**params)
                lgbm.fit(X_train_fold, y_train_fold, 
                        eval_set=[(X_val_fold, y_val_fold)], 
                        eval_metric='rmse',
                        callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False)])
                
                y_pred = lgbm.predict(X_val_fold)
                rmse = np.sqrt(mean_squared_error(y_val_fold, y_pred))
                fold_scores.append(rmse)
            except Exception as e:
                print(f"Fold训练失败: {e}")
                continue
        
        return np.mean(fold_scores) if fold_scores else float('inf')

    optuna.logging.set_verbosity(optuna.logging.WARNING)
    study = optuna.create_study(direction='minimize')  # 最小化RMSE
    study.optimize(objective, n_trials=n_trials)

    print(f"=== Optuna 交叉验证完成 ===")
    if study.best_value == float('inf'):
        print("⚠️ 警告: Optuna 未找到有效的参数组合，将使用默认参数。")
        return {}, float('inf')
    else:
        print(f"最佳RMSE: {study.best_value:.6f}")
        print(f"最佳参数: {study.best_params}")
    
    # 保存交叉验证结果
    cv_results_df = study.trials_dataframe()
    output_dir = get_continuous_output_dir()
    cv_results_file = os.path.join(output_dir, f'cv_results_continuous_v2_{MODEL_BASENAME}.csv')
    cv_results_df.to_csv(cv_results_file, index=False)
    print(f"交叉验证结果已保存到: {cv_results_file}")
    
    return study.best_params, study.best_value

def train_model(args):
    """主训练函数"""
    print(f"开始训练连续移动预测模型 V2 - {MODEL_BASENAME.replace('_', ' ').title()}")
    data_file = args.data_file or f"continuous_v2_{MODEL_BASENAME}.pkl"

    if args.load_data:
        df_clean, train_df, val_df, test_df, _ = load_processed_data(data_file)
        if df_clean is None:
            args.load_data = False
    
    if not args.load_data:
        df = load_data_for_training(
            args.coin, args.db_path, args.symbol, args.interval, args.market, 
            start_time=args.start_time, end_time=args.end_time
        )
        if df is None:
            print("❌ 数据加载失败，退出训练。")
            return
        
        df_clean = prepare_features_and_labels(df, args.symbol, args.market)
        train_df, val_df, test_df = split_data(df_clean)
        
        if args.save_data:
            all_features = get_feature_list(df_clean, time_frame=TIMEFRAME_MINUTES)
            save_processed_data(df_clean, train_df, val_df, test_df, all_features, data_file)

    target = 'continuous_movement'
    X_train, y_train = train_df.drop(columns=[target]), train_df[target]
    X_val, y_val = val_df.drop(columns=[target]), val_df[target]
    X_test, y_test = test_df.drop(columns=[target]), test_df[target]

    # 获取特征列表
    all_features = get_feature_list(df_clean, time_frame=TIMEFRAME_MINUTES)
    features = all_features

    print(f"\n📊 训练数据统计:")
    print(f"训练集标签统计:")
    train_stats = calculate_movement_statistics(y_train.values)
    print_movement_statistics(train_stats)

    # 模型训练
    best_params = {}
    best_cv_score = float('inf')
    use_time_series_cv = not args.no_time_series_cv

    if use_time_series_cv:
        print(f"🔍 使用时序交叉验证对 {len(features)} 个特征进行超参数搜索...")
        train_val_df = pd.concat([train_df, val_df])
        best_params, best_cv_score = time_series_cross_validation(
            train_val_df, features, target, n_splits=args.cv_splits, n_trials=args.cv_trials
        )
    else:
        print(f"\n⚠️  禁用时序交叉验证，使用默认参数...")
    
    # 设置最终参数
    final_params = {
        'objective': 'regression',
        'metric': 'rmse',
        'n_jobs': -1,
        'random_state': 42,
        'n_estimators': 2000,
        'learning_rate': 0.05
    }
    final_params.update(best_params)

    print(f"\n开始使用 {len(features)} 个特征训练LightGBM回归模型...")
    lgbm = lgb.LGBMRegressor(**final_params)
    lgbm.fit(X_train[features], y_train, 
             eval_set=[(X_val[features], y_val)], 
             eval_metric='rmse',
             callbacks=[lgb.early_stopping(stopping_rounds=100, verbose=False)])

    print("模型训练完成，开始评估...")
    
    # 在验证集上评估
    val_predictions = lgbm.predict(X_val[features])
    val_rmse = np.sqrt(mean_squared_error(y_val, val_predictions))
    val_mae = mean_absolute_error(y_val, val_predictions)
    val_r2 = r2_score(y_val, val_predictions)
    
    print(f"验证集性能:")
    print(f"  RMSE: {val_rmse:.6f}")
    print(f"  MAE: {val_mae:.6f}")
    print(f"  R²: {val_r2:.4f}")

    # 在测试集上评估
    evaluate_on_test_set(lgbm, X_test, y_test, test_df, features)
    
    # 准备保存的配置
    extra_config = {
        'time_series_cv_used': use_time_series_cv,
        'final_feature_count': len(features),
        'val_rmse': float(val_rmse),
        'val_mae': float(val_mae),
        'val_r2': float(val_r2),
        'algorithm_version': 'v2_improved'
    }
    if use_time_series_cv:
        extra_config.update({
            'cv_splits': args.cv_splits,
            'cv_trials': args.cv_trials,
            'best_cv_score': float(best_cv_score),
            'best_cv_params': best_params
        })

    save_model_and_config(lgbm, features, len(X_train), len(X_val), len(X_test), extra_config)
    analyze_feature_importance(lgbm, features)

def evaluate_on_test_set(model, X_test, y_test, test_df, features):
    """在测试集上评估模型"""
    print(f"\n--- 测试集评估 ---")
    
    X_test_final = X_test[features]
    test_predictions = model.predict(X_test_final)
    
    # 计算回归指标
    test_rmse = np.sqrt(mean_squared_error(y_test, test_predictions))
    test_mae = mean_absolute_error(y_test, test_predictions)
    test_r2 = r2_score(y_test, test_predictions)
    
    print(f"测试集性能:")
    print(f"  RMSE: {test_rmse:.6f}")
    print(f"  MAE: {test_mae:.6f}")
    print(f"  R²: {test_r2:.4f}")
    
    # 方向准确性分析
    correct_direction = ((test_predictions > 0) == (y_test > 0)).sum()
    direction_accuracy = correct_direction / len(y_test) * 100
    
    print(f"方向预测准确率: {direction_accuracy:.2f}%")
    
    # 创建详细结果日志
    results_log = []
    for i in range(len(test_predictions)):
        predicted_movement = test_predictions[i]
        actual_movement = y_test.iloc[i]
        current_price = test_df.iloc[i]['close']
        timestamp = test_df.index[i]
        
        log_entry = {
            'Timestamp': timestamp,
            'ClosePrice': current_price,
            'PredictedMovement': predicted_movement,
            'ActualMovement': actual_movement,
            'AbsoluteError': abs(predicted_movement - actual_movement),
            'PredictedPercent': predicted_movement * 100,
            'ActualPercent': actual_movement * 100
        }
        results_log.append(log_entry)

    results_df = pd.DataFrame(results_log)
    
    print(f"平均绝对误差: {results_df['AbsoluteError'].mean()*100:.2f}%")

    # 保存结果
    results_filename = os.path.join(get_continuous_output_dir(), f'test_results_continuous_v2_{MODEL_BASENAME}.csv')
    results_df.to_csv(results_filename, index=False, float_format='%.6f')
    print(f"\n详细测试结果已保存到: {results_filename}")

def save_processed_data(df_clean, train_df, val_df, test_df, features, data_file):
    """保存预处理数据"""
    print(f"保存预处理数据到 {data_file}...")
    data_dict = {
        'df_clean': df_clean,
        'train_df': train_df,
        'val_df': val_df,
        'test_df': test_df,
        'features': features,
        'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'version': 'v2'
    }
    with open(data_file, 'wb') as f:
        pickle.dump(data_dict, f)
    print(f"数据已保存到 {data_file}")

def load_processed_data(data_file):
    """加载预处理数据"""
    print(f"从 {data_file} 加载预处理数据...")
    if not os.path.exists(data_file):
        print(f"错误：数据文件 {data_file} 不存在！")
        return None, None, None, None, None
    
    with open(data_file, 'rb') as f:
        data_dict = pickle.load(f)
    
    version = data_dict.get('version', 'v1')
    print(f"数据加载完成，版本: {version}, 保存时间: {data_dict.get('save_time', '未知')}")
    return (data_dict['df_clean'], data_dict['train_df'], 
            data_dict['val_df'], data_dict['test_df'], 
            data_dict.get('features'))

def save_model_and_config(model, features, train_size, val_size, test_size, extra_config=None):
    """保存模型和配置"""
    output_dir = get_continuous_output_dir()
    model_file = os.path.join(output_dir, f'continuous_v2_{MODEL_BASENAME}_model.joblib')
    config_file = os.path.join(output_dir, f'continuous_v2_{MODEL_BASENAME}_config.json')
    
    joblib.dump(model, model_file)
    
    config = {
        'model_type': f'LGBM_Continuous_V2_{MODEL_BASENAME}',
        'target_description': f'predict_max_continuous_movement_max_retracement_{MAX_RETRACEMENT*100}%',
        'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'train_size': train_size,
        'val_size': val_size,
        'test_size': test_size,
        'max_retracement': MAX_RETRACEMENT,
        'max_lookforward_minutes': MAX_LOOKFORWARD_MINUTES,
        'timeframe_minutes': TIMEFRAME_MINUTES,
        'feature_list': features,
        'algorithm_version': 'v2_improved'
    }
    
    if extra_config:
        config.update(extra_config)
    
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"\n模型和配置已保存。")
    print(f"模型文件: {model_file}")
    print(f"配置文件: {config_file}")

def analyze_feature_importance(lgbm, features):
    """分析特征重要性"""
    output_dir = get_continuous_output_dir()
    importance_df = pd.DataFrame({
        'feature': features,
        'importance': lgbm.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("\n" + "="*20 + " 特征重要性 (Top 20) " + "="*20)
    print(importance_df.head(20).to_string(index=False))
    
    importance_file = os.path.join(output_dir, f'feature_importance_continuous_v2_{MODEL_BASENAME}.csv')
    importance_df.to_csv(importance_file, index=False)
    print(f"\n完整特征重要性已保存到 {importance_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="连续移动预测模型训练器 V2")
    parser.add_argument("--coin", default="ETH", help="币种名称 (如: ETH, DOT, SUI)")
    parser.add_argument("--max-retracement", type=float, default=0.01, help="最大回撤百分比 (默认: 0.01 = 1%)")
    parser.add_argument("--save-data", action='store_true', help="保存预处理后的数据")
    parser.add_argument("--load-data", action='store_true', help="加载预处理后的数据")
    parser.add_argument("--data-file", help="预处理数据文件路径")
    parser.add_argument("--no-time-series-cv", action='store_true', help="禁用时序交叉验证")
    parser.add_argument("--cv-splits", type=int, default=5, help="CV分割数")
    parser.add_argument("--cv-trials", type=int, default=100, help="Optuna尝试次数")
    parser.add_argument("--db-path", default='../coin_data.db', help="SQLite数据库路径")
    parser.add_argument("--symbol", help="交易对符号")
    parser.add_argument("--interval", help="时间间隔")
    parser.add_argument("--market", choices=['spot', 'futures'], default='spot', help="市场类型")
    parser.add_argument("--start-time", help="数据开始时间 (YYYY-MM-DD)")
    parser.add_argument("--end-time", help="数据结束时间 (YYYY-MM-DD)")

    args = parser.parse_args()

    # 使用连续移动配置
    coin_config = get_continuous_config(f"{args.coin}_CONTINUOUS_5M")
    if coin_config is None:
        # 尝试其他配置
        coin_config = get_continuous_config(args.coin)
        if coin_config is None:
            exit(1)

    # 设置全局变量
    global TIMEFRAME_MINUTES, MAX_RETRACEMENT, MAX_LOOKFORWARD_MINUTES, MODEL_BASENAME
    TIMEFRAME_MINUTES = coin_config['timeframe_minutes']
    MAX_RETRACEMENT = args.max_retracement  # 使用命令行参数覆盖配置
    MAX_LOOKFORWARD_MINUTES = coin_config['max_lookforward_minutes']
    MODEL_BASENAME = coin_config['model_basename']

    print(f"=== {coin_config['display_name']} 连续移动预测模型训练 V2 ===")
    print(f"算法版本: V2 (改进版)")
    print(f"最大回撤: {MAX_RETRACEMENT*100:.1f}%")
    print(f"时间框架: {TIMEFRAME_MINUTES}分钟")
    print(f"最大前瞻: {MAX_LOOKFORWARD_MINUTES}分钟")
    print(f"训练目标: 预测任意百分比的最大连续移动")
    
    train_model(args)

if __name__ == '__main__':
    main()