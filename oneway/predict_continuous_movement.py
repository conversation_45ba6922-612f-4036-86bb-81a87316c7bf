#!/usr/bin/env python3
# predict_continuous_movement.py
# 使用训练好的连续移动模型进行预测

import pandas as pd
import numpy as np
import joblib
import json
import os
import sys
import argparse
from datetime import datetime, timedelta

sys.path.append('..')
from model_utils_815 import calculate_features
from data_loader import load_data_for_training
from continuous_utils import get_continuous_config, get_continuous_output_dir

class ContinuousMovementPredictor:
    """连续移动预测器"""
    
    def __init__(self, model_basename):
        self.model_basename = model_basename
        self.model = None
        self.config = None
        self.features = None
        self.load_model()
    
    def load_model(self):
        """加载训练好的模型和配置"""
        output_dir = get_continuous_output_dir()
        
        # 加载模型
        model_file = os.path.join(output_dir, f'continuous_{self.model_basename}_model.joblib')
        if not os.path.exists(model_file):
            raise FileNotFoundError(f"模型文件不存在: {model_file}")
        
        self.model = joblib.load(model_file)
        print(f"✅ 模型加载成功: {model_file}")
        
        # 加载配置
        config_file = os.path.join(output_dir, f'continuous_{self.model_basename}_config.json')
        if not os.path.exists(config_file):
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        
        self.features = self.config['feature_list']
        print(f"✅ 配置加载成功: {len(self.features)} 个特征")
        print(f"模型类型: {self.config['model_type']}")
        print(f"训练日期: {self.config['training_date']}")
    
    def prepare_features(self, df):
        """准备特征数据"""
        timeframe = self.config['timeframe_minutes']
        df_with_features = calculate_features(df.copy(), timeframe=timeframe)
        
        # 确保所有需要的特征都存在
        missing_features = set(self.features) - set(df_with_features.columns)
        if missing_features:
            raise ValueError(f"缺少特征: {missing_features}")
        
        return df_with_features[self.features]
    
    def predict(self, df):
        """进行预测"""
        X = self.prepare_features(df)
        predictions = self.model.predict(X)
        
        # 创建预测结果DataFrame
        results = pd.DataFrame({
            'timestamp': df.index,
            'close_price': df['close'],
            'predicted_movement': predictions,
            'predicted_percentage': predictions * 100
        })
        
        return results
    
    def predict_latest(self, df, n_latest=10):
        """预测最新的N个数据点"""
        if len(df) < n_latest:
            n_latest = len(df)
        
        latest_df = df.tail(n_latest)
        results = self.predict(latest_df)
        
        print(f"\n📊 最新 {n_latest} 个预测结果:")
        print("-" * 80)
        
        for i, row in results.iterrows():
            timestamp = row['timestamp']
            price = row['close_price']
            movement = row['predicted_movement']
            percentage = row['predicted_percentage']
            
            direction = "📈" if movement > 0 else "📉" if movement < 0 else "➡️"
            
            print(f"{direction} {timestamp} | 价格: ${price:.4f} | "
                  f"预测移动: {percentage:+.2f}%")
        
        return results
    
    def analyze_predictions(self, results):
        """分析预测结果"""
        movements = results['predicted_movement']
        
        stats = {
            'count': len(movements),
            'mean': movements.mean(),
            'std': movements.std(),
            'min': movements.min(),
            'max': movements.max(),
            'positive_ratio': (movements > 0).sum() / len(movements),
            'negative_ratio': (movements < 0).sum() / len(movements)
        }
        
        print(f"\n📈 预测统计分析:")
        print(f"  预测数量: {stats['count']}")
        print(f"  平均预测: {stats['mean']*100:+.2f}%")
        print(f"  标准差: {stats['std']*100:.2f}%")
        print(f"  最大上涨: {stats['max']*100:+.2f}%")
        print(f"  最大下跌: {stats['min']*100:+.2f}%")
        print(f"  看涨比例: {stats['positive_ratio']*100:.1f}%")
        print(f"  看跌比例: {stats['negative_ratio']*100:.1f}%")
        
        return stats
    
    def save_predictions(self, results, filename=None):
        """保存预测结果"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'predictions_continuous_{self.model_basename}_{timestamp}.csv'
        
        output_dir = get_continuous_output_dir()
        filepath = os.path.join(output_dir, filename)
        
        results.to_csv(filepath, index=False, float_format='%.6f')
        print(f"💾 预测结果已保存: {filepath}")
        
        return filepath

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="连续移动预测")
    parser.add_argument("model_basename", help="模型基础名称 (如: eth_5m_continuous)")
    parser.add_argument("--coin", default="ETH", help="币种名称")
    parser.add_argument("--db-path", default="../coin_data.db", help="数据库路径")
    parser.add_argument("--symbol", help="交易对符号")
    parser.add_argument("--interval", help="时间间隔")
    parser.add_argument("--market", choices=['spot', 'futures'], default='spot', help="市场类型")
    parser.add_argument("--start-time", help="数据开始时间 (YYYY-MM-DD)")
    parser.add_argument("--end-time", help="数据结束时间 (YYYY-MM-DD)")
    parser.add_argument("--latest", type=int, default=10, help="显示最新N个预测")
    parser.add_argument("--save", action='store_true', help="保存预测结果")
    parser.add_argument("--output-file", help="输出文件名")
    
    args = parser.parse_args()
    
    try:
        # 创建预测器
        predictor = ContinuousMovementPredictor(args.model_basename)
        
        # 加载数据
        print(f"📊 加载数据...")
        df = load_data_for_training(
            args.coin, args.db_path, args.symbol, args.interval, args.market,
            start_time=args.start_time, end_time=args.end_time
        )
        
        if df is None:
            print("❌ 数据加载失败")
            return
        
        print(f"✅ 数据加载成功: {len(df)} 条记录")
        print(f"时间范围: {df.index.min()} 到 {df.index.max()}")
        
        # 进行预测
        print(f"🔮 开始预测...")
        results = predictor.predict(df)
        
        # 显示最新预测
        predictor.predict_latest(df, args.latest)
        
        # 分析预测结果
        predictor.analyze_predictions(results)
        
        # 保存结果
        if args.save:
            predictor.save_predictions(results, args.output_file)
        
        print(f"\n✅ 预测完成!")
        
    except Exception as e:
        print(f"❌ 预测失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()