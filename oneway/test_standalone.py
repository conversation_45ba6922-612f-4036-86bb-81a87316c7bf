#!/usr/bin/env python3
# test_standalone.py
# 测试独立版本的功能

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from standalone_utils import (
    get_continuous_config, get_continuous_output_dir,
    calculate_features, load_data_for_training
)

def test_config_loading():
    """测试配置加载"""
    print("=" * 50)
    print("测试1: 配置加载")
    print("=" * 50)
    
    config = get_continuous_config("ETH_CONTINUOUS_5M")
    if config:
        print("✅ 配置加载成功")
        print(f"币种: {config['display_name']}")
        print(f"时间框架: {config['timeframe_minutes']}分钟")
        print(f"目标百分比: {config['target_percentage']*100:.1f}%")
        print(f"最大回撤: {config['max_retracement']*100:.1f}%")
    else:
        print("❌ 配置加载失败")
    
    output_dir = get_continuous_output_dir()
    print(f"输出目录: {output_dir}")
    return config is not None

def test_feature_calculation():
    """测试特征计算"""
    print("\n" + "=" * 50)
    print("测试2: 特征计算")
    print("=" * 50)
    
    # 创建模拟数据
    dates = pd.date_range(start='2024-01-01', periods=1000, freq='5T')
    np.random.seed(42)
    
    # 生成模拟价格数据
    base_price = 3000
    returns = np.random.normal(0, 0.001, 1000)  # 0.1%标准差
    prices = [base_price]
    
    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(new_price)
    
    # 创建OHLCV数据
    df = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.0005))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.0005))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(1000, 10000, 1000)
    }, index=dates)
    
    # 确保high >= close >= low 和 high >= open >= low
    df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
    df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))
    
    print(f"模拟数据创建完成: {len(df)} 条记录")
    print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    try:
        # 计算特征
        df_with_features = calculate_features(df.copy(), timeframe=5)
        feature_count = len([col for col in df_with_features.columns 
                           if col not in ['open', 'high', 'low', 'close', 'volume']])
        
        print(f"✅ 特征计算成功")
        print(f"总特征数: {feature_count}")
        print(f"数据形状: {df_with_features.shape}")
        
        # 显示一些特征名称
        feature_cols = [col for col in df_with_features.columns 
                       if col not in ['open', 'high', 'low', 'close', 'volume']]
        print(f"示例特征: {feature_cols[:10]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 特征计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loading():
    """测试数据加载 (如果数据库存在)"""
    print("\n" + "=" * 50)
    print("测试3: 数据加载")
    print("=" * 50)
    
    import os
    
    # 首先检查数据库
    db_path = "../coin_data.db"
    if not os.path.exists(db_path):
        print(f"⚠️ 数据库文件不存在: {db_path}")
        print("可以使用 get_coin_history.py 获取数据")
        return False
    
    # 检查数据库内容
    import sqlite3
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [t[0] for t in cursor.fetchall()]
        conn.close()
        
        print(f"数据库中的表: {tables}")
        
        # 查找ETH相关的表
        eth_tables = [t for t in tables if 'ETH' in t.upper()]
        if not eth_tables:
            print("⚠️ 未找到ETH相关的表")
            return False
        
        print(f"ETH相关表: {eth_tables}")
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return False
    
    try:
        df = load_data_for_training(
            coin="ETH", 
            db_path=db_path,
            start_time="2024-01-01",
            end_time="2024-01-02"
        )
        
        if df is not None:
            print(f"✅ 数据加载成功: {len(df)} 条记录")
            print(f"时间范围: {df.index.min()} 到 {df.index.max()}")
            print(f"列名: {list(df.columns)}")
            return True
        else:
            print("⚠️ 数据加载返回None")
            return False
            
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_continuous_movement_calculation():
    """测试连续移动计算"""
    print("\n" + "=" * 50)
    print("测试4: 连续移动计算")
    print("=" * 50)
    
    # 创建一个简单的测试用例
    dates = pd.date_range(start='2024-01-01', periods=100, freq='5T')
    
    # 创建一个明确的价格模式：先涨5%，然后回撤0.5%，再涨2%
    base_price = 100
    prices = [base_price]
    
    # 前20个点：涨5%
    for i in range(20):
        prices.append(base_price * (1 + 0.05 * (i+1) / 20))
    
    # 接下来5个点：回撤0.5%
    peak_price = prices[-1]
    for i in range(5):
        prices.append(peak_price * (1 - 0.005 * (i+1) / 5))
    
    # 剩余点：再涨2%
    retracement_price = prices[-1]
    for i in range(75):
        if len(prices) < 100:
            prices.append(retracement_price * (1 + 0.02 * (i+1) / 75))
    
    # 确保正好100个价格点
    prices = prices[:100]
    
    df = pd.DataFrame({
        'open': prices,
        'high': [p * 1.001 for p in prices],  # 稍微高一点
        'low': [p * 0.999 for p in prices],   # 稍微低一点
        'close': prices,
        'volume': [1000] * 100
    }, index=dates)
    
    print(f"测试数据: 价格从 {prices[0]:.2f} 变化到 {prices[-1]:.2f}")
    print(f"预期最大连续涨幅: ~7% (5% + 2%，中途回撤0.5%)")
    
    # 导入连续移动计算函数
    from train_standalone import calculate_max_continuous_movement
    
    try:
        continuous_movements = calculate_max_continuous_movement(
            df, max_retracement=0.01, max_lookforward_minutes=400, timeframe=5
        )
        
        if len(continuous_movements) > 0:
            max_movement = continuous_movements.iloc[0]  # 第一个点的预测
            print(f"✅ 连续移动计算成功")
            print(f"第一个点的最大连续移动: {max_movement*100:.2f}%")
            print(f"计算出的移动数量: {len(continuous_movements)}")
            return True
        else:
            print("❌ 未计算出任何连续移动")
            return False
            
    except Exception as e:
        print(f"❌ 连续移动计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("🧪 独立版本功能测试")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("配置加载", test_config_loading),
        ("特征计算", test_feature_calculation),
        ("数据加载", test_data_loading),
        ("连续移动计算", test_continuous_movement_calculation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 异常: {e}")
            results[test_name] = False
    
    # 打印测试总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(tests)} 测试通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！独立版本可以正常使用。")
    elif passed >= len(tests) - 1:
        print("⚠️ 大部分测试通过，可以尝试运行训练。")
    else:
        print("❌ 多个测试失败，请检查配置和依赖。")

if __name__ == '__main__':
    main()