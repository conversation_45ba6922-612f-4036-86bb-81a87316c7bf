#!/usr/bin/env python3
# volume_price_momentum_test.py
# 验证价格和交易量暴涨后的动量效应

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import argparse
from datetime import datetime, timedelta
from standalone_utils import load_data_for_training, get_continuous_output_dir
import os

def detect_volume_price_surge(df, volume_threshold=2.0, price_threshold=0.02):
    """
    检测交易量和价格的突然暴涨
    
    参数:
    - volume_threshold: 交易量暴涨倍数（默认2倍）
    - price_threshold: 价格暴涨百分比（默认2%）
    
    返回:
    - 包含暴涨信号的DataFrame
    """
    
    # 计算交易量的移动平均（过去20根K线）
    df['volume_ma20'] = df['volume'].rolling(window=20).mean()
    
    # 计算价格变化
    df['price_change_pct'] = (df['close'] - df['open']) / df['open']
    
    # 检测暴涨条件
    surge_conditions = (
        (df['volume'] > df['volume_ma20'] * volume_threshold) &  # 交易量暴涨
        (df['price_change_pct'] > price_threshold) &  # 价格暴涨
        (df['volume_ma20'].notna())  # 确保有足够的历史数据
    )
    
    surge_signals = df[surge_conditions].copy()
    
    print(f"检测到 {len(surge_signals)} 个价量暴涨信号")
    print(f"暴涨条件: 交易量 > {volume_threshold}倍均值, 价格涨幅 > {price_threshold*100:.1f}%")
    
    return surge_signals

def analyze_next_candle_performance(df, surge_signals):
    """
    分析暴涨信号后下一根K线的表现
    """
    results = []
    
    for idx, surge_row in surge_signals.iterrows():
        # 找到下一根K线
        current_pos = df.index.get_loc(idx)
        
        if current_pos + 1 < len(df):
            next_candle = df.iloc[current_pos + 1]
            
            # 计算下一根K线的收益
            next_return = (next_candle['close'] - surge_row['close']) / surge_row['close']
            
            # 计算下一根K线的涨跌幅
            next_candle_change = (next_candle['close'] - next_candle['open']) / next_candle['open']
            
            results.append({
                'surge_timestamp': idx,
                'surge_price': surge_row['close'],
                'surge_volume': surge_row['volume'],
                'surge_volume_ratio': surge_row['volume'] / surge_row['volume_ma20'],
                'surge_price_change': surge_row['price_change_pct'],
                'next_timestamp': next_candle.name,
                'next_price': next_candle['close'],
                'next_return': next_return,
                'next_candle_change': next_candle_change,
                'next_volume': next_candle['volume']
            })
    
    return pd.DataFrame(results)

def calculate_strategy_performance(results_df):
    """
    计算策略表现
    """
    if len(results_df) == 0:
        return None
    
    # 基本统计
    total_signals = len(results_df)
    positive_returns = len(results_df[results_df['next_return'] > 0])
    negative_returns = len(results_df[results_df['next_return'] <= 0])
    win_rate = positive_returns / total_signals
    
    # 收益统计
    avg_return = results_df['next_return'].mean()
    total_return = results_df['next_return'].sum()
    max_return = results_df['next_return'].max()
    min_return = results_df['next_return'].min()
    
    # 下一根K线本身的涨跌统计
    next_candle_up = len(results_df[results_df['next_candle_change'] > 0])
    next_candle_down = len(results_df[results_df['next_candle_change'] <= 0])
    next_candle_win_rate = next_candle_up / total_signals
    
    return {
        'total_signals': total_signals,
        'positive_returns': positive_returns,
        'negative_returns': negative_returns,
        'win_rate': win_rate,
        'avg_return': avg_return,
        'total_return': total_return,
        'max_return': max_return,
        'min_return': min_return,
        'next_candle_up': next_candle_up,
        'next_candle_down': next_candle_down,
        'next_candle_win_rate': next_candle_win_rate
    }

def plot_results(results_df, stats, coin, days):
    """
    绘制分析结果图表
    """
    if len(results_df) == 0:
        print("没有数据可绘制")
        return
    
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 图1: 下一根K线收益分布
    ax1 = axes[0, 0]
    returns_pct = results_df['next_return'] * 100
    ax1.hist(returns_pct, bins=30, alpha=0.7, color='blue', edgecolor='black')
    ax1.axvline(x=0, color='red', linestyle='--', alpha=0.7)
    ax1.set_title('下一根K线收益分布', fontsize=12, fontweight='bold')
    ax1.set_xlabel('收益 (%)')
    ax1.set_ylabel('频次')
    ax1.grid(True, alpha=0.3)
    
    # 图2: 交易量倍数 vs 下一根K线收益
    ax2 = axes[0, 1]
    ax2.scatter(results_df['surge_volume_ratio'], returns_pct, alpha=0.6)
    ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax2.set_title('交易量倍数 vs 下一根K线收益', fontsize=12, fontweight='bold')
    ax2.set_xlabel('交易量倍数')
    ax2.set_ylabel('下一根K线收益 (%)')
    ax2.grid(True, alpha=0.3)
    
    # 图3: 暴涨幅度 vs 下一根K线收益
    ax3 = axes[1, 0]
    surge_pct = results_df['surge_price_change'] * 100
    ax3.scatter(surge_pct, returns_pct, alpha=0.6, color='green')
    ax3.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax3.set_title('暴涨幅度 vs 下一根K线收益', fontsize=12, fontweight='bold')
    ax3.set_xlabel('暴涨幅度 (%)')
    ax3.set_ylabel('下一根K线收益 (%)')
    ax3.grid(True, alpha=0.3)
    
    # 图4: 累计收益曲线
    ax4 = axes[1, 1]
    cumulative_returns = (results_df['next_return'] + 1).cumprod() - 1
    ax4.plot(range(len(cumulative_returns)), cumulative_returns * 100, 'b-', linewidth=2)
    ax4.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax4.set_title('累计收益曲线', fontsize=12, fontweight='bold')
    ax4.set_xlabel('信号序号')
    ax4.set_ylabel('累计收益 (%)')
    ax4.grid(True, alpha=0.3)
    
    plt.suptitle(f'{coin} 价量暴涨动量效应分析 ({days}天)', fontsize=14, fontweight='bold')
    plt.tight_layout()
    
    # 保存图表
    output_dir = get_continuous_output_dir()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    chart_filename = os.path.join(output_dir, f'volume_price_momentum_{coin}_{timestamp}.png')
    plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
    print(f"📊 图表已保存到: {chart_filename}")
    
    try:
        plt.show()
    except:
        pass
    
    plt.close()

def print_analysis_results(stats):
    """
    打印分析结果
    """
    if stats is None:
        print("❌ 没有足够的数据进行分析")
        return
    
    print(f"\n{'='*60}")
    print(f"价量暴涨动量效应分析结果")
    print(f"{'='*60}")
    
    print(f"📊 基本统计:")
    print(f"  总信号数: {stats['total_signals']}")
    print(f"  下一根K线收益为正: {stats['positive_returns']} ({stats['win_rate']*100:.1f}%)")
    print(f"  下一根K线收益为负: {stats['negative_returns']} ({(1-stats['win_rate'])*100:.1f}%)")
    
    print(f"\n💰 收益分析:")
    print(f"  平均收益: {stats['avg_return']*100:.3f}%")
    print(f"  总收益: {stats['total_return']*100:.2f}%")
    print(f"  最大收益: {stats['max_return']*100:.2f}%")
    print(f"  最大损失: {stats['min_return']*100:.2f}%")
    
    print(f"\n📈 下一根K线涨跌分析:")
    print(f"  下一根K线上涨: {stats['next_candle_up']} ({stats['next_candle_win_rate']*100:.1f}%)")
    print(f"  下一根K线下跌: {stats['next_candle_down']} ({(1-stats['next_candle_win_rate'])*100:.1f}%)")
    
    # 结论
    print(f"\n🎯 结论:")
    if stats['win_rate'] > 0.5 and stats['avg_return'] > 0:
        print("✅ 价量暴涨后确实存在正向动量效应")
    elif stats['next_candle_win_rate'] > 0.5:
        print("⚠️ 下一根K线更容易上涨，但持有收益不明显")
    else:
        print("❌ 价量暴涨后没有明显的正向动量效应")

def main():
    parser = argparse.ArgumentParser(
        description="验证价量暴涨的动量效应",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python volume_price_momentum_test.py --coin ETH --days 90
  python volume_price_momentum_test.py --coin BTC --days 60 --volume-threshold 3.0 --price-threshold 0.03
        """
    )
    
    parser.add_argument("--coin", default="ETH", help="币种名称")
    parser.add_argument("--days", type=int, default=60, help="回测天数")
    parser.add_argument("--volume-threshold", type=float, default=2.0, help="交易量暴涨倍数阈值")
    parser.add_argument("--price-threshold", type=float, default=0.02, help="价格暴涨百分比阈值")
    parser.add_argument("--db-path", default='../coin_data.db', help="数据库路径")
    
    args = parser.parse_args()
    
    print(f"🚀 开始价量暴涨动量效应分析")
    print(f"币种: {args.coin}, 回测天数: {args.days}")
    print(f"交易量阈值: {args.volume_threshold}倍, 价格阈值: {args.price_threshold*100:.1f}%")
    
    # 确定时间范围
    end_time = datetime.now().strftime('%Y-%m-%d')
    start_time = (datetime.now() - timedelta(days=args.days)).strftime('%Y-%m-%d')
    
    # 加载数据
    df = load_data_for_training(
        args.coin, args.db_path, market='spot',
        start_time=start_time, end_time=end_time
    )
    
    if df is None or len(df) == 0:
        print("❌ 无法加载数据")
        return
    
    print(f"📈 加载数据: {len(df)} 条K线记录")
    
    # 检测价量暴涨信号
    surge_signals = detect_volume_price_surge(
        df, args.volume_threshold, args.price_threshold
    )
    
    if len(surge_signals) == 0:
        print("❌ 未检测到符合条件的价量暴涨信号")
        return
    
    # 分析下一根K线表现
    results_df = analyze_next_candle_performance(df, surge_signals)
    
    # 计算策略表现
    stats = calculate_strategy_performance(results_df)
    
    # 打印结果
    print_analysis_results(stats)
    
    # 绘制图表
    plot_results(results_df, stats, args.coin, args.days)
    
    # 保存详细结果
    if len(results_df) > 0:
        output_dir = get_continuous_output_dir()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_filename = os.path.join(output_dir, f'volume_price_momentum_{args.coin}_{timestamp}.csv')
        results_df.to_csv(results_filename, index=False, float_format='%.6f')
        print(f"📄 详细结果已保存到: {results_filename}")

if __name__ == '__main__':
    main()