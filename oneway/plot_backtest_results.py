#!/usr/bin/env python3
# plot_backtest_results.py
# 从CSV文件绘制回测结果图表

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import argparse
import os
from datetime import datetime

def plot_backtest_from_csv(csv_file, output_dir=None):
    """从CSV文件绘制回测结果"""
    
    # 读取CSV文件
    if not os.path.exists(csv_file):
        print(f"❌ 文件不存在: {csv_file}")
        return
    
    df = pd.read_csv(csv_file)
    print(f"📊 加载回测数据: {len(df)} 条记录")
    
    # 转换时间戳
    df['Timestamp'] = pd.to_datetime(df['Timestamp'])
    
    # 过滤出有交易的记录
    trades_df = df[df['TradingAction'] != 'Hold'].copy()
    
    if len(trades_df) == 0:
        print("⚠️ 没有交易记录")
        return
    
    print(f"📈 交易记录: {len(trades_df)} 次")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 计算累计收益
    trades_df = trades_df.sort_values('Timestamp')
    trades_df['CumulativeReturn'] = (1 + trades_df['TradingReturnPct'] / 100).cumprod() - 1
    trades_df['CumulativeReturnPct'] = trades_df['CumulativeReturn'] * 100
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 图1: 累计收益曲线
    ax1 = axes[0, 0]
    ax1.plot(trades_df['Timestamp'], trades_df['CumulativeReturnPct'], 
            linewidth=2, color='blue', label='累计收益')
    ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax1.set_title('累计收益曲线', fontsize=14, fontweight='bold')
    ax1.set_ylabel('累计收益 (%)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
    
    # 图2: 单次交易收益分布
    ax2 = axes[0, 1]
    ax2.hist(trades_df['TradingReturnPct'], bins=50, alpha=0.7, color='green', edgecolor='black')
    ax2.axvline(x=0, color='red', linestyle='--', alpha=0.7)
    ax2.set_title('单次交易收益分布', fontsize=14, fontweight='bold')
    ax2.set_xlabel('单次收益 (%)', fontsize=12)
    ax2.set_ylabel('频次', fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    # 图3: 交易动作时间分布
    ax3 = axes[1, 0]
    long_trades = trades_df[trades_df['TradingAction'] == 'Long']
    short_trades = trades_df[trades_df['TradingAction'] == 'Short']
    
    if len(long_trades) > 0:
        ax3.scatter(long_trades['Timestamp'], long_trades['TradingReturnPct'], 
                   c='green', marker='^', s=50, alpha=0.7, label=f'做多 ({len(long_trades)}次)')
    
    if len(short_trades) > 0:
        ax3.scatter(short_trades['Timestamp'], short_trades['TradingReturnPct'], 
                   c='red', marker='v', s=50, alpha=0.7, label=f'做空 ({len(short_trades)}次)')
    
    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax3.set_title('交易时间点与收益', fontsize=14, fontweight='bold')
    ax3.set_xlabel('时间', fontsize=12)
    ax3.set_ylabel('单次收益 (%)', fontsize=12)
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    ax3.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
    
    # 图4: 回撤分析
    ax4 = axes[1, 1]
    cumulative_value = (1 + trades_df['TradingReturnPct'] / 100).cumprod()
    running_max = cumulative_value.expanding().max()
    drawdown = (cumulative_value - running_max) / running_max * 100
    
    ax4.fill_between(trades_df['Timestamp'], drawdown, 0, alpha=0.3, color='red')
    ax4.plot(trades_df['Timestamp'], drawdown, color='red', linewidth=1)
    ax4.set_title('回撤分析', fontsize=14, fontweight='bold')
    ax4.set_xlabel('时间', fontsize=12)
    ax4.set_ylabel('回撤 (%)', fontsize=12)
    ax4.grid(True, alpha=0.3)
    ax4.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if output_dir is None:
        output_dir = os.path.dirname(csv_file)
    
    base_name = os.path.splitext(os.path.basename(csv_file))[0]
    chart_filename = os.path.join(output_dir, f'{base_name}_chart.png')
    plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
    print(f"📊 图表已保存到: {chart_filename}")
    
    # 显示统计信息
    print_trading_stats(trades_df)
    
    # 显示图表
    try:
        plt.show()
    except:
        pass
    
    plt.close()

def print_trading_stats(trades_df):
    """打印交易统计信息"""
    total_return = trades_df['TradingReturnPct'].sum()
    avg_return = trades_df['TradingReturnPct'].mean()
    win_trades = len(trades_df[trades_df['TradingReturnPct'] > 0])
    total_trades = len(trades_df)
    win_rate = win_trades / total_trades
    max_win = trades_df['TradingReturnPct'].max()
    max_loss = trades_df['TradingReturnPct'].min()
    
    # 最大回撤
    cumulative_value = (1 + trades_df['TradingReturnPct'] / 100).cumprod()
    running_max = cumulative_value.expanding().max()
    drawdown = (cumulative_value - running_max) / running_max
    max_drawdown = drawdown.min() * 100
    
    print(f"\n📊 交易统计:")
    print(f"  总交易次数: {total_trades}")
    print(f"  总收益: {total_return:.2f}%")
    print(f"  平均收益: {avg_return:.3f}%")
    print(f"  胜率: {win_rate*100:.1f}% ({win_trades}/{total_trades})")
    print(f"  最大收益: {max_win:.2f}%")
    print(f"  最大损失: {max_loss:.2f}%")
    print(f"  最大回撤: {max_drawdown:.2f}%")
    
    # 按交易类型统计
    long_trades = trades_df[trades_df['TradingAction'] == 'Long']
    short_trades = trades_df[trades_df['TradingAction'] == 'Short']
    
    if len(long_trades) > 0:
        long_return = long_trades['TradingReturnPct'].mean()
        long_win_rate = len(long_trades[long_trades['TradingReturnPct'] > 0]) / len(long_trades)
        print(f"  做多: {len(long_trades)}次, 平均收益: {long_return:.3f}%, 胜率: {long_win_rate*100:.1f}%")
    
    if len(short_trades) > 0:
        short_return = short_trades['TradingReturnPct'].mean()
        short_win_rate = len(short_trades[short_trades['TradingReturnPct'] > 0]) / len(short_trades)
        print(f"  做空: {len(short_trades)}次, 平均收益: {short_return:.3f}%, 胜率: {short_win_rate*100:.1f}%")

def main():
    parser = argparse.ArgumentParser(
        description="从CSV文件绘制回测结果图表",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python plot_backtest_results.py backtest_eth_5m_20250829_191718.csv
  python plot_backtest_results.py models/backtest_eth_5m_20250829_191718.csv
        """
    )
    
    parser.add_argument("csv_file", help="回测结果CSV文件路径")
    parser.add_argument("--output-dir", help="图表输出目录（默认与CSV文件同目录）")
    
    args = parser.parse_args()
    
    plot_backtest_from_csv(args.csv_file, args.output_dir)

if __name__ == '__main__':
    main()