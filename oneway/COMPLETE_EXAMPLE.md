# 🚀 完整使用示例

## 📋 从零开始的完整流程

### 1️⃣ 环境准备
```bash
# 确保在oneway目录
cd oneway

# 安装依赖
pip install pandas numpy lightgbm scikit-learn optuna joblib requests

# 或者运行自动设置
python setup.py
```

### 2️⃣ 获取数据
```bash
# 获取ETH 5分钟数据 (5000条，约17天)
python get_sample_data.py --symbol ETHUSDT --limit 5000

# 检查数据是否正确获取
python check_database.py --db ../coin_data.db
```

**预期输出:**
```
🔄 获取 ETHUSDT 5m 样本数据 (5000 条)...
✅ 获取到 5000 条数据
💾 保存到数据库表: ETHUSDT_5min_spot
✅ 数据保存完成
记录数: 5000
时间范围: 2024-01-15 10:25:00 到 2024-01-28 14:20:00
```

### 3️⃣ 测试功能
```bash
# 运行完整测试
python test_standalone.py
```

**预期输出:**
```
✅ 配置加载测试
✅ 特征计算测试
✅ 数据加载测试
✅ 连续移动计算测试
总计: 4/4 测试通过
```

### 4️⃣ 训练模型
```bash
# 快速训练 (2-3分钟)
python train_standalone.py --coin ETH --no-time-series-cv

# 或完整训练 (10-15分钟)
python train_standalone.py --coin ETH --cv-trials 30
```

**预期输出:**
```
=== ETH/USDT 连续移动 连续移动预测模型训练 ===
📊 加载数据...
✅ 数据加载成功: 5000 条记录
在完整数据集上计算增强特征...
开始计算特征 (5-min K线)...
特征计算完成。
计算最大连续移动：最大回撤1.0%，最大等待48根K线
...
✅ 模型和配置已保存
模型文件: ../models/standalone_eth_5m_model.joblib
配置文件: ../models/standalone_eth_5m_config.json
```

### 5️⃣ 进行预测
```bash
# 预测最新10个数据点
python predict_standalone.py eth_5m --coin ETH --latest 10
```

**预期输出:**
```
🔮 加载模型: eth_5m
✅ 模型加载成功
✅ 配置加载成功: 156 个特征
📊 加载数据...
✅ 数据加载成功: 4500 条记录

📊 最新 10 个预测结果:
📈 2024-01-28 14:00:00 | 价格: $2456.78 | 预测移动: +2.34% | 信心: 高
📉 2024-01-28 14:05:00 | 价格: $2461.23 | 预测移动: -1.87% | 信心: 中
...
```

## 🎯 一键运行所有步骤

```bash
# 运行完整示例 (包含数据获取、测试、训练、预测)
python run_example.py --mode full
```

## 📊 结果文件

训练完成后，在 `../models/` 目录会生成：

```
models/
├── standalone_eth_5m_model.joblib           # 训练好的模型
├── standalone_eth_5m_config.json           # 模型配置
├── test_results_standalone_eth_5m.csv      # 测试结果
└── feature_importance_standalone_eth_5m.csv # 特征重要性
```

## 🔍 结果分析

### 查看测试结果
```bash
# 在Excel或其他工具中打开
open ../models/test_results_standalone_eth_5m.csv
```

**测试结果包含:**
- 时间戳
- 价格
- 预测移动百分比
- 实际移动百分比
- 预测误差

### 查看特征重要性
```bash
# 查看最重要的特征
head -20 ../models/feature_importance_standalone_eth_5m.csv
```

## 🎨 自定义训练

### 调整参数
```bash
# 更严格的回撤控制 (0.5%)
python train_standalone.py --coin ETH --max-retracement 0.005

# 更多的优化试验
python train_standalone.py --coin ETH --cv-trials 100

# 训练其他币种 (需要先获取数据)
python get_sample_data.py --symbol BTCUSDT --limit 5000
python train_standalone.py --coin BTC --max-retracement 0.008
```

### 获取更多数据
```bash
# 获取更多历史数据 (30天)
python get_sample_data.py --symbol ETHUSDT --limit 8640

# 获取不同时间间隔的数据
python get_sample_data.py --symbol ETHUSDT --interval 15m --limit 2000
```

## 🚨 常见问题解决

### 问题1: 网络连接失败
```bash
# 使用代理
export HTTP_PROXY=http://127.0.0.1:7890
python get_sample_data.py --symbol ETHUSDT --limit 5000
```

### 问题2: 数据库表不存在
```bash
# 检查数据库内容
python check_database.py --db ../coin_data.db

# 重新获取数据
python get_sample_data.py --symbol ETHUSDT --limit 5000
```

### 问题3: 训练时间过长
```bash
# 使用快速模式
python train_standalone.py --coin ETH --no-time-series-cv --cv-trials 0
```

### 问题4: 预测结果不理想
```bash
# 调整回撤参数
python train_standalone.py --coin ETH --max-retracement 0.015

# 使用更多数据
python get_sample_data.py --symbol ETHUSDT --limit 10000
```

## 📈 性能预期

### 训练性能
- **快速模式**: 1-3分钟
- **标准模式**: 5-15分钟  
- **完整模式**: 15-30分钟

### 预测性能
- **RMSE**: 通常 0.005-0.015 (0.5%-1.5%)
- **方向准确率**: 通常 55%-65%
- **R²**: 通常 0.1-0.4

### 数据要求
- **最少数据**: 1000条记录
- **推荐数据**: 5000+条记录
- **最佳数据**: 10000+条记录

## 🎯 下一步

1. **优化参数**: 根据结果调整回撤阈值
2. **增加数据**: 获取更多历史数据
3. **多币种**: 训练其他币种的模型
4. **实盘测试**: 在模拟环境中测试策略
5. **持续改进**: 定期重新训练模型

---

**🎉 恭喜！** 你已经成功完成了连续移动预测模型的完整流程！