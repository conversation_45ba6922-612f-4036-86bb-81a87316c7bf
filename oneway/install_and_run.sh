#!/bin/bash
# install_and_run.sh
# 连续移动预测模型一键安装和运行脚本

set -e  # 遇到错误立即退出

echo "🚀 连续移动预测模型一键安装和运行"
echo "=================================="

# 检查Python
echo "📋 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装，请先安装Python 3.8+"
    exit 1
fi

PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "✅ Python版本: $PYTHON_VERSION"

# 检查pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3未安装"
    exit 1
fi

# 安装依赖
echo "📦 安装Python依赖包..."
pip3 install pandas numpy lightgbm scikit-learn optuna joblib --quiet --user
echo "✅ 依赖包安装完成"

# 运行环境设置
echo "⚙️ 运行环境设置..."
python3 setup.py
if [ $? -ne 0 ]; then
    echo "❌ 环境设置失败"
    exit 1
fi

# 运行测试
echo "🧪 运行功能测试..."
python3 test_standalone.py
if [ $? -ne 0 ]; then
    echo "⚠️ 测试失败，但继续执行"
fi

# 询问用户是否要训练模型
echo ""
read -p "🤖 是否要训练ETH模型? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🏋️ 开始训练ETH模型 (快速模式)..."
    python3 train_standalone.py --coin ETH --no-time-series-cv
    
    if [ $? -eq 0 ]; then
        echo "✅ 模型训练完成!"
        
        # 询问是否要进行预测
        echo ""
        read -p "🔮 是否要运行预测示例? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "🔮 运行预测示例..."
            python3 predict_standalone.py eth_5m --coin ETH --latest 5
        fi
    else
        echo "❌ 模型训练失败"
    fi
fi

echo ""
echo "🎉 安装和设置完成!"
echo ""
echo "📚 下一步:"
echo "1. 查看快速指南: cat QUICKSTART.md"
echo "2. 运行完整示例: python3 run_example.py"
echo "3. 训练其他币种: python3 train_standalone.py --coin BTC"
echo "4. 查看帮助: python3 train_standalone.py --help"
echo ""
echo "⚠️ 风险提示: 模型预测仅供参考，不构成投资建议"