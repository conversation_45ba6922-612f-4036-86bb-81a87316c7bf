#!/usr/bin/env python3
# batch_train_continuous.py
# 批量训练连续移动预测模型

import json
import os
from datetime import datetime
from run_continuous_training import run_training
from continuous_utils import get_continuous_config

def load_continuous_configs():
    """加载所有连续移动配置"""
    config_file = os.path.join(os.path.dirname(__file__), 'continuous_config.json')
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    return config.get('continuous_configs', {})

def batch_train_all(save_data=True, cv_trials=50):
    """批量训练所有配置"""
    configs = load_continuous_configs()
    
    print(f"🚀 开始批量训练 {len(configs)} 个配置")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    results = {}
    
    for config_name, config in configs.items():
        print(f"\n📊 训练配置: {config_name}")
        print(f"币种: {config['display_name']}")
        print(f"目标: {config['target_percentage']*100:.1f}%, 回撤: {config['max_retracement']*100:.1f}%")
        
        try:
            success = run_training(
                config_name,
                save_data=save_data,
                cv_trials=cv_trials
            )
            results[config_name] = "成功" if success else "失败"
        except Exception as e:
            print(f"❌ 训练异常: {e}")
            results[config_name] = f"异常: {str(e)}"
        
        print("-" * 80)
    
    # 打印总结
    print(f"\n📋 批量训练总结:")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 40)
    
    success_count = 0
    for config_name, result in results.items():
        status_emoji = "✅" if result == "成功" else "❌"
        print(f"{status_emoji} {config_name}: {result}")
        if result == "成功":
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(results)} 成功")
    return results

def batch_train_selected(config_names, **kwargs):
    """批量训练选定的配置"""
    print(f"🚀 开始训练选定的 {len(config_names)} 个配置")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    results = {}
    
    for config_name in config_names:
        config = get_continuous_config(config_name)
        if config is None:
            results[config_name] = "配置不存在"
            continue
        
        print(f"\n📊 训练配置: {config_name}")
        print(f"币种: {config['display_name']}")
        
        try:
            success = run_training(config_name, **kwargs)
            results[config_name] = "成功" if success else "失败"
        except Exception as e:
            print(f"❌ 训练异常: {e}")
            results[config_name] = f"异常: {str(e)}"
        
        print("-" * 80)
    
    # 打印总结
    print(f"\n📋 批量训练总结:")
    success_count = sum(1 for r in results.values() if r == "成功")
    for config_name, result in results.items():
        status_emoji = "✅" if result == "成功" else "❌"
        print(f"{status_emoji} {config_name}: {result}")
    
    print(f"\n总计: {success_count}/{len(results)} 成功")
    return results

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="批量训练连续移动预测模型")
    parser.add_argument("--all", action='store_true', help="训练所有配置")
    parser.add_argument("--configs", nargs='+', help="指定要训练的配置名称")
    parser.add_argument("--save-data", action='store_true', default=True, help="保存预处理数据")
    parser.add_argument("--cv-trials", type=int, default=50, help="Optuna尝试次数")
    parser.add_argument("--no-time-series-cv", action='store_true', help="禁用时序交叉验证")
    
    args = parser.parse_args()
    
    kwargs = {
        'save_data': args.save_data,
        'cv_trials': args.cv_trials,
        'no_time_series_cv': args.no_time_series_cv
    }
    
    if args.all:
        results = batch_train_all(**kwargs)
    elif args.configs:
        results = batch_train_selected(args.configs, **kwargs)
    else:
        # 默认训练主要配置
        main_configs = [
            "ETH_CONTINUOUS_5M",
            "BTC_CONTINUOUS_5M",
            "DOT_CONTINUOUS_5M"
        ]
        print("未指定配置，训练主要配置...")
        results = batch_train_selected(main_configs, **kwargs)
    
    return results

if __name__ == '__main__':
    main()