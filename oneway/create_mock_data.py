#!/usr/bin/env python3
# create_mock_data.py
# 创建模拟数据用于测试训练

import pandas as pd
import numpy as np
import sqlite3
import os
from datetime import datetime, timedelta

def create_mock_data(symbol='ETHUSDT', interval='5m', days=30, db_path='../coin_data.db'):
    """创建模拟的K线数据"""
    print(f"🎭 创建 {symbol} {interval} 模拟数据 ({days} 天)...")
    
    # 计算数据点数量
    if interval == '5m':
        points_per_day = 24 * 60 // 5  # 288 points per day
    elif interval == '15m':
        points_per_day = 24 * 60 // 15  # 96 points per day
    elif interval == '1h':
        points_per_day = 24
    else:
        points_per_day = 288  # 默认5分钟
    
    total_points = days * points_per_day
    
    # 生成时间序列
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)
    
    if interval == '5m':
        freq = '5T'
    elif interval == '15m':
        freq = '15T'
    elif interval == '1h':
        freq = '1H'
    else:
        freq = '5T'
    
    timestamps = pd.date_range(start=start_time, end=end_time, freq=freq)[:total_points]
    
    # 生成价格数据 (使用随机游走模拟真实价格)
    np.random.seed(42)  # 确保可重复
    
    base_price = 2500.0  # ETH基础价格
    price_changes = np.random.normal(0, 0.002, len(timestamps))  # 0.2%标准差
    
    # 添加一些趋势和周期性
    trend = np.linspace(-0.1, 0.1, len(timestamps))  # 轻微趋势
    cycle = 0.01 * np.sin(np.arange(len(timestamps)) * 2 * np.pi / (24 * 12))  # 日周期
    
    price_changes += trend + cycle
    
    # 计算累积价格
    prices = [base_price]
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 100))  # 确保价格不会太低
    
    # 生成OHLCV数据
    data_list = []
    for i, (timestamp, close_price) in enumerate(zip(timestamps, prices)):
        # 生成开盘价 (基于前一个收盘价)
        if i == 0:
            open_price = close_price
        else:
            open_price = prices[i-1]
        
        # 生成高低价 (在开盘和收盘价基础上添加随机波动)
        price_range = abs(close_price - open_price) + close_price * 0.001
        high_price = max(open_price, close_price) + np.random.uniform(0, price_range * 0.5)
        low_price = min(open_price, close_price) - np.random.uniform(0, price_range * 0.5)
        
        # 确保价格关系正确
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        # 生成成交量 (随机但合理)
        volume = np.random.uniform(1000, 10000)
        
        data_list.append({
            'timestamp': int(timestamp.timestamp()),
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(close_price, 2),
            'volume': round(volume, 2),
            'close_time': int(timestamp.timestamp()) + 299,  # 5分钟后
            'quote_volume': round(volume * close_price, 2),
            'trade_count': int(np.random.uniform(100, 1000)),
            'taker_buy_base_volume': round(volume * 0.6, 2),
            'taker_buy_quote_volume': round(volume * close_price * 0.6, 2)
        })
    
    print(f"✅ 生成了 {len(data_list)} 条模拟数据")
    
    # 保存到数据库
    save_to_database(data_list, symbol, interval, db_path)
    return True

def save_to_database(data_list, symbol, interval, db_path):
    """保存数据到数据库"""
    # 创建表名 (与get_coin_history.py格式一致)
    interval_formatted = interval.replace('m', 'min')
    table_name = f"{symbol.upper()}_{interval_formatted}_spot"
    
    print(f"💾 保存到数据库表: {table_name}")
    
    # 确保目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建表
    cursor.execute(f'''
        CREATE TABLE IF NOT EXISTS {table_name} (
            timestamp INTEGER PRIMARY KEY,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            volume REAL,
            close_time INTEGER,
            quote_volume REAL,
            trade_count INTEGER,
            taker_buy_base_volume REAL,
            taker_buy_quote_volume REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 插入数据
    insert_sql = f'''
        INSERT OR REPLACE INTO {table_name}
        (timestamp, open, high, low, close, volume, close_time, quote_volume, 
         trade_count, taker_buy_base_volume, taker_buy_quote_volume)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    '''
    
    values = [
        (data['timestamp'], data['open'], data['high'], data['low'], data['close'],
         data['volume'], data['close_time'], data['quote_volume'], data['trade_count'],
         data['taker_buy_base_volume'], data['taker_buy_quote_volume'])
        for data in data_list
    ]
    
    cursor.executemany(insert_sql, values)
    conn.commit()
    
    # 检查结果
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    count = cursor.fetchone()[0]
    
    cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table_name}")
    min_ts, max_ts = cursor.fetchone()
    
    conn.close()
    
    print(f"✅ 数据保存完成")
    print(f"记录数: {count}")
    if min_ts and max_ts:
        min_time = datetime.fromtimestamp(min_ts).strftime('%Y-%m-%d %H:%M:%S')
        max_time = datetime.fromtimestamp(max_ts).strftime('%Y-%m-%d %H:%M:%S')
        print(f"时间范围: {min_time} 到 {max_time}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="创建模拟数据用于测试")
    parser.add_argument("--symbol", default="ETHUSDT", help="交易对")
    parser.add_argument("--interval", default="5m", help="时间间隔")
    parser.add_argument("--days", type=int, default=30, help="数据天数")
    parser.add_argument("--db", default="../coin_data.db", help="数据库路径")
    
    args = parser.parse_args()
    
    success = create_mock_data(args.symbol, args.interval, args.days, args.db)
    
    if success:
        print(f"\n🎉 模拟数据创建完成！")
        print(f"现在可以运行训练: python train_standalone.py --coin ETH")
        print(f"检查数据: python check_database.py --db {args.db}")
    else:
        print(f"\n❌ 模拟数据创建失败")

if __name__ == '__main__':
    main()