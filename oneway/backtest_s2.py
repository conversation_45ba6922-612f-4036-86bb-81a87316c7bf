#!/usr/bin/env python3
# backtest_standalone.py
# 使用训练好的模型进行回测 (修改版：只做多，带两种出场条件)

import pandas as pd
import numpy as np
import joblib
import json
import argparse
import os
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.font_manager import FontProperties

# 导入独立工具函数
from standalone_utils import (
    get_continuous_output_dir, calculate_features, load_data_for_training
)

def load_model_and_config(model_basename):
    """加载训练好的模型和配置"""
    output_dir = get_continuous_output_dir()
    model_file = os.path.join(output_dir, f'standalone_{model_basename}_model.joblib')
    config_file = os.path.join(output_dir, f'standalone_{model_basename}_config.json')
    
    if not os.path.exists(model_file):
        print(f"❌ 模型文件不存在: {model_file}")
        return None, None
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return None, None
    
    # 加载模型
    model = joblib.load(model_file)
    
    # 加载配置
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    print(f"✅ 成功加载模型: {model_file}")
    print(f"✅ 成功加载配置: {config_file}")
    print(f"模型类型: {config.get('model_type', 'Unknown')}")
    print(f"训练日期: {config.get('training_date', 'Unknown')}")
    print(f"特征数量: {len(config.get('feature_list', []))}")
    
    return model, config

def prepare_backtest_data(df, config):
    """准备回测数据"""
    timeframe_minutes = config['timeframe_minutes']
    
    print("计算特征...")
    df_with_features = calculate_features(df.copy(), timeframe=timeframe_minutes)
    
    # 获取模型需要的特征
    required_features = config['feature_list']
    
    # 检查特征是否存在
    missing_features = [f for f in required_features if f not in df_with_features.columns]
    if missing_features:
        print(f"⚠️ 警告: 缺少以下特征: {missing_features}")
        # 移除缺少的特征
        available_features = [f for f in required_features if f in df_with_features.columns]
        print(f"使用可用特征: {len(available_features)}/{len(required_features)}")
    else:
        available_features = required_features
    
    # 准备特征数据
    X = df_with_features[available_features].dropna()
    
    print(f"回测数据准备完成: {len(X)} 条记录")
    return X, available_features, df_with_features

def check_trading_conditions_hardcoded(proba_lowest, proba_neutral, proba_highest, predicted_class):
    """硬编码的交易条件检查"""
    
    # 做多条件（预测最低点时）
    if predicted_class == 0:
        return predicted_class
        # if (proba_neutral < 0.29 and 
        #     0.38 <= proba_lowest <= 0.43 and 
        #     0.32 <= proba_highest <= 0.33):
        #     return 0  # 满足做多条件
        # else:
        #     return 1  # 不满足条件，改为持有
    
    # 做空条件（预测最高点时）- 暂时保持原始预测
    elif predicted_class == 2:
        return predicted_class
    
    # 中性点保持不变
    return predicted_class

def check_trading_conditions(proba_lowest, proba_neutral, proba_highest, predicted_class, trading_thresholds):
    """检查是否满足交易条件 - 使用硬编码版本"""
    # 直接使用硬编码的条件
    return check_trading_conditions_hardcoded(proba_lowest, proba_neutral, proba_highest, predicted_class)

def run_backtest(model, X, df_with_features, config, lookforward_minutes):
    """运行回测 - 修改版：只做多，带两种出场条件"""
    print(f"\n--- 开始回测 (修改版：只做多) ---")
    
    # 策略说明
    print("策略: 只做多")
    print("入场条件: 模型预测为 '最低点' (信号 0)")
    print(f"出场条件 1: 持有时间达到 {lookforward_minutes} 分钟")
    print("出场条件 2: 模型预测为 '最高点' (信号 2)")
    
    # 进行预测
    predictions = model.predict(X)
    probabilities = model.predict_proba(X)
    
    # 将预测结果合并到 X DataFrame 中，方便索引
    X['OriginalPredictedClass'] = predictions
    X['ProbaLowest'] = probabilities[:, 0]
    X['ProbaNeutral'] = probabilities[:, 1]
    X['ProbaHighest'] = probabilities[:, 2]

    # 应用硬编码的交易条件进行过滤
    print("✅ 使用硬编码概率阈值过滤交易信号")
    X['FilteredPredictedClass'] = X.apply(
        lambda row: check_trading_conditions(
            row['ProbaLowest'], row['ProbaNeutral'], row['ProbaHighest'], row['OriginalPredictedClass'], {}
        ), axis=1
    )
    
    # 准备回测
    trades_log = []
    active_position = None
    timeframe_minutes = config['timeframe_minutes']
    lookforward_candles = lookforward_minutes // timeframe_minutes

    print(f"时间框架: {timeframe_minutes} 分钟, 前瞻周期: {lookforward_candles} 根K线")

    # 遍历所有数据点
    for i in range(len(X)):
        current_idx = X.index[i]
        current_row_features = X.iloc[i]
        
        # 获取当前K线信息
        if current_idx not in df_with_features.index:
            continue
        current_row_ohlc = df_with_features.loc[current_idx]
        current_price = current_row_ohlc['close']
        current_signal = current_row_features['FilteredPredictedClass']
        
        # 1. 检查是否需要平仓
        if active_position:
            exit_condition_met = False
            exit_reason = None
            
            # 条件1: 达到持仓时间上限
            time_exit_candle_index = active_position['entry_candle_index'] + lookforward_candles
            if i >= time_exit_candle_index:
                exit_condition_met = True
                exit_reason = f"时间达到 ({lookforward_minutes} 分钟)"
            
            # 条件2: 遇到 '最高点' 信号
            # 注意：此处需判断 i > active_position['entry_candle_index']，避免在入场同一根K线立即出场
            if current_signal == 2 and i > active_position['entry_candle_index']:
                exit_condition_met = True
                exit_reason = "预测到最高点"

            if exit_condition_met:
                exit_price = current_price
                trade_return = (exit_price - active_position['entry_price']) / active_position['entry_price']
                
                trades_log.append({
                    'EntryTimestamp': active_position['entry_timestamp'],
                    'EntryPrice': active_position['entry_price'],
                    'ExitTimestamp': current_idx,
                    'ExitPrice': exit_price,
                    'HoldingCandles': i - active_position['entry_candle_index'],
                    'TradingReturnPct': trade_return * 100,
                    'ExitReason': exit_reason
                })
                active_position = None

        # 2. 检查是否可以开仓
        if not active_position:
            # 入场条件: 预测为 '最低点'
            if current_signal == 0:
                active_position = {
                    'entry_timestamp': current_idx,
                    'entry_price': current_price,
                    'entry_candle_index': i
                }

    # 如果循环结束时仍有持仓，则在最后一根K线平仓
    if active_position:
        last_idx = X.index[-1]
        last_price = df_with_features.loc[last_idx, 'close']
        last_candle_index = len(X) - 1
        trade_return = (last_price - active_position['entry_price']) / active_position['entry_price']
        
        trades_log.append({
            'EntryTimestamp': active_position['entry_timestamp'],
            'EntryPrice': active_position['entry_price'],
            'ExitTimestamp': last_idx,
            'ExitPrice': last_price,
            'HoldingCandles': last_candle_index - active_position['entry_candle_index'],
            'TradingReturnPct': trade_return * 100,
            'ExitReason': '回测结束'
        })

    print(f"回测完成, 共完成 {len(trades_log)} 笔交易。")
    
    return pd.DataFrame(trades_log)

def calculate_backtest_statistics(results_df, total_periods):
    """计算回测统计信息 - 适配新的交易日志格式"""
    trades_df = results_df.copy()
    
    if len(trades_df) == 0:
        return {'total_trades': 0}
    
    # 基本统计
    total_trades = len(trades_df)
    
    # 收益统计
    total_return = trades_df['TradingReturnPct'].sum()
    avg_return = trades_df['TradingReturnPct'].mean()
    win_trades = len(trades_df[trades_df['TradingReturnPct'] > 0])
    lose_trades = len(trades_df[trades_df['TradingReturnPct'] < 0])
    win_rate = win_trades / total_trades if total_trades > 0 else 0
    
    # 风险指标
    returns = trades_df['TradingReturnPct'].values
    max_win = returns.max() if len(returns) > 0 else 0
    max_loss = returns.min() if len(returns) > 0 else 0
    std_return = returns.std() if len(returns) > 0 else 0
    sharpe_ratio = avg_return / std_return if std_return > 0 else 0
    
    # 最大回撤
    trades_df = trades_df.sort_values('ExitTimestamp')
    cumulative_returns = (1 + trades_df['TradingReturnPct'] / 100).cumprod()
    running_max = cumulative_returns.expanding().max()
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = drawdown.min() * 100 if not drawdown.empty else 0
    
    return {
        'total_trades': total_trades,
        'total_return_pct': total_return,
        'avg_return_pct': avg_return,
        'win_trades': win_trades,
        'lose_trades': lose_trades,
        'win_rate': win_rate,
        'max_win_pct': max_win,
        'max_loss_pct': max_loss,
        'std_return': std_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown_pct': max_drawdown,
        'total_periods': total_periods,
        'trading_frequency': total_trades / total_periods if total_periods > 0 else 0,
    }

def print_backtest_statistics(stats, start_time, end_time):
    """打印回测统计信息"""
    print(f"\n{'='*60}")
    print(f"回测结果统计 ({start_time} 到 {end_time}) - (策略: 只做多)")
    print(f"{'='*60}")
    
    if 'total_trades' not in stats or stats['total_trades'] == 0:
        print("❌ 没有执行任何交易")
        return
    
    print(f"📊 基本统计:")
    print(f"  回测周期: {stats['total_periods']} 个时间点")
    print(f"  交易次数: {stats['total_trades']}")
    print(f"  交易频率: {stats['trading_frequency']*100:.2f}%")
    
    print(f"\n💰 收益统计:")
    print(f"  总收益 (累加): {stats['total_return_pct']:.2f}%")
    print(f"  平均每次交易收益: {stats['avg_return_pct']:.3f}%")
    print(f"  胜率: {stats['win_rate']*100:.1f}% ({stats['win_trades']}/{stats['total_trades']})")
    print(f"  最大单次收益: {stats['max_win_pct']:.2f}%")
    print(f"  最大单次损失: {stats['max_loss_pct']:.2f}%")
    
    print(f"\n📈 风险指标:")
    print(f"  收益标准差: {stats['std_return']:.3f}%")
    print(f"  夏普比率: {stats['sharpe_ratio']:.3f}")
    print(f"  最大回撤: {stats['max_drawdown_pct']:.2f}%")

def plot_returns_chart(results_df, model_name, timestamp):
    """绘制收益图表"""
    try:
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        trades_df = results_df.copy()
        
        if len(trades_df) == 0:
            print("⚠️ 没有交易记录，跳过绘图")
            return
        
        # 计算累计收益
        trades_df = trades_df.sort_values('ExitTimestamp')
        trades_df['CumulativeReturnPct'] = trades_df['TradingReturnPct'].cumsum()
        
        # 创建图表
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12), sharex=False)
        
        # 图1: 累计收益曲线
        ax1.plot(trades_df['ExitTimestamp'], trades_df['CumulativeReturnPct'], 
                linewidth=2, color='blue', label='累计收益')
        ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        ax1.set_title(f'{model_name} 回测累计收益曲线 (只做多策略)', fontsize=14, fontweight='bold')
        ax1.set_ylabel('累计收益 (%)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.tick_params(axis='x', rotation=30)
        
        # 图2: 单次交易收益分布
        ax2.hist(trades_df['TradingReturnPct'], bins=50, alpha=0.7, color='green', edgecolor='black')
        ax2.axvline(x=0, color='red', linestyle='--', alpha=0.7)
        ax2.set_title('单次交易收益分布', fontsize=14, fontweight='bold')
        ax2.set_xlabel('单次收益 (%)', fontsize=12)
        ax2.set_ylabel('频次', fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        # 图3: 交易动作时间分布
        ax3.scatter(trades_df['ExitTimestamp'], trades_df['TradingReturnPct'], 
                   c='green', marker='^', s=50, alpha=0.7, label=f'做多平仓 ({len(trades_df)}次)')
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax3.set_title('交易时间点与收益', fontsize=14, fontweight='bold')
        ax3.set_xlabel('平仓时间', fontsize=12)
        ax3.set_ylabel('单次收益 (%)', fontsize=12)
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        ax3.tick_params(axis='x', rotation=30)
        
        # 格式化x轴日期
        for ax in [ax1, ax3]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
            ax.xaxis.set_major_locator(plt.MaxNLocator(10))

        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        chart_filename = f'backtest_chart_{model_name}_{timestamp}.png'
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        print(f"📊 收益图表已保存到: {chart_filename}")
        
        try:
            plt.show()
        except Exception:
            pass
        
        plt.close()
        
    except Exception as e:
        print(f"⚠️ 绘图失败: {e}")

def main():
    parser = argparse.ArgumentParser(
        description="使用训练好的模型进行回测",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python backtest_standalone.py --model eth_5m --start-time 2024-01-01 --end-time 2024-02-01
  python backtest_standalone.py --model eth_5m --coin ETH --days 30
        """
    )
    
    parser.add_argument("--model", required=True, help="模型基名（如: eth_5m）")
    parser.add_argument("--coin", default="ETH", help="币种名称")
    parser.add_argument("--start-time", help="回测开始时间 (YYYY-MM-DD)")
    parser.add_argument("--end-time", help="回测结束时间 (YYYY-MM-DD)")
    parser.add_argument("--days", type=int, help="回测天数（从最新数据往前）")
    parser.add_argument("--db-path", default='../coin_data.db', help="数据库路径")
    parser.add_argument("--symbol", help="交易对符号")
    parser.add_argument("--interval", help="时间间隔")
    parser.add_argument("--market", choices=['spot', 'futures'], default='spot', help="市场类型")
    parser.add_argument("--lookforward-hours", type=float, default=4.0, help="前瞻时间（小时），用于时间出场条件")
    
    args = parser.parse_args()
    
    # 加载模型和配置
    model, config = load_model_and_config(args.model)
    if model is None or config is None:
        return
    
    # 确定时间范围
    if args.days:
        end_time = datetime.now().strftime('%Y-%m-%d')
        start_time = (datetime.now() - timedelta(days=args.days)).strftime('%Y-%m-%d')
    else:
        start_time = args.start_time
        end_time = args.end_time
    
    if not start_time or not end_time:
        print("❌ 请指定时间范围（--start-time 和 --end-time，或者 --days）")
        return
    
    print(f"回测时间范围: {start_time} 到 {end_time}")
    
    # 加载回测数据
    df = load_data_for_training(
        args.coin, args.db_path, args.symbol, args.interval, args.market,
        start_time=start_time, end_time=end_time
    )
    
    if df is None or len(df) == 0:
        print("❌ 无法加载回测数据")
        return
    
    print(f"加载原始数据: {len(df)} 条记录")
    
    # 准备回测数据
    X, features, df_with_features = prepare_backtest_data(df, config)
    
    if len(X) == 0:
        print("❌ 没有可用的回测数据")
        return
    
    # 运行回测
    lookforward_minutes = int(args.lookforward_hours * 60)
    results_df = run_backtest(model, X, df_with_features, config, lookforward_minutes)
    
    # 计算统计信息
    stats = calculate_backtest_statistics(results_df, len(X))
    print_backtest_statistics(stats, start_time, end_time)
    
    # 保存回测结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_filename = f'backtest_results_longonly_{args.model}_{timestamp}.csv'
    if not results_df.empty:
        results_df.to_csv(results_filename, index=False, float_format='%.6f')
        print(f"\n✅ 回测交易日志已保存到: {results_filename}")
    
    # 绘制收益图表
    plot_returns_chart(results_df, args.model, timestamp)

if __name__ == '__main__':
    main()