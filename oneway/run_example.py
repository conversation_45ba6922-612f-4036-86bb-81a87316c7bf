#!/usr/bin/env python3
# run_example.py
# 连续移动预测模型完整运行示例

import os
import sys
import subprocess
from datetime import datetime

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败，错误码: {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"❌ 找不到命令: {cmd[0]}")
        return False

def check_prerequisites():
    """检查先决条件"""
    print("🔍 检查先决条件...")
    
    # 检查Python包
    required_packages = ['pandas', 'numpy', 'lightgbm', 'scikit-learn', 'optuna', 'joblib']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少以下包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    # 检查配置文件
    if os.path.exists('continuous_config.json'):
        print("✅ continuous_config.json")
    else:
        print("❌ continuous_config.json (缺失)")
        return False
    
    # 检查数据库文件
    if os.path.exists('../coin_data.db'):
        print("✅ ../coin_data.db")
    else:
        print("⚠️ ../coin_data.db (不存在，将使用模拟数据)")
    
    return True

def run_full_example():
    """运行完整示例"""
    print("🎯 连续移动预测模型完整示例")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if not check_prerequisites():
        print("\n❌ 先决条件检查失败，请解决上述问题后重试")
        return False
    
    steps = [
        {
            'cmd': [sys.executable, 'test_standalone.py'],
            'description': '功能测试',
            'required': True
        },
        {
            'cmd': [sys.executable, 'get_sample_data.py', '--symbol', 'ETHUSDT', '--limit', '5000'],
            'description': '获取ETH样本数据',
            'required': True
        },
        {
            'cmd': [sys.executable, 'train_standalone.py', '--coin', 'ETH', 
                   '--max-retracement', '0.01', '--cv-trials', '20', '--no-time-series-cv'],
            'description': 'ETH模型训练 (快速版)',
            'required': True
        },
        {
            'cmd': [sys.executable, 'predict_standalone.py', 'eth_5m', '--coin', 'ETH', '--latest', '5'],
            'description': 'ETH模型预测',
            'required': False
        }
    ]
    
    results = {}
    
    for i, step in enumerate(steps, 1):
        print(f"\n📋 步骤 {i}/{len(steps)}: {step['description']}")
        
        success = run_command(step['cmd'], step['description'])
        results[step['description']] = success
        
        if not success and step['required']:
            print(f"\n❌ 必需步骤失败，停止执行")
            break
        
        if not success:
            print(f"\n⚠️ 可选步骤失败，继续执行")
    
    # 打印总结
    print(f"\n{'='*60}")
    print("📊 执行总结")
    print(f"{'='*60}")
    
    for step_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{step_name}: {status}")
    
    success_count = sum(results.values())
    total_count = len(results)
    
    print(f"\n总计: {success_count}/{total_count} 步骤成功")
    
    if success_count == total_count:
        print("🎉 所有步骤完成！")
        print("\n📁 生成的文件:")
        print("- ../models/standalone_eth_5m_model.joblib (训练好的模型)")
        print("- ../models/standalone_eth_5m_config.json (模型配置)")
        print("- ../models/test_results_standalone_eth_5m.csv (测试结果)")
        print("- ../models/feature_importance_standalone_eth_5m.csv (特征重要性)")
        
        print("\n🔮 下一步:")
        print("1. 查看测试结果分析模型性能")
        print("2. 使用 predict_standalone.py 进行实时预测")
        print("3. 调整参数重新训练以优化性能")
        
    elif success_count >= total_count - 1:
        print("⚠️ 大部分步骤完成，可以查看结果")
    else:
        print("❌ 多个步骤失败，请检查错误信息")
    
    return success_count == total_count

def run_quick_test():
    """运行快速测试"""
    print("⚡ 快速功能测试")
    
    if not check_prerequisites():
        return False
    
    return run_command([sys.executable, 'test_standalone.py'], "快速功能测试")

def run_training_only():
    """仅运行训练"""
    print("🏋️ 仅训练模型")
    
    if not check_prerequisites():
        return False
    
    return run_command([
        sys.executable, 'train_standalone.py', 
        '--coin', 'ETH', 
        '--max-retracement', '0.01',
        '--cv-trials', '30'
    ], "ETH模型训练")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="连续移动预测模型运行示例")
    parser.add_argument("--mode", choices=['full', 'test', 'train'], default='full',
                       help="运行模式: full=完整示例, test=仅测试, train=仅训练")
    
    args = parser.parse_args()
    
    if args.mode == 'full':
        success = run_full_example()
    elif args.mode == 'test':
        success = run_quick_test()
    elif args.mode == 'train':
        success = run_training_only()
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()