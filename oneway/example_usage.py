#!/usr/bin/env python3
# example_usage.py
# 连续移动预测模型使用示例

import os
import sys
from datetime import datetime

def example_single_training():
    """示例：单个模型训练"""
    print("=" * 60)
    print("示例1: 单个模型训练")
    print("=" * 60)
    
    print("训练ETH 5分钟连续移动模型...")
    print("命令: python train_continuous_movement.py --coin ETH --save-data")
    print()
    
    # 实际执行（注释掉以避免意外运行）
    # os.system("python train_continuous_movement.py --coin ETH --save-data")

def example_custom_parameters():
    """示例：自定义参数训练"""
    print("=" * 60)
    print("示例2: 自定义参数训练")
    print("=" * 60)
    
    print("使用自定义参数训练BTC模型...")
    print("目标: 6%, 最大回撤: 1.5%")
    print("命令: python train_continuous_movement.py --coin BTC \\")
    print("    --target-percentage 0.06 \\")
    print("    --max-retracement 0.015 \\")
    print("    --save-data")
    print()

def example_batch_training():
    """示例：批量训练"""
    print("=" * 60)
    print("示例3: 批量训练")
    print("=" * 60)
    
    print("批量训练主要币种...")
    print("命令: python batch_train_continuous.py \\")
    print("    --configs ETH_CONTINUOUS_5M BTC_CONTINUOUS_5M DOT_CONTINUOUS_5M \\")
    print("    --cv-trials 50")
    print()

def example_prediction():
    """示例：模型预测"""
    print("=" * 60)
    print("示例4: 模型预测")
    print("=" * 60)
    
    print("使用训练好的模型进行预测...")
    print("命令: python predict_continuous_movement.py eth_5m_continuous \\")
    print("    --coin ETH \\")
    print("    --latest 20 \\")
    print("    --save")
    print()

def example_workflow():
    """示例：完整工作流程"""
    print("=" * 60)
    print("示例5: 完整工作流程")
    print("=" * 60)
    
    print("完整的训练和预测流程:")
    print()
    
    print("步骤1: 训练模型")
    print("python run_continuous_training.py ETH_CONTINUOUS_5M --save-data")
    print()
    
    print("步骤2: 验证模型")
    print("# 检查输出文件:")
    print("# - models/continuous_eth_5m_continuous_model.joblib")
    print("# - models/test_results_continuous_eth_5m_continuous.csv")
    print()
    
    print("步骤3: 进行预测")
    print("python predict_continuous_movement.py eth_5m_continuous --coin ETH --latest 10")
    print()

def show_configuration_examples():
    """显示配置示例"""
    print("=" * 60)
    print("配置文件示例")
    print("=" * 60)
    
    config_example = '''
{
  "ETH_CONTINUOUS_5M": {
    "display_name": "ETH/USDT 连续移动",
    "timeframe_minutes": 5,
    "target_percentage": 0.05,      // 5%目标
    "max_retracement": 0.01,        // 1%最大回撤
    "max_lookforward_minutes": 240  // 4小时前瞻
  }
}
'''
    
    print("continuous_config.json 示例:")
    print(config_example)

def show_parameter_guidelines():
    """显示参数设置指南"""
    print("=" * 60)
    print("参数设置指南")
    print("=" * 60)
    
    guidelines = '''
币种特性建议:

高波动性币种 (如山寨币):
- 目标百分比: 6-10%
- 最大回撤: 1.5-2.5%

中等波动性币种 (如ETH):
- 目标百分比: 4-6%
- 最大回撤: 1-1.5%

低波动性币种 (如BTC):
- 目标百分比: 3-5%
- 最大回撤: 0.8-1.2%

时间框架建议:
- 5分钟: 前瞻2-4小时
- 15分钟: 前瞻8-24小时
- 1小时: 前瞻1-3天
'''
    
    print(guidelines)

def main():
    """主函数"""
    print("🚀 连续移动预测模型使用示例")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 显示各种示例
    example_single_training()
    example_custom_parameters()
    example_batch_training()
    example_prediction()
    example_workflow()
    show_configuration_examples()
    show_parameter_guidelines()
    
    print("=" * 60)
    print("💡 提示:")
    print("1. 首次使用建议先用小数据集测试")
    print("2. 使用 --save-data 保存预处理数据以节省时间")
    print("3. 批量训练时可以减少 --cv-trials 以加快速度")
    print("4. 定期重新训练模型以适应市场变化")
    print("5. 查看 README.md 获取详细文档")
    print("=" * 60)

if __name__ == '__main__':
    main()