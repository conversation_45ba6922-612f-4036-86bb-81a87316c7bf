# standalone_utils.py
# 独立工具函数 - 不依赖外部文件

import pandas as pd
import numpy as np
import json
import os
import sqlite3
from typing import Optional, Dict, Any

def load_continuous_config(config_file='continuous_config.json'):
    """加载连续移动配置文件"""
    try:
        config_path = os.path.join(os.path.dirname(__file__), config_file)
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except Exception as e:
        print(f"加载配置文件失败: {config_file} {e}")
        return None

def get_continuous_config(coin_name):
    """获取连续移动配置"""
    config = load_continuous_config()
    if config is None:
        return None
    
    # 查找匹配的配置
    continuous_configs = config.get('continuous_configs', {})
    
    # 直接匹配
    if coin_name in continuous_configs:
        return continuous_configs[coin_name]
    
    # 模糊匹配
    for key, value in continuous_configs.items():
        if coin_name.upper() in key.upper():
            return value
    
    print(f"❌ 未找到币种 '{coin_name}' 的配置")
    print(f"可用配置: {list(continuous_configs.keys())}")
    return None

def get_continuous_output_dir():
    """获取输出目录"""
    config = load_continuous_config()
    if config:
        output_dir = config.get('output_dir', 'models')
    else:
        output_dir = 'models'
    
    # 如果是相对路径，确保输出目录存在
    if not os.path.isabs(output_dir):
        # 相对于oneway目录的父目录
        output_dir = os.path.join('..', output_dir)
    
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    return output_dir

def calculate_features(df, timeframe):
    """计算技术指标特征 - 独立版本"""
    print(f"开始计算特征 ({timeframe}-min K线)...")
    epsilon = 1e-9
    FEATURE_WINDOWS_MINUTES = [120, 360, 720]

    # 确保索引是datetime类型
    if not isinstance(df.index, pd.DatetimeIndex):
        if 'timestamp' in df.columns:
            df.index = pd.to_datetime(df['timestamp'])
        elif 'Timestamp' in df.columns:
            df.index = pd.to_datetime(df['Timestamp'])
    
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek

    # K线基础特征计算
    df = get_standardized_kline_features(df, timeframe_suffix=f'_{timeframe}m', epsilon=epsilon)

    # 动量特征
    for n_minutes in FEATURE_WINDOWS_MINUTES:
        n_candles = n_minutes // timeframe
        if n_candles >= 1: 
            df[f'return_{n_minutes}min'] = df['close'].pct_change(n_candles)
    
    df['return_60min'] = df['close'].pct_change(60 // timeframe)

    # 波动率和趋势特征
    for n_minutes in FEATURE_WINDOWS_MINUTES:
        n_candles = n_minutes // timeframe
        if n_candles >= 1:
            df[f'volatility_ratio_{n_minutes}'] = df['close'].rolling(window=n_candles).std() / (df['close'] + epsilon)
            df[f'sma_{n_minutes}'] = df['close'].rolling(window=n_candles).mean()
            df[f'price_div_sma_{n_minutes}'] = df['close'] / (df[f'sma_{n_minutes}'] + epsilon)

    if (120 // timeframe > 0 and 720 // timeframe > 0):
        if f'sma_120' in df.columns and f'sma_720' in df.columns:
            df['sma_120_div_sma_720'] = df['sma_120'] / (df['sma_720'] + epsilon)

    # RSI (相对强弱指数)
    rsi_period = 14
    delta = df['close'].diff(1)
    gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
    rs = gain / (loss + epsilon)
    df[f'rsi_{rsi_period}'] = 100 - (100 / (1 + rs))

    # Bollinger Bands Width
    bb_period = 20
    bb_std_dev = 2
    middle_band = df['close'].rolling(window=bb_period).mean()
    std_dev = df['close'].rolling(window=bb_period).std()
    upper_band = middle_band + bb_std_dev * std_dev
    lower_band = middle_band - bb_std_dev * std_dev
    df[f'bb_width_{bb_period}'] = (upper_band - lower_band) / (middle_band + epsilon)

    # RSI和BBW动态变化特征
    df[f'rsi_{rsi_period}_diff_1'] = df[f'rsi_{rsi_period}'].diff(1)
    df[f'bb_width_{bb_period}_diff_1'] = df[f'bb_width_{bb_period}'].diff(1)

    # 指标移动平均
    indicator_ma_period = 5
    df[f'rsi_{rsi_period}_ma_{indicator_ma_period}'] = df[f'rsi_{rsi_period}'].rolling(window=indicator_ma_period).mean()
    df[f'bb_width_{bb_period}_ma_{indicator_ma_period}'] = df[f'bb_width_{bb_period}'].rolling(window=indicator_ma_period).mean()

    # VWAP特征
    vwap_candles = 360 // timeframe
    if vwap_candles > 0:
        df['price_x_volume'] = df['close'] * df['volume']
        vwap_numerator = df['price_x_volume'].rolling(window=vwap_candles).sum()
        vwap_denominator = df['volume'].rolling(window=vwap_candles).sum()
        df['vwap_360'] = vwap_numerator / (vwap_denominator + epsilon)
        df['price_div_vwap_360'] = df['close'] / (df['vwap_360'] + epsilon)
        df.drop('price_x_volume', axis=1, inplace=True)

    # VMA特征
    for timePeriod in [360, 720, 1440]:
        vma_candles = timePeriod // timeframe
        vma_col = f'vma_{timePeriod}'
        if vma_candles > 0:
            df[vma_col] = df['volume'].rolling(window=vma_candles).mean()
            df[f'volume_div_{vma_col}'] = df['volume'] / (df[vma_col] + epsilon)

    # 滚动K线特征
    for window in [5, 12, 24]:
        range_col = f'range_norm_by_atr_{timeframe}m'
        body_col = f'body_percent_of_range_{timeframe}m'

        if range_col in df.columns:
            df[f'range_norm_by_atr_mean_{window}'] = df[range_col].rolling(window=window).mean()

        if body_col in df.columns:
            df[f'body_percent_mean_{window}'] = df[body_col].rolling(window=window).mean()

    print("特征计算完成。")
    return df

def get_standardized_kline_features(df, timeframe_suffix='', epsilon=1e-9):
    """计算标准化的K线特征"""
    try:
        # 计算ATR
        high_low = df['high'] - df['low']
        high_close_prev = np.abs(df['high'] - df['close'].shift(1))
        low_close_prev = np.abs(df['low'] - df['close'].shift(1))
        true_range = np.maximum(high_low, np.maximum(high_close_prev, low_close_prev))
        atr_14 = true_range.rolling(window=14).mean()
        df[f'atr_14{timeframe_suffix}'] = atr_14

        # K线范围特征
        df[f'range{timeframe_suffix}'] = df['high'] - df['low']
        df[f'range_norm_by_atr{timeframe_suffix}'] = df[f'range{timeframe_suffix}'] / (atr_14 + epsilon)

        # K线实体特征
        df[f'body{timeframe_suffix}'] = np.abs(df['close'] - df['open'])
        df[f'body_percent_of_range{timeframe_suffix}'] = df[f'body{timeframe_suffix}'] / (df[f'range{timeframe_suffix}'] + epsilon)

        # 上下影线特征
        df[f'upper_shadow{timeframe_suffix}'] = df['high'] - np.maximum(df['open'], df['close'])
        df[f'lower_shadow{timeframe_suffix}'] = np.minimum(df['open'], df['close']) - df['low']
        df[f'upper_shadow_percent{timeframe_suffix}'] = df[f'upper_shadow{timeframe_suffix}'] / (df[f'range{timeframe_suffix}'] + epsilon)
        df[f'lower_shadow_percent{timeframe_suffix}'] = df[f'lower_shadow{timeframe_suffix}'] / (df[f'range{timeframe_suffix}'] + epsilon)

        # 价格位置特征
        df[f'close_position_in_range{timeframe_suffix}'] = (df['close'] - df['low']) / (df[f'range{timeframe_suffix}'] + epsilon)

    except Exception as e:
        print(f"计算K线特征时出错: {e}")
    
    return df

def get_feature_list(df, time_frame=5):
    """获取特征列表 - 排除基础OHLCV和时间列"""
    exclude_columns = [
        'open', 'high', 'low', 'close', 'volume', 'timestamp', 'Timestamp',
        'hour', 'day_of_week', 'continuous_movement', 'label',
        'close_time', 'quote_volume', 'trade_count', 'taker_buy_base_volume', 
        'taker_buy_quote_volume', 'created_at'  # 排除数据库相关列
    ]
    
    feature_columns = [col for col in df.columns if col not in exclude_columns]
    print(f"总特征数量: {len(feature_columns)}")
    return feature_columns

def load_data_from_sqlite(db_path, symbol, interval, market='spot', start_time=None, end_time=None):
    """从SQLite数据库加载数据"""
    try:
        conn = sqlite3.connect(db_path)
        
        # 根据get_coin_history.py的格式构建表名
        # 格式: ETHUSDT_5min_spot (大写symbol，interval转换为min格式)
        interval_formatted = interval.replace('m', 'min').replace('h', 'hour').replace('d', 'day')
        table_name = f"{symbol.upper()}_{interval_formatted}_{market}"
        
        # 首先检查表是否存在
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        if not cursor.fetchone():
            print(f"❌ 表不存在: {table_name}")
            # 尝试列出可用的表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            if tables:
                print(f"可用的表: {[t[0] for t in tables]}")
            conn.close()
            return None
        
        query = f"SELECT * FROM {table_name}"
        
        conditions = []
        if start_time:
            # 将日期字符串转换为Unix时间戳
            start_ts = int(pd.to_datetime(start_time).timestamp())
            conditions.append(f"timestamp >= {start_ts}")
        if end_time:
            end_ts = int(pd.to_datetime(end_time).timestamp())
            conditions.append(f"timestamp <= {end_ts}")
        
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        query += " ORDER BY timestamp"
        
        print(f"执行查询: {query}")
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if df.empty:
            print(f"❌ 未找到数据: {table_name}")
            return None
        
        # 设置时间索引 - 从Unix时间戳转换
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
        df.set_index('timestamp', inplace=True)
        
        print(f"✅ 数据加载成功: {len(df)} 条记录")
        print(f"时间范围: {df.index.min()} 到 {df.index.max()}")
        
        return df
        
    except Exception as e:
        print(f"❌ 数据库加载失败: {e}")
        return None

def load_data_for_training(coin, db_path, symbol=None, interval=None, market='spot', 
                          start_time=None, end_time=None):
    """为训练加载数据 - 独立版本"""
    
    # 获取配置
    config = get_continuous_config(f"{coin}_CONTINUOUS_5M")
    if config is None:
        config = get_continuous_config(coin)
        if config is None:
            print(f"❌ 未找到币种 {coin} 的配置")
            return None
    
    # 使用配置或命令行参数
    final_symbol = symbol or config.get('api_symbol', f"{coin}USDT")
    final_interval = interval or f"{config['timeframe_minutes']}m"
    config_db_path = config.get('db_path', 'coin_data.db')
    
    # 处理数据库路径
    if not os.path.isabs(config_db_path):
        # 相对路径，相对于oneway目录的父目录
        config_db_path = os.path.join('..', config_db_path)
    
    final_db_path = db_path or config_db_path
    
    print(f"📊 加载数据: {final_symbol} {final_interval} {market}")
    
    # 从数据库加载数据
    df = load_data_from_sqlite(
        final_db_path, final_symbol, final_interval, market, 
        start_time, end_time
    )
    
    return df

def calculate_movement_statistics(movements):
    """计算移动统计信息"""
    if len(movements) == 0:
        return {}
    
    stats = {
        'count': len(movements),
        'mean': np.mean(movements),
        'std': np.std(movements),
        'min': np.min(movements),
        'max': np.max(movements),
        'positive_ratio': (movements > 0).sum() / len(movements),
        'negative_ratio': (movements < 0).sum() / len(movements),
        'zero_ratio': (movements == 0).sum() / len(movements)
    }
    
    # 计算分位数
    stats['q25'] = np.percentile(movements, 25)
    stats['q50'] = np.percentile(movements, 50)  # 中位数
    stats['q75'] = np.percentile(movements, 75)
    
    return stats

def print_movement_statistics(stats):
    """打印移动统计信息"""
    if not stats:
        print("无统计信息")
        return
    
    def format_percentage(value, decimals=2):
        return f"{value*100:.{decimals}f}%"
    
    print(f"\n连续移动统计:")
    print(f"  样本数量: {stats['count']:,}")
    print(f"  平均值: {format_percentage(stats['mean'])}")
    print(f"  标准差: {format_percentage(stats['std'])}")
    print(f"  最小值: {format_percentage(stats['min'])}")
    print(f"  最大值: {format_percentage(stats['max'])}")
    print(f"  中位数: {format_percentage(stats['q50'])}")
    print(f"  25%分位: {format_percentage(stats['q25'])}")
    print(f"  75%分位: {format_percentage(stats['q75'])}")
    print(f"  正值比例: {format_percentage(stats['positive_ratio'])}")
    print(f"  负值比例: {format_percentage(stats['negative_ratio'])}")
    if stats['zero_ratio'] > 0:
        print(f"  零值比例: {format_percentage(stats['zero_ratio'])}")

def print_continuous_training_summary(config, max_retracement):
    """打印训练摘要"""
    print("\n" + "="*60)
    print("连续移动预测模型训练摘要")
    print("="*60)
    print(f"币种: {config['display_name']}")
    print(f"时间框架: {config['timeframe_minutes']}分钟")
    print(f"最大回撤: {max_retracement*100:.1f}%")
    print(f"最大前瞻时间: {config['max_lookforward_minutes']}分钟")
    print(f"模型基础名: {config['model_basename']}")
    print("="*60)