# continuous_utils.py
# 连续移动预测的工具函数

import json
import os
import sys
sys.path.append('..')

def get_continuous_config(coin_name):
    """获取连续移动配置"""
    config_file = os.path.join(os.path.dirname(__file__), 'continuous_config.json')
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print(f"❌ 配置文件未找到: {config_file}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        return None
    
    # 查找匹配的配置
    continuous_configs = config.get('continuous_configs', {})
    
    # 直接匹配
    if coin_name in continuous_configs:
        return continuous_configs[coin_name]
    
    # 模糊匹配
    for key, value in continuous_configs.items():
        if coin_name.upper() in key.upper():
            return value
    
    print(f"❌ 未找到币种 '{coin_name}' 的配置")
    print(f"可用配置: {list(continuous_configs.keys())}")
    return None

def get_continuous_output_dir():
    """获取输出目录"""
    config_file = os.path.join(os.path.dirname(__file__), 'continuous_config.json')
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        output_dir = config.get('output_dir', '../models')
    except:
        output_dir = '../models'
    
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    return output_dir

def validate_continuous_parameters(target_percentage, max_retracement):
    """验证连续移动参数的合理性"""
    if target_percentage <= 0:
        raise ValueError("目标百分比必须大于0")
    
    if max_retracement <= 0:
        raise ValueError("最大回撤必须大于0")
    
    if max_retracement >= target_percentage:
        print(f"⚠️ 警告: 最大回撤 ({max_retracement*100:.1f}%) 接近或超过目标百分比 ({target_percentage*100:.1f}%)")
        print("这可能导致很少的有效信号")
    
    if target_percentage > 0.2:  # 20%
        print(f"⚠️ 警告: 目标百分比 ({target_percentage*100:.1f}%) 可能过高")
    
    if max_retracement < 0.005:  # 0.5%
        print(f"⚠️ 警告: 最大回撤 ({max_retracement*100:.1f}%) 可能过低，可能导致信号过少")

def calculate_expected_signals(df_length, timeframe_minutes, max_lookforward_minutes):
    """估算预期的信号数量"""
    max_lookforward_candles = max_lookforward_minutes // timeframe_minutes
    expected_valid_samples = max(0, df_length - max_lookforward_candles)
    
    print(f"数据集信息:")
    print(f"  总K线数: {df_length}")
    print(f"  最大前瞻K线数: {max_lookforward_candles}")
    print(f"  预期有效样本数: {expected_valid_samples}")
    print(f"  有效样本比例: {expected_valid_samples/df_length*100:.1f}%")
    
    return expected_valid_samples

def print_continuous_training_summary(config, target_percentage, max_retracement):
    """打印训练摘要"""
    print("\n" + "="*60)
    print("连续移动预测模型训练摘要")
    print("="*60)
    print(f"币种: {config['display_name']}")
    print(f"时间框架: {config['timeframe_minutes']}分钟")
    print(f"目标百分比: {target_percentage*100:.1f}%")
    print(f"最大回撤: {max_retracement*100:.1f}%")
    print(f"最大前瞻时间: {config['max_lookforward_minutes']}分钟")
    print(f"模型基础名: {config['model_basename']}")
    print("="*60)

def format_percentage(value, decimals=2):
    """格式化百分比显示"""
    return f"{value*100:.{decimals}f}%"

def calculate_movement_statistics(movements):
    """计算移动统计信息"""
    import numpy as np
    
    if len(movements) == 0:
        return {}
    
    stats = {
        'count': len(movements),
        'mean': np.mean(movements),
        'std': np.std(movements),
        'min': np.min(movements),
        'max': np.max(movements),
        'positive_ratio': (movements > 0).sum() / len(movements),
        'negative_ratio': (movements < 0).sum() / len(movements),
        'zero_ratio': (movements == 0).sum() / len(movements)
    }
    
    # 计算分位数
    stats['q25'] = np.percentile(movements, 25)
    stats['q50'] = np.percentile(movements, 50)  # 中位数
    stats['q75'] = np.percentile(movements, 75)
    
    return stats

def print_movement_statistics(stats):
    """打印移动统计信息"""
    if not stats:
        print("无统计信息")
        return
    
    print(f"\n连续移动统计:")
    print(f"  样本数量: {stats['count']:,}")
    print(f"  平均值: {format_percentage(stats['mean'])}")
    print(f"  标准差: {format_percentage(stats['std'])}")
    print(f"  最小值: {format_percentage(stats['min'])}")
    print(f"  最大值: {format_percentage(stats['max'])}")
    print(f"  中位数: {format_percentage(stats['q50'])}")
    print(f"  25%分位: {format_percentage(stats['q25'])}")
    print(f"  75%分位: {format_percentage(stats['q75'])}")
    print(f"  正值比例: {format_percentage(stats['positive_ratio'])}")
    print(f"  负值比例: {format_percentage(stats['negative_ratio'])}")
    if stats['zero_ratio'] > 0:
        print(f"  零值比例: {format_percentage(stats['zero_ratio'])}")