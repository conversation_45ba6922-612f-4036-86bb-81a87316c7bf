#!/usr/bin/env python3
# train_standalone.py
# 完全独立的连续移动预测模型训练脚本 - 不依赖外部文件

import pandas as pd
import numpy as np
from datetime import datetime
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
import json
import argparse
import os
import pickle
import optuna

# 导入独立工具函数
from standalone_utils import (
    get_continuous_config, get_continuous_output_dir, 
    calculate_features, get_feature_list, load_data_for_training,
    calculate_movement_statistics, print_movement_statistics,
    print_continuous_training_summary
)

# 全局变量
TIMEFRAME_MINUTES = None
MAX_RETRACEMENT = None
MAX_LOOKFORWARD_MINUTES = None
MODEL_BASENAME = None

def calculate_extreme_points(df, lookforward_minutes=240, timeframe=5, min_price_diff=0.005, percentile_threshold=90):
    """
    计算每个时间点是否为未来时间窗口内的最高点、最低点或中性点（三分类）
    
    参数:
    - lookforward_minutes: 前瞻时间窗口（分钟）
    - timeframe: K线时间间隔
    - min_price_diff: 最小价格差异比例
    
    返回:
    - 标签序列: 2=最高点, 0=最低点, 1=中性点
    """
    lookforward_candles = lookforward_minutes // timeframe
    print(f"计算极值点（三分类，宽松模式）：前瞻{lookforward_minutes}分钟（{lookforward_candles}根K线）")
    print(f"使用{percentile_threshold}%/{100-percentile_threshold}%百分位数判断极值点，最小价格差异: {min_price_diff*100:.1f}%")
    
    extreme_labels = []
    extreme_indices = []
    
    # 使用NumPy进行向量化操作
    high_prices = df['high'].to_numpy()
    low_prices = df['low'].to_numpy()
    
    for i in range(len(df) - lookforward_candles):
        if i % 10000 == 0:
            print(f"处理进度: {i}/{len(df)} ({i/len(df)*100:.1f}%)")
        
        current_high = high_prices[i]
        current_low = low_prices[i]
        
        # 获取未来时间窗口内的价格
        future_highs = high_prices[i+1:i+1+lookforward_candles]
        future_lows = low_prices[i+1:i+1+lookforward_candles]
        
        if len(future_highs) == 0:
            continue
            
        # 放宽极值点定义：使用百分位数而不是绝对极值
        max_future_high = np.max(future_highs)
        min_future_low = np.min(future_lows)
        
        # 计算未来价格的百分位数
        high_percentile = percentile_threshold
        low_percentile = 100 - percentile_threshold
        
        future_high_threshold = np.percentile(future_highs, high_percentile)
        future_low_threshold = np.percentile(future_lows, low_percentile)
        
        # 更宽松的极值点定义
        # 最高点：当前高点 >= 未来高百分位数
        is_highest = current_high >= future_high_threshold
        
        # 最低点：当前低点 <= 未来低百分位数
        is_lowest = current_low <= future_low_threshold
        
        # 可选：添加最小价格差异要求（但更宽松）
        if min_price_diff > 0:
            if is_highest:
                price_diff = (current_high - np.median(future_highs)) / current_high
                is_highest = price_diff >= min_price_diff * 0.5  # 减半要求
            
            if is_lowest:
                price_diff = (np.median(future_lows) - current_low) / current_low
                is_lowest = price_diff >= min_price_diff * 0.5  # 减半要求
        
        # 三分类标签分配
        if is_highest and not is_lowest:
            extreme_labels.append(2)  # 最高点
            extreme_indices.append(df.index[i])
        elif is_lowest and not is_highest:
            extreme_labels.append(0)  # 最低点
            extreme_indices.append(df.index[i])
        else:
            extreme_labels.append(1)  # 中性点（既不是最高也不是最低，或同时是最高和最低）
            extreme_indices.append(df.index[i])
    
    print(f"有效标签数量: {len(extreme_labels)}/{len(df)} ({len(extreme_labels)/len(df)*100:.1f}%)")
    
    if len(extreme_labels) > 0:
        stats = calculate_extreme_statistics(extreme_labels)
        print_extreme_statistics(stats)
    else:
        print("未生成任何有效标签。")
    
    return pd.Series(index=extreme_indices, data=extreme_labels)

def calculate_extreme_statistics(labels):
    """计算极值点标签的统计信息（三分类）"""
    labels_array = np.array(labels)
    total = len(labels_array)
    highest_count = np.sum(labels_array == 2)
    neutral_count = np.sum(labels_array == 1)
    lowest_count = np.sum(labels_array == 0)
    
    return {
        'total': total,
        'highest_count': highest_count,
        'neutral_count': neutral_count,
        'lowest_count': lowest_count,
        'highest_ratio': highest_count / total if total > 0 else 0,
        'neutral_ratio': neutral_count / total if total > 0 else 0,
        'lowest_ratio': lowest_count / total if total > 0 else 0
    }

def print_extreme_statistics(stats):
    """打印极值点统计信息（三分类）"""
    print(f"极值点标签统计（三分类）:")
    print(f"  总数: {stats['total']}")
    print(f"  最高点 (2): {stats['highest_count']} ({stats['highest_ratio']*100:.1f}%)")
    print(f"  中性点 (1): {stats['neutral_count']} ({stats['neutral_ratio']*100:.1f}%)")
    print(f"  最低点 (0): {stats['lowest_count']} ({stats['lowest_ratio']*100:.1f}%)")
    
    # 检查类别平衡性
    if stats['total'] > 0:
        ratios = [stats['highest_ratio'], stats['neutral_ratio'], stats['lowest_ratio']]
        balance_ratio = min(ratios) / max(ratios) if max(ratios) > 0 else 0
        print(f"  类别平衡度: {balance_ratio:.3f} (1.0为完全平衡)")

def prepare_features_and_labels(df, symbol='ETHUSDT', market='spot', min_price_diff=0.005, percentile_threshold=90):
    """准备特征和标签"""
    print("在完整数据集上计算增强特征...")
    df_with_features = calculate_features(df.copy(), timeframe=TIMEFRAME_MINUTES)
    
    print("创建极值点标签...")
    extreme_labels = calculate_extreme_points(
        df, MAX_LOOKFORWARD_MINUTES, TIMEFRAME_MINUTES, 
        min_price_diff=min_price_diff, percentile_threshold=percentile_threshold
    )
    
    print("合并特征与标签...")
    df_combined = df_with_features.join(extreme_labels.rename('extreme_point'), how='inner')
    df_clean = df_combined.dropna()
    print(f"清理NaN后剩余 {len(df_clean)} 条记录")
    return df_clean

def split_data(df_clean):
    """按时间顺序分割数据"""
    train_size = int(len(df_clean) * 0.70)
    val_size = int(len(df_clean) * 0.15)
    train_df = df_clean.iloc[:train_size]
    val_df = df_clean.iloc[train_size:train_size + val_size]
    test_df = df_clean.iloc[train_size + val_size:]
    
    print(f"训练集: {len(train_df)} ({train_df.index.min()} to {train_df.index.max()})")
    print(f"验证集: {len(val_df)} ({val_df.index.min()} to {val_df.index.max()})")
    print(f"测试集: {len(test_df)} ({test_df.index.min()} to {test_df.index.max()})")
    return train_df, val_df, test_df

def balance_dataset(X, y, target_col='extreme_point', max_neutral_ratio=0.6):
    """平衡数据集，减少中性点的比例"""
    # 确保X和y的索引一致
    common_index = X.index.intersection(y.index)
    X_aligned = X.loc[common_index]
    y_aligned = y.loc[common_index]
    
    # 重置索引以避免索引问题
    X_reset = X_aligned.reset_index(drop=True)
    y_reset = y_aligned.reset_index(drop=True)
    
    # 统计各类别数量
    class_counts = y_reset.value_counts().sort_index()
    print(f"原始类别分布: {dict(class_counts)}")
    
    # 检查是否有足够的极值点
    if 0 not in class_counts or 2 not in class_counts:
        print("⚠️ 警告: 缺少某些极值点类别，跳过数据平衡")
        return X_aligned, y_aligned
    
    # 计算目标样本数
    extreme_count = class_counts.get(0, 0) + class_counts.get(2, 0)  # 最低点 + 最高点
    if extreme_count == 0:
        print("⚠️ 警告: 没有极值点，跳过数据平衡")
        return X_aligned, y_aligned
        
    max_neutral_count = int(extreme_count * max_neutral_ratio / (1 - max_neutral_ratio))
    
    # 如果中性点太多，进行下采样
    if class_counts.get(1, 0) > max_neutral_count:
        # 获取各类别的索引
        neutral_mask = (y_reset == 1)
        extreme_mask = (y_reset != 1)
        
        neutral_indices = np.where(neutral_mask)[0]
        extreme_indices = np.where(extreme_mask)[0]
        
        # 随机采样中性点
        np.random.seed(42)  # 确保可重复性
        sampled_neutral_indices = np.random.choice(
            neutral_indices, size=min(max_neutral_count, len(neutral_indices)), replace=False
        )
        
        # 合并索引
        final_indices = np.concatenate([extreme_indices, sampled_neutral_indices])
        final_indices.sort()  # 保持顺序
        
        # 创建平衡后的数据集
        X_balanced = X_reset.iloc[final_indices]
        y_balanced = y_reset.iloc[final_indices]
        
        # 恢复原始索引
        original_indices = common_index[final_indices]
        X_balanced.index = original_indices
        y_balanced.index = original_indices
        
        print(f"平衡后类别分布: {dict(y_balanced.value_counts().sort_index())}")
        
        return X_balanced, y_balanced
    else:
        print("数据集已经相对平衡，无需采样")
        return X_aligned, y_aligned

def time_series_cross_validation(df_clean, features, target='extreme_point', n_splits=5, n_trials=50):
    """使用Optuna进行时序交叉验证（三分类）"""
    print(f"\n=== 开始使用 Optuna 进行时序交叉验证 (n_splits={n_splits}, n_trials={n_trials}) ===")
    tscv = TimeSeriesSplit(n_splits=n_splits)
    X = df_clean[features]
    y = df_clean[target]

    def objective(trial):
        params = {
            'objective': 'multiclass',
            'num_class': 3,  # 三个类别：0, 1, 2
            'metric': 'multi_logloss',
            'random_state': 42,
            'verbose': -1,
            'n_jobs': -1,
            'class_weight': 'balanced',  # 自动平衡类别权重
            'n_estimators': trial.suggest_int('n_estimators', 500, 1500),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.1, log=True),
            'max_depth': trial.suggest_int('max_depth', 5, 10),
            'num_leaves': trial.suggest_int('num_leaves', 20, 100),
            'min_child_samples': trial.suggest_int('min_child_samples', 20, 80),
            'subsample': trial.suggest_float('subsample', 0.7, 1.0),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.7, 1.0),
        }
        
        fold_scores = []
        for train_idx, val_idx in tscv.split(X):
            X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
            y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]
            
            try:
                lgbm = lgb.LGBMClassifier(**params)
                lgbm.fit(X_train_fold, y_train_fold, 
                        eval_set=[(X_val_fold, y_val_fold)], 
                        eval_metric='multi_logloss',
                        callbacks=[lgb.early_stopping(stopping_rounds=30, verbose=False)])
                
                y_pred = lgbm.predict(X_val_fold)
                
                # 计算极值点的F1分数（更关注极值点的识别能力）
                from sklearn.metrics import f1_score
                extreme_mask = (y_val_fold != 1)  # 非中性点
                if extreme_mask.sum() > 0:
                    extreme_f1 = f1_score(y_val_fold[extreme_mask], y_pred[extreme_mask], average='weighted')
                    fold_scores.append(1 - extreme_f1)  # 转换为损失
                else:
                    fold_scores.append(1.0)  # 如果没有极值点，给最大损失
            except Exception as e:
                continue
        
        return np.mean(fold_scores) if fold_scores else float('inf')

    optuna.logging.set_verbosity(optuna.logging.WARNING)
    study = optuna.create_study(direction='minimize')
    study.optimize(objective, n_trials=n_trials)

    print(f"=== Optuna 交叉验证完成 ===")
    if study.best_value == float('inf'):
        print("⚠️ 警告: Optuna 未找到有效的参数组合，将使用默认参数。")
        return {}, float('inf')
    else:
        print(f"最佳准确率: {(1-study.best_value)*100:.2f}%")
        print(f"最佳参数: {study.best_params}")
    
    return study.best_params, study.best_value

def train_model(args):
    """主训练函数"""
    print(f"开始训练极值点预测模型（三分类） - {MODEL_BASENAME.replace('_', ' ').title()}")
    
    # 加载数据
    df = load_data_for_training(
        args.coin, args.db_path, args.symbol, args.interval, args.market, 
        start_time=args.start_time, end_time=args.end_time
    )
    if df is None:
        print("❌ 数据加载失败，退出训练。")
        return
    
    df_clean = prepare_features_and_labels(df, args.symbol, args.market, args.min_price_diff, args.percentile_threshold)
    train_df, val_df, test_df = split_data(df_clean)

    target = 'extreme_point'
    X_train, y_train = train_df.drop(columns=[target]), train_df[target]
    X_val, y_val = val_df.drop(columns=[target]), val_df[target]
    X_test, y_test = test_df.drop(columns=[target]), test_df[target]
    
    # 平衡训练数据集
    print(f"\n🔄 平衡训练数据集...")
    X_train, y_train = balance_dataset(X_train, y_train, target)

    # 获取特征列表（排除目标列）
    all_features = get_feature_list(df_clean, time_frame=TIMEFRAME_MINUTES)
    features = [f for f in all_features if f != target and f in X_train.columns]
    print(f"可用特征数量: {len(features)}/{len(all_features)}")

    print(f"\n📊 训练数据统计:")
    train_stats = calculate_extreme_statistics(y_train.values)
    print_extreme_statistics(train_stats)

    # 模型训练
    best_params = {}
    best_cv_score = float('inf')
    use_time_series_cv = not args.no_time_series_cv

    if use_time_series_cv:
        print(f"🔍 使用时序交叉验证对 {len(features)} 个特征进行超参数搜索...")
        train_val_df = pd.concat([train_df, val_df])
        best_params, best_cv_score = time_series_cross_validation(
            train_val_df, features, target, n_splits=args.cv_splits, n_trials=args.cv_trials
        )
    else:
        print(f"\n⚠️  禁用时序交叉验证，使用默认参数...")
    
    # 设置最终参数
    final_params = {
        'objective': 'multiclass',
        'num_class': 3,
        'metric': 'multi_logloss',
        'n_jobs': -1,
        'random_state': 42,
        'n_estimators': 1000,
        'learning_rate': 0.05,
        'class_weight': 'balanced'  # 自动平衡类别权重
    }
    final_params.update(best_params)

    print(f"\n开始使用 {len(features)} 个特征训练LightGBM三分类模型...")
    lgbm = lgb.LGBMClassifier(**final_params)
    lgbm.fit(X_train[features], y_train, 
             eval_set=[(X_val[features], y_val)], 
             eval_metric='multi_logloss',
             callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False)])

    print("模型训练完成，开始评估...")
    
    # 在验证集上评估
    val_predictions = lgbm.predict(X_val[features])
    val_proba = lgbm.predict_proba(X_val[features])
    
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report
    
    val_accuracy = accuracy_score(y_val, val_predictions)
    val_precision = precision_score(y_val, val_predictions, average='weighted')
    val_recall = recall_score(y_val, val_predictions, average='weighted')
    val_f1 = f1_score(y_val, val_predictions, average='weighted')
    
    print(f"验证集性能:")
    print(f"  准确率: {val_accuracy:.4f}")
    print(f"  精确率 (加权): {val_precision:.4f}")
    print(f"  召回率 (加权): {val_recall:.4f}")
    print(f"  F1分数 (加权): {val_f1:.4f}")
    
    print(f"\n详细分类报告:")
    print(classification_report(y_val, val_predictions, target_names=['最低点', '中性点', '最高点']))

    # 在测试集上评估
    evaluate_on_test_set(lgbm, X_test, y_test, test_df, features)
    
    # 保存模型和配置
    save_model_and_config(lgbm, features, len(X_train), len(X_val), len(X_test), {
        'val_accuracy': float(val_accuracy),
        'val_precision': float(val_precision),
        'val_recall': float(val_recall),
        'val_f1': float(val_f1),
        'best_cv_score': float(best_cv_score) if best_cv_score != float('inf') else None,
        'best_cv_params': best_params
    })
    
    analyze_feature_importance(lgbm, features)

def evaluate_on_test_set(model, X_test, y_test, test_df, features):
    """在测试集上评估模型（三分类）"""
    print(f"\n--- 测试集评估 ---")
    
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report
    
    X_test_final = X_test[features]
    test_predictions = model.predict(X_test_final)
    test_proba = model.predict_proba(X_test_final)
    
    # 计算分类指标
    test_accuracy = accuracy_score(y_test, test_predictions)
    test_precision = precision_score(y_test, test_predictions, average='weighted')
    test_recall = recall_score(y_test, test_predictions, average='weighted')
    test_f1 = f1_score(y_test, test_predictions, average='weighted')
    
    print(f"测试集性能:")
    print(f"  准确率: {test_accuracy:.4f}")
    print(f"  精确率 (加权): {test_precision:.4f}")
    print(f"  召回率 (加权): {test_recall:.4f}")
    print(f"  F1分数 (加权): {test_f1:.4f}")
    
    # 详细分类报告
    print(f"\n详细分类报告:")
    print(classification_report(y_test, test_predictions, target_names=['最低点', '中性点', '最高点']))
    
    # 混淆矩阵
    cm = confusion_matrix(y_test, test_predictions)
    print(f"\n混淆矩阵:")
    print(f"  实际\\预测  最低点(0)  中性点(1)  最高点(2)")
    for i, actual_label in enumerate(['最低点(0)', '中性点(1)', '最高点(2)']):
        print(f"  {actual_label:8s}    {cm[i,0]:6d}    {cm[i,1]:6d}    {cm[i,2]:6d}")

    # 计算交易收益
    print(f"\n--- 交易收益分析 ---")
    results_log = []
    label_names = {0: '最低点', 1: '中性点', 2: '最高点'}
    
    # 计算4小时后的价格变化
    lookforward_candles = MAX_LOOKFORWARD_MINUTES // TIMEFRAME_MINUTES
    
    for i in range(len(test_predictions)):
        current_timestamp = test_df.index[i]
        current_price = test_df.iloc[i]['close']
        predicted_class = test_predictions[i]
        actual_class = y_test.iloc[i]
        
        # 计算4小时后的价格
        future_price = None
        price_change_pct = 0.0
        trading_return = 0.0
        trading_action = 'Hold'
        
        # 找到4小时后的价格
        future_idx = i + lookforward_candles
        if future_idx < len(test_df):
            future_price = test_df.iloc[future_idx]['close']
            price_change_pct = (future_price - current_price) / current_price
            
            # 根据预测执行交易策略
            if predicted_class == 0:  # 预测最低点，做多
                trading_return = price_change_pct  # 价格上涨为正收益
                trading_action = 'Long'
            elif predicted_class == 2:  # 预测最高点，做空
                trading_return = -price_change_pct  # 价格下跌为正收益
                trading_action = 'Short'
            else:  # 预测中性点，不交易
                trading_return = 0.0
                trading_action = 'Hold'
        
        results_log.append({
            'Timestamp': current_timestamp,
            'ClosePrice': current_price,
            'FuturePrice': future_price,
            'PriceChangePct': price_change_pct * 100,  # 转换为百分比
            'PredictedClass': predicted_class,
            'ActualClass': actual_class,
            'ProbaLowest': test_proba[i][0],
            'ProbaNeutral': test_proba[i][1],
            'ProbaHighest': test_proba[i][2],
            'Correct': predicted_class == actual_class,
            'PredictedLabel': label_names[predicted_class],
            'ActualLabel': label_names[actual_class],
            'TradingAction': trading_action,
            'TradingReturnPct': trading_return * 100,  # 转换为百分比
        })

    results_df = pd.DataFrame(results_log)
    
    # 计算交易统计
    trading_stats = calculate_trading_statistics(results_df)
    print_trading_statistics(trading_stats)
    
    # 保存结果
    results_filename = os.path.join(get_continuous_output_dir(), f'test_results_standalone_{MODEL_BASENAME}.csv')
    results_df.to_csv(results_filename, index=False, float_format='%.6f')
    print(f"\n详细测试结果已保存到: {results_filename}")

def calculate_trading_statistics(results_df):
    """计算交易统计信息"""
    # 过滤出实际交易的记录（非Hold）
    trades_df = results_df[results_df['TradingAction'] != 'Hold'].copy()
    
    if len(trades_df) == 0:
        return {'total_trades': 0}
    
    # 基本统计
    total_trades = len(trades_df)
    long_trades = len(trades_df[trades_df['TradingAction'] == 'Long'])
    short_trades = len(trades_df[trades_df['TradingAction'] == 'Short'])
    
    # 收益统计
    total_return = trades_df['TradingReturnPct'].sum()
    avg_return = trades_df['TradingReturnPct'].mean()
    win_trades = len(trades_df[trades_df['TradingReturnPct'] > 0])
    lose_trades = len(trades_df[trades_df['TradingReturnPct'] < 0])
    win_rate = win_trades / total_trades if total_trades > 0 else 0
    
    # 最大收益和损失
    max_win = trades_df['TradingReturnPct'].max()
    max_loss = trades_df['TradingReturnPct'].min()
    
    # 按交易类型分组统计
    long_stats = trades_df[trades_df['TradingAction'] == 'Long']['TradingReturnPct']
    short_stats = trades_df[trades_df['TradingAction'] == 'Short']['TradingReturnPct']
    
    return {
        'total_trades': total_trades,
        'long_trades': long_trades,
        'short_trades': short_trades,
        'total_return_pct': total_return,
        'avg_return_pct': avg_return,
        'win_trades': win_trades,
        'lose_trades': lose_trades,
        'win_rate': win_rate,
        'max_win_pct': max_win,
        'max_loss_pct': max_loss,
        'long_avg_return': long_stats.mean() if len(long_stats) > 0 else 0,
        'short_avg_return': short_stats.mean() if len(short_stats) > 0 else 0,
        'long_win_rate': len(long_stats[long_stats > 0]) / len(long_stats) if len(long_stats) > 0 else 0,
        'short_win_rate': len(short_stats[short_stats > 0]) / len(short_stats) if len(short_stats) > 0 else 0
    }

def print_trading_statistics(stats):
    """打印交易统计信息"""
    if stats['total_trades'] == 0:
        print("没有执行任何交易")
        return
    
    print(f"交易统计:")
    print(f"  总交易次数: {stats['total_trades']}")
    print(f"  做多次数: {stats['long_trades']}")
    print(f"  做空次数: {stats['short_trades']}")
    print(f"  总收益: {stats['total_return_pct']:.2f}%")
    print(f"  平均收益: {stats['avg_return_pct']:.3f}%")
    print(f"  胜率: {stats['win_rate']*100:.1f}% ({stats['win_trades']}/{stats['total_trades']})")
    print(f"  最大单次收益: {stats['max_win_pct']:.2f}%")
    print(f"  最大单次损失: {stats['max_loss_pct']:.2f}%")
    
    print(f"\n按交易类型分析:")
    print(f"  做多平均收益: {stats['long_avg_return']:.3f}%")
    print(f"  做空平均收益: {stats['short_avg_return']:.3f}%")
    print(f"  做多胜率: {stats['long_win_rate']*100:.1f}%")
    print(f"  做空胜率: {stats['short_win_rate']*100:.1f}%")

def save_model_and_config(model, features, train_size, val_size, test_size, extra_config=None):
    """保存模型和配置"""
    output_dir = get_continuous_output_dir()
    model_file = os.path.join(output_dir, f'standalone_{MODEL_BASENAME}_model.joblib')
    config_file = os.path.join(output_dir, f'standalone_{MODEL_BASENAME}_config.json')
    
    joblib.dump(model, model_file)
    
    config = {
        'model_type': f'LGBM_Standalone_{MODEL_BASENAME}_3Class',
        'target_description': f'predict_extreme_points_3class_lookforward_{MAX_LOOKFORWARD_MINUTES}min',
        'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'train_size': train_size,
        'val_size': val_size,
        'test_size': test_size,
        'max_lookforward_minutes': MAX_LOOKFORWARD_MINUTES,
        'timeframe_minutes': TIMEFRAME_MINUTES,
        'feature_list': features,
        'algorithm_version': 'standalone_3class',
        'class_labels': {0: '最低点', 1: '中性点', 2: '最高点'},
        'trading_thresholds': {
            'long_conditions': {
                'min_proba_lowest': 0.4,    # 最低点概率至少40%
                'max_proba_neutral': 0.5,   # 中性点概率最多50%
                'max_proba_highest': 0.3    # 最高点概率最多30%
            },
            'short_conditions': {
                'max_proba_lowest': 0.3,    # 最低点概率最多30%
                'max_proba_neutral': 0.5,   # 中性点概率最多50%
                'min_proba_highest': 0.4    # 最高点概率至少40%
            },
            'description': '概率阈值过滤条件，只有满足条件的预测才会执行交易'
        }
    }
    
    if extra_config:
        config.update(extra_config)
    
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"\n✅ 模型和配置已保存")
    print(f"模型文件: {model_file}")
    print(f"配置文件: {config_file}")

def analyze_feature_importance(lgbm, features):
    """分析特征重要性"""
    output_dir = get_continuous_output_dir()
    importance_df = pd.DataFrame({
        'feature': features,
        'importance': lgbm.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("\n" + "="*20 + " 特征重要性 (Top 15) " + "="*20)
    print(importance_df.head(15).to_string(index=False))
    
    importance_file = os.path.join(output_dir, f'feature_importance_standalone_{MODEL_BASENAME}.csv')
    importance_df.to_csv(importance_file, index=False)
    print(f"\n完整特征重要性已保存到 {importance_file}")

def main():
    """主函数"""
    # 导入版本信息
    try:
        from VERSION import __version__, print_version_info
        import sys
        if '--version' in sys.argv:
            print_version_info()
            return
    except ImportError:
        __version__ = "1.0.0"
    
    parser = argparse.ArgumentParser(
        description=f"独立极值点预测模型训练器（三分类） v{__version__}",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python train_standalone.py --coin ETH                    # 基础训练
  python train_standalone.py --coin ETH --no-time-series-cv # 快速训练
  python train_standalone.py --coin BTC --cv-trials 50     # 自定义参数
  python train_standalone.py --version                     # 显示版本信息
        """
    )
    parser.add_argument("--coin", default="ETH", help="币种名称 (如: ETH, DOT, SUI)")
    parser.add_argument("--lookforward-hours", type=float, default=24.0, help="前瞻时间（小时，默认: 4.0）")
    parser.add_argument("--min-price-diff", type=float, default=0.002, help="最小价格差异比例（默认: 0.002 = 0.2%）")
    parser.add_argument("--percentile-threshold", type=float, default=90, help="百分位数阈值（默认: 90，即90%/10%）")
    parser.add_argument("--no-time-series-cv", action='store_true', help="禁用时序交叉验证")
    parser.add_argument("--cv-splits", type=int, default=5, help="CV分割数")
    parser.add_argument("--cv-trials", type=int, default=30, help="Optuna尝试次数")
    parser.add_argument("--db-path", default='../coin_data.db', help="SQLite数据库路径")
    parser.add_argument("--symbol", help="交易对符号")
    parser.add_argument("--interval", help="时间间隔")
    parser.add_argument("--market", choices=['spot', 'futures'], default='spot', help="市场类型")
    parser.add_argument("--start-time", help="数据开始时间 (YYYY-MM-DD)")
    parser.add_argument("--end-time", help="数据结束时间 (YYYY-MM-DD)")
    parser.add_argument("--version", action='store_true', help="显示版本信息")

    args = parser.parse_args()
    
    if args.version:
        try:
            from VERSION import print_version_info
            print_version_info()
        except ImportError:
            print("连续移动预测模型 v1.0.0")
        return

    # 获取配置
    coin_config = get_continuous_config(f"{args.coin}_CONTINUOUS_1M")
    if coin_config is None:
        coin_config = get_continuous_config(args.coin)
        if coin_config is None:
            print(f"❌ 未找到币种 {args.coin} 的配置")
            exit(1)

    # 设置全局变量
    global TIMEFRAME_MINUTES, MAX_LOOKFORWARD_MINUTES, MODEL_BASENAME
    TIMEFRAME_MINUTES = coin_config['timeframe_minutes']
    MAX_LOOKFORWARD_MINUTES = int(args.lookforward_hours * 60)  # 转换为分钟
    MODEL_BASENAME = coin_config['model_basename']

    # 打印训练摘要
    print(f"="*60)
    print(f"极值点预测模型训练 - {args.coin}")
    print(f"="*60)
    print(f"时间框架: {TIMEFRAME_MINUTES}分钟")
    print(f"前瞻时间: {MAX_LOOKFORWARD_MINUTES}分钟 ({args.lookforward_hours}小时)")
    print(f"最小价格差异: {args.min_price_diff*100:.1f}%")
    print(f"百分位数阈值: {args.percentile_threshold}%/{100-args.percentile_threshold}%")
    print(f"模型基名: {MODEL_BASENAME}")
    print(f"算法版本: 独立三分类版本（类别平衡，宽松模式）")
    print(f"训练目标: 预测未来{args.lookforward_hours}小时内的极值点（最高点/中性点/最低点）")
    print(f"="*60)
    
    train_model(args)

if __name__ == '__main__':
    main()