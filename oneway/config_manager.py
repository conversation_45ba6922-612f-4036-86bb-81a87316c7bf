#!/usr/bin/env python3
# config_manager.py
# 模型配置管理工具

import json
import os
import argparse
from standalone_utils import get_continuous_output_dir

def load_config(model_basename):
    """加载模型配置"""
    output_dir = get_continuous_output_dir()
    config_file = os.path.join(output_dir, f'standalone_{model_basename}_config.json')
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return None, None
    
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    return config, config_file

def save_config(config, config_file):
    """保存模型配置"""
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    print(f"✅ 配置已保存到: {config_file}")

def show_config(model_basename):
    """显示当前配置"""
    config, config_file = load_config(model_basename)
    if config is None:
        return
    
    print(f"\n{'='*60}")
    print(f"模型配置: {model_basename}")
    print(f"{'='*60}")
    
    print(f"模型类型: {config.get('model_type', 'Unknown')}")
    print(f"训练日期: {config.get('training_date', 'Unknown')}")
    print(f"前瞻时间: {config.get('max_lookforward_minutes', 0)} 分钟")
    print(f"时间框架: {config.get('timeframe_minutes', 0)} 分钟")
    
    if 'trading_thresholds' in config:
        thresholds = config['trading_thresholds']
        print(f"\n📊 交易阈值配置:")
        
        long_cond = thresholds['long_conditions']
        print(f"做多条件:")
        print(f"  最低点概率 ≥ {long_cond['min_proba_lowest']}")
        print(f"  中性点概率 ≤ {long_cond['max_proba_neutral']}")
        print(f"  最高点概率 ≤ {long_cond['max_proba_highest']}")
        
        short_cond = thresholds['short_conditions']
        print(f"做空条件:")
        print(f"  最低点概率 ≤ {short_cond['max_proba_lowest']}")
        print(f"  中性点概率 ≤ {short_cond['max_proba_neutral']}")
        print(f"  最高点概率 ≥ {short_cond['min_proba_highest']}")
    else:
        print(f"\n⚠️ 未配置交易阈值")

def update_thresholds(model_basename, **kwargs):
    """更新交易阈值"""
    config, config_file = load_config(model_basename)
    if config is None:
        return
    
    # 确保有交易阈值配置
    if 'trading_thresholds' not in config:
        config['trading_thresholds'] = {
            'long_conditions': {
                'min_proba_lowest': 0.4,
                'max_proba_neutral': 0.5,
                'max_proba_highest': 0.3
            },
            'short_conditions': {
                'max_proba_lowest': 0.3,
                'max_proba_neutral': 0.5,
                'min_proba_highest': 0.4
            },
            'description': '概率阈值过滤条件'
        }
    
    thresholds = config['trading_thresholds']
    
    # 更新做多条件
    if 'long_min_lowest' in kwargs:
        thresholds['long_conditions']['min_proba_lowest'] = kwargs['long_min_lowest']
    if 'long_max_neutral' in kwargs:
        thresholds['long_conditions']['max_proba_neutral'] = kwargs['long_max_neutral']
    if 'long_max_highest' in kwargs:
        thresholds['long_conditions']['max_proba_highest'] = kwargs['long_max_highest']
    
    # 更新做空条件
    if 'short_max_lowest' in kwargs:
        thresholds['short_conditions']['max_proba_lowest'] = kwargs['short_max_lowest']
    if 'short_max_neutral' in kwargs:
        thresholds['short_conditions']['max_proba_neutral'] = kwargs['short_max_neutral']
    if 'short_min_highest' in kwargs:
        thresholds['short_conditions']['min_proba_highest'] = kwargs['short_min_highest']
    
    save_config(config, config_file)
    print(f"\n✅ 交易阈值已更新")

def main():
    parser = argparse.ArgumentParser(
        description="模型配置管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 查看配置
  python config_manager.py --model eth_5m --show
  
  # 更新做多条件
  python config_manager.py --model eth_5m --long-min-lowest 0.5 --long-max-neutral 0.4
  
  # 更新做空条件
  python config_manager.py --model eth_5m --short-min-highest 0.6 --short-max-lowest 0.2
  
  # 设置保守策略（高阈值）
  python config_manager.py --model eth_5m --conservative
  
  # 设置激进策略（低阈值）
  python config_manager.py --model eth_5m --aggressive
        """
    )
    
    parser.add_argument("--model", required=True, help="模型基名")
    parser.add_argument("--show", action='store_true', help="显示当前配置")
    
    # 做多条件参数
    parser.add_argument("--long-min-lowest", type=float, help="做多：最低点概率最小值")
    parser.add_argument("--long-max-neutral", type=float, help="做多：中性点概率最大值")
    parser.add_argument("--long-max-highest", type=float, help="做多：最高点概率最大值")
    
    # 做空条件参数
    parser.add_argument("--short-max-lowest", type=float, help="做空：最低点概率最大值")
    parser.add_argument("--short-max-neutral", type=float, help="做空：中性点概率最大值")
    parser.add_argument("--short-min-highest", type=float, help="做空：最高点概率最小值")
    
    # 预设策略
    parser.add_argument("--conservative", action='store_true', help="设置保守策略")
    parser.add_argument("--aggressive", action='store_true', help="设置激进策略")
    
    args = parser.parse_args()
    
    if args.show:
        show_config(args.model)
        return
    
    # 预设策略
    if args.conservative:
        print("🛡️ 设置保守策略（高阈值，少交易）")
        update_thresholds(args.model,
                         long_min_lowest=0.6, long_max_neutral=0.3, long_max_highest=0.2,
                         short_max_lowest=0.2, short_max_neutral=0.3, short_min_highest=0.6)
        show_config(args.model)
        return
    
    if args.aggressive:
        print("⚡ 设置激进策略（低阈值，多交易）")
        update_thresholds(args.model,
                         long_min_lowest=0.35, long_max_neutral=0.6, long_max_highest=0.4,
                         short_max_lowest=0.4, short_max_neutral=0.6, short_min_highest=0.35)
        show_config(args.model)
        return
    
    # 自定义更新
    update_params = {}
    if args.long_min_lowest is not None:
        update_params['long_min_lowest'] = args.long_min_lowest
    if args.long_max_neutral is not None:
        update_params['long_max_neutral'] = args.long_max_neutral
    if args.long_max_highest is not None:
        update_params['long_max_highest'] = args.long_max_highest
    if args.short_max_lowest is not None:
        update_params['short_max_lowest'] = args.short_max_lowest
    if args.short_max_neutral is not None:
        update_params['short_max_neutral'] = args.short_max_neutral
    if args.short_min_highest is not None:
        update_params['short_min_highest'] = args.short_min_highest
    
    if update_params:
        update_thresholds(args.model, **update_params)
        show_config(args.model)
    else:
        show_config(args.model)

if __name__ == '__main__':
    main()