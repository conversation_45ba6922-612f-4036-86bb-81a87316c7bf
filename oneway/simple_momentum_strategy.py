#!/usr/bin/env python3
# simple_momentum_strategy.py
# 简单的价量动量策略回测

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from standalone_utils import load_data_for_training, get_continuous_output_dir
import os

def simple_momentum_backtest(coin='ETH', days=60, volume_multiplier=2.5, price_threshold=0.025):
    """
    简单的价量动量策略回测
    
    策略逻辑:
    1. 当前K线交易量 > 过去20根K线平均交易量的2.5倍
    2. 当前K线涨幅 > 2.5%
    3. 满足条件时，在收盘价买入，下一根K线收盘价卖出
    """
    
    print(f"🚀 简单价量动量策略回测")
    print(f"币种: {coin}, 天数: {days}")
    print(f"策略: 交易量>{volume_multiplier}倍均值 且 涨幅>{price_threshold*100:.1f}% 时买入")
    print(f"持有期: 1根K线")
    
    # 加载数据
    end_time = datetime.now().strftime('%Y-%m-%d')
    start_time = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
    
    df = load_data_for_training(coin, '../coin_data.db', market='spot',
                               start_time=start_time, end_time=end_time)
    
    if df is None or len(df) == 0:
        print("❌ 无法加载数据")
        return None
    
    print(f"📊 数据加载: {len(df)} 根K线")
    
    # 计算指标
    df['volume_ma20'] = df['volume'].rolling(window=20).mean()
    df['price_change'] = (df['close'] - df['open']) / df['open']
    df['volume_ratio'] = df['volume'] / df['volume_ma20']
    
    # 生成交易信号
    df['signal'] = (
        (df['volume_ratio'] > volume_multiplier) & 
        (df['price_change'] > price_threshold) &
        (df['volume_ma20'].notna())
    )
    
    # 计算下一根K线收益
    df['next_return'] = df['close'].pct_change().shift(-1)
    
    # 提取交易记录
    trades = df[df['signal'] == True].copy()
    trades = trades[trades['next_return'].notna()]  # 移除最后一根K线
    
    if len(trades) == 0:
        print("❌ 没有产生交易信号")
        return None
    
    print(f"📈 交易信号: {len(trades)} 次")
    
    # 计算策略表现
    returns = trades['next_return'].values
    total_return = np.sum(returns)
    avg_return = np.mean(returns)
    win_rate = np.sum(returns > 0) / len(returns)
    max_return = np.max(returns)
    min_return = np.min(returns)
    
    # 计算累计收益
    cumulative_returns = np.cumprod(1 + returns) - 1
    
    # 打印结果
    print(f"\n📊 策略表现:")
    print(f"  交易次数: {len(trades)}")
    print(f"  总收益: {total_return*100:.2f}%")
    print(f"  平均收益: {avg_return*100:.3f}%")
    print(f"  胜率: {win_rate*100:.1f}%")
    print(f"  最大收益: {max_return*100:.2f}%")
    print(f"  最大损失: {min_return*100:.2f}%")
    print(f"  最终累计收益: {cumulative_returns[-1]*100:.2f}%")
    
    # 绘制结果
    plot_strategy_results(trades, returns, cumulative_returns, coin, days)
    
    return {
        'trades': len(trades),
        'total_return': total_return,
        'avg_return': avg_return,
        'win_rate': win_rate,
        'max_return': max_return,
        'min_return': min_return,
        'final_return': cumulative_returns[-1]
    }

def plot_strategy_results(trades, returns, cumulative_returns, coin, days):
    """绘制策略结果"""
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(14, 10))
    
    # 图1: 累计收益曲线
    ax1 = axes[0, 0]
    ax1.plot(range(len(cumulative_returns)), cumulative_returns * 100, 'b-', linewidth=2)
    ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax1.set_title('累计收益曲线', fontsize=12, fontweight='bold')
    ax1.set_xlabel('交易次数')
    ax1.set_ylabel('累计收益 (%)')
    ax1.grid(True, alpha=0.3)
    
    # 图2: 单次收益分布
    ax2 = axes[0, 1]
    ax2.hist(returns * 100, bins=30, alpha=0.7, color='green', edgecolor='black')
    ax2.axvline(x=0, color='red', linestyle='--', alpha=0.7)
    ax2.set_title('单次收益分布', fontsize=12, fontweight='bold')
    ax2.set_xlabel('单次收益 (%)')
    ax2.set_ylabel('频次')
    ax2.grid(True, alpha=0.3)
    
    # 图3: 交易量倍数分布
    ax3 = axes[1, 0]
    ax3.hist(trades['volume_ratio'], bins=30, alpha=0.7, color='orange', edgecolor='black')
    ax3.set_title('触发交易的交易量倍数分布', fontsize=12, fontweight='bold')
    ax3.set_xlabel('交易量倍数')
    ax3.set_ylabel('频次')
    ax3.grid(True, alpha=0.3)
    
    # 图4: 价格涨幅分布
    ax4 = axes[1, 1]
    ax4.hist(trades['price_change'] * 100, bins=30, alpha=0.7, color='purple', edgecolor='black')
    ax4.set_title('触发交易的价格涨幅分布', fontsize=12, fontweight='bold')
    ax4.set_xlabel('价格涨幅 (%)')
    ax4.set_ylabel('频次')
    ax4.grid(True, alpha=0.3)
    
    plt.suptitle(f'{coin} 简单价量动量策略 ({days}天)', fontsize=14, fontweight='bold')
    plt.tight_layout()
    
    # 保存图表
    output_dir = get_continuous_output_dir()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    chart_filename = os.path.join(output_dir, f'simple_momentum_{coin}_{timestamp}.png')
    plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
    print(f"📊 图表已保存到: {chart_filename}")
    
    try:
        plt.show()
    except:
        pass
    
    plt.close()

def compare_different_thresholds(coin='ETH', days=60):
    """
    比较不同阈值的策略表现
    """
    print(f"\n🔍 比较不同阈值的策略表现")
    
    # 不同的参数组合
    param_combinations = [
        (20.0, 0.01),  # 2倍交易量, 2%涨幅
        (5, 0.01), # 2.5倍交易量, 2.5%涨幅
        (10, 0.01),  # 3倍交易量, 3%涨幅
    ]
    
    results = []
    
    for vol_mult, price_thresh in param_combinations:
        print(f"\n测试参数: 交易量{vol_mult}倍, 涨幅{price_thresh*100:.1f}%")
        result = simple_momentum_backtest(coin, days, vol_mult, price_thresh)
        
        if result:
            results.append({
                'volume_multiplier': vol_mult,
                'price_threshold': price_thresh,
                'trades': result['trades'],
                'total_return': result['total_return'],
                'avg_return': result['avg_return'],
                'win_rate': result['win_rate'],
                'final_return': result['final_return']
            })
    
    # 显示比较结果
    if results:
        results_df = pd.DataFrame(results)
        print(f"\n📊 参数比较结果:")
        print(results_df.to_string(index=False, float_format='%.4f'))
        
        # 找出最佳参数
        best_idx = results_df['final_return'].idxmax()
        best_params = results_df.iloc[best_idx]
        
        print(f"\n🏆 最佳参数组合:")
        print(f"  交易量倍数: {best_params['volume_multiplier']}")
        print(f"  价格阈值: {best_params['price_threshold']*100:.1f}%")
        print(f"  最终收益: {best_params['final_return']*100:.2f}%")
        print(f"  交易次数: {best_params['trades']}")
        print(f"  胜率: {best_params['win_rate']*100:.1f}%")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1:
        coin = sys.argv[1]
    else:
        coin = 'ETH'
    
    if len(sys.argv) > 2:
        days = int(sys.argv[2])
    else:
        days = 60
    
    # 运行单个策略测试
    simple_momentum_backtest(coin, days)
    
    # 运行参数比较
    compare_different_thresholds(coin, days)