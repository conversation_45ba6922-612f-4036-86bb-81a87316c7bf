#!/usr/bin/env python3
# realtime_standalone.py
# 使用训练好的模型进行实时预测（已修复时区问题）

import pandas as pd
import numpy as np
import joblib
import json
import argparse
import os
from datetime import datetime
import time
import requests
import logging
import sys
import platform
import pytz  # 用于时区转换

# 导入独立工具函数
from standalone_utils import (
    get_continuous_output_dir, calculate_features
)

# --- Binance API 配置 ---
BINANCE_API_ENDPOINTS = [
    "https://api.binance.com",
    "https://api1.binance.com",
    "https://api2.binance.com",
    "https://api3.binance.com",
]

def setup_logging(log_file):
    """设置日志记录器，输出到文件和控制台。"""
    logger = logging.getLogger('RealtimePredictor')
    logger.setLevel(logging.INFO)
    
    if logger.hasHandlers():
        logger.handlers.clear()
        
    formatter = logging.Formatter('%(message)s')
    
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)
    
    return logger

def play_alert_sound():
    """为关键信号播放系统警报声。"""
    try:
        system = platform.system().lower()
        if system == "windows":
            import winsound
            winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        elif system == "darwin":
            os.system('say "发现强买入信号"')
        elif system == "linux":
            os.system('paplay /usr/share/sounds/freedesktop/stereo/complete.oga 2>/dev/null || echo -e "\\a"')
        else:
            print('\a' * 3)
    except Exception as e:
        print('\a' * 3)
        logging.warning(f"播放报警声失败: {e}")

def load_model_and_config(model_basename):
    """加载训练好的模型和配置。"""
    output_dir = get_continuous_output_dir()
    model_file = os.path.join(output_dir, f'standalone_{model_basename}_model.joblib')
    config_file = os.path.join(output_dir, f'standalone_{model_basename}_config.json')
    
    if not os.path.exists(model_file) or not os.path.exists(config_file):
        logging.error(f"模型或配置文件未找到: {model_basename}")
        return None, None
        
    model = joblib.load(model_file)
    with open(config_file, 'r') as f:
        config = json.load(f)
        
    logging.info(f"✅ 模型加载成功: {model_file}")
    logging.info(f"✅ 配置加载成功: {config_file}")
    
    return model, config

def fetch_binance_klines(symbol, interval, limit=1000):
    """从币安获取K线数据。"""
    params = {'symbol': symbol, 'interval': interval, 'limit': limit}
    for url in BINANCE_API_ENDPOINTS:
        try:
            response = requests.get(f"{url}/api/v3/klines", params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume', 'close_time',
                'quote_asset_volume', 'number_of_trades', 'taker_buy_base_asset_volume',
                'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # --- FIX: 这里是关键修改 ---
            # 在转换时间戳时，直接使用 utc=True 参数将其本地化为UTC时区
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
            
            df.set_index('timestamp', inplace=True)
            
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col])
                
            return df
            
        except requests.exceptions.RequestException as e:
            logging.warning(f"从 {url} 获取数据失败: {e}")
            continue
            
    logging.error("所有币安API节点均请求失败。")
    return None

def check_trading_conditions_hardcoded(proba_lowest, proba_neutral, proba_highest, predicted_class):
    """硬编码的交易条件检查（用于生成最终信号）。"""
    if predicted_class == 0:
        return predicted_class
            
    elif predicted_class == 2:
        return predicted_class
        
    return predicted_class

def main():
    parser = argparse.ArgumentParser(
        description="使用训练好的模型进行实时预测（中文日志版）。",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python realtime_standalone.py --model eth_5m --symbol ETHUSDT
"""
    )
    
    parser.add_argument("--model", required=True, help="模型基名 (例如: eth_5m)")
    parser.add_argument("--symbol", required=True, help="交易对 (例如: ETHUSDT)")
    parser.add_argument("--interval", default="5m", help="K线周期")
    
    args = parser.parse_args()
    
    log_file = f'realtime_standalone_{args.model}.log'
    logger = setup_logging(log_file)
    beijing_tz = pytz.timezone('Asia/Shanghai')

    logger.info(f"--- 实时预测脚本启动 [{datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}] ---")
    
    model, config = load_model_and_config(args.model)
    if model is None or config is None:
        return
        
    timeframe_minutes = config['timeframe_minutes']
    feature_list = config['feature_list']
    
    logger.info(f"模型类型: {config.get('model_type', '未知')}")
    logger.info(f"训练日期: {config.get('training_date', '未知')}")
    logger.info(f"特征数量: {len(feature_list)}")
    logger.info("="*50)

    last_kline_timestamp = None
    previous_signal = 1

    while True:
        try:
            df = fetch_binance_klines(args.symbol, args.interval)
            
            if df is None or len(df) < 2:
                logger.warning("数据不足，等待重试...")
                time.sleep(60)
                continue
                
            current_kline_timestamp = df.index[-2]
            if last_kline_timestamp and current_kline_timestamp <= last_kline_timestamp:
                time.sleep(30)
                continue
            
            last_kline_timestamp = current_kline_timestamp
            
            df_with_features = calculate_features(df.copy(), timeframe=timeframe_minutes)
            latest_data = df_with_features.iloc[-2]
            X = latest_data[feature_list].values.reshape(1, -1)
            
            original_prediction = model.predict(X)[0]
            probabilities = model.predict_proba(X)[0]
            
            filtered_prediction = check_trading_conditions_hardcoded(
                probabilities[0], probabilities[1], probabilities[2], original_prediction
            )
            
            log_parts = []
            # 现在 current_kline_timestamp 是带有时区信息的，可以安全转换
            beijing_time_str = current_kline_timestamp.astimezone(beijing_tz).strftime('%H:%M:%S')

            log_parts.append(f"时间: {beijing_time_str}")
            log_parts.append(f"价格: {latest_data['close']:.4f}")
            log_parts.append(f"信息: [低:{probabilities[0]:.2f} 中:{probabilities[1]:.2f} 高:{probabilities[2]:.2f}]")
            
            if filtered_prediction == 0:
                log_parts.append("信号: 🟢 做多")
            elif filtered_prediction == 2:
                log_parts.append("信号: 🔴 做空")
            else:
                log_parts.append("信号: ⚪ 观望")

            is_strong_buy = False
            proba_lowest = probabilities[0]
            if previous_signal == 2 and original_prediction == 0 and proba_lowest >= 0.4:
                is_strong_buy = True
                log_parts.append("强买入信号: 🚨🔄 反转买入")
            
            logger.info(" | ".join(log_parts))

            if is_strong_buy:
                play_alert_sound()

            previous_signal = original_prediction
                
            time.sleep(60)
            
        except KeyboardInterrupt:
            logger.info("="*50)
            logger.info(f"脚本被用户终止 [{datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}]")
            break
        except Exception as e:
            logger.error(f"主循环发生错误: {e}", exc_info=True)
            time.sleep(60)

if __name__ == '__main__':
    main()