#!/usr/bin/env python3
# diagnose_model.py
# 模型诊断工具 - 分析模型性能问题

import pandas as pd
import numpy as np
from sklearn.metrics import mean_squared_error, r2_score
import joblib
import json
import os

def load_test_results(model_basename):
    """加载测试结果"""
    results_file = f"../models/test_results_standalone_{model_basename}.csv"
    if os.path.exists(results_file):
        return pd.read_csv(results_file)
    return None

def analyze_predictions(results_df):
    """分析预测结果"""
    print("=" * 60)
    print("📊 预测结果分析")
    print("=" * 60)
    
    pred = results_df['PredictedMovement'].values
    actual = results_df['ActualMovement'].values
    
    # 基础统计
    print(f"样本数量: {len(pred):,}")
    print(f"预测范围: {pred.min():.4f} 到 {pred.max():.4f}")
    print(f"实际范围: {actual.min():.4f} 到 {actual.max():.4f}")
    print(f"预测标准差: {pred.std():.4f}")
    print(f"实际标准差: {actual.std():.4f}")
    
    # 性能指标
    rmse = np.sqrt(mean_squared_error(actual, pred))
    mae = np.mean(np.abs(actual - pred))
    r2 = r2_score(actual, pred)
    
    print(f"\n📈 性能指标:")
    print(f"RMSE: {rmse:.6f}")
    print(f"MAE: {mae:.6f}")
    print(f"R²: {r2:.6f}")
    
    # 方向准确率
    direction_acc = ((pred > 0) == (actual > 0)).mean() * 100
    print(f"方向准确率: {direction_acc:.2f}%")
    
    # 预测分布分析
    print(f"\n📊 预测分布:")
    print(f"预测为正: {(pred > 0).sum():,} ({(pred > 0).mean()*100:.1f}%)")
    print(f"预测为负: {(pred < 0).sum():,} ({(pred < 0).mean()*100:.1f}%)")
    print(f"预测为零: {(pred == 0).sum():,} ({(pred == 0).mean()*100:.1f}%)")
    
    print(f"\n📊 实际分布:")
    print(f"实际为正: {(actual > 0).sum():,} ({(actual > 0).mean()*100:.1f}%)")
    print(f"实际为负: {(actual < 0).sum():,} ({(actual < 0).mean()*100:.1f}%)")
    print(f"实际为零: {(actual == 0).sum():,} ({(actual == 0).mean()*100:.1f}%)")
    
    return {
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'direction_acc': direction_acc,
        'pred_std': pred.std(),
        'actual_std': actual.std()
    }

def analyze_feature_importance(model_basename):
    """分析特征重要性"""
    importance_file = f"../models/feature_importance_standalone_{model_basename}.csv"
    if not os.path.exists(importance_file):
        print("❌ 特征重要性文件不存在")
        return
    
    print("\n" + "=" * 60)
    print("🔍 特征重要性分析")
    print("=" * 60)
    
    importance_df = pd.read_csv(importance_file)
    
    print("Top 10 重要特征:")
    top_features = importance_df.head(10)
    for i, row in top_features.iterrows():
        print(f"{i+1:2d}. {row['feature']:<25} {row['importance']:>8.1f}")
    
    # 分析特征类型
    feature_types = {
        'volume': 0, 'sma': 0, 'volatility': 0, 'return': 0, 
        'price': 0, 'vwap': 0, 'atr': 0, 'other': 0
    }
    
    for _, row in importance_df.iterrows():
        feature = row['feature'].lower()
        if 'volume' in feature or 'vma' in feature:
            feature_types['volume'] += row['importance']
        elif 'sma' in feature:
            feature_types['sma'] += row['importance']
        elif 'volatility' in feature:
            feature_types['volatility'] += row['importance']
        elif 'return' in feature:
            feature_types['return'] += row['importance']
        elif 'price' in feature:
            feature_types['price'] += row['importance']
        elif 'vwap' in feature:
            feature_types['vwap'] += row['importance']
        elif 'atr' in feature:
            feature_types['atr'] += row['importance']
        else:
            feature_types['other'] += row['importance']
    
    print(f"\n特征类型重要性:")
    for ftype, importance in sorted(feature_types.items(), key=lambda x: x[1], reverse=True):
        if importance > 0:
            print(f"{ftype:<12}: {importance:>8.1f}")

def diagnose_data_quality(results_df):
    """诊断数据质量问题"""
    print("\n" + "=" * 60)
    print("🔍 数据质量诊断")
    print("=" * 60)
    
    pred = results_df['PredictedMovement'].values
    actual = results_df['ActualMovement'].values
    
    # 检查预测变化范围
    pred_range = pred.max() - pred.min()
    actual_range = actual.max() - actual.min()
    
    print(f"预测变化范围: {pred_range:.6f}")
    print(f"实际变化范围: {actual_range:.6f}")
    print(f"范围比率: {pred_range/actual_range:.4f}")
    
    if pred_range < actual_range * 0.1:
        print("⚠️ 问题: 预测变化范围过小，模型可能欠拟合")
    
    # 检查预测集中度
    pred_unique = len(np.unique(np.round(pred, 6)))
    print(f"预测唯一值数量: {pred_unique}")
    
    if pred_unique < len(pred) * 0.01:
        print("⚠️ 问题: 预测值过于集中，模型缺乏多样性")
    
    # 检查零预测比例
    zero_pred_ratio = (np.abs(pred) < 1e-6).mean()
    print(f"接近零的预测比例: {zero_pred_ratio*100:.2f}%")
    
    if zero_pred_ratio > 0.5:
        print("⚠️ 问题: 过多零预测，模型可能学习不充分")
    
    # 检查预测偏差
    pred_mean = pred.mean()
    actual_mean = actual.mean()
    print(f"预测均值: {pred_mean:.6f}")
    print(f"实际均值: {actual_mean:.6f}")
    print(f"均值偏差: {abs(pred_mean - actual_mean):.6f}")

def suggest_improvements(stats):
    """根据诊断结果提供改进建议"""
    print("\n" + "=" * 60)
    print("💡 改进建议")
    print("=" * 60)
    
    suggestions = []
    
    if stats['r2'] < 0:
        suggestions.append("1. R²为负，模型预测不如均值预测")
        suggestions.append("   - 增加更多有效特征")
        suggestions.append("   - 检查特征工程质量")
        suggestions.append("   - 考虑使用不同的模型算法")
    
    if stats['direction_acc'] < 45:
        suggestions.append("2. 方向准确率过低")
        suggestions.append("   - 调整连续移动的定义")
        suggestions.append("   - 增加趋势相关特征")
        suggestions.append("   - 考虑使用分类+回归的混合方法")
    
    if stats['pred_std'] < stats['actual_std'] * 0.1:
        suggestions.append("3. 预测方差过小")
        suggestions.append("   - 减少正则化强度")
        suggestions.append("   - 增加模型复杂度")
        suggestions.append("   - 检查特征缩放是否合适")
    
    if stats['rmse'] > 0.02:
        suggestions.append("4. RMSE过高")
        suggestions.append("   - 优化超参数")
        suggestions.append("   - 增加训练数据")
        suggestions.append("   - 改进特征工程")
    
    suggestions.extend([
        "5. 通用改进建议:",
        "   - 使用更多历史数据",
        "   - 添加市场情绪指标",
        "   - 考虑外部因素(新闻、事件等)",
        "   - 尝试集成学习方法",
        "   - 调整连续移动的时间窗口",
        "   - 使用不同的回撤阈值"
    ])
    
    for suggestion in suggestions:
        print(suggestion)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="模型诊断工具")
    parser.add_argument("--model", default="eth_5m", help="模型基础名称")
    
    args = parser.parse_args()
    
    print(f"🔍 诊断模型: {args.model}")
    
    # 加载测试结果
    results_df = load_test_results(args.model)
    if results_df is None:
        print("❌ 未找到测试结果文件")
        return
    
    # 分析预测结果
    stats = analyze_predictions(results_df)
    
    # 分析特征重要性
    analyze_feature_importance(args.model)
    
    # 诊断数据质量
    diagnose_data_quality(results_df)
    
    # 提供改进建议
    suggest_improvements(stats)
    
    print(f"\n🎯 诊断完成！请根据建议改进模型。")

if __name__ == '__main__':
    main()