# 🚀 连续移动预测模型快速启动指南

## 📋 5分钟快速开始

### 1. 检查环境
```bash
# 确保在 oneway 目录中
cd oneway

# 检查Python包
python -c "import pandas, numpy, lightgbm, sklearn, optuna, joblib; print('✅ 所有包已安装')"
```

### 2. 获取样本数据
```bash
# 获取ETH 5分钟数据 (5000条)
python get_sample_data.py --symbol ETHUSDT --limit 5000

# 检查数据库内容
python check_database.py --db ../coin_data.db
```

### 3. 运行测试
```bash
# 测试所有功能
python test_standalone.py
```

### 4. 训练模型
```bash
# 快速训练 (约2-5分钟)
python train_standalone.py --coin ETH --no-time-series-cv

# 完整训练 (约10-20分钟)
python train_standalone.py --coin ETH --cv-trials 30
```

### 5. 进行预测
```bash
# 预测最新数据
python predict_standalone.py eth_5m --coin ETH --latest 10
```

## 🎯 一键运行示例

```bash
# 运行完整示例 (测试+训练+预测)
python run_example.py --mode full

# 仅运行测试
python run_example.py --mode test

# 仅训练模型
python run_example.py --mode train
```

## 📁 文件说明

| 文件 | 用途 | 必需 |
|------|------|------|
| `train_standalone.py` | 主训练脚本 | ✅ |
| `predict_standalone.py` | 预测脚本 | ✅ |
| `standalone_utils.py` | 工具函数 | ✅ |
| `continuous_config.json` | 配置文件 | ✅ |
| `test_standalone.py` | 功能测试 | 推荐 |
| `run_example.py` | 一键运行 | 推荐 |

## ⚙️ 参数说明

### 训练参数
- `--coin ETH`: 币种 (ETH/BTC/DOT/SUI)
- `--max-retracement 0.01`: 最大回撤1%
- `--cv-trials 30`: 优化试验次数
- `--no-time-series-cv`: 禁用交叉验证(快速)

### 预测参数
- `--latest 10`: 显示最新10个预测
- `--save`: 保存预测结果
- `--start-time 2024-01-01`: 指定开始时间

## 🔧 常见问题

### Q: 没有数据库文件怎么办？
A: 测试脚本会使用模拟数据，可以正常运行训练流程

### Q: 训练时间太长？
A: 使用 `--no-time-series-cv` 参数快速训练

### Q: 预测结果如何理解？
A: 
- 正值表示预测上涨，负值表示预测下跌
- 绝对值越大表示预测幅度越大
- >2%为高信心，1-2%为中信心，<1%为低信心

### Q: 如何提高预测准确性？
A: 
1. 增加训练数据量
2. 调整 `max_retracement` 参数
3. 使用更多的 `cv_trials`
4. 定期重新训练模型

## 📊 输出文件

训练完成后会在 `../models/` 目录生成：
- `standalone_eth_5m_model.joblib`: 训练好的模型
- `standalone_eth_5m_config.json`: 模型配置
- `test_results_standalone_eth_5m.csv`: 测试结果
- `feature_importance_standalone_eth_5m.csv`: 特征重要性

## 🎨 自定义配置

编辑 `continuous_config.json` 添加新币种：
```json
{
  "YOUR_COIN_CONTINUOUS_5M": {
    "api_symbol": "YOURUSDT",
    "display_name": "YOUR/USDT 连续移动",
    "timeframe_minutes": 5,
    "max_retracement": 0.01,
    "max_lookforward_minutes": 240,
    "model_basename": "your_5m"
  }
}
```

## ⚡ 性能优化

### 快速训练 (1-2分钟)
```bash
python train_standalone.py --coin ETH --no-time-series-cv --cv-trials 0
```

### 平衡训练 (5-10分钟)
```bash
python train_standalone.py --coin ETH --cv-trials 20
```

### 完整训练 (15-30分钟)
```bash
python train_standalone.py --coin ETH --cv-trials 100
```

## 🚨 注意事项

1. **风险提示**: 模型预测仅供参考，不构成投资建议
2. **数据质量**: 确保使用高质量的历史数据
3. **定期更新**: 建议定期重新训练以适应市场变化
4. **参数调优**: 根据不同币种特性调整参数

## 📞 获取帮助

```bash
# 查看训练参数
python train_standalone.py --help

# 查看预测参数
python predict_standalone.py --help

# 查看运行示例参数
python run_example.py --help
```