#!/usr/bin/env python3
# quick_backtest.py
# 快速回测脚本

import pandas as pd
import numpy as np
import joblib
import json
import os
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 导入独立工具函数
from standalone_utils import (
    get_continuous_output_dir, calculate_features, load_data_for_training
)

def quick_backtest(model_basename, coin='ETH', days=30, lookforward_hours=4.0):
    """快速回测函数"""
    print(f"🚀 开始快速回测: {model_basename}")
    print(f"币种: {coin}, 回测天数: {days}, 前瞻时间: {lookforward_hours}小时")
    
    # 加载模型和配置
    output_dir = get_continuous_output_dir()
    model_file = os.path.join(output_dir, f'standalone_{model_basename}_model.joblib')
    config_file = os.path.join(output_dir, f'standalone_{model_basename}_config.json')
    
    if not os.path.exists(model_file) or not os.path.exists(config_file):
        print(f"❌ 模型文件不存在: {model_file}")
        return None
    
    model = joblib.load(model_file)
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    # 确定时间范围
    end_time = datetime.now().strftime('%Y-%m-%d')
    start_time = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
    
    # 加载数据
    df = load_data_for_training(coin, '../coin_data.db', market='spot',
                               start_time=start_time, end_time=end_time)
    
    if df is None or len(df) == 0:
        print("❌ 无法加载数据")
        return None
    
    # 准备特征
    timeframe_minutes = config['timeframe_minutes']
    df_with_features = calculate_features(df.copy(), timeframe=timeframe_minutes)
    
    required_features = config['feature_list']
    available_features = [f for f in required_features if f in df_with_features.columns]
    X = df_with_features[available_features].dropna()
    
    if len(X) == 0:
        print("❌ 没有可用数据")
        return None
    
    # 进行预测
    predictions = model.predict(X)
    probabilities = model.predict_proba(X)
    
    # 使用硬编码的概率阈值
    print(f"✅ 使用硬编码概率阈值过滤")
    print("做多条件: ProbaNeutral < 0.29, ProbaLowest ∈ [0.38, 0.43], ProbaHighest ∈ [0.32, 0.33]")
    
    # 计算收益
    lookforward_minutes = int(lookforward_hours * 60)
    lookforward_candles = lookforward_minutes // timeframe_minutes
    
    trades = []
    filtered_count = 0
    
    for i in range(len(predictions) - lookforward_candles):
        current_idx = X.index[i]
        original_predicted_class = predictions[i]
        
        if current_idx not in df_with_features.index:
            continue
        
        # 应用硬编码概率阈值过滤
        predicted_class = original_predicted_class
        proba_lowest = probabilities[i][0]
        proba_neutral = probabilities[i][1]
        proba_highest = probabilities[i][2]
        
        # 硬编码做多条件
        if original_predicted_class == 0:
            if not (proba_neutral < 0.29 and 
                   0.38 <= proba_lowest <= 0.43 and 
                   0.32 <= proba_highest <= 0.33):
                predicted_class = 1  # 改为持有
                filtered_count += 1
        
        # 做空条件暂时保持原样
        # elif original_predicted_class == 2:
        #     # 这里可以添加做空的硬编码条件
            
        current_price = df_with_features.loc[current_idx, 'close']
        
        # 找到未来价格
        current_pos = df_with_features.index.get_loc(current_idx)
        future_pos = current_pos + lookforward_candles
        
        if future_pos < len(df_with_features):
            future_price = df_with_features.iloc[future_pos]['close']
            price_change_pct = (future_price - current_price) / current_price
            
            # 计算交易收益
            if predicted_class == 0:  # 做多
                trading_return = price_change_pct * 100
                trades.append(('Long', trading_return))
            elif predicted_class == 2:  # 做空
                trading_return = -price_change_pct * 100
                trades.append(('Short', trading_return))
    
    print(f"概率过滤: {filtered_count} 个做多信号被过滤")
    
    if not trades:
        print("❌ 没有交易信号")
        return None
    
    # 计算统计
    returns = [t[1] for t in trades]
    total_return = sum(returns)
    avg_return = np.mean(returns)
    win_rate = len([r for r in returns if r > 0]) / len(returns)
    max_win = max(returns)
    max_loss = min(returns)
    
    # 按类型统计
    long_returns = [t[1] for t in trades if t[0] == 'Long']
    short_returns = [t[1] for t in trades if t[0] == 'Short']
    
    print(f"\n📊 快速回测结果:")
    print(f"  时间范围: {start_time} 到 {end_time}")
    print(f"  总交易: {len(trades)} 次")
    print(f"  做多: {len(long_returns)} 次, 做空: {len(short_returns)} 次")
    print(f"  总收益: {total_return:.2f}%")
    print(f"  平均收益: {avg_return:.3f}%")
    print(f"  胜率: {win_rate*100:.1f}%")
    print(f"  最大收益: {max_win:.2f}%")
    print(f"  最大损失: {max_loss:.2f}%")
    
    if long_returns:
        print(f"  做多平均: {np.mean(long_returns):.3f}%")
    if short_returns:
        print(f"  做空平均: {np.mean(short_returns):.3f}%")
    
    # 绘制简单的收益图
    if len(trades) > 0:
        plot_quick_returns(trades, model_basename)
    
    return {
        'total_trades': len(trades),
        'total_return': total_return,
        'avg_return': avg_return,
        'win_rate': win_rate,
        'max_win': max_win,
        'max_loss': max_loss
    }

def plot_quick_returns(trades, model_name):
    """绘制快速回测收益图"""
    try:
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        returns = [t[1] for t in trades]
        cumulative_returns = np.cumsum(returns)
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # 累计收益
        ax1.plot(range(len(cumulative_returns)), cumulative_returns, 'b-', linewidth=2)
        ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        ax1.set_title(f'{model_name} 快速回测累计收益', fontsize=14, fontweight='bold')
        ax1.set_ylabel('累计收益 (%)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        
        # 收益分布
        ax2.hist(returns, bins=30, alpha=0.7, color='green', edgecolor='black')
        ax2.axvline(x=0, color='red', linestyle='--', alpha=0.7)
        ax2.set_title('单次交易收益分布', fontsize=14, fontweight='bold')
        ax2.set_xlabel('单次收益 (%)', fontsize=12)
        ax2.set_ylabel('频次', fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        output_dir = get_continuous_output_dir()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        chart_filename = os.path.join(output_dir, f'quick_backtest_chart_{model_name}_{timestamp}.png')
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        print(f"📊 快速回测图表已保存到: {chart_filename}")
        
        try:
            plt.show()
        except:
            pass
        
        plt.close()
        
    except Exception as e:
        print(f"⚠️ 绘图失败: {e}")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python quick_backtest.py <model_basename> [coin] [days]")
        print("示例: python quick_backtest.py eth_5m ETH 30")
        sys.exit(1)
    
    model_basename = sys.argv[1]
    coin = sys.argv[2] if len(sys.argv) > 2 else 'ETH'
    days = int(sys.argv[3]) if len(sys.argv) > 3 else 30
    
    quick_backtest(model_basename, coin, days)