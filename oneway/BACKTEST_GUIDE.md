# 模型回测使用指南

## 概述

训练完成后，你会得到两个重要文件：
- `standalone_<model_basename>_model.joblib` - 训练好的模型
- `standalone_<model_basename>_config.json` - 模型配置信息

使用这些文件可以对任意时间段进行回测。

## 方法一：完整回测（推荐）

使用 `backtest_standalone.py` 进行详细回测：

### 基本用法

```bash
# 回测最近30天
python backtest_standalone.py --model eth_5m --days 30

# 回测指定时间段
python backtest_standalone.py --model eth_5m --start-time 2024-01-01 --end-time 2024-02-01

# 回测不同币种
python backtest_standalone.py --model eth_5m --coin BTC --days 30

# 自定义前瞻时间
python backtest_standalone.py --model eth_5m --days 30 --lookforward-hours 6
```

### 参数说明

- `--model`: 模型基名（必需），如 `eth_5m`
- `--coin`: 币种名称，默认 `ETH`
- `--days`: 回测天数（从最新数据往前）
- `--start-time`: 回测开始时间 (YYYY-MM-DD)
- `--end-time`: 回测结束时间 (YYYY-MM-DD)
- `--lookforward-hours`: 前瞻时间（小时），默认4.0
- `--db-path`: 数据库路径，默认 `../coin_data.db`

### 输出内容

完整回测会显示：

```
📊 基本统计:
  回测周期: 8640 个时间点
  总交易次数: 1250
  交易频率: 14.5%
  做多次数: 625
  做空次数: 625

💰 收益统计:
  总收益: 15.67%
  平均收益: 0.125%
  胜率: 52.3% (654/1250)
  最大单次收益: 8.45%
  最大单次损失: -6.23%

📈 风险指标:
  收益标准差: 2.145%
  夏普比率: 0.058
  最大回撤: -12.34%

🔄 按交易类型分析:
  做多平均收益: 0.134%
  做空平均收益: 0.116%
  做多胜率: 53.1%
  做空胜率: 51.5%
```

## 方法二：快速回测

使用 `quick_backtest.py` 进行快速验证：

```bash
# 快速回测
python quick_backtest.py eth_5m

# 指定币种和天数
python quick_backtest.py eth_5m BTC 60
```

## 交易策略说明

模型的交易策略：

1. **预测最低点 (0)** → **做多 (Long)**
   - 买入当前价格
   - 4小时后卖出
   - 收益 = (未来价格 - 当前价格) / 当前价格

2. **预测最高点 (2)** → **做空 (Short)**
   - 卖出当前价格
   - 4小时后买回
   - 收益 = (当前价格 - 未来价格) / 当前价格

3. **预测中性点 (1)** → **持有 (Hold)**
   - 不进行交易
   - 收益 = 0

## 概率阈值过滤

为了提高交易质量，模型支持基于预测概率的过滤机制：

### 做多条件（预测最低点时）
- 最低点概率 ≥ 阈值（默认0.4）
- 中性点概率 ≤ 阈值（默认0.5）
- 最高点概率 ≤ 阈值（默认0.3）

### 做空条件（预测最高点时）
- 最低点概率 ≤ 阈值（默认0.3）
- 中性点概率 ≤ 阈值（默认0.5）
- 最高点概率 ≥ 阈值（默认0.4）

只有同时满足所有条件的预测才会执行交易，否则改为持有。

## 配置管理

使用 `config_manager.py` 管理交易阈值：

```bash
# 查看当前配置
python config_manager.py --model eth_5m --show

# 设置保守策略（高阈值，少交易）
python config_manager.py --model eth_5m --conservative

# 设置激进策略（低阈值，多交易）
python config_manager.py --model eth_5m --aggressive

# 自定义做多条件
python config_manager.py --model eth_5m --long-min-lowest 0.5 --long-max-neutral 0.4

# 自定义做空条件
python config_manager.py --model eth_5m --short-min-highest 0.6 --short-max-lowest 0.2
```

## 结果文件

回测完成后会生成CSV文件，包含以下列：

- `Timestamp`: 时间戳
- `ClosePrice`: 当前价格
- `FuturePrice`: 4小时后价格
- `PriceChangePct`: 价格变化百分比
- `OriginalPredictedClass`: 原始预测类别
- `FilteredPredictedClass`: 过滤后预测类别
- `ProbaLowest/ProbaNeutral/ProbaHighest`: 各类别概率
- `OriginalPredictedLabel`: 原始预测标签
- `FilteredPredictedLabel`: 过滤后预测标签
- `TradingAction`: 交易动作 (Long/Short/Hold)
- `TradingReturnPct`: 交易收益百分比
- `WasFiltered`: 是否被过滤

## 评估指标说明

### 基本指标
- **总收益**: 所有交易的累计收益
- **平均收益**: 单次交易的平均收益
- **胜率**: 盈利交易占总交易的比例
- **交易频率**: 实际交易次数占总时间点的比例

### 风险指标
- **夏普比率**: 风险调整后收益，越高越好
- **最大回撤**: 从峰值到谷值的最大损失
- **收益标准差**: 收益的波动性

## 注意事项

1. **数据质量**: 确保回测时间段有足够的数据
2. **特征一致性**: 回测数据的特征计算必须与训练时一致
3. **前瞻偏差**: 模型使用未来4小时的信息进行预测，实际交易中需要考虑执行延迟
4. **交易成本**: 回测结果未考虑手续费、滑点等交易成本
5. **市场环境**: 不同市场环境下模型表现可能差异较大

## 示例工作流

```bash
# 1. 训练模型
python train_standalone.py --coin ETH

# 2. 查看模型配置
python config_manager.py --model eth_5m --show

# 3. 快速验证最近30天表现
python quick_backtest.py eth_5m ETH 30

# 4. 如果表现不佳，调整为保守策略
python config_manager.py --model eth_5m --conservative

# 5. 重新回测验证
python quick_backtest.py eth_5m ETH 30

# 6. 详细回测特定时间段
python backtest_standalone.py --model eth_5m --start-time 2024-01-01 --end-time 2024-03-01

# 7. 优化阈值参数
python config_manager.py --model eth_5m --long-min-lowest 0.5 --short-min-highest 0.5

# 8. 验证优化效果
python backtest_standalone.py --model eth_5m --days 60
```

## 模型文件管理

训练好的模型文件保存在 `models/` 目录下：

```
models/
├── standalone_eth_5m_model.joblib      # 模型文件
├── standalone_eth_5m_config.json       # 配置文件
├── test_results_standalone_eth_5m.csv  # 测试结果
└── backtest_eth_5m_20241201_143022.csv # 回测结果
```

你可以保存多个不同配置的模型，通过不同的 `model_basename` 来区分。