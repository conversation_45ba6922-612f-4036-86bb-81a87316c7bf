# VERSION.py
# 连续移动预测模型版本信息

__version__ = "1.0.0"
__author__ = "Kiro AI Assistant"
__description__ = "连续移动预测模型 - 预测加密货币连续不回头百分比移动"
__license__ = "MIT"

# 版本历史
VERSION_HISTORY = {
    "1.0.0": {
        "date": "2024-01-28",
        "features": [
            "独立版本实现，无外部依赖",
            "连续移动算法优化",
            "LightGBM回归模型",
            "Optuna超参数优化",
            "完整的测试和文档",
            "一键运行示例"
        ],
        "improvements": [
            "算法效率提升",
            "代码结构优化",
            "错误处理完善",
            "用户体验改进"
        ]
    }
}

# 系统要求
REQUIREMENTS = {
    "python": ">=3.8",
    "packages": {
        "pandas": ">=1.3.0",
        "numpy": ">=1.21.0", 
        "lightgbm": ">=3.3.0",
        "scikit-learn": ">=1.0.0",
        "optuna": ">=3.0.0",
        "joblib": ">=1.1.0"
    }
}

# 配置信息
CONFIG_INFO = {
    "supported_coins": ["ETH", "BTC", "DOT", "SUI"],
    "timeframes": ["5m", "15m"],
    "default_retracement": 0.01,  # 1%
    "default_lookforward": 240,   # 4小时
    "model_type": "LightGBM Regressor"
}

def print_version_info():
    """打印版本信息"""
    print(f"连续移动预测模型 v{__version__}")
    print(f"作者: {__author__}")
    print(f"描述: {__description__}")
    print(f"许可: {__license__}")
    
    current_version = VERSION_HISTORY[__version__]
    print(f"\n版本 {__version__} ({current_version['date']}):")
    
    print("\n新功能:")
    for feature in current_version['features']:
        print(f"  • {feature}")
    
    print("\n改进:")
    for improvement in current_version['improvements']:
        print(f"  • {improvement}")
    
    print(f"\n系统要求:")
    print(f"  Python: {REQUIREMENTS['python']}")
    print(f"  主要依赖:")
    for pkg, version in REQUIREMENTS['packages'].items():
        print(f"    - {pkg} {version}")

def get_version():
    """获取版本号"""
    return __version__

def check_compatibility():
    """检查兼容性"""
    import sys
    
    # 检查Python版本
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}"
    required_version = REQUIREMENTS['python'].replace('>=', '')
    
    if sys.version_info < tuple(map(int, required_version.split('.'))):
        return False, f"Python版本过低: {python_version}, 需要: {REQUIREMENTS['python']}"
    
    # 检查包
    missing_packages = []
    for package in REQUIREMENTS['packages']:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        return False, f"缺少包: {', '.join(missing_packages)}"
    
    return True, "兼容性检查通过"

if __name__ == '__main__':
    print_version_info()
    
    print(f"\n兼容性检查:")
    compatible, message = check_compatibility()
    status = "✅" if compatible else "❌"
    print(f"{status} {message}")