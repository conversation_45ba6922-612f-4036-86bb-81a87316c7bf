#!/usr/bin/env python3
# run_continuous_training.py
# 连续移动预测模型训练启动器

import subprocess
import sys
import os
from continuous_utils import get_continuous_config

def run_training(config_name, **kwargs):
    """运行训练任务"""
    config = get_continuous_config(config_name)
    if config is None:
        print(f"❌ 配置 '{config_name}' 不存在")
        return False
    
    # 构建命令行参数
    cmd = [sys.executable, "train_continuous_movement.py"]
    
    # 从配置中提取币种名称
    coin_name = config_name.split('_')[0]
    cmd.extend(["--coin", coin_name])
    
    # 添加自定义参数
    if 'target_percentage' in kwargs:
        cmd.extend(["--target-percentage", str(kwargs['target_percentage'])])
    else:
        cmd.extend(["--target-percentage", str(config['target_percentage'])])
    
    if 'max_retracement' in kwargs:
        cmd.extend(["--max-retracement", str(kwargs['max_retracement'])])
    else:
        cmd.extend(["--max-retracement", str(config['max_retracement'])])
    
    # 其他可选参数
    if kwargs.get('save_data'):
        cmd.append("--save-data")
    
    if kwargs.get('load_data'):
        cmd.append("--load-data")
    
    if kwargs.get('data_file'):
        cmd.extend(["--data-file", kwargs['data_file']])
    
    if kwargs.get('no_time_series_cv'):
        cmd.append("--no-time-series-cv")
    
    if kwargs.get('cv_splits'):
        cmd.extend(["--cv-splits", str(kwargs['cv_splits'])])
    
    if kwargs.get('cv_trials'):
        cmd.extend(["--cv-trials", str(kwargs['cv_trials'])])
    
    print(f"🚀 开始训练: {config['display_name']}")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, check=True, cwd=os.path.dirname(__file__))
        print(f"✅ 训练完成: {config['display_name']}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 训练失败: {config['display_name']}, 错误码: {e.returncode}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="连续移动预测模型训练启动器")
    parser.add_argument("config", help="配置名称 (如: ETH_CONTINUOUS_5M)")
    parser.add_argument("--target-percentage", type=float, help="覆盖目标百分比")
    parser.add_argument("--max-retracement", type=float, help="覆盖最大回撤")
    parser.add_argument("--save-data", action='store_true', help="保存预处理数据")
    parser.add_argument("--load-data", action='store_true', help="加载预处理数据")
    parser.add_argument("--data-file", help="数据文件路径")
    parser.add_argument("--no-time-series-cv", action='store_true', help="禁用时序交叉验证")
    parser.add_argument("--cv-splits", type=int, default=5, help="CV分割数")
    parser.add_argument("--cv-trials", type=int, default=100, help="Optuna尝试次数")
    
    args = parser.parse_args()
    
    # 准备参数字典
    kwargs = {}
    for key, value in vars(args).items():
        if key != 'config' and value is not None:
            kwargs[key] = value
    
    # 运行训练
    success = run_training(args.config, **kwargs)
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()