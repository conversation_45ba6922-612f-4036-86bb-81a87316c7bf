#!/usr/bin/env python3
# get_sample_data.py
# 快速获取样本数据用于测试训练

import requests
import pandas as pd
import sqlite3
import os
from datetime import datetime

def get_sample_data(symbol='ETHUSDT', interval='5m', limit=5000, db_path='../coin_data.db'):
    """获取样本数据用于测试"""
    print(f"🔄 获取 {symbol} {interval} 样本数据 ({limit} 条)...")
    
    # 币安API
    url = "https://api.binance.com/api/v3/klines"
    params = {
        'symbol': symbol,
        'interval': interval,
        'limit': limit
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        klines = response.json()
        
        if not klines:
            print("❌ 未获取到数据")
            return False
        
        print(f"✅ 获取到 {len(klines)} 条数据")
        
        # 转换数据格式
        data_list = []
        for k in klines:
            data_list.append({
                'timestamp': int(k[0]) // 1000,  # 转换为秒
                'open': float(k[1]),
                'high': float(k[2]),
                'low': float(k[3]),
                'close': float(k[4]),
                'volume': float(k[5]),
                'close_time': int(k[6]) // 1000,
                'quote_volume': float(k[7]),
                'trade_count': int(k[8]),
                'taker_buy_base_volume': float(k[9]),
                'taker_buy_quote_volume': float(k[10])
            })
        
        # 保存到数据库
        save_to_database(data_list, symbol, interval, db_path)
        return True
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return False

def save_to_database(data_list, symbol, interval, db_path):
    """保存数据到数据库"""
    # 创建表名 (与get_coin_history.py格式一致)
    interval_formatted = interval.replace('m', 'min')
    table_name = f"{symbol.upper()}_{interval_formatted}_spot"
    
    print(f"💾 保存到数据库表: {table_name}")
    
    # 确保目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建表
    cursor.execute(f'''
        CREATE TABLE IF NOT EXISTS {table_name} (
            timestamp INTEGER PRIMARY KEY,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            volume REAL,
            close_time INTEGER,
            quote_volume REAL,
            trade_count INTEGER,
            taker_buy_base_volume REAL,
            taker_buy_quote_volume REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 插入数据
    insert_sql = f'''
        INSERT OR REPLACE INTO {table_name}
        (timestamp, open, high, low, close, volume, close_time, quote_volume, 
         trade_count, taker_buy_base_volume, taker_buy_quote_volume)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    '''
    
    values = [
        (data['timestamp'], data['open'], data['high'], data['low'], data['close'],
         data['volume'], data['close_time'], data['quote_volume'], data['trade_count'],
         data['taker_buy_base_volume'], data['taker_buy_quote_volume'])
        for data in data_list
    ]
    
    cursor.executemany(insert_sql, values)
    conn.commit()
    
    # 检查结果
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    count = cursor.fetchone()[0]
    
    cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table_name}")
    min_ts, max_ts = cursor.fetchone()
    
    conn.close()
    
    print(f"✅ 数据保存完成")
    print(f"记录数: {count}")
    if min_ts and max_ts:
        min_time = datetime.fromtimestamp(min_ts).strftime('%Y-%m-%d %H:%M:%S')
        max_time = datetime.fromtimestamp(max_ts).strftime('%Y-%m-%d %H:%M:%S')
        print(f"时间范围: {min_time} 到 {max_time}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="获取样本数据用于测试")
    parser.add_argument("--symbol", default="ETHUSDT", help="交易对")
    parser.add_argument("--interval", default="5m", help="时间间隔")
    parser.add_argument("--limit", type=int, default=5000, help="数据条数")
    parser.add_argument("--db", default="../coin_data.db", help="数据库路径")
    
    args = parser.parse_args()
    
    success = get_sample_data(args.symbol, args.interval, args.limit, args.db)
    
    if success:
        print(f"\n🎉 样本数据获取完成！")
        print(f"现在可以运行训练: python train_standalone.py --coin ETH")
    else:
        print(f"\n❌ 样本数据获取失败")

if __name__ == '__main__':
    main()