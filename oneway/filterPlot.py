import pandas as pd
import numpy as np # 引入 numpy 来使用 np.nan
import matplotlib.pyplot as plt
import matplotlib.ticker as mticker
import matplotlib.dates as mdates

def plot_capital_curve_from_score(
    csv_file_path, 
    initial_capital=1000, 
    score_multiplier=0.02, 
    start_date=None, 
    end_date=None,
    mode='trade' # 'simple' for non-compounding, 'trade' for realistic trade-by-trade compounding
):
    """
    从 CSV 文件中读取数据，并根据 'Score' 列绘制资金曲线。
    (函数说明保持不变)
    ...
    """
    # --- 1. 数据加载和预处理 ---
    try:
        df = pd.read_csv(csv_file_path)
        if 'Timestamp' not in df.columns or 'Score' not in df.columns:
            print("❌ 错误: CSV文件必须包含 'Timestamp' 和 'Score' 列。")
            return

        df['Timestamp'] = pd.to_datetime(df['Timestamp'])
        df = df.sort_values('Timestamp').reset_index(drop=True)
        df['Score'] = pd.to_numeric(df['Score'], errors='coerce').fillna(0)
        
    except FileNotFoundError:
        print(f"❌ 错误: 文件未找到 -> {csv_file_path}")
        return
    except Exception as e:
        print(f"❌ 读取文件时发生错误: {e}")
        return

    # --- 1.5. 时间过滤 ---
    if start_date or end_date:
        start_date_dt = pd.to_datetime(start_date) if start_date else df['Timestamp'].min()
        end_date_dt = pd.to_datetime(end_date) if end_date else df['Timestamp'].max()
        df = df[(df['Timestamp'] >= start_date_dt) & (df['Timestamp'] <= end_date_dt)].copy()
        print(f"⏳ 已应用时间过滤器: 从 {start_date if start_date else '开始'} 到 {end_date if end_date else '结束'}")
    else:
        print("⏳ 未设置时间过滤器，将分析所有数据。")

    if df.empty:
        print("❌ 错误: 在指定的时间范围内没有找到数据。")
        return
        
    print(f"✅ 成功加载并处理数据: 共 {len(df)} 条记录。")

    # --- 2. 计算资金曲线 ---
    if mode == 'simple':
        print("📈 计算模式: 简单模式 (非复利)")
        df['Pnl'] = df['Score'] * score_multiplier * initial_capital
        df['Capital'] = initial_capital + df['Pnl'].cumsum()
        
    elif mode == 'trade':
        print("📈 计算模式: 交易复利模式 (真实模拟)")
        is_new_trade = (df['Score'] != 0) & (df['Score'].shift(1).fillna(0) == 0)
        trade_group_id = is_new_trade.cumsum()
        df['TradeID'] = trade_group_id.where(df['Score'] != 0, 0)

        df['Capital'] = 0.0
        capital_at_trade_start = initial_capital

        # 检查是否有任何交易
        if df['TradeID'].max() > 0:
            for trade_id in range(1, df['TradeID'].max() + 1):
                trade_mask = df['TradeID'] == trade_id
                trade_indices = df.index[trade_mask]
                
                return_multipliers = 1 + df.loc[trade_indices, 'Score'] * score_multiplier
                intra_trade_capital = capital_at_trade_start * return_multipliers.cumprod()
                df.loc[trade_indices, 'Capital'] = intra_trade_capital
                
                capital_at_trade_start = intra_trade_capital.iloc[-1]

        # --- 代码修正部分 ---
        # 修正1: 解决 FutureWarning
        # 将0替换为NaN，然后用 .ffill() 进行前向填充
        df['Capital'] = df['Capital'].replace(0, np.nan).ffill()
        
        # 修正2: 解决 KeyError
        # 使用 df.index[0] 获取第一行的实际索引标签
        first_row_index = df.index[0]
        # 检查第一行的 Capital 是否为空，如果为空则填充初始资金
        if pd.isna(df.loc[first_row_index, 'Capital']):
            df.loc[first_row_index, 'Capital'] = initial_capital
        
        # 再次进行前向填充，以确保整个序列是完整的
        df['Capital'] = df['Capital'].ffill()
        # --- 修正结束 ---

    else:
        print(f"❌ 错误: 未知的模式 '{mode}'。请选择 'simple' 或 'trade'。")
        return

    # --- 3. 结果统计和打印 (包含最大回撤) ---
    final_capital = df['Capital'].iloc[-1]
    total_return_pct = (final_capital / initial_capital - 1) * 100
    
    df['Peak'] = df['Capital'].cummax()
    df['Drawdown'] = (df['Peak'] - df['Capital']) / df['Peak']
    max_drawdown = df['Drawdown'].max()
    
    print("\n--- 回测统计 ---")
    print(f"初始资金: ${initial_capital:,.2f}")
    print(f"最终资金: ${final_capital:,.2f}")
    print(f"总收益率: {total_return_pct:.2f}%")
    print(f"最大资金 (峰值): ${df['Capital'].max():,.2f}")
    print(f"最低资金 (谷值): ${df['Capital'].min():,.2f}")
    print(f"最大回撤 (Max Drawdown): {max_drawdown:.2%}")

    # --- 4. 绘图 (此部分无变化) ---
    plt.style.use('seaborn-v0_8-darkgrid')
    fig, ax = plt.subplots(figsize=(14, 8))

    ax.plot(df['Timestamp'], df['Capital'], color='dodgerblue', linewidth=2, label='资金曲线')
    ax.fill_between(df['Timestamp'], initial_capital, df['Capital'], 
                    where=df['Capital'] >= initial_capital, 
                    facecolor='g', alpha=0.3, interpolate=True, label='盈利区间')
    ax.fill_between(df['Timestamp'], initial_capital, df['Capital'], 
                    where=df['Capital'] < initial_capital, 
                    facecolor='r', alpha=0.3, interpolate=True, label='亏损区间')
    ax.axhline(y=initial_capital, color='grey', linestyle='--', linewidth=1.5, label=f'初始资金 (${initial_capital})')

    mode_text_map = {'simple': '简单模式 (非复利)', 'trade': '交易复利模式'}
    title = f'资金变化曲线 ({mode_text_map.get(mode, "")})\n(初始资金: ${initial_capital})'
    if start_date or end_date:
        title += f" (时间范围: {start_date if start_date else '...'} to {end_date if end_date else '...'})"
        
    ax.set_title(title, fontsize=16, fontweight='bold')
    ax.set_xlabel('日期', fontsize=12)
    ax.set_ylabel('总资金 ($)', fontsize=12)

    formatter = mticker.FormatStrFormatter('$%.2f')
    ax.yaxis.set_major_formatter(formatter)
    
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    fig.autofmt_xdate()

    ax.legend(loc='upper left')
    ax.grid(True, which='major', linestyle='--', linewidth=0.5)

    plt.tight_layout()
    chart_filename = 'capital_curve_from_score.png'
    plt.savefig(chart_filename, dpi=300)
    print(f"\n📊 资金曲线图已保存到: {chart_filename}")
    
    plt.show()

# --- 主程序入口 (无变化) ---
if __name__ == '__main__':
    csv_file = 'filtered_and_merged_data_corrected.csv'
    
    print("--- 运行示例 1: 交易复利模式，分析全部数据 ---")
    # plot_capital_curve_from_score(csv_file, mode='trade')

    print("\n\n--- 运行示例 2: 简单模式 (非复利)，分析全部数据 ---")
    # plot_capital_curve_from_score(csv_file, mode='simple')
    
    print("\n\n--- 运行示例 3: 交易复利模式，分析 2023 年全年数据 ---")
    plot_capital_curve_from_score(
        csv_file, 
        start_date='2025-05-01', 
        end_date='2025-12-31',
        mode='trade'
    )