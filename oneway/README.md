# 连续移动预测模型

这个系统用于训练和预测加密货币的连续不回头百分比移动。与传统的"先涨先跌"二元分类不同，这个模型预测在给定时间窗口内，价格能够连续移动的最大百分比，且中途回撤不超过设定阈值。

## 核心概念

### 连续移动定义
- **目标百分比**: 期望的最大连续移动幅度（如5%）
- **最大回撤**: 允许的最大回撤幅度（如1%）
- **连续性**: 在达到目标过程中，回撤不能超过最大回撤阈值

### 示例
如果设置目标5%，最大回撤1%：
- ✅ 价格从100涨到105，中途最大回撤0.8% → 有效的+5%移动
- ❌ 价格从100涨到105，但中途回撤了1.5% → 无效，连续性被破坏

## 文件结构

```
oneway/
├── train_continuous_movement.py    # 主训练脚本
├── continuous_utils.py             # 工具函数
├── continuous_config.json          # 配置文件
├── run_continuous_training.py      # 单个训练启动器
├── batch_train_continuous.py       # 批量训练脚本
├── predict_continuous_movement.py  # 预测脚本
└── README.md                       # 说明文档
```

## 配置文件

`continuous_config.json` 包含不同币种和时间框架的配置：

```json
{
  "ETH_CONTINUOUS_5M": {
    "display_name": "ETH/USDT 连续移动",
    "timeframe_minutes": 5,
    "target_percentage": 0.05,      // 5%目标
    "max_retracement": 0.01,        // 1%最大回撤
    "max_lookforward_minutes": 240  // 4小时前瞻窗口
  }
}
```

## 使用方法

### 🚀 推荐：使用独立版本 (无外部依赖)

```bash
# 测试功能是否正常
python test_standalone.py

# 基础训练
python train_standalone.py --coin ETH

# 自定义参数训练
python train_standalone.py --coin ETH \
    --max-retracement 0.015 \
    --cv-trials 50

# 快速训练 (禁用交叉验证)
python train_standalone.py --coin ETH \
    --no-time-series-cv
```

### 1. 完整版本训练 (需要外部依赖)

```bash
# 基础训练
python train_continuous_movement.py --coin ETH

# 自定义参数训练
python train_continuous_movement.py --coin ETH \
    --target-percentage 0.06 \
    --max-retracement 0.015 \
    --save-data

# 使用预处理数据
python train_continuous_movement.py --coin ETH \
    --load-data \
    --data-file eth_continuous_data.pkl
```

### 2. 使用启动器

```bash
# 训练指定配置
python run_continuous_training.py ETH_CONTINUOUS_5M --save-data

# 覆盖配置参数
python run_continuous_training.py ETH_CONTINUOUS_5M \
    --target-percentage 0.08 \
    --max-retracement 0.02
```

### 3. 批量训练

```bash
# 训练所有配置
python batch_train_continuous.py --all

# 训练指定配置
python batch_train_continuous.py --configs ETH_CONTINUOUS_5M BTC_CONTINUOUS_5M

# 快速训练（减少CV试验次数）
python batch_train_continuous.py --all --cv-trials 20
```

### 4. 模型预测

```bash
# 基础预测
python predict_continuous_movement.py eth_5m_continuous --coin ETH

# 预测最新20个数据点并保存
python predict_continuous_movement.py eth_5m_continuous \
    --coin ETH \
    --latest 20 \
    --save

# 指定时间范围预测
python predict_continuous_movement.py eth_5m_continuous \
    --coin ETH \
    --start-time 2024-01-01 \
    --end-time 2024-01-31 \
    --save
```

## 训练参数说明

### 核心参数
- `--target-percentage`: 目标百分比（默认从配置读取）
- `--max-retracement`: 最大回撤百分比（默认从配置读取）
- `--coin`: 币种名称

### 数据参数
- `--save-data`: 保存预处理数据
- `--load-data`: 加载预处理数据
- `--data-file`: 数据文件路径

### 训练参数
- `--no-time-series-cv`: 禁用时序交叉验证
- `--cv-splits`: 交叉验证分割数（默认5）
- `--cv-trials`: Optuna优化试验次数（默认100）

## 输出文件

训练完成后会生成以下文件：

```
models/
├── continuous_eth_5m_continuous_model.joblib           # 训练好的模型
├── continuous_eth_5m_continuous_config.json           # 模型配置
├── test_results_continuous_eth_5m_continuous.csv      # 测试结果
├── feature_importance_continuous_eth_5m_continuous.csv # 特征重要性
└── cv_results_continuous_eth_5m_continuous.csv        # 交叉验证结果
```

## 模型评估指标

### 回归指标
- **RMSE**: 均方根误差，越小越好
- **MAE**: 平均绝对误差，越小越好
- **R²**: 决定系数，越接近1越好

### 方向准确性
- **方向预测准确率**: 预测涨跌方向的准确率
- **平均绝对误差**: 预测值与实际值的平均差异

## 最佳实践

### 参数设置建议
1. **目标百分比**: 
   - 5分钟K线: 2-6%
   - 15分钟K线: 5-10%
   - 根据币种波动性调整

2. **最大回撤**:
   - 通常设为目标百分比的15-25%
   - 太小会导致信号过少
   - 太大会失去连续性意义

3. **前瞻窗口**:
   - 5分钟K线: 2-4小时
   - 15分钟K线: 8-24小时

### 训练建议
1. 先用小数据集测试参数合理性
2. 使用`--save-data`保存预处理数据，避免重复计算
3. 批量训练时适当减少CV试验次数以节省时间
4. 定期重新训练模型以适应市场变化

## 故障排除

### 常见问题
1. **标签数量过少**: 调整目标百分比或最大回撤参数
2. **训练时间过长**: 减少CV试验次数或禁用交叉验证
3. **预测效果差**: 检查特征质量，考虑增加数据量或调整参数

### 调试技巧
1. 使用`--no-time-series-cv`快速测试
2. 检查数据质量和特征分布
3. 分析特征重要性，移除无用特征

## 扩展功能

### 添加新币种
1. 在`continuous_config.json`中添加新配置
2. 根据币种特性调整参数
3. 测试训练效果

### 自定义特征
1. 修改`model_utils_815.py`中的特征计算函数
2. 重新训练模型
3. 分析新特征的重要性

## 注意事项

1. **数据质量**: 确保K线数据完整且准确
2. **参数合理性**: 避免设置过于极端的参数
3. **过拟合风险**: 使用时序交叉验证避免数据泄露
4. **市场适应性**: 定期重新训练以适应市场变化
5. **风险管理**: 模型预测仅供参考，不构成投资建议
## 
🔧 独立版本说明

### 优势
- **无外部依赖**: 不需要父目录的 `model_utils_815.py` 和 `data_loader.py`
- **简化配置**: 使用独立的 `continuous_config.json`
- **快速测试**: 包含测试脚本验证功能
- **易于部署**: 整个 `oneway` 文件夹可以独立运行

### 文件说明
- `train_standalone.py`: 独立训练脚本 ⭐ **推荐使用**
- `standalone_utils.py`: 独立工具函数
- `test_standalone.py`: 功能测试脚本
- `continuous_config.json`: 配置文件

### 快速开始
```bash
# 1. 测试功能
python test_standalone.py

# 2. 训练模型
python train_standalone.py --coin ETH --max-retracement 0.01

# 3. 查看结果
ls ../models/standalone_*
```

### 依赖对比

| 功能 | 独立版本 | 完整版本 |
|------|----------|----------|
| 外部依赖 | ❌ 无 | ✅ 需要 model_utils_815.py |
| 配置文件 | continuous_config.json | config.json |
| 特征计算 | 内置简化版 | 完整版 |
| 数据加载 | SQLite直连 | 通过data_loader |
| 部署难度 | 简单 | 复杂 |

### 故障排除

#### 配置文件错误
```bash
# 确保配置文件存在
ls continuous_config.json

# 检查配置格式
python -c "import json; print(json.load(open('continuous_config.json')))"
```

#### 数据库连接问题
```bash
# 检查数据库文件
ls ../coin_data.db

# 测试数据加载
python test_standalone.py
```

#### 依赖问题
```bash
# 安装必要的包
pip install pandas numpy lightgbm scikit-learn optuna joblib
```