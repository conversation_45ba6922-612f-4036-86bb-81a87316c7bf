"""
Example usage of PolicyNetwork for RL Trading Agent.

This script demonstrates how to use the PolicyNetwork class in a trading context,
showing how to create, train, and use the network for making trading decisions.
"""

import torch
import numpy as np
import json
from policy_network import PolicyNetwork, create_default_policy_network


def create_sample_market_state():
    """
    Create a sample market state vector based on the design specifications.
    
    Returns:
        torch.Tensor: Sample state vector
    """
    # Based on the design document, our state includes:
    # - Model signal and confidence (3 features)
    # - Market basic info (4 features) 
    # - Portfolio state (5 features)
    # - Time features (3 features)
    # Total: 15 features
    
    state = torch.tensor([
        # Model predictions
        1.0,    # model_signal (0 or 1)
        0.75,   # signal_confidence
        0.82,   # signal_probability
        
        # Market info
        2500.0, # current_price (normalized)
        0.02,   # price_change_1h
        -0.01,  # price_change_4h
        0.03,   # volatility_recent
        
        # Portfolio state
        0.8,    # cash_ratio
        2.0,    # position_count (normalized)
        150.0,  # unrealized_pnl (normalized)
        0.6,    # recent_win_rate
        0.0,    # consecutive_losses
        
        # Time features
        14.0,   # hour (normalized)
        2.0,    # day_of_week
        1.0,    # is_good_trading_time
    ], dtype=torch.float32).unsqueeze(0)  # Add batch dimension
    
    return state


def demonstrate_policy_network():
    """Demonstrate PolicyNetwork functionality."""
    print("PolicyNetwork Usage Example")
    print("=" * 50)
    
    # 1. Create the network
    state_dim = 15  # Based on our state design
    network = create_default_policy_network(state_dim)
    
    print(f"Created PolicyNetwork with {network.get_model_info()['total_parameters']} parameters")
    print(f"Model size: {network.get_model_info()['model_size_mb']:.2f} MB")
    print()
    
    # 2. Create sample market state
    market_state = create_sample_market_state()
    print("Sample market state:")
    print(f"  Shape: {market_state.shape}")
    print(f"  Values: {market_state.squeeze().tolist()}")
    print()
    
    # 3. Get deterministic actions (for inference)
    print("Deterministic Actions (for inference):")
    det_actions = network.get_actions(market_state, deterministic=True)
    for key, value in det_actions.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.item()}")
        else:
            print(f"  {key}: {value}")
    print()
    
    # 4. Sample actions with exploration (for training)
    print("Stochastic Actions (for training):")
    actions, log_probs = network.sample_action(market_state)
    print("Actions:")
    for key, value in actions.items():
        print(f"  {key}: {value}")
    
    print("Log Probabilities:")
    for key, value in log_probs.items():
        print(f"  {key}: {value.item():.4f}")
    print()
    
    # 5. Demonstrate batch processing
    print("Batch Processing Example:")
    batch_size = 4
    batch_states = torch.randn(batch_size, state_dim)
    batch_actions = network.get_actions(batch_states, deterministic=True)
    
    print(f"Processed batch of {batch_size} states:")
    for i in range(batch_size):
        pos_size = batch_actions['position_size'][i].item()
        enter_trade = batch_actions['enter_trade'][i].item()
        print(f"  State {i+1}: Position={pos_size:.3f}, Enter={bool(enter_trade)}")
    print()
    
    # 6. Save and load example
    print("Save/Load Example:")
    model_path = "example_policy_model"
    metadata = {
        'training_episodes': 1000,
        'performance': {'sharpe_ratio': 1.5, 'total_return': 0.25},
        'created_by': 'example_script'
    }
    
    network.save_model(model_path, metadata)
    loaded_network = PolicyNetwork.load_model(model_path)
    
    # Verify loaded model works
    loaded_actions = loaded_network.get_actions(market_state, deterministic=True)
    print("Loaded model produces same actions:", 
          torch.allclose(det_actions['position_size'], loaded_actions['position_size']))
    print()
    
    # 7. Training simulation example
    print("Training Simulation Example:")
    network.train()  # Set to training mode
    optimizer = torch.optim.Adam(network.parameters(), lr=3e-4)
    
    # Simulate a few training steps
    for step in range(3):
        # Sample some actions
        actions, log_probs = network.sample_action(market_state)
        
        # Simulate reward (in real training, this would come from environment)
        reward = torch.tensor([np.random.normal(0.01, 0.05)])  # Random reward
        
        # Simple policy gradient loss (in real training, use PPO)
        total_log_prob = sum(log_probs.values())
        loss = -total_log_prob * reward
        
        # Update network
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        print(f"  Step {step+1}: Loss={loss.item():.4f}, Reward={reward.item():.4f}")
    
    print()
    print("Example completed successfully!")
    
    # Clean up example files
    import os
    try:
        os.remove(f"{model_path}.pth")
        os.remove(f"{model_path}_config.json")
        print("Cleaned up example files.")
    except:
        pass


def trading_decision_example():
    """
    Example of how the PolicyNetwork would be used in actual trading decisions.
    """
    print("\nTrading Decision Example")
    print("=" * 30)
    
    # Create network
    network = create_default_policy_network(15)
    network.eval()  # Set to evaluation mode
    
    # Simulate different market scenarios
    scenarios = [
        {
            'name': 'Strong Buy Signal',
            'state': [1.0, 0.9, 0.95, 2500.0, 0.05, 0.03, 0.02, 0.9, 1.0, 50.0, 0.8, 0.0, 14.0, 2.0, 1.0]
        },
        {
            'name': 'Weak Buy Signal',
            'state': [1.0, 0.6, 0.65, 2500.0, 0.01, -0.01, 0.04, 0.7, 3.0, -20.0, 0.4, 2.0, 3.0, 6.0, 0.0]
        },
        {
            'name': 'No Signal',
            'state': [0.0, 0.5, 0.48, 2500.0, 0.0, 0.0, 0.03, 0.8, 2.0, 0.0, 0.6, 1.0, 10.0, 3.0, 1.0]
        }
    ]
    
    for scenario in scenarios:
        state = torch.tensor(scenario['state'], dtype=torch.float32).unsqueeze(0)
        actions = network.get_actions(state, deterministic=True)
        
        print(f"\nScenario: {scenario['name']}")
        print(f"  Decision: {'ENTER' if actions['enter_trade'].item() else 'WAIT'}")
        if actions['enter_trade'].item():
            print(f"  Position Size: {actions['position_size'].item():.2%}")
            print(f"  Stop Loss: {actions['stop_loss_pct'].item():.2%}")
            print(f"  Take Profit: {actions['take_profit_pct'].item():.2%}")
            print(f"  Max Hold Time: {actions['max_hold_time'].item():.0f} minutes")


if __name__ == "__main__":
    demonstrate_policy_network()
    trading_decision_example()