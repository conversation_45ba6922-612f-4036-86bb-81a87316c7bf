#!/usr/bin/env python3
# config_cli.py
# 配置管理命令行工具

import argparse
import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional

from .training_config import TrainingConfigManager, FullTrainingConfig
from .config_validator import ConfigValidator, validate_config_file


def list_configs(args):
    """列出所有配置"""
    manager = TrainingConfigManager(args.config_dir)
    configs = manager.list_configs()
    
    print("可用配置:")
    print("-" * 40)
    
    if configs['templates']:
        print("📋 模板配置:")
        for config_name in sorted(configs['templates']):
            summary = manager.get_config_summary(config_name)
            if summary:
                print(f"  • {config_name}: {summary['description']}")
                print(f"    风险: {summary['risk_profile']}, 风格: {summary['trading_style']}")
            else:
                print(f"  • {config_name}")
        print()
    
    if configs['custom']:
        print("🔧 自定义配置:")
        for config_name in sorted(configs['custom']):
            summary = manager.get_config_summary(config_name)
            if summary:
                print(f"  • {config_name}: {summary['description']}")
            else:
                print(f"  • {config_name}")
        print()
    
    if configs['root']:
        print("📁 其他配置:")
        for config_name in sorted(configs['root']):
            print(f"  • {config_name}")


def show_config(args):
    """显示配置详情"""
    manager = TrainingConfigManager(args.config_dir)
    config = manager.load_config(args.name)
    
    if not config:
        print(f"❌ 配置 '{args.name}' 不存在")
        return
    
    if args.summary:
        # 显示摘要
        summary = manager.get_config_summary(args.name)
        if summary:
            print(f"配置摘要: {args.name}")
            print("-" * 30)
            print(f"描述: {summary['description']}")
            print(f"版本: {summary['version']}")
            print(f"风险档案: {summary['risk_profile']}")
            print(f"交易风格: {summary['trading_style']}")
            print("\n关键参数:")
            for key, value in summary['key_parameters'].items():
                print(f"  {key}: {value}")
    else:
        # 显示完整配置
        config_dict = config.__dict__
        print(f"配置详情: {args.name}")
        print("-" * 30)
        print(json.dumps(config_dict, indent=2, ensure_ascii=False, default=str))


def validate_config(args):
    """验证配置"""
    if args.file:
        # 验证文件
        success = validate_config_file(args.file)
        sys.exit(0 if success else 1)
    else:
        # 验证已存在的配置
        manager = TrainingConfigManager(args.config_dir)
        config = manager.load_config(args.name)
        
        if not config:
            print(f"❌ 配置 '{args.name}' 不存在")
            sys.exit(1)
        
        validator = ConfigValidator()
        report = validator.generate_validation_report(config)
        print(report)
        
        issues = validator.validate_config(config)
        sys.exit(0 if len(issues['errors']) == 0 else 1)


def create_config(args):
    """创建新配置"""
    manager = TrainingConfigManager(args.config_dir)
    
    # 解析修改参数
    modifications = {}
    if args.set:
        for param in args.set:
            if '=' not in param:
                print(f"❌ 无效的参数格式: {param} (应为 key=value)")
                sys.exit(1)
            key, value = param.split('=', 1)
            
            # 尝试转换值类型
            try:
                if value.lower() in ['true', 'false']:
                    value = value.lower() == 'true'
                elif '.' in value:
                    value = float(value)
                elif value.isdigit():
                    value = int(value)
            except ValueError:
                pass  # 保持字符串类型
            
            modifications[key] = value
    
    # 创建配置
    try:
        config = manager.create_custom_config(
            name=args.name,
            base_config=args.base,
            modifications=modifications
        )
        
        print(f"✅ 成功创建配置: {args.name}")
        
        # 验证新配置
        if args.validate:
            validator = ConfigValidator()
            issues = validator.validate_config(config)
            
            if issues['errors']:
                print("\n⚠️  配置验证发现问题:")
                for error in issues['errors']:
                    print(f"  ❌ {error}")
            else:
                print("\n✅ 配置验证通过")
                
    except Exception as e:
        print(f"❌ 创建配置失败: {e}")
        sys.exit(1)


def compare_configs(args):
    """比较配置"""
    manager = TrainingConfigManager(args.config_dir)
    
    # 加载配置
    configs = {}
    for name in args.names:
        config = manager.load_config(name)
        if not config:
            print(f"❌ 配置 '{name}' 不存在")
            sys.exit(1)
        configs[name] = config
    
    # 比较关键参数
    print("配置比较")
    print("=" * 60)
    
    # 定义要比较的关键字段
    key_fields = [
        ('数据', [
            ('data.coin', '币种'),
            ('data.interval', '时间间隔'),
            ('data.train_ratio', '训练比例')
        ]),
        ('环境', [
            ('environment.initial_capital', '初始资金'),
            ('environment.max_position_size', '最大仓位'),
            ('environment.max_positions', '最大持仓数'),
            ('environment.transaction_cost', '交易成本')
        ]),
        ('强化学习', [
            ('rl.learning_rate', '学习率'),
            ('rl.batch_size', '批次大小'),
            ('rl.gamma', '折扣因子'),
            ('rl.clip_epsilon', '裁剪参数')
        ]),
        ('训练', [
            ('training.episodes', '训练轮次'),
            ('training.episode_length', 'Episode长度'),
            ('training.update_frequency', '更新频率')
        ]),
        ('奖励', [
            ('reward.pnl_weight', 'PnL权重'),
            ('reward.risk_penalty_weight', '风险惩罚权重'),
            ('reward.max_drawdown_threshold', '最大回撤阈值')
        ])
    ]
    
    for section_name, fields in key_fields:
        print(f"\n{section_name}:")
        print("-" * 30)
        
        for field_path, field_name in fields:
            print(f"{field_name:15}", end="")
            
            for config_name in args.names:
                config = configs[config_name]
                
                # 获取嵌套字段值
                value = config
                for part in field_path.split('.'):
                    value = getattr(value, part)
                
                # 格式化显示
                if isinstance(value, float):
                    if value < 0.01:
                        formatted = f"{value:.6f}"
                    elif value < 1:
                        formatted = f"{value:.4f}"
                    else:
                        formatted = f"{value:.2f}"
                else:
                    formatted = str(value)
                
                print(f" | {formatted:>12}", end="")
            
            print()  # 换行
    
    # 显示风险评分
    print(f"\n风险评分:")
    print("-" * 30)
    validator = ConfigValidator()
    
    print(f"{'配置名称':15}", end="")
    for config_name in args.names:
        print(f" | {'风险评分':>12}", end="")
    print()
    
    print(f"{'':15}", end="")
    for config_name in args.names:
        config = configs[config_name]
        risk_score = validator._calculate_risk_score(config)
        print(f" | {risk_score:>12}/10", end="")
    print()


def export_config(args):
    """导出配置"""
    manager = TrainingConfigManager(args.config_dir)
    config = manager.load_config(args.name)
    
    if not config:
        print(f"❌ 配置 '{args.name}' 不存在")
        sys.exit(1)
    
    # 导出到文件
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    manager.save_config(config, str(output_path))
    print(f"✅ 配置已导出到: {output_path}")


def import_config(args):
    """导入配置"""
    input_path = Path(args.file)
    if not input_path.exists():
        print(f"❌ 文件不存在: {input_path}")
        sys.exit(1)
    
    manager = TrainingConfigManager(args.config_dir)
    
    try:
        # 加载配置
        config = manager._load_config_from_file(input_path)
        if not config:
            print(f"❌ 无法加载配置文件: {input_path}")
            sys.exit(1)
        
        # 设置新名称
        if args.name:
            config.config_name = args.name
        
        # 保存配置
        manager.save_config(config)
        print(f"✅ 配置已导入: {config.config_name}")
        
        # 验证配置
        if args.validate:
            validator = ConfigValidator()
            issues = validator.validate_config(config)
            
            if issues['errors']:
                print("\n⚠️  配置验证发现问题:")
                for error in issues['errors']:
                    print(f"  ❌ {error}")
            else:
                print("\n✅ 配置验证通过")
                
    except Exception as e:
        print(f"❌ 导入配置失败: {e}")
        sys.exit(1)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="RL交易配置管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 列出所有配置
  python -m rl.config_cli list
  
  # 显示配置详情
  python -m rl.config_cli show conservative
  
  # 创建自定义配置
  python -m rl.config_cli create my_config --base conservative --set rl.learning_rate=0.0001
  
  # 验证配置
  python -m rl.config_cli validate my_config
  
  # 比较配置
  python -m rl.config_cli compare conservative aggressive
  
  # 导出配置
  python -m rl.config_cli export conservative --output my_config.json
        """
    )
    
    parser.add_argument(
        '--config-dir', 
        default='rl/configs',
        help='配置目录路径 (默认: rl/configs)'
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # list 命令
    list_parser = subparsers.add_parser('list', help='列出所有配置')
    list_parser.set_defaults(func=list_configs)
    
    # show 命令
    show_parser = subparsers.add_parser('show', help='显示配置详情')
    show_parser.add_argument('name', help='配置名称')
    show_parser.add_argument('--summary', action='store_true', help='只显示摘要')
    show_parser.set_defaults(func=show_config)
    
    # validate 命令
    validate_parser = subparsers.add_parser('validate', help='验证配置')
    validate_group = validate_parser.add_mutually_exclusive_group(required=True)
    validate_group.add_argument('--name', help='配置名称')
    validate_group.add_argument('--file', help='配置文件路径')
    validate_parser.set_defaults(func=validate_config)
    
    # create 命令
    create_parser = subparsers.add_parser('create', help='创建新配置')
    create_parser.add_argument('name', help='新配置名称')
    create_parser.add_argument('--base', default='conservative', help='基础配置 (默认: conservative)')
    create_parser.add_argument('--set', action='append', help='设置参数 (格式: key=value)')
    create_parser.add_argument('--validate', action='store_true', help='创建后验证配置')
    create_parser.set_defaults(func=create_config)
    
    # compare 命令
    compare_parser = subparsers.add_parser('compare', help='比较配置')
    compare_parser.add_argument('names', nargs='+', help='要比较的配置名称')
    compare_parser.set_defaults(func=compare_configs)
    
    # export 命令
    export_parser = subparsers.add_parser('export', help='导出配置')
    export_parser.add_argument('name', help='配置名称')
    export_parser.add_argument('--output', required=True, help='输出文件路径')
    export_parser.set_defaults(func=export_config)
    
    # import 命令
    import_parser = subparsers.add_parser('import', help='导入配置')
    import_parser.add_argument('file', help='配置文件路径')
    import_parser.add_argument('--name', help='新配置名称 (可选)')
    import_parser.add_argument('--validate', action='store_true', help='导入后验证配置')
    import_parser.set_defaults(func=import_config)
    
    # 解析参数
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # 执行命令
    try:
        args.func(args)
    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()