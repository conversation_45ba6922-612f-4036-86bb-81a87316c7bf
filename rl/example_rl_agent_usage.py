"""
Example usage of RLTradingAgent

This script demonstrates how to use the RL trading agent for:
1. Training on historical data with existing model signals
2. Evaluating trained agent performance
3. Integrating with existing backtesting framework
"""

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rl.rl_trading_agent import RLTradingAgent, create_default_rl_agent
from rl.trading_environment import TradingEnvironment, MarketState, TradingAction
from rl.policy_network import PolicyNetwork


def create_sample_market_data(n_samples: int = 1000) -> List[MarketState]:
    """创建示例市场数据用于演示"""
    market_states = []
    
    for i in range(n_samples):
        # 模拟现有模型的信号
        signal_prob = np.random.uniform(0.3, 0.9)
        model_signal = 1 if signal_prob > 0.5 else 0
        confidence = abs(signal_prob - 0.5) * 2  # 转换为0-1的置信度
        
        # 模拟市场数据
        base_price = 2500 + np.sin(i / 100) * 200 + np.random.normal(0, 50)
        
        market_state = MarketState(
            model_signal=model_signal,
            signal_confidence=confidence,
            signal_probability=signal_prob,
            current_price=max(1000, base_price),  # 确保价格为正
            price_change_1h=np.random.normal(0, 0.02),
            price_change_4h=np.random.normal(0, 0.05),
            volatility_recent=np.random.uniform(0.01, 0.08),
            cash_ratio=np.random.uniform(0.7, 1.0),
            position_count=np.random.randint(0, 4),
            unrealized_pnl=np.random.normal(0, 100),
            recent_win_rate=np.random.uniform(0.3, 0.8),
            consecutive_losses=np.random.randint(0, 4),
            hour=i % 24,
            day_of_week=(i // 24) % 7,
            is_good_trading_time=np.random.choice([True, False], p=[0.7, 0.3])
        )
        
        market_states.append(market_state)
    
    return market_states


def simulate_trading_episode(agent: RLTradingAgent, env: TradingEnvironment, 
                           market_data: List[MarketState], 
                           training: bool = True) -> Dict:
    """模拟一个交易episode"""
    
    env.reset()
    episode_rewards = []
    episode_actions = []
    episode_trades = []
    
    for i, market_state in enumerate(market_data):
        # 获取当前状态向量
        state_vector = env.get_state_vector(market_state)
        
        # 代理选择动作
        action_dict, log_prob, value = agent.get_action(
            state_vector, 
            deterministic=not training
        )
        
        # 转换为交易动作
        trading_action = TradingAction(
            enter_trade=action_dict['enter_trade'],
            position_size=action_dict['position_size'],
            stop_loss_pct=action_dict['stop_loss_pct'],
            take_profit_pct=action_dict['take_profit_pct'],
            max_hold_time=action_dict['max_hold_time']
        )
        
        # 执行交易
        trade_result = env.execute_action(
            trading_action, 
            market_state, 
            pd.Timestamp.now() + pd.Timedelta(minutes=i), 
            i
        )
        
        # 更新现有仓位
        completed_trades = env.update_positions(
            market_state.current_price,
            pd.Timestamp.now() + pd.Timedelta(minutes=i),
            i
        )
        
        # 计算奖励
        reward = env.calculate_reward(completed_trades, market_state)
        
        # 存储经验（如果是训练模式）
        if training and i < len(market_data) - 1:
            next_state_vector = env.get_state_vector(market_data[i + 1])
            agent.store_experience(
                state=state_vector,
                action=action_dict,
                reward=reward,
                next_state=next_state_vector,
                done=(i == len(market_data) - 1),
                log_prob=log_prob,
                value=value
            )
        
        # 记录数据
        episode_rewards.append(reward)
        episode_actions.append(action_dict)
        episode_trades.extend(completed_trades)
    
    # 计算episode统计
    total_reward = sum(episode_rewards)
    total_trades = len(episode_trades)
    win_rate = sum(1 for trade in episode_trades if trade['pnl'] > 0) / max(total_trades, 1)
    
    portfolio_metrics = env.get_portfolio_metrics()
    
    return {
        'total_reward': total_reward,
        'total_trades': total_trades,
        'win_rate': win_rate,
        'final_capital': env.current_capital,
        'total_return': portfolio_metrics['total_return'],
        'episode_rewards': episode_rewards,
        'episode_actions': episode_actions,
        'completed_trades': episode_trades
    }


def train_rl_agent(episodes: int = 100, episode_length: int = 500):
    """训练RL代理"""
    print(f"开始训练RL代理，{episodes}个episodes，每个episode {episode_length}步")
    
    # 创建环境和代理
    env_config = {
        'initial_capital': 10000,
        'transaction_cost': 0.001,
        'max_position_size': 0.05,
        'max_positions': 3
    }
    
    env = TradingEnvironment(env_config)
    agent = create_default_rl_agent(state_dim=env.get_state_dim())
    
    # 训练统计
    training_rewards = []
    training_returns = []
    training_win_rates = []
    
    for episode in range(episodes):
        print(f"\n--- Episode {episode + 1}/{episodes} ---")
        
        # 生成新的市场数据
        market_data = create_sample_market_data(episode_length)
        
        # 运行训练episode
        episode_result = simulate_trading_episode(agent, env, market_data, training=True)
        
        # 更新策略（每个episode后）
        if len(agent.experience_buffer) >= agent.batch_size:
            losses = agent.update_policy()
            print(f"策略损失: {losses['policy_loss']:.6f}, "
                  f"价值损失: {losses['value_loss']:.6f}, "
                  f"Epsilon: {losses['epsilon']:.4f}")
        
        # 记录统计
        training_rewards.append(episode_result['total_reward'])
        training_returns.append(episode_result['total_return'])
        training_win_rates.append(episode_result['win_rate'])
        
        print(f"Episode奖励: {episode_result['total_reward']:.4f}")
        print(f"总收益率: {episode_result['total_return']:.2%}")
        print(f"交易次数: {episode_result['total_trades']}")
        print(f"胜率: {episode_result['win_rate']:.2%}")
        
        # 每10个episode保存一次模型
        if (episode + 1) % 10 == 0:
            model_path = f"rl_agent_episode_{episode + 1}"
            agent.save_model(model_path, metadata={
                'episode': episode + 1,
                'avg_reward': np.mean(training_rewards[-10:]),
                'avg_return': np.mean(training_returns[-10:])
            })
            print(f"模型已保存: {model_path}")
    
    return agent, {
        'rewards': training_rewards,
        'returns': training_returns,
        'win_rates': training_win_rates
    }


def evaluate_agent(agent: RLTradingAgent, episodes: int = 10, episode_length: int = 500):
    """评估训练好的代理"""
    print(f"\n开始评估代理，{episodes}个测试episodes")
    
    env_config = {
        'initial_capital': 10000,
        'transaction_cost': 0.001,
        'max_position_size': 0.05,
        'max_positions': 3
    }
    
    env = TradingEnvironment(env_config)
    agent.set_training_mode(False)  # 设置为评估模式
    
    eval_results = []
    
    for episode in range(episodes):
        market_data = create_sample_market_data(episode_length)
        result = simulate_trading_episode(agent, env, market_data, training=False)
        eval_results.append(result)
        
        print(f"测试Episode {episode + 1}: "
              f"收益率 {result['total_return']:.2%}, "
              f"交易 {result['total_trades']}次, "
              f"胜率 {result['win_rate']:.2%}")
    
    # 计算平均性能
    avg_return = np.mean([r['total_return'] for r in eval_results])
    avg_trades = np.mean([r['total_trades'] for r in eval_results])
    avg_win_rate = np.mean([r['win_rate'] for r in eval_results])
    
    print(f"\n=== 评估结果 ===")
    print(f"平均收益率: {avg_return:.2%}")
    print(f"平均交易次数: {avg_trades:.1f}")
    print(f"平均胜率: {avg_win_rate:.2%}")
    
    return eval_results


def plot_training_progress(training_stats: Dict):
    """绘制训练进度图"""
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # 奖励曲线
    axes[0, 0].plot(training_stats['rewards'])
    axes[0, 0].set_title('Episode Rewards')
    axes[0, 0].set_xlabel('Episode')
    axes[0, 0].set_ylabel('Total Reward')
    
    # 收益率曲线
    axes[0, 1].plot([r * 100 for r in training_stats['returns']])
    axes[0, 1].set_title('Episode Returns')
    axes[0, 1].set_xlabel('Episode')
    axes[0, 1].set_ylabel('Return (%)')
    
    # 胜率曲线
    axes[1, 0].plot([w * 100 for w in training_stats['win_rates']])
    axes[1, 0].set_title('Win Rate')
    axes[1, 0].set_xlabel('Episode')
    axes[1, 0].set_ylabel('Win Rate (%)')
    
    # 移动平均奖励
    window = 10
    if len(training_stats['rewards']) >= window:
        moving_avg = pd.Series(training_stats['rewards']).rolling(window).mean()
        axes[1, 1].plot(moving_avg)
        axes[1, 1].set_title(f'Moving Average Reward (window={window})')
        axes[1, 1].set_xlabel('Episode')
        axes[1, 1].set_ylabel('Average Reward')
    
    plt.tight_layout()
    plt.savefig('rl_training_progress.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("训练进度图已保存为 rl_training_progress.png")


def compare_with_baseline():
    """与基准策略比较"""
    print("\n=== 与基准策略比较 ===")
    
    # 创建测试数据
    test_data = create_sample_market_data(1000)
    
    env_config = {
        'initial_capital': 10000,
        'transaction_cost': 0.001,
        'max_position_size': 0.05,
        'max_positions': 3
    }
    
    env = TradingEnvironment(env_config)
    
    # 基准策略：简单跟随信号
    def baseline_strategy(market_state: MarketState) -> TradingAction:
        if market_state.model_signal == 1 and market_state.signal_confidence > 0.6:
            return TradingAction(
                enter_trade=True,
                position_size=0.03,  # 固定3%
                stop_loss_pct=0.02,  # 固定2%止损
                take_profit_pct=0.02,  # 固定2%止盈
                max_hold_time=120    # 固定2小时
            )
        else:
            return TradingAction(
                enter_trade=False,
                position_size=0.0,
                stop_loss_pct=0.02,
                take_profit_pct=0.02,
                max_hold_time=120
            )
    
    # 测试基准策略
    env.reset()
    baseline_trades = []
    
    for i, market_state in enumerate(test_data):
        action = baseline_strategy(market_state)
        trade_result = env.execute_action(action, market_state, 
                                        pd.Timestamp.now() + pd.Timedelta(minutes=i), i)
        completed_trades = env.update_positions(market_state.current_price,
                                              pd.Timestamp.now() + pd.Timedelta(minutes=i), i)
        baseline_trades.extend(completed_trades)
    
    baseline_metrics = env.get_portfolio_metrics()
    
    print(f"基准策略结果:")
    print(f"  总收益率: {baseline_metrics['total_return']:.2%}")
    print(f"  交易次数: {len(baseline_trades)}")
    print(f"  胜率: {sum(1 for t in baseline_trades if t['pnl'] > 0) / max(len(baseline_trades), 1):.2%}")
    
    return baseline_metrics, baseline_trades


def main():
    """主函数 - 完整的训练和评估流程"""
    print("🚀 RL交易代理完整示例")
    print("=" * 50)
    
    # 1. 训练代理
    print("\n📈 第一阶段：训练RL代理")
    trained_agent, training_stats = train_rl_agent(episodes=20, episode_length=200)
    
    # 2. 绘制训练进度
    print("\n📊 第二阶段：分析训练进度")
    plot_training_progress(training_stats)
    
    # 3. 评估代理
    print("\n🎯 第三阶段：评估训练好的代理")
    eval_results = evaluate_agent(trained_agent, episodes=5, episode_length=200)
    
    # 4. 与基准策略比较
    print("\n⚖️ 第四阶段：与基准策略比较")
    baseline_metrics, baseline_trades = compare_with_baseline()
    
    # 5. 保存最终模型
    print("\n💾 第五阶段：保存最终模型")
    final_model_path = "final_rl_trading_agent"
    trained_agent.save_model(final_model_path, metadata={
        'training_episodes': len(training_stats['rewards']),
        'final_avg_return': np.mean([r['total_return'] for r in eval_results]),
        'final_avg_win_rate': np.mean([r['win_rate'] for r in eval_results])
    })
    
    print(f"\n✅ 训练完成！最终模型已保存为: {final_model_path}")
    print("\n使用方法:")
    print(f"  agent = RLTradingAgent.load_model('{final_model_path}')")
    print("  action, _, _ = agent.get_action(state_vector, deterministic=True)")


if __name__ == "__main__":
    # 运行完整示例
    main()
    
    print("\n" + "="*50)
    print("🎉 RL交易代理示例运行完成！")
    print("现在你可以:")
    print("1. 查看训练进度图 (rl_training_progress.png)")
    print("2. 加载保存的模型进行进一步测试")
    print("3. 集成到现有的回测系统中")
    print("="*50)