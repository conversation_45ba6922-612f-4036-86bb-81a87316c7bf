# config_updater.py
# 配置动态更新工具

import json
import os
import time
import threading
from typing import Dict, Any, Optional, Callable, List
from pathlib import Path
from dataclasses import asdict
import copy

from .training_config import FullTrainingConfig, TrainingConfigManager
from .config_validator import ConfigValidator


class ConfigWatcher:
    """配置文件监控器"""
    
    def __init__(self, config_path: str, callback: Callable[[FullTrainingConfig], None]):
        self.config_path = Path(config_path)
        self.callback = callback
        self.last_modified = 0
        self.running = False
        self.thread = None
        self.manager = TrainingConfigManager()
        self.validator = ConfigValidator()
    
    def start(self):
        """开始监控"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._watch_loop, daemon=True)
        self.thread.start()
        print(f"开始监控配置文件: {self.config_path}")
    
    def stop(self):
        """停止监控"""
        self.running = False
        if self.thread:
            self.thread.join()
        print("配置文件监控已停止")
    
    def _watch_loop(self):
        """监控循环"""
        while self.running:
            try:
                if self.config_path.exists():
                    current_modified = self.config_path.stat().st_mtime
                    
                    if current_modified > self.last_modified:
                        self.last_modified = current_modified
                        self._handle_config_change()
                
                time.sleep(1)  # 每秒检查一次
                
            except Exception as e:
                print(f"监控配置文件时出错: {e}")
                time.sleep(5)  # 出错时等待更长时间
    
    def _handle_config_change(self):
        """处理配置文件变化"""
        try:
            # 加载新配置
            config = self.manager._load_config_from_file(self.config_path)
            if not config:
                print(f"无法加载配置文件: {self.config_path}")
                return
            
            # 验证配置
            issues = self.validator.validate_config(config)
            if issues['errors']:
                print(f"配置文件有错误，跳过更新:")
                for error in issues['errors']:
                    print(f"  - {error}")
                return
            
            # 调用回调函数
            print(f"检测到配置文件更新: {self.config_path}")
            self.callback(config)
            
        except Exception as e:
            print(f"处理配置文件变化时出错: {e}")


class DynamicConfigManager:
    """动态配置管理器"""
    
    def __init__(self, initial_config: FullTrainingConfig):
        self.current_config = copy.deepcopy(initial_config)
        self.config_history: List[FullTrainingConfig] = [copy.deepcopy(initial_config)]
        self.update_callbacks: List[Callable[[FullTrainingConfig, FullTrainingConfig], None]] = []
        self.validator = ConfigValidator()
        self._lock = threading.Lock()
    
    def register_update_callback(self, callback: Callable[[FullTrainingConfig, FullTrainingConfig], None]):
        """注册配置更新回调"""
        self.update_callbacks.append(callback)
    
    def update_config(self, new_config: FullTrainingConfig, validate: bool = True) -> bool:
        """更新配置"""
        with self._lock:
            # 验证新配置
            if validate:
                issues = self.validator.validate_config(new_config)
                if issues['errors']:
                    print("配置更新失败，存在错误:")
                    for error in issues['errors']:
                        print(f"  - {error}")
                    return False
            
            # 保存旧配置
            old_config = copy.deepcopy(self.current_config)
            
            # 更新配置
            self.current_config = copy.deepcopy(new_config)
            self.config_history.append(copy.deepcopy(new_config))
            
            # 限制历史记录长度
            if len(self.config_history) > 50:
                self.config_history = self.config_history[-50:]
            
            # 调用回调函数
            for callback in self.update_callbacks:
                try:
                    callback(old_config, new_config)
                except Exception as e:
                    print(f"配置更新回调出错: {e}")
            
            print(f"配置已更新: {new_config.config_name}")
            return True
    
    def update_partial(self, updates: Dict[str, Any], validate: bool = True) -> bool:
        """部分更新配置"""
        with self._lock:
            # 创建新配置
            new_config = copy.deepcopy(self.current_config)
            
            # 应用更新
            for key, value in updates.items():
                self._set_nested_value(new_config, key, value)
            
            return self.update_config(new_config, validate)
    
    def _set_nested_value(self, obj: Any, key: str, value: Any):
        """设置嵌套属性值"""
        if '.' in key:
            parts = key.split('.')
            for part in parts[:-1]:
                obj = getattr(obj, part)
            setattr(obj, parts[-1], value)
        else:
            setattr(obj, key, value)
    
    def get_current_config(self) -> FullTrainingConfig:
        """获取当前配置"""
        with self._lock:
            return copy.deepcopy(self.current_config)
    
    def rollback_config(self, steps: int = 1) -> bool:
        """回滚配置"""
        with self._lock:
            if len(self.config_history) <= steps:
                print("没有足够的历史配置进行回滚")
                return False
            
            # 回滚到指定步数前的配置
            target_config = self.config_history[-(steps + 1)]
            
            # 更新配置（不验证，因为历史配置应该是有效的）
            return self.update_config(target_config, validate=False)
    
    def get_config_diff(self, steps: int = 1) -> Dict[str, Any]:
        """获取配置差异"""
        with self._lock:
            if len(self.config_history) <= steps:
                return {}
            
            current = asdict(self.config_history[-1])
            previous = asdict(self.config_history[-(steps + 1)])
            
            return self._dict_diff(previous, current)
    
    def _dict_diff(self, old_dict: Dict, new_dict: Dict, prefix: str = "") -> Dict[str, Any]:
        """计算字典差异"""
        diff = {}
        
        # 检查所有键
        all_keys = set(old_dict.keys()) | set(new_dict.keys())
        
        for key in all_keys:
            full_key = f"{prefix}.{key}" if prefix else key
            
            if key not in old_dict:
                diff[full_key] = {'action': 'added', 'new_value': new_dict[key]}
            elif key not in new_dict:
                diff[full_key] = {'action': 'removed', 'old_value': old_dict[key]}
            elif old_dict[key] != new_dict[key]:
                if isinstance(old_dict[key], dict) and isinstance(new_dict[key], dict):
                    # 递归处理嵌套字典
                    nested_diff = self._dict_diff(old_dict[key], new_dict[key], full_key)
                    diff.update(nested_diff)
                else:
                    diff[full_key] = {
                        'action': 'changed',
                        'old_value': old_dict[key],
                        'new_value': new_dict[key]
                    }
        
        return diff
    
    def export_config_history(self, output_path: str):
        """导出配置历史"""
        history_data = {
            'current_config': asdict(self.current_config),
            'history': [asdict(config) for config in self.config_history],
            'export_time': time.time()
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(history_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"配置历史已导出到: {output_path}")


class ConfigScheduler:
    """配置调度器 - 支持定时配置更新"""
    
    def __init__(self, dynamic_manager: DynamicConfigManager):
        self.dynamic_manager = dynamic_manager
        self.scheduled_updates: List[Dict] = []
        self.running = False
        self.thread = None
    
    def schedule_update(self, delay_seconds: float, updates: Dict[str, Any], 
                       description: str = ""):
        """调度配置更新"""
        schedule_time = time.time() + delay_seconds
        
        self.scheduled_updates.append({
            'time': schedule_time,
            'updates': updates,
            'description': description
        })
        
        # 按时间排序
        self.scheduled_updates.sort(key=lambda x: x['time'])
        
        print(f"已调度配置更新: {description} (将在 {delay_seconds:.1f} 秒后执行)")
    
    def schedule_learning_rate_decay(self, initial_lr: float, decay_factor: float, 
                                   decay_interval_seconds: float, min_lr: float = 1e-6):
        """调度学习率衰减"""
        current_lr = initial_lr
        delay = decay_interval_seconds
        
        while current_lr > min_lr:
            new_lr = max(current_lr * decay_factor, min_lr)
            
            self.schedule_update(
                delay,
                {'rl.learning_rate': new_lr},
                f"学习率衰减到 {new_lr:.6f}"
            )
            
            current_lr = new_lr
            delay += decay_interval_seconds
    
    def schedule_exploration_decay(self, initial_epsilon: float, final_epsilon: float,
                                 decay_steps: int, step_interval_seconds: float):
        """调度探索率衰减"""
        decay_per_step = (initial_epsilon - final_epsilon) / decay_steps
        
        for step in range(1, decay_steps + 1):
            new_epsilon = max(initial_epsilon - decay_per_step * step, final_epsilon)
            delay = step * step_interval_seconds
            
            self.schedule_update(
                delay,
                {'rl.epsilon_start': new_epsilon},
                f"探索率衰减到 {new_epsilon:.4f}"
            )
    
    def start(self):
        """开始调度器"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.thread.start()
        print("配置调度器已启动")
    
    def stop(self):
        """停止调度器"""
        self.running = False
        if self.thread:
            self.thread.join()
        print("配置调度器已停止")
    
    def _scheduler_loop(self):
        """调度器循环"""
        while self.running:
            current_time = time.time()
            
            # 检查是否有需要执行的更新
            while (self.scheduled_updates and 
                   self.scheduled_updates[0]['time'] <= current_time):
                
                update = self.scheduled_updates.pop(0)
                
                try:
                    success = self.dynamic_manager.update_partial(
                        update['updates'], 
                        validate=True
                    )
                    
                    if success:
                        print(f"执行调度更新: {update['description']}")
                    else:
                        print(f"调度更新失败: {update['description']}")
                        
                except Exception as e:
                    print(f"执行调度更新时出错: {e}")
            
            time.sleep(1)  # 每秒检查一次


def create_adaptive_config_manager(initial_config: FullTrainingConfig,
                                 enable_file_watching: bool = False,
                                 config_file_path: Optional[str] = None) -> DynamicConfigManager:
    """创建自适应配置管理器"""
    
    # 创建动态配置管理器
    dynamic_manager = DynamicConfigManager(initial_config)
    
    # 添加自适应更新回调
    def adaptive_callback(old_config: FullTrainingConfig, new_config: FullTrainingConfig):
        """自适应配置更新回调"""
        print("配置已更新，检查自适应调整...")
        
        # 获取配置差异
        diff = dynamic_manager.get_config_diff()
        
        if diff:
            print("配置变化:")
            for key, change in diff.items():
                if change['action'] == 'changed':
                    print(f"  {key}: {change['old_value']} -> {change['new_value']}")
                elif change['action'] == 'added':
                    print(f"  {key}: 新增 {change['new_value']}")
                elif change['action'] == 'removed':
                    print(f"  {key}: 删除 {change['old_value']}")
    
    dynamic_manager.register_update_callback(adaptive_callback)
    
    # 启用文件监控
    if enable_file_watching and config_file_path:
        def file_update_callback(new_config: FullTrainingConfig):
            dynamic_manager.update_config(new_config, validate=True)
        
        watcher = ConfigWatcher(config_file_path, file_update_callback)
        watcher.start()
    
    return dynamic_manager


if __name__ == "__main__":
    # 测试动态配置管理
    print("测试动态配置管理系统")
    
    # 创建初始配置
    manager = TrainingConfigManager()
    initial_config = manager.load_config("default")
    
    if initial_config:
        # 创建动态配置管理器
        dynamic_manager = create_adaptive_config_manager(initial_config)
        
        # 测试部分更新
        print("\n测试部分配置更新...")
        success = dynamic_manager.update_partial({
            'rl.learning_rate': 0.0001,
            'training.episodes': 1500
        })
        
        if success:
            print("配置更新成功")
            
            # 显示配置差异
            diff = dynamic_manager.get_config_diff()
            print("配置差异:", diff)
        
        # 测试配置调度器
        print("\n测试配置调度器...")
        scheduler = ConfigScheduler(dynamic_manager)
        
        # 调度学习率衰减
        scheduler.schedule_learning_rate_decay(
            initial_lr=0.001,
            decay_factor=0.9,
            decay_interval_seconds=5,
            min_lr=1e-5
        )
        
        scheduler.start()
        
        # 等待一段时间观察调度效果
        print("等待调度执行...")
        time.sleep(12)
        
        scheduler.stop()
        
        print("动态配置管理系统测试完成！")
    else:
        print("无法加载默认配置")