"""
Training Manager for RL Trading Optimization

This module implements the TrainingManager class that orchestrates the entire training process
for the RL trading agent. It handles data splitting, episode sampling, training loops,
and progress monitoring.
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import os
import joblib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pickle
import time
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import torch

from .trading_environment import TradingEnvironment, MarketState, TradingAction
from .rl_trading_agent import RLTradingAgent
from .policy_network import PolicyNetwork
from .performance_optimizer import (
    PerformanceProfiler, MemoryOptimizer, TrainingSpeedOptimizer,
    setup_performance_optimizations, monitor_system_resources
)

# Import existing utilities
try:
    from model_utils_815 import calculate_features, get_coin_config
    from get_coin_history import get_table_name
except ImportError:
    print("Warning: model_utils_815 or get_coin_history not found. Some features may not work.")
    def calculate_features(df, timeframe): return df
    def get_coin_config(coin): return {'model_basename': f"{coin}_model", 'api_symbol': coin.upper()}
    def get_table_name(coin, interval, market): return f"{coin}_{interval}_{market}"


@dataclass
class TrainingConfig:
    """训练配置数据结构"""
    # 数据配置
    db_path: str = "coin_data.db"
    coin: str = "ETH"
    interval: str = "5m"
    market: str = "spot"
    
    # 模型配置
    model_file: str = None
    config_file: str = None
    
    # 训练配置
    episodes: int = 1000
    episode_length: int = 1000  # 每个episode的时间步数
    train_ratio: float = 0.7
    val_ratio: float = 0.15
    test_ratio: float = 0.15
    
    # 环境配置
    initial_capital: float = 10000.0
    transaction_cost: float = 0.001
    max_position_size: float = 0.1
    max_positions: int = 5
    
    # RL配置
    learning_rate: float = 3e-4
    batch_size: int = 64
    update_frequency: int = 100  # 每100步更新一次策略
    gamma: float = 0.99
    
    # 日志配置
    log_dir: str = "rl_training_logs"
    save_frequency: int = 100  # 每100个episode保存一次
    eval_frequency: int = 50   # 每50个episode评估一次


@dataclass
class EpisodeResult:
    """单个episode的结果"""
    episode_id: int
    total_reward: float
    total_return: float
    num_trades: int
    win_rate: float
    max_drawdown: float
    sharpe_ratio: float
    episode_length: int
    training_time: float


class TrainingLogger:
    """训练日志记录器"""
    
    def __init__(self, log_dir: str):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.logger = logging.getLogger('rl_training')
        self.logger.setLevel(logging.INFO)
        
        # 文件处理器
        log_file = self.log_dir / f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        # 训练统计
        self.episode_results: List[EpisodeResult] = []
        self.training_metrics = {
            'policy_losses': [],
            'value_losses': [],
            'rewards': [],
            'returns': [],
            'win_rates': [],
            'sharpe_ratios': [],
            'max_drawdowns': []
        }
    
    def log_episode(self, result: EpisodeResult):
        """记录episode结果"""
        self.episode_results.append(result)
        
        # 更新指标
        self.training_metrics['rewards'].append(result.total_reward)
        self.training_metrics['returns'].append(result.total_return)
        self.training_metrics['win_rates'].append(result.win_rate)
        self.training_metrics['sharpe_ratios'].append(result.sharpe_ratio)
        self.training_metrics['max_drawdowns'].append(result.max_drawdown)
        
        # 日志输出
        self.logger.info(
            f"Episode {result.episode_id}: "
            f"Reward={result.total_reward:.4f}, "
            f"Return={result.total_return:.2%}, "
            f"Trades={result.num_trades}, "
            f"WinRate={result.win_rate:.2%}, "
            f"Sharpe={result.sharpe_ratio:.3f}, "
            f"MaxDD={result.max_drawdown:.2%}"
        )
    
    def log_training_step(self, episode: int, losses: Dict[str, float]):
        """记录训练步骤"""
        if 'policy_loss' in losses:
            self.training_metrics['policy_losses'].append(losses['policy_loss'])
        if 'value_loss' in losses:
            self.training_metrics['value_losses'].append(losses['value_loss'])
        
        self.logger.info(
            f"Episode {episode} - Training: "
            f"PolicyLoss={losses.get('policy_loss', 0):.6f}, "
            f"ValueLoss={losses.get('value_loss', 0):.6f}, "
            f"Epsilon={losses.get('epsilon', 0):.4f}"
        )
    
    def save_metrics(self):
        """保存训练指标"""
        metrics_file = self.log_dir / "training_metrics.pkl"
        with open(metrics_file, 'wb') as f:
            pickle.dump({
                'episode_results': self.episode_results,
                'training_metrics': self.training_metrics
            }, f)
        
        # 保存CSV格式的结果
        if self.episode_results:
            df = pd.DataFrame([
                {
                    'episode': r.episode_id,
                    'total_reward': r.total_reward,
                    'total_return': r.total_return,
                    'num_trades': r.num_trades,
                    'win_rate': r.win_rate,
                    'max_drawdown': r.max_drawdown,
                    'sharpe_ratio': r.sharpe_ratio,
                    'episode_length': r.episode_length,
                    'training_time': r.training_time
                }
                for r in self.episode_results
            ])
            df.to_csv(self.log_dir / "episode_results.csv", index=False)
    
    def plot_training_progress(self):
        """绘制训练进度图"""
        if len(self.episode_results) < 10:
            return
        
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('RL Trading Training Progress', fontsize=16)
        
        episodes = [r.episode_id for r in self.episode_results]
        
        # 奖励曲线
        rewards = [r.total_reward for r in self.episode_results]
        axes[0, 0].plot(episodes, rewards, alpha=0.7)
        axes[0, 0].plot(episodes, pd.Series(rewards).rolling(50).mean(), 'r-', linewidth=2)
        axes[0, 0].set_title('Total Reward')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Reward')
        axes[0, 0].grid(True)
        
        # 收益率曲线
        returns = [r.total_return for r in self.episode_results]
        axes[0, 1].plot(episodes, returns, alpha=0.7)
        axes[0, 1].plot(episodes, pd.Series(returns).rolling(50).mean(), 'r-', linewidth=2)
        axes[0, 1].set_title('Total Return')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Return (%)')
        axes[0, 1].grid(True)
        
        # 胜率曲线
        win_rates = [r.win_rate for r in self.episode_results]
        axes[0, 2].plot(episodes, win_rates, alpha=0.7)
        axes[0, 2].plot(episodes, pd.Series(win_rates).rolling(50).mean(), 'r-', linewidth=2)
        axes[0, 2].set_title('Win Rate')
        axes[0, 2].set_xlabel('Episode')
        axes[0, 2].set_ylabel('Win Rate (%)')
        axes[0, 2].grid(True)
        
        # 夏普比率
        sharpe_ratios = [r.sharpe_ratio for r in self.episode_results]
        axes[1, 0].plot(episodes, sharpe_ratios, alpha=0.7)
        axes[1, 0].plot(episodes, pd.Series(sharpe_ratios).rolling(50).mean(), 'r-', linewidth=2)
        axes[1, 0].set_title('Sharpe Ratio')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('Sharpe Ratio')
        axes[1, 0].grid(True)
        
        # 最大回撤
        max_drawdowns = [r.max_drawdown for r in self.episode_results]
        axes[1, 1].plot(episodes, max_drawdowns, alpha=0.7)
        axes[1, 1].plot(episodes, pd.Series(max_drawdowns).rolling(50).mean(), 'r-', linewidth=2)
        axes[1, 1].set_title('Max Drawdown')
        axes[1, 1].set_xlabel('Episode')
        axes[1, 1].set_ylabel('Max Drawdown (%)')
        axes[1, 1].grid(True)
        
        # 交易次数
        num_trades = [r.num_trades for r in self.episode_results]
        axes[1, 2].plot(episodes, num_trades, alpha=0.7)
        axes[1, 2].plot(episodes, pd.Series(num_trades).rolling(50).mean(), 'r-', linewidth=2)
        axes[1, 2].set_title('Number of Trades')
        axes[1, 2].set_xlabel('Episode')
        axes[1, 2].set_ylabel('Trades')
        axes[1, 2].grid(True)
        
        plt.tight_layout()
        plt.savefig(self.log_dir / 'training_progress.png', dpi=300, bbox_inches='tight')
        plt.close()


class TrainingManager:
    """
    强化学习训练管理器
    
    负责管理整个训练流程，包括：
    - 数据加载和分割
    - Episode采样和执行
    - 训练循环和策略更新
    - 进度监控和日志记录
    """
    
    def __init__(self, config):
        """
        初始化训练管理器
        
        Args:
            config: 训练配置
        """
        self.config = config
        
        # 创建日志记录器
        self.logger = TrainingLogger(config.logging.log_dir)
        self.logger.logger.info("初始化训练管理器...")
        
        # 初始化性能优化器
        self.performance_profiler = PerformanceProfiler()
        self.memory_optimizer = MemoryOptimizer()
        self.speed_optimizer = TrainingSpeedOptimizer()
        
        # 设置性能优化
        self.speed_optimizer.setup_torch_optimizations()
        self.memory_optimizer.clear_memory()
        
        # 多进程配置
        self.use_multiprocessing = getattr(config, 'use_multiprocessing', False)
        self.num_workers = getattr(config, 'num_workers', mp.cpu_count() // 2)
        
        # 加载现有模型和配置
        self.existing_model = None
        self.existing_config = None
        if config.model.model_file and config.model.config_file:
            self.existing_model = joblib.load(config.model.model_file)
            with open(config.model.config_file, 'r') as f:
                self.existing_config = json.load(f)
            self.logger.logger.info(f"已加载现有模型: {config.model.model_file}")
        
        # 加载和预处理数据
        self.train_data, self.val_data, self.test_data = self._load_and_split_data()
        
        # 预计算所有信号（如果有现有模型）
        self.train_signals = None
        self.val_signals = None
        self.test_signals = None
        if self.existing_model and self.existing_config:
            self._precompute_signals()
        
        # 创建环境和代理
        self.env = self._create_environment()
        self.agent = self._create_agent()
        
        # 训练状态
        self.current_episode = 0
        self.best_performance = -float('inf')
        self.best_model_path = None
        
        self.logger.logger.info("训练管理器初始化完成")
        self.logger.logger.info(f"训练数据: {len(self.train_data)} 条")
        self.logger.logger.info(f"验证数据: {len(self.val_data)} 条")
        self.logger.logger.info(f"测试数据: {len(self.test_data)} 条")
        
        # 记录初始系统资源
        initial_resources = monitor_system_resources()
        self.logger.logger.info(f"初始系统资源: {initial_resources}")
    
    def _load_and_split_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """加载数据并按时间顺序分割"""
        self.logger.logger.info("加载历史数据...")
        
        # 从SQLite加载数据
        table_name = get_table_name(self.config.data.coin, self.config.data.interval, self.config.data.market)
        conn = sqlite3.connect(self.config.data.db_path)
        
        query = f"""
        SELECT timestamp, open, high, low, close, volume 
        FROM {table_name} 
        ORDER BY timestamp ASC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if df.empty:
            raise ValueError(f"数据库中没有找到表 {table_name} 的数据")
        
        # 转换时间戳
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
        df.set_index('timestamp', inplace=True)
        df = df.astype(float)
        
        # 按时间顺序分割数据
        total_len = len(df)
        train_end = int(total_len * self.config.data.train_ratio)
        val_end = int(total_len * (self.config.data.train_ratio + self.config.data.val_ratio))
        
        train_data = df.iloc[:train_end].copy()
        val_data = df.iloc[train_end:val_end].copy()
        test_data = df.iloc[val_end:].copy()
        
        self.logger.logger.info(f"数据分割完成:")
        self.logger.logger.info(f"  训练集: {len(train_data)} 条 ({train_data.index[0]} 到 {train_data.index[-1]})")
        self.logger.logger.info(f"  验证集: {len(val_data)} 条 ({val_data.index[0]} 到 {val_data.index[-1]})")
        self.logger.logger.info(f"  测试集: {len(test_data)} 条 ({test_data.index[0]} 到 {test_data.index[-1]})")
        
        return train_data, val_data, test_data
    
    def _precompute_signals(self):
        """预计算所有数据的交易信号"""
        self.logger.logger.info("预计算交易信号...")
        
        def compute_signals_for_data(data: pd.DataFrame) -> pd.DataFrame:
            """为给定数据计算信号"""
            # 计算特征
            features_df = calculate_features(data, timeframe=self.existing_config['timeframe_minutes'])
            features_df_clean = features_df.dropna()
            
            if features_df_clean.empty:
                return pd.DataFrame()
            
            # 提取特征
            feature_list = self.existing_config['feature_list']
            available_features = [f for f in feature_list if f in features_df_clean.columns]
            
            if not available_features:
                return pd.DataFrame()
            
            X = features_df_clean[available_features]
            
            # 预测
            probabilities = self.existing_model.predict_proba(X)[:, 1]
            threshold = self.existing_config['best_threshold']
            
            # 生成信号
            signals = np.where(probabilities > threshold, 1, 
                             np.where(probabilities < (1 - threshold), 0, -1))  # -1表示无信号
            
            # 创建结果DataFrame
            result_df = features_df_clean[['close']].copy()
            result_df['signal'] = signals
            result_df['probability'] = probabilities
            result_df['confidence'] = np.abs(probabilities - 0.5) * 2  # 置信度 [0, 1]
            
            return result_df
        
        # 计算各数据集的信号
        self.train_signals = compute_signals_for_data(self.train_data)
        self.val_signals = compute_signals_for_data(self.val_data)
        self.test_signals = compute_signals_for_data(self.test_data)
        
        self.logger.logger.info(f"信号计算完成:")
        self.logger.logger.info(f"  训练信号: {len(self.train_signals)} 条")
        self.logger.logger.info(f"  验证信号: {len(self.val_signals)} 条")
        self.logger.logger.info(f"  测试信号: {len(self.test_signals)} 条")
    
    def _create_environment(self) -> TradingEnvironment:
        """创建交易环境"""
        env_config = {
            'initial_capital': self.config.environment.initial_capital,
            'transaction_cost': self.config.environment.transaction_cost,
            'max_position_size': self.config.environment.max_position_size,
            'max_positions': self.config.environment.max_positions,
            'reward_config': {
                'pnl_weight': self.config.reward.pnl_weight,
                'risk_penalty': self.config.reward.risk_penalty_weight,
                'time_efficiency': self.config.reward.time_efficiency_weight,
                'max_drawdown_penalty': self.config.reward.drawdown_penalty_weight
            }
        }
        
        return TradingEnvironment(env_config)
    
    def _create_agent(self) -> RLTradingAgent:
        """创建RL代理"""
        agent_config = {
            'state_dim': self.env.get_state_dim(),
            'hidden_dim': self.config.model.hidden_dim,
            'learning_rate': self.config.rl.learning_rate,
            'batch_size': self.config.rl.batch_size,
            'gamma': self.config.rl.gamma,
            'clip_epsilon': self.config.rl.clip_epsilon,
            'value_loss_coef': self.config.rl.value_loss_coef,
            'entropy_coef': self.config.rl.entropy_coef,
            'gae_lambda': self.config.rl.gae_lambda,
            'update_epochs': self.config.rl.update_epochs,
            'max_grad_norm': self.config.rl.max_grad_norm,
            'buffer_size': self.config.rl.buffer_size,
            'exploration_noise': self.config.rl.exploration_noise,
            'epsilon_start': self.config.rl.epsilon_start,
            'epsilon_end': self.config.rl.epsilon_end,
            'epsilon_decay': self.config.rl.epsilon_decay
        }
        
        return RLTradingAgent(agent_config) 
   
    def sample_episode_data(self, data: pd.DataFrame, signals: pd.DataFrame, 
                           episode_length: int) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        从数据中采样一个episode
        
        Args:
            data: 价格数据
            signals: 信号数据
            episode_length: episode长度
            
        Returns:
            Tuple of (episode_data, episode_signals)
        """
        if len(data) < episode_length:
            return data.copy(), signals.copy()
        
        # 随机选择起始点
        max_start = len(data) - episode_length
        start_idx = np.random.randint(0, max_start)
        end_idx = start_idx + episode_length
        
        episode_data = data.iloc[start_idx:end_idx].copy()
        
        # 对应的信号数据
        if signals is not None and not signals.empty:
            # 找到对应的信号数据
            episode_signals = signals.loc[episode_data.index].copy()
        else:
            episode_signals = pd.DataFrame()
        
        return episode_data, episode_signals
    
    def create_market_state(self, current_idx: int, data: pd.DataFrame, 
                           signals: pd.DataFrame) -> MarketState:
        """
        创建当前时刻的市场状态
        
        Args:
            current_idx: 当前数据索引
            data: 价格数据
            signals: 信号数据
            
        Returns:
            MarketState对象
        """
        current_row = data.iloc[current_idx]
        current_time = data.index[current_idx]
        
        # 获取信号信息
        model_signal = None
        signal_confidence = 0.5
        signal_probability = 0.5
        
        if not signals.empty and current_time in signals.index:
            signal_row = signals.loc[current_time]
            if signal_row['signal'] != -1:  # -1表示无信号
                model_signal = int(signal_row['signal'])
                signal_confidence = float(signal_row['confidence'])
                signal_probability = float(signal_row['probability'])
        
        # 计算价格变化
        price_change_1h = 0.0
        price_change_4h = 0.0
        volatility_recent = 0.0
        
        if current_idx >= 12:  # 1小时 = 12个5分钟K线
            price_1h_ago = data.iloc[current_idx - 12]['close']
            price_change_1h = (current_row['close'] - price_1h_ago) / price_1h_ago
        
        if current_idx >= 48:  # 4小时 = 48个5分钟K线
            price_4h_ago = data.iloc[current_idx - 48]['close']
            price_change_4h = (current_row['close'] - price_4h_ago) / price_4h_ago
        
        if current_idx >= 20:  # 最近20个K线的波动率
            recent_prices = data.iloc[current_idx-20:current_idx+1]['close']
            returns = recent_prices.pct_change().dropna()
            volatility_recent = returns.std() if len(returns) > 1 else 0.0
        
        # 获取投资组合指标
        portfolio_metrics = self.env.get_portfolio_metrics()
        
        # 时间特征
        hour = current_time.hour
        day_of_week = current_time.weekday()
        is_good_trading_time = True  # 简化处理，实际可以加载chushou.json
        
        return MarketState(
            model_signal=model_signal,
            signal_confidence=signal_confidence,
            signal_probability=signal_probability,
            current_price=float(current_row['close']),
            price_change_1h=price_change_1h,
            price_change_4h=price_change_4h,
            volatility_recent=volatility_recent,
            cash_ratio=portfolio_metrics['current_capital'] / self.config.initial_capital,
            position_count=len(self.env.active_positions),
            unrealized_pnl=sum(pos.get('unrealized_pnl', 0) for pos in self.env.active_positions.values()),
            recent_win_rate=portfolio_metrics['recent_win_rate'],
            consecutive_losses=portfolio_metrics['consecutive_losses'],
            hour=hour,
            day_of_week=day_of_week,
            is_good_trading_time=is_good_trading_time
        )
    
    def run_episode(self, data: pd.DataFrame, signals: pd.DataFrame, 
                   training: bool = True) -> EpisodeResult:
        """
        运行一个完整的episode
        
        Args:
            data: 价格数据
            signals: 信号数据
            training: 是否为训练模式
            
        Returns:
            EpisodeResult对象
        """
        start_time = time.time()
        
        # 重置环境
        self.env.reset()
        
        # 采样episode数据
        episode_data, episode_signals = self.sample_episode_data(
            data, signals, self.config.training.episode_length
        )
        
        total_reward = 0.0
        step_count = 0
        
        # 设置代理模式
        self.agent.set_training_mode(training)
        
        for i in range(len(episode_data)):
            # 创建市场状态
            market_state = self.create_market_state(i, episode_data, episode_signals)
            
            # 跳过没有有效信号的时刻
            if market_state.model_signal is None:
                # 更新现有仓位
                current_price = market_state.current_price
                current_time = episode_data.index[i]
                completed_trades = self.env.update_positions(current_price, current_time, i)
                
                # 计算奖励
                if completed_trades:
                    reward = self.env.calculate_reward(completed_trades, market_state)
                    total_reward += reward
                
                continue
            
            # 获取状态向量
            state_vector = self.env.get_state_vector(market_state)
            
            # 代理选择动作
            action_dict, log_prob, value = self.agent.get_action(
                state_vector, deterministic=not training
            )
            
            # 创建交易动作对象
            trading_action = TradingAction(
                enter_trade=action_dict['enter_trade'],
                position_size=action_dict['position_size'],
                stop_loss_pct=action_dict['stop_loss_pct'],
                take_profit_pct=action_dict['take_profit_pct'],
                max_hold_time=action_dict['max_hold_time']
            )
            
            # 执行动作
            current_time = episode_data.index[i]
            execution_result = self.env.execute_action(
                trading_action, market_state, current_time, i
            )
            
            # 更新现有仓位
            current_price = market_state.current_price
            completed_trades = self.env.update_positions(current_price, current_time, i)
            
            # 计算奖励
            step_reward = 0.0
            if completed_trades:
                step_reward = self.env.calculate_reward(completed_trades, market_state)
                total_reward += step_reward
            
            # 存储经验（仅在训练模式）
            if training and i < len(episode_data) - 1:
                next_market_state = self.create_market_state(i + 1, episode_data, episode_signals)
                next_state_vector = self.env.get_state_vector(next_market_state)
                
                self.agent.store_experience(
                    state=state_vector,
                    action=action_dict,
                    reward=step_reward,
                    next_state=next_state_vector,
                    done=(i == len(episode_data) - 1),
                    log_prob=log_prob,
                    value=value
                )
            
            step_count += 1
            
            # 定期更新策略（仅在训练模式）
            if training and step_count % self.config.training.update_frequency == 0:
                losses = self.agent.update_policy()
                if losses and losses.get('policy_loss', 0) > 0:  # 有实际更新
                    self.logger.log_training_step(self.current_episode, losses)
        
        # 强制平仓所有剩余仓位
        if self.env.active_positions:
            final_price = episode_data.iloc[-1]['close']
            final_time = episode_data.index[-1]
            final_trades = []
            
            for pos_id, position in list(self.env.active_positions.items()):
                # 计算最终盈亏
                if position['signal'] == 1:  # 看涨仓位
                    pnl_pct = (final_price - position['entry_price']) / position['entry_price']
                else:  # 看跌仓位
                    pnl_pct = (position['entry_price'] - final_price) / position['entry_price']
                
                final_pnl = position['capital'] * pnl_pct - position['transaction_cost']
                exit_cost = position['capital'] * self.env.transaction_cost
                final_pnl -= exit_cost
                
                # 更新资金
                if hasattr(self.env, 'current_capital'):
                    self.env.current_capital += position['capital'] + final_pnl
                
                # 记录交易
                completed_trade = {
                    'position_id': pos_id,
                    'signal': position['signal'],
                    'confidence': position['confidence'],
                    'entry_price': position['entry_price'],
                    'exit_price': final_price,
                    'entry_time': position['entry_time'],
                    'exit_time': final_time,
                    'hold_time_minutes': len(episode_data) - position['entry_idx'],
                    'capital': position['capital'],
                    'pnl': final_pnl,
                    'pnl_pct': final_pnl / position['capital'],
                    'max_loss_pct': position['max_loss'],
                    'exit_reason': 'episode_end',
                    'total_cost': position['transaction_cost'] + exit_cost
                }
                
                final_trades.append(completed_trade)
                self.env.completed_trades.append(completed_trade)
            
            # 清空活跃仓位
            self.env.active_positions.clear()
            
            # 计算最终奖励
            if final_trades:
                final_reward = self.env.calculate_reward(final_trades, market_state)
                total_reward += final_reward
        
        # 计算episode统计
        training_time = time.time() - start_time
        current_capital = getattr(self.env, 'current_capital', self.config.environment.initial_capital)
        total_return = (current_capital - self.config.environment.initial_capital) / self.config.environment.initial_capital
        
        # 计算交易统计
        if self.env.completed_trades:
            winning_trades = [t for t in self.env.completed_trades if t['pnl'] > 0]
            win_rate = len(winning_trades) / len(self.env.completed_trades)
            
            # 计算夏普比率（简化版本）
            returns = [t['pnl_pct'] for t in self.env.completed_trades]
            if len(returns) > 1:
                sharpe_ratio = np.mean(returns) / (np.std(returns) + 1e-8)
            else:
                sharpe_ratio = 0.0
            
            # 计算最大回撤
            capital_curve = [self.config.environment.initial_capital]
            for trade in getattr(self.env, 'completed_trades', []):
                capital_curve.append(capital_curve[-1] + trade['pnl'])
            
            peak = capital_curve[0]
            max_drawdown = 0.0
            for capital in capital_curve:
                if capital > peak:
                    peak = capital
                drawdown = (peak - capital) / peak
                max_drawdown = max(max_drawdown, drawdown)
        else:
            win_rate = 0.0
            sharpe_ratio = 0.0
            max_drawdown = 0.0
        
        return EpisodeResult(
            episode_id=self.current_episode,
            total_reward=total_reward,
            total_return=total_return,
            num_trades=len(getattr(self.env, 'completed_trades', [])),
            win_rate=win_rate,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            episode_length=len(episode_data),
            training_time=training_time
        )
    
    def evaluate_agent(self, data: pd.DataFrame, signals: pd.DataFrame, 
                      num_episodes: int = 10) -> Dict[str, float]:
        """
        评估代理性能
        
        Args:
            data: 评估数据
            signals: 信号数据
            num_episodes: 评估episode数量
            
        Returns:
            评估指标字典
        """
        self.logger.logger.info(f"开始评估代理性能 ({num_episodes} episodes)...")
        
        results = []
        for i in range(num_episodes):
            result = self.run_episode(data, signals, training=False)
            results.append(result)
        
        # 计算平均指标
        metrics = {
            'avg_reward': np.mean([r.total_reward for r in results]),
            'avg_return': np.mean([r.total_return for r in results]),
            'avg_win_rate': np.mean([r.win_rate for r in results]),
            'avg_sharpe_ratio': np.mean([r.sharpe_ratio for r in results]),
            'avg_max_drawdown': np.mean([r.max_drawdown for r in results]),
            'avg_num_trades': np.mean([r.num_trades for r in results]),
            'std_return': np.std([r.total_return for r in results]),
            'best_return': max([r.total_return for r in results]),
            'worst_return': min([r.total_return for r in results])
        }
        
        self.logger.logger.info("评估完成:")
        for key, value in metrics.items():
            if 'rate' in key or 'return' in key or 'drawdown' in key:
                self.logger.logger.info(f"  {key}: {value:.2%}")
            else:
                self.logger.logger.info(f"  {key}: {value:.4f}")
        
        return metrics
    
    def save_checkpoint(self, episode: int, performance: float):
        """保存训练检查点"""
        checkpoint_dir = Path(self.config.log_dir) / "checkpoints"
        checkpoint_dir.mkdir(exist_ok=True)
        
        # 保存代理模型
        model_path = checkpoint_dir / f"rl_agent_episode_{episode}"
        self.agent.save_model(
            str(model_path),
            metadata={
                'episode': episode,
                'performance': performance,
                'timestamp': datetime.now().isoformat(),
                'config': self.config.__dict__
            }
        )
        
        # 如果是最佳性能，保存为best模型
        if performance > self.best_performance:
            self.best_performance = performance
            best_model_path = checkpoint_dir / "rl_agent_best"
            self.agent.save_model(
                str(best_model_path),
                metadata={
                    'episode': episode,
                    'best_performance': performance,
                    'timestamp': datetime.now().isoformat(),
                    'config': self.config.__dict__
                }
            )
            self.best_model_path = str(best_model_path)
            self.logger.logger.info(f"新的最佳模型已保存: {best_model_path}")
        
        # 保存训练指标
        self.logger.save_metrics()
        
        self.logger.logger.info(f"检查点已保存: episode {episode}, performance {performance:.4f}")
    
    def load_checkpoint(self, checkpoint_path: str):
        """加载训练检查点"""
        try:
            self.agent.load_model(checkpoint_path)
            
            # 尝试加载元数据
            metadata_file = Path(checkpoint_path).with_suffix('.json')
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                self.current_episode = metadata.get('episode', 0)
                self.best_performance = metadata.get('performance', -float('inf'))
                
                self.logger.logger.info(f"已从检查点恢复: episode {self.current_episode}, performance {self.best_performance:.4f}")
            else:
                self.logger.logger.warning("未找到检查点元数据文件")
                
        except Exception as e:
            self.logger.logger.error(f"加载检查点失败: {e}")
            raise
    
    def train(self, progress_callback=None, interrupted_flag=None) -> Dict[str, Any]:
        """
        执行完整的训练流程
        
        Args:
            progress_callback: 进度回调函数 (episode, metrics) -> None
            interrupted_flag: 中断标志字典 {'value': bool}
        
        Returns:
            训练结果字典
        """
        self.logger.logger.info("开始RL交易代理训练...")
        self.logger.logger.info(f"训练配置: {self.config.training.episodes} episodes, {self.config.training.episode_length} steps/episode")
        
        training_start_time = time.time()
        
        for episode in range(1, self.config.training.episodes + 1):
            # 检查中断标志
            if interrupted_flag and interrupted_flag.get('value', False):
                self.logger.logger.info(f"训练在第 {episode} episode 被中断")
                break
            
            self.current_episode = episode
            
            # 运行训练episode
            result = self.run_episode(self.train_data, self.train_signals, training=True)
            
            # 记录结果
            self.logger.log_episode(result)
            
            # 调用进度回调
            if progress_callback:
                episode_metrics = {
                    'episode': episode,
                    'total_reward': result.total_reward,
                    'total_return': result.total_return,
                    'win_rate': result.win_rate,
                    'num_trades': result.num_trades,
                    'sharpe_ratio': result.sharpe_ratio,
                    'max_drawdown': result.max_drawdown,
                    'training_time': result.training_time
                }
                progress_callback(episode, episode_metrics)
            
            # 定期评估
            if episode % self.config.training.eval_frequency == 0 and episode > 0:
                eval_metrics = self.evaluate_agent(self.val_data, self.val_signals, num_episodes=5)
                
                # 保存检查点
                performance_score = eval_metrics['avg_return']  # 使用平均收益率作为性能指标
                self.save_checkpoint(episode, performance_score)
                
                # 绘制进度图
                self.logger.plot_training_progress()
            
            # 定期保存
            elif episode % self.config.training.save_frequency == 0 and episode > 0:
                self.save_checkpoint(episode, result.total_return)
            
            # 检查最大训练时间
            elapsed_time = time.time() - training_start_time
            if elapsed_time > self.config.training.max_training_time_hours * 3600:
                self.logger.logger.info(f"达到最大训练时间 {self.config.training.max_training_time_hours} 小时，停止训练")
                break
        
        # 训练完成
        training_time = time.time() - training_start_time
        
        # 最终评估
        self.logger.logger.info("训练完成，进行最终评估...")
        
        final_train_metrics = self.evaluate_agent(self.train_data, self.train_signals, num_episodes=10)
        final_val_metrics = self.evaluate_agent(self.val_data, self.val_signals, num_episodes=10)
        final_test_metrics = self.evaluate_agent(self.test_data, self.test_signals, num_episodes=10)
        
        # 保存最终模型
        final_model_path = Path(self.config.logging.log_dir) / "checkpoints" / "rl_agent_final"
        self.agent.save_model(
            str(final_model_path),
            metadata={
                'episode': self.current_episode,
                'training_time': training_time,
                'final_train_metrics': final_train_metrics,
                'final_val_metrics': final_val_metrics,
                'final_test_metrics': final_test_metrics,
                'config': self.config.__dict__
            }
        )
        
        # 绘制最终进度图
        self.logger.plot_training_progress()
        
        # 训练总结
        training_summary = {
            'training_time': training_time,
            'total_episodes': self.current_episode,
            'best_performance': self.best_performance,
            'best_model_path': self.best_model_path,
            'final_train_metrics': final_train_metrics,
            'final_val_metrics': final_val_metrics,
            'final_test_metrics': final_test_metrics,
            'interrupted': interrupted_flag.get('value', False) if interrupted_flag else False
        }
        
        self.logger.logger.info("=" * 60)
        self.logger.logger.info("训练完成总结:")
        self.logger.logger.info(f"总训练时间: {training_time:.2f} 秒")
        self.logger.logger.info(f"最佳性能: {self.best_performance:.2%}")
        self.logger.logger.info(f"最佳模型路径: {self.best_model_path}")
        self.logger.logger.info("最终测试集性能:")
        for key, value in final_test_metrics.items():
            if 'rate' in key or 'return' in key or 'drawdown' in key:
                self.logger.logger.info(f"  {key}: {value:.2%}")
            else:
                self.logger.logger.info(f"  {key}: {value:.4f}")
        self.logger.logger.info("=" * 60)
        
        return training_summary


def create_default_training_config(
    model_file: str,
    config_file: str,
    coin: str = "ETH",
    interval: str = "5m"
) -> TrainingConfig:
    """
    创建默认的训练配置
    
    Args:
        model_file: 现有模型文件路径
        config_file: 现有配置文件路径
        coin: 币种
        interval: 时间间隔
        
    Returns:
        TrainingConfig对象
    """
    return TrainingConfig(
        model_file=model_file,
        config_file=config_file,
        coin=coin,
        interval=interval,
        episodes=1000,
        episode_length=1000,
        initial_capital=10000.0,
        log_dir=f"rl_training_logs_{coin}_{interval}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )


if __name__ == "__main__":
    # 测试训练管理器
    print("测试训练管理器...")
    
    # 创建测试配置
    config = TrainingConfig(
        model_file="models/eth_5m_model.joblib",
        config_file="models/eth_5m_config.json",
        coin="ETH",
        interval="5m",
        episodes=10,  # 少量episode用于测试
        episode_length=100,
        log_dir="test_rl_training"
    )
    
    try:
        # 创建训练管理器
        trainer = TrainingManager(config)
        
        # 运行少量训练
        results = trainer.train()
        
        print("训练管理器测试完成！")
        print(f"测试结果: {results}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc() 
   
    def train_with_multiprocessing(self, episodes: int = None) -> Dict[str, Any]:
        """
        使用多进程进行训练以提高速度
        
        Args:
            episodes: 训练episode数量
            
        Returns:
            训练结果字典
        """
        if episodes is None:
            episodes = self.config.training.episodes
            
        self.logger.logger.info(f"开始多进程训练 ({episodes} episodes, {self.num_workers} workers)...")
        
        # 将episodes分配给不同的进程
        episodes_per_worker = episodes // self.num_workers
        remaining_episodes = episodes % self.num_workers
        
        episode_batches = []
        for i in range(self.num_workers):
            batch_size = episodes_per_worker + (1 if i < remaining_episodes else 0)
            episode_batches.append(batch_size)
        
        # 使用进程池执行训练
        with ProcessPoolExecutor(max_workers=self.num_workers) as executor:
            futures = []
            
            for worker_id, batch_size in enumerate(episode_batches):
                if batch_size > 0:
                    future = executor.submit(
                        self._train_worker_batch,
                        worker_id, batch_size, self.train_data, self.train_signals
                    )
                    futures.append(future)
            
            # 收集结果
            all_results = []
            for future in as_completed(futures):
                try:
                    worker_results = future.result()
                    all_results.extend(worker_results)
                except Exception as e:
                    self.logger.logger.error(f"Worker failed: {e}")
        
        # 合并结果并更新主模型
        self._merge_worker_results(all_results)
        
        return {'total_episodes': len(all_results), 'avg_reward': np.mean([r.total_reward for r in all_results])}
    
    def _train_worker_batch(self, worker_id: int, batch_size: int, 
                           data: pd.DataFrame, signals: pd.DataFrame) -> List[EpisodeResult]:
        """
        工作进程执行的训练批次
        
        Args:
            worker_id: 工作进程ID
            batch_size: 批次大小
            data: 训练数据
            signals: 信号数据
            
        Returns:
            Episode结果列表
        """
        # 为每个worker创建独立的环境和代理
        worker_env = self._create_environment()
        worker_agent = self._create_agent()
        
        results = []
        for episode in range(batch_size):
            result = self.run_episode(data, signals, training=True)
            results.append(result)
            
            # 定期清理内存
            if episode % 10 == 0:
                self.memory_optimizer.clear_memory()
        
        return results
    
    def _merge_worker_results(self, all_results: List[EpisodeResult]):
        """
        合并多个worker的训练结果
        
        Args:
            all_results: 所有worker的结果
        """
        # 记录所有结果
        for result in all_results:
            self.logger.log_episode(result)
        
        # 更新当前episode计数
        self.current_episode += len(all_results)
        
        # 计算平均性能并更新最佳模型
        avg_performance = np.mean([r.total_reward for r in all_results])
        if avg_performance > self.best_performance:
            self.best_performance = avg_performance
            self.save_checkpoint(self.current_episode, avg_performance)
    
    def optimize_training_performance(self):
        """
        优化训练性能设置
        """
        # 优化批次大小
        if hasattr(self.agent, 'policy_network'):
            optimal_batch_size = self.speed_optimizer.batch_size_finder(
                self.agent.policy_network, 
                (self.env.get_state_dim(),),
                max_memory_mb=6000  # 限制GPU内存使用
            )
            
            # 更新配置
            if optimal_batch_size != self.config.rl.batch_size:
                self.logger.logger.info(f"优化批次大小: {self.config.rl.batch_size} -> {optimal_batch_size}")
                self.config.rl.batch_size = optimal_batch_size
                self.agent.batch_size = optimal_batch_size
        
        # 优化数据加载
        if hasattr(self, 'train_data'):
            # 优化numpy数组
            optimized_data = self.memory_optimizer.optimize_numpy_arrays([
                self.train_data.values, self.val_data.values, self.test_data.values
            ])
            
            # 创建内存映射数据集（对于大数据集）
            if len(self.train_data) > 100000:
                self.logger.logger.info("创建内存映射数据集以优化大数据集处理...")
                train_memmap = self.memory_optimizer.create_memory_mapped_dataset(
                    optimized_data[0], 
                    str(Path(self.config.logging.log_dir) / "train_data.memmap")
                )
    
    def monitor_training_performance(self) -> Dict[str, float]:
        """
        监控训练性能指标
        
        Returns:
            性能指标字典
        """
        # 获取当前系统资源使用
        current_resources = monitor_system_resources()
        
        # 获取训练性能摘要
        performance_summary = self.performance_profiler.get_performance_summary()
        
        # 合并指标
        all_metrics = {**current_resources, **performance_summary}
        
        # 记录到日志
        self.logger.logger.info("当前性能指标:")
        for key, value in all_metrics.items():
            self.logger.logger.info(f"  {key}: {value}")
        
        return all_metrics
    
    def run_episode_with_profiling(self, data: pd.DataFrame, signals: pd.DataFrame, 
                                  training: bool = True) -> Tuple[EpisodeResult, Dict[str, float]]:
        """
        运行带性能分析的episode
        
        Args:
            data: 价格数据
            signals: 信号数据
            training: 是否为训练模式
            
        Returns:
            Tuple of (EpisodeResult, performance_metrics)
        """
        def episode_func():
            return self.run_episode(data, signals, training)
        
        # 使用性能分析器
        result, metrics = self.performance_profiler.profile_training_step(episode_func)
        
        return result, {
            'memory_usage_mb': metrics.memory_usage_mb,
            'cpu_usage_percent': metrics.cpu_usage_percent,
            'gpu_memory_mb': metrics.gpu_memory_mb,
            'training_time_seconds': metrics.training_time_seconds,
            'episodes_per_second': metrics.episodes_per_second
        }
    
    def adaptive_batch_size_training(self, episodes: int = None) -> Dict[str, Any]:
        """
        自适应批次大小训练
        
        Args:
            episodes: 训练episode数量
            
        Returns:
            训练结果
        """
        if episodes is None:
            episodes = self.config.training.episodes
        
        self.logger.logger.info("开始自适应批次大小训练...")
        
        # 初始批次大小
        current_batch_size = self.config.rl.batch_size
        performance_history = []
        
        for episode in range(episodes):
            # 运行带性能分析的episode
            result, perf_metrics = self.run_episode_with_profiling(
                self.train_data, self.train_signals, training=True
            )
            
            performance_history.append(perf_metrics)
            self.logger.log_episode(result)
            
            # 每50个episode调整批次大小
            if episode > 0 and episode % 50 == 0:
                avg_memory = np.mean([p['memory_usage_mb'] for p in performance_history[-50:]])
                avg_speed = np.mean([p['episodes_per_second'] for p in performance_history[-50:]])
                
                # 如果内存使用过高，减少批次大小
                if avg_memory > 6000:  # 6GB限制
                    new_batch_size = max(16, int(current_batch_size * 0.8))
                    self.logger.logger.info(f"内存使用过高，减少批次大小: {current_batch_size} -> {new_batch_size}")
                    current_batch_size = new_batch_size
                    self.agent.batch_size = new_batch_size
                
                # 如果速度太慢且内存充足，增加批次大小
                elif avg_speed < 0.5 and avg_memory < 4000:  # 每秒0.5个episode，4GB内存
                    new_batch_size = min(256, int(current_batch_size * 1.2))
                    self.logger.logger.info(f"速度较慢且内存充足，增加批次大小: {current_batch_size} -> {new_batch_size}")
                    current_batch_size = new_batch_size
                    self.agent.batch_size = new_batch_size
            
            # 定期清理内存
            if episode % 20 == 0:
                self.memory_optimizer.clear_memory()
            
            # 定期监控性能
            if episode % 100 == 0:
                self.monitor_training_performance()
        
        return {
            'total_episodes': episodes,
            'final_batch_size': current_batch_size,
            'avg_performance': performance_history
        }
    
    def memory_efficient_training(self, episodes: int = None) -> Dict[str, Any]:
        """
        内存高效训练模式
        
        Args:
            episodes: 训练episode数量
            
        Returns:
            训练结果
        """
        if episodes is None:
            episodes = self.config.training.episodes
        
        self.logger.logger.info("开始内存高效训练...")
        
        # 使用内存监控器
        with self.memory_optimizer.memory_monitor("memory_efficient_training"):
            results = []
            
            for episode in range(episodes):
                # 每个episode前清理内存
                if episode % 10 == 0:
                    self.memory_optimizer.clear_memory()
                
                # 运行episode
                result = self.run_episode(self.train_data, self.train_signals, training=True)
                results.append(result)
                self.logger.log_episode(result)
                
                # 定期保存检查点并清理
                if episode % 100 == 0:
                    avg_performance = np.mean([r.total_reward for r in results[-100:]])
                    self.save_checkpoint(episode, avg_performance)
                    
                    # 强制垃圾回收
                    self.memory_optimizer.clear_memory()
                    
                    # 监控内存使用
                    memory_stats = self.memory_optimizer.get_memory_usage()
                    self.logger.logger.info(f"Episode {episode} 内存使用: {memory_stats}")
        
        return {
            'total_episodes': len(results),
            'avg_reward': np.mean([r.total_reward for r in results])
        }
    
    def save_performance_report(self, filepath: str = None):
        """
        保存详细的性能报告
        
        Args:
            filepath: 报告文件路径
        """
        if filepath is None:
            filepath = Path(self.config.logging.log_dir) / "performance_report.txt"
        
        # 保存性能分析器报告
        self.performance_profiler.save_performance_report(filepath)
        
        # 添加额外的系统信息
        with open(filepath, 'a') as f:
            f.write("\n\n系统配置信息\n")
            f.write("================\n")
            f.write(f"CPU核心数: {mp.cpu_count()}\n")
            f.write(f"PyTorch版本: {torch.__version__}\n")
            f.write(f"CUDA可用: {torch.cuda.is_available()}\n")
            
            if torch.cuda.is_available():
                f.write(f"GPU设备: {torch.cuda.get_device_name()}\n")
                f.write(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\n")
            
            # 训练配置
            f.write(f"\n训练配置:\n")
            f.write(f"批次大小: {self.config.rl.batch_size}\n")
            f.write(f"学习率: {self.config.rl.learning_rate}\n")
            f.write(f"Episode长度: {self.config.training.episode_length}\n")
            f.write(f"更新频率: {self.config.training.update_frequency}\n")