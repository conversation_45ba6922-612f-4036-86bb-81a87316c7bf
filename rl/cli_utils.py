#!/usr/bin/env python3
"""
CLI Utilities for RL Trading System

Provides utility functions for model management, batch operations, and system monitoring.
"""

import os
import json
import glob
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import sqlite3
import pandas as pd
from datetime import datetime, timedel<PERSON>


def list_available_models(models_dir: str = "models/") -> List[Dict[str, Any]]:
    """列出所有可用的模型"""
    models_path = Path(models_dir)
    if not models_path.exists():
        return []
    
    models = []
    
    # 查找所有模型文件
    for model_file in models_path.glob("*_model.joblib"):
        config_file = model_file.with_name(model_file.name.replace('_model.joblib', '_config.json'))
        
        model_info = {
            'name': model_file.stem.replace('_model', ''),
            'model_file': str(model_file),
            'config_file': str(config_file) if config_file.exists() else None,
            'size_mb': model_file.stat().st_size / 1024 / 1024,
            'modified': datetime.fromtimestamp(model_file.stat().st_mtime).isoformat()
        }
        
        # 尝试读取配置信息
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                model_info.update({
                    'coin': extract_coin_from_name(model_info['name']),
                    'interval': extract_interval_from_name(model_info['name']),
                    'features': len(config.get('feature_list', [])),
                    'threshold': config.get('best_threshold', 'Unknown'),
                    'accuracy': config.get('best_accuracy', 'Unknown')
                })
            except Exception:
                pass
        
        models.append(model_info)
    
    return sorted(models, key=lambda x: x['modified'], reverse=True)


def extract_coin_from_name(name: str) -> str:
    """从模型名称提取币种"""
    name_upper = name.upper()
    coins = ['ETH', 'BTC', 'DOT', 'UNI', 'SUI', 'ENA', 'LINK']
    
    for coin in coins:
        if coin in name_upper:
            return coin
    
    return 'Unknown'


def extract_interval_from_name(name: str) -> str:
    """从模型名称提取时间间隔"""
    intervals = ['1m', '5m', '15m', '30m', '1h', '4h']
    
    for interval in intervals:
        if interval in name:
            return interval
    
    return 'Unknown'


def check_database_status(db_path: str = "coin_data.db") -> Dict[str, Any]:
    """检查数据库状态"""
    if not Path(db_path).exists():
        return {'exists': False, 'error': 'Database file not found'}
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        table_info = {}
        total_rows = 0
        
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                
                cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table}")
                min_ts, max_ts = cursor.fetchone()
                
                table_info[table] = {
                    'rows': count,
                    'start_date': datetime.fromtimestamp(min_ts).isoformat() if min_ts else None,
                    'end_date': datetime.fromtimestamp(max_ts).isoformat() if max_ts else None
                }
                total_rows += count
                
            except Exception as e:
                table_info[table] = {'error': str(e)}
        
        conn.close()
        
        return {
            'exists': True,
            'tables': table_info,
            'total_tables': len(tables),
            'total_rows': total_rows,
            'file_size_mb': Path(db_path).stat().st_size / 1024 / 1024
        }
        
    except Exception as e:
        return {'exists': True, 'error': str(e)}


def validate_model_compatibility(model_file: str, config_file: str, db_path: str) -> Dict[str, Any]:
    """验证模型与数据库的兼容性"""
    issues = []
    warnings = []
    
    # 检查文件存在性
    if not Path(model_file).exists():
        issues.append(f"模型文件不存在: {model_file}")
    
    if not Path(config_file).exists():
        issues.append(f"配置文件不存在: {config_file}")
    
    if not Path(db_path).exists():
        issues.append(f"数据库文件不存在: {db_path}")
    
    if issues:
        return {'compatible': False, 'issues': issues, 'warnings': warnings}
    
    try:
        # 读取配置
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        # 提取模型信息
        model_name = Path(model_file).stem.replace('_model', '')
        coin = extract_coin_from_name(model_name)
        interval = extract_interval_from_name(model_name)
        
        # 检查数据库表
        db_status = check_database_status(db_path)
        if not db_status['exists']:
            issues.append("数据库不可访问")
            return {'compatible': False, 'issues': issues, 'warnings': warnings}
        
        # 构造预期表名
        expected_table = f"{coin}_{interval}_spot".lower()
        available_tables = list(db_status['tables'].keys())
        
        if expected_table not in available_tables:
            # 尝试其他可能的表名
            possible_tables = [t for t in available_tables if coin.lower() in t.lower() and interval in t]
            if possible_tables:
                warnings.append(f"未找到预期表 {expected_table}，但找到可能匹配的表: {possible_tables}")
            else:
                issues.append(f"数据库中未找到匹配的表，预期: {expected_table}")
        else:
            table_info = db_status['tables'][expected_table]
            if table_info.get('rows', 0) < 1000:
                warnings.append(f"表 {expected_table} 数据量较少: {table_info.get('rows', 0)} 行")
        
        # 检查特征兼容性
        feature_list = config.get('feature_list', [])
        if len(feature_list) < 10:
            warnings.append(f"特征数量较少: {len(feature_list)}")
        
        # 检查模型参数
        if 'best_threshold' not in config:
            warnings.append("配置中缺少最佳阈值")
        
        if 'timeframe_minutes' not in config:
            warnings.append("配置中缺少时间框架信息")
        
        return {
            'compatible': len(issues) == 0,
            'issues': issues,
            'warnings': warnings,
            'model_info': {
                'coin': coin,
                'interval': interval,
                'features': len(feature_list),
                'threshold': config.get('best_threshold'),
                'expected_table': expected_table
            }
        }
        
    except Exception as e:
        issues.append(f"验证过程中出错: {str(e)}")
        return {'compatible': False, 'issues': issues, 'warnings': warnings}


def find_training_logs(base_dir: str = ".") -> List[Dict[str, Any]]:
    """查找训练日志目录"""
    base_path = Path(base_dir)
    log_dirs = []
    
    # 查找RL训练日志目录
    for log_dir in base_path.glob("rl_training_logs_*"):
        if log_dir.is_dir():
            log_info = {
                'path': str(log_dir),
                'name': log_dir.name,
                'created': datetime.fromtimestamp(log_dir.stat().st_ctime).isoformat(),
                'size_mb': sum(f.stat().st_size for f in log_dir.rglob('*') if f.is_file()) / 1024 / 1024
            }
            
            # 检查是否有检查点
            checkpoints_dir = log_dir / "checkpoints"
            if checkpoints_dir.exists():
                checkpoints = list(checkpoints_dir.glob("rl_agent_episode_*"))
                log_info['checkpoints'] = len(checkpoints)
                if checkpoints:
                    latest_checkpoint = max(checkpoints, key=lambda x: x.stat().st_mtime)
                    log_info['latest_checkpoint'] = str(latest_checkpoint)
            else:
                log_info['checkpoints'] = 0
            
            # 检查训练结果
            results_file = log_dir / "episode_results.csv"
            if results_file.exists():
                try:
                    df = pd.read_csv(results_file)
                    log_info['episodes_completed'] = len(df)
                    if len(df) > 0:
                        log_info['best_return'] = df['total_return'].max()
                        log_info['avg_return'] = df['total_return'].mean()
                except Exception:
                    pass
            
            log_dirs.append(log_info)
    
    return sorted(log_dirs, key=lambda x: x['created'], reverse=True)


def cleanup_old_logs(base_dir: str = ".", days_old: int = 30, dry_run: bool = True) -> Dict[str, Any]:
    """清理旧的训练日志"""
    cutoff_date = datetime.now() - timedelta(days=days_old)
    log_dirs = find_training_logs(base_dir)
    
    old_logs = []
    total_size = 0
    
    for log_info in log_dirs:
        created_date = datetime.fromisoformat(log_info['created'])
        if created_date < cutoff_date:
            old_logs.append(log_info)
            total_size += log_info['size_mb']
    
    if not dry_run:
        import shutil
        removed_count = 0
        for log_info in old_logs:
            try:
                shutil.rmtree(log_info['path'])
                removed_count += 1
            except Exception as e:
                print(f"删除失败 {log_info['path']}: {e}")
        
        return {
            'found': len(old_logs),
            'removed': removed_count,
            'total_size_mb': total_size,
            'dry_run': False
        }
    else:
        return {
            'found': len(old_logs),
            'total_size_mb': total_size,
            'logs': old_logs,
            'dry_run': True
        }


def generate_training_report(log_dir: str) -> Dict[str, Any]:
    """生成训练报告"""
    log_path = Path(log_dir)
    if not log_path.exists():
        return {'error': 'Log directory not found'}
    
    report = {
        'log_dir': str(log_path),
        'generated_at': datetime.now().isoformat()
    }
    
    # 读取episode结果
    results_file = log_path / "episode_results.csv"
    if results_file.exists():
        try:
            df = pd.read_csv(results_file)
            
            report['training_summary'] = {
                'total_episodes': len(df),
                'total_training_time': df['training_time'].sum(),
                'avg_episode_time': df['training_time'].mean(),
                'best_return': df['total_return'].max(),
                'worst_return': df['total_return'].min(),
                'avg_return': df['total_return'].mean(),
                'final_return': df['total_return'].iloc[-1] if len(df) > 0 else 0,
                'best_episode': df.loc[df['total_return'].idxmax(), 'episode'] if len(df) > 0 else 0,
                'avg_win_rate': df['win_rate'].mean(),
                'avg_sharpe_ratio': df['sharpe_ratio'].mean(),
                'avg_max_drawdown': df['max_drawdown'].mean()
            }
            
            # 计算学习曲线统计
            if len(df) >= 100:
                first_100 = df.head(100)['total_return'].mean()
                last_100 = df.tail(100)['total_return'].mean()
                report['learning_progress'] = {
                    'first_100_avg': first_100,
                    'last_100_avg': last_100,
                    'improvement': last_100 - first_100
                }
            
        except Exception as e:
            report['results_error'] = str(e)
    
    # 检查检查点
    checkpoints_dir = log_path / "checkpoints"
    if checkpoints_dir.exists():
        checkpoints = list(checkpoints_dir.glob("rl_agent_episode_*"))
        report['checkpoints'] = {
            'count': len(checkpoints),
            'latest': str(max(checkpoints, key=lambda x: x.stat().st_mtime)) if checkpoints else None
        }
    
    # 读取训练配置
    config_files = list(log_path.glob("*config*.json"))
    if config_files:
        try:
            with open(config_files[0], 'r') as f:
                config = json.load(f)
            report['training_config'] = config
        except Exception:
            pass
    
    return report


def compare_training_runs(log_dirs: List[str]) -> Dict[str, Any]:
    """比较多个训练运行"""
    comparison = {
        'runs': [],
        'comparison_metrics': {}
    }
    
    for log_dir in log_dirs:
        report = generate_training_report(log_dir)
        if 'training_summary' in report:
            run_info = {
                'log_dir': log_dir,
                'name': Path(log_dir).name,
                **report['training_summary']
            }
            comparison['runs'].append(run_info)
    
    if comparison['runs']:
        # 计算比较指标
        metrics = ['best_return', 'avg_return', 'avg_win_rate', 'avg_sharpe_ratio']
        
        for metric in metrics:
            values = [run[metric] for run in comparison['runs'] if metric in run]
            if values:
                comparison['comparison_metrics'][metric] = {
                    'best': max(values),
                    'worst': min(values),
                    'avg': sum(values) / len(values),
                    'best_run': next(run['name'] for run in comparison['runs'] if run.get(metric) == max(values))
                }
    
    return comparison


def export_model_info(models_dir: str = "models/", output_file: str = "model_inventory.json"):
    """导出模型信息清单"""
    models = list_available_models(models_dir)
    
    inventory = {
        'generated_at': datetime.now().isoformat(),
        'models_dir': models_dir,
        'total_models': len(models),
        'models': models
    }
    
    # 按币种和时间间隔分组统计
    by_coin = {}
    by_interval = {}
    
    for model in models:
        coin = model.get('coin', 'Unknown')
        interval = model.get('interval', 'Unknown')
        
        by_coin[coin] = by_coin.get(coin, 0) + 1
        by_interval[interval] = by_interval.get(interval, 0) + 1
    
    inventory['statistics'] = {
        'by_coin': by_coin,
        'by_interval': by_interval,
        'total_size_mb': sum(model['size_mb'] for model in models)
    }
    
    with open(output_file, 'w') as f:
        json.dump(inventory, f, indent=2, ensure_ascii=False)
    
    return inventory


if __name__ == "__main__":
    # 测试CLI工具
    print("测试CLI工具")
    
    # 列出可用模型
    models = list_available_models()
    print(f"找到 {len(models)} 个模型")
    
    for model in models[:3]:  # 显示前3个
        print(f"  {model['name']}: {model['coin']} {model['interval']}")
    
    # 检查数据库状态
    db_status = check_database_status()
    print(f"数据库状态: {db_status.get('total_tables', 0)} 个表")
    
    # 查找训练日志
    logs = find_training_logs()
    print(f"找到 {len(logs)} 个训练日志")
    
    print("CLI工具测试完成！")