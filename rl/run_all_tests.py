"""
Test suite runner for RL trading optimization system.
Runs all unit tests and integration tests with performance monitoring.
"""

import unittest
import sys
import time
import logging
from pathlib import Path
import argparse
from typing import Dict, List
import coverage

# Import all test modules
from . import (
    test_performance_optimizer,
    test_integration_comprehensive,
    test_trading_environment,
    test_rl_trading_agent,
    test_policy_network,
    test_reward_functions,
    test_signal_generator,
    test_training_manager,
    test_rl_backtester,
    test_training_config,
    test_reward_optimization
)


class TestRunner:
    """Comprehensive test runner with performance monitoring"""
    
    def __init__(self, verbose: bool = True, coverage_report: bool = False):
        self.verbose = verbose
        self.coverage_report = coverage_report
        self.test_results = {}
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO if verbose else logging.WARNING,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Setup coverage if requested
        self.cov = None
        if coverage_report:
            self.cov = coverage.Coverage()
    
    def discover_tests(self) -> Dict[str, unittest.TestSuite]:
        """Discover all test suites"""
        test_modules = {
            'performance_optimizer': test_performance_optimizer,
            'integration_comprehensive': test_integration_comprehensive,
            'trading_environment': test_trading_environment,
            'rl_trading_agent': test_rl_trading_agent,
            'policy_network': test_policy_network,
            'reward_functions': test_reward_functions,
            'signal_generator': test_signal_generator,
            'training_manager': test_training_manager,
            'rl_backtester': test_rl_backtester,
            'training_config': test_training_config,
            'reward_optimization': test_reward_optimization
        }
        
        test_suites = {}
        loader = unittest.TestLoader()
        
        for name, module in test_modules.items():
            try:
                suite = loader.loadTestsFromModule(module)
                if suite.countTestCases() > 0:
                    test_suites[name] = suite
                    self.logger.info(f"Loaded {suite.countTestCases()} tests from {name}")
                else:
                    self.logger.warning(f"No tests found in {name}")
            except Exception as e:
                self.logger.error(f"Failed to load tests from {name}: {e}")
        
        return test_suites
    
    def run_test_suite(self, name: str, suite: unittest.TestSuite) -> Dict[str, any]:
        """Run a single test suite with timing"""
        self.logger.info(f"Running test suite: {name}")
        
        start_time = time.time()
        
        # Create test runner
        runner = unittest.TextTestRunner(
            verbosity=2 if self.verbose else 1,
            stream=sys.stdout,
            buffer=True
        )
        
        # Run tests
        result = runner.run(suite)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Collect results
        suite_result = {
            'name': name,
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'skipped': len(result.skipped) if hasattr(result, 'skipped') else 0,
            'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / max(result.testsRun, 1),
            'duration': duration,
            'failure_details': result.failures,
            'error_details': result.errors
        }
        
        self.logger.info(
            f"Suite {name} completed: {suite_result['tests_run']} tests, "
            f"{suite_result['failures']} failures, {suite_result['errors']} errors, "
            f"{suite_result['success_rate']:.1%} success rate, {duration:.2f}s"
        )
        
        return suite_result
    
    def run_all_tests(self) -> Dict[str, any]:
        """Run all test suites"""
        self.logger.info("Starting comprehensive test run...")
        
        if self.cov:
            self.cov.start()
        
        start_time = time.time()
        
        # Discover tests
        test_suites = self.discover_tests()
        
        if not test_suites:
            self.logger.error("No test suites found!")
            return {'error': 'No tests found'}
        
        # Run each test suite
        suite_results = []
        total_tests = 0
        total_failures = 0
        total_errors = 0
        
        for name, suite in test_suites.items():
            try:
                result = self.run_test_suite(name, suite)
                suite_results.append(result)
                
                total_tests += result['tests_run']
                total_failures += result['failures']
                total_errors += result['errors']
                
            except Exception as e:
                self.logger.error(f"Failed to run test suite {name}: {e}")
                suite_results.append({
                    'name': name,
                    'error': str(e),
                    'tests_run': 0,
                    'failures': 0,
                    'errors': 1,
                    'success_rate': 0.0,
                    'duration': 0.0
                })
                total_errors += 1
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        if self.cov:
            self.cov.stop()
        
        # Calculate overall statistics
        overall_success_rate = (total_tests - total_failures - total_errors) / max(total_tests, 1)
        
        results = {
            'summary': {
                'total_tests': total_tests,
                'total_failures': total_failures,
                'total_errors': total_errors,
                'overall_success_rate': overall_success_rate,
                'total_duration': total_duration,
                'suites_run': len(suite_results)
            },
            'suite_results': suite_results,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        self.test_results = results
        return results
    
    def generate_report(self, output_file: str = None) -> str:
        """Generate detailed test report"""
        if not self.test_results:
            return "No test results available"
        
        report_lines = []
        summary = self.test_results['summary']
        
        # Header
        report_lines.append("=" * 80)
        report_lines.append("RL TRADING OPTIMIZATION - TEST REPORT")
        report_lines.append("=" * 80)
        report_lines.append(f"Generated: {self.test_results['timestamp']}")
        report_lines.append("")
        
        # Overall Summary
        report_lines.append("OVERALL SUMMARY")
        report_lines.append("-" * 40)
        report_lines.append(f"Total Tests Run: {summary['total_tests']}")
        report_lines.append(f"Failures: {summary['total_failures']}")
        report_lines.append(f"Errors: {summary['total_errors']}")
        report_lines.append(f"Success Rate: {summary['overall_success_rate']:.1%}")
        report_lines.append(f"Total Duration: {summary['total_duration']:.2f} seconds")
        report_lines.append(f"Test Suites: {summary['suites_run']}")
        report_lines.append("")
        
        # Suite Details
        report_lines.append("SUITE DETAILS")
        report_lines.append("-" * 40)
        
        for suite_result in self.test_results['suite_results']:
            if 'error' in suite_result:
                report_lines.append(f"❌ {suite_result['name']}: ERROR - {suite_result['error']}")
            else:
                status = "✅" if suite_result['success_rate'] == 1.0 else "⚠️" if suite_result['success_rate'] > 0.5 else "❌"
                report_lines.append(
                    f"{status} {suite_result['name']}: "
                    f"{suite_result['tests_run']} tests, "
                    f"{suite_result['failures']} failures, "
                    f"{suite_result['errors']} errors, "
                    f"{suite_result['success_rate']:.1%} success, "
                    f"{suite_result['duration']:.2f}s"
                )
        
        report_lines.append("")
        
        # Failure Details
        failures_found = False
        for suite_result in self.test_results['suite_results']:
            if suite_result.get('failure_details') or suite_result.get('error_details'):
                if not failures_found:
                    report_lines.append("FAILURE DETAILS")
                    report_lines.append("-" * 40)
                    failures_found = True
                
                report_lines.append(f"\n{suite_result['name']}:")
                
                for failure in suite_result.get('failure_details', []):
                    report_lines.append(f"  FAILURE: {failure[0]}")
                    report_lines.append(f"    {failure[1]}")
                
                for error in suite_result.get('error_details', []):
                    report_lines.append(f"  ERROR: {error[0]}")
                    report_lines.append(f"    {error[1]}")
        
        if not failures_found:
            report_lines.append("🎉 NO FAILURES OR ERRORS!")
        
        report_lines.append("")
        report_lines.append("=" * 80)
        
        report_text = "\n".join(report_lines)
        
        # Save to file if requested
        if output_file:
            with open(output_file, 'w') as f:
                f.write(report_text)
            self.logger.info(f"Test report saved to {output_file}")
        
        return report_text
    
    def generate_coverage_report(self, output_dir: str = "coverage_html"):
        """Generate coverage report if coverage was enabled"""
        if not self.cov:
            self.logger.warning("Coverage not enabled")
            return
        
        try:
            # Generate HTML report
            self.cov.html_report(directory=output_dir)
            self.logger.info(f"Coverage HTML report generated in {output_dir}")
            
            # Generate console report
            self.logger.info("Coverage Summary:")
            self.cov.report()
            
        except Exception as e:
            self.logger.error(f"Failed to generate coverage report: {e}")


def main():
    """Main entry point for test runner"""
    parser = argparse.ArgumentParser(description="Run RL Trading Optimization Tests")
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--coverage', '-c', action='store_true', help='Generate coverage report')
    parser.add_argument('--output', '-o', help='Output file for test report')
    parser.add_argument('--suite', '-s', help='Run specific test suite only')
    
    args = parser.parse_args()
    
    # Create test runner
    runner = TestRunner(verbose=args.verbose, coverage_report=args.coverage)
    
    if args.suite:
        # Run specific suite
        test_suites = runner.discover_tests()
        if args.suite in test_suites:
            result = runner.run_test_suite(args.suite, test_suites[args.suite])
            print(f"\nSuite {args.suite} completed with {result['success_rate']:.1%} success rate")
        else:
            print(f"Test suite '{args.suite}' not found")
            print(f"Available suites: {list(test_suites.keys())}")
            sys.exit(1)
    else:
        # Run all tests
        results = runner.run_all_tests()
        
        # Generate report
        report = runner.generate_report(args.output)
        print(report)
        
        # Generate coverage report if requested
        if args.coverage:
            runner.generate_coverage_report()
        
        # Exit with error code if tests failed
        if results['summary']['total_failures'] > 0 or results['summary']['total_errors'] > 0:
            sys.exit(1)


if __name__ == '__main__':
    main()