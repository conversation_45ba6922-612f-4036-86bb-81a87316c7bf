# PolicyNetwork Implementation

This document describes the PolicyNetwork implementation for the RL Trading Optimization system.

## Overview

The PolicyNetwork is a PyTorch-based neural network that takes market state as input and outputs trading decisions including:
- Position size (0-10% of capital)
- Stop loss percentage (1-5%)
- Take profit percentage (1-4%) 
- Maximum hold time (60-240 minutes)
- Enter trade decision (yes/no)

## Architecture

```
Input (15 features) 
    ↓
Shared Layers (256 → 128 → 128)
    ↓
Multiple Output Heads:
├── Position Size Head → Sigmoid → [0, 0.1]
├── Stop Loss Head → Sigmoid → [0.01, 0.05] 
├── Take Profit Head → Sigmoid → [0.01, 0.04]
├── Hold Time Head → Sigmoid → [60, 240]
└── Enter Trade Head → Softmax → [0, 1]
```

## Key Features

### 1. Multi-Head Architecture
- Separate output heads for different action types
- Continuous actions for position sizing and risk management
- Discrete action for entry decision

### 2. Action Scaling
- Raw network outputs are scaled to appropriate trading ranges
- Position size: 0-10% of available capital
- Stop loss: 1-5% from entry price
- Take profit: 1-4% from entry price
- Hold time: 60-240 minutes

### 3. Exploration Support
- Deterministic actions for inference/deployment
- Stochastic actions with exploration noise for training
- Log probability calculation for policy gradient methods

### 4. Model Persistence
- Save/load functionality with metadata
- Configuration files in JSON format
- Version management support

## Usage Examples

### Basic Usage

```python
from rl.policy_network import create_default_policy_network
import torch

# Create network
network = create_default_policy_network(state_dim=15)

# Create sample state (15 features)
state = torch.randn(1, 15)

# Get deterministic actions (for trading)
actions = network.get_actions(state, deterministic=True)
print(f"Position size: {actions['position_size'].item():.2%}")
print(f"Enter trade: {actions['enter_trade'].item()}")

# Sample actions with exploration (for training)
actions, log_probs = network.sample_action(state)
```

### Training Integration

```python
# Set up for training
network.train()
optimizer = torch.optim.Adam(network.parameters(), lr=3e-4)

# Training step
actions, log_probs = network.sample_action(state)
# ... execute action in environment, get reward ...
loss = -sum(log_probs.values()) * reward
optimizer.zero_grad()
loss.backward()
optimizer.step()
```

### Model Persistence

```python
# Save model
metadata = {'episodes': 1000, 'performance': {'sharpe': 1.5}}
network.save_model('my_model', metadata)

# Load model
loaded_network = PolicyNetwork.load_model('my_model')
```

## State Space Design

The network expects a 15-dimensional state vector:

| Index | Feature | Description | Range |
|-------|---------|-------------|-------|
| 0 | model_signal | Existing model prediction | 0 or 1 |
| 1 | signal_confidence | Model confidence | [0, 1] |
| 2 | signal_probability | Raw probability | [0, 1] |
| 3 | current_price | Current price (normalized) | Any |
| 4 | price_change_1h | 1h price change | [-1, 1] |
| 5 | price_change_4h | 4h price change | [-1, 1] |
| 6 | volatility_recent | Recent volatility | [0, 1] |
| 7 | cash_ratio | Available cash ratio | [0, 1] |
| 8 | position_count | Current positions | [0, N] |
| 9 | unrealized_pnl | Unrealized P&L | Any |
| 10 | recent_win_rate | Recent win rate | [0, 1] |
| 11 | consecutive_losses | Consecutive losses | [0, N] |
| 12 | hour | Hour of day | [0, 23] |
| 13 | day_of_week | Day of week | [0, 6] |
| 14 | is_good_trading_time | Good trading time flag | 0 or 1 |

## Action Space

The network outputs 5 types of actions:

### Continuous Actions
- **position_size**: Fraction of capital to use (0-10%)
- **stop_loss_pct**: Stop loss distance (1-5%)
- **take_profit_pct**: Take profit distance (1-4%)
- **max_hold_time**: Maximum hold time in minutes (60-240)

### Discrete Actions
- **enter_trade**: Whether to enter the trade (True/False)

## Integration with RL Training

The PolicyNetwork is designed to work with PPO (Proximal Policy Optimization):

1. **Forward Pass**: Convert state to action probabilities
2. **Action Sampling**: Sample actions with exploration noise
3. **Log Probability**: Calculate log probabilities for policy gradient
4. **Policy Update**: Update network weights based on rewards

## Testing

Run the test suite to verify functionality:

```bash
python rl/test_policy_network.py
```

Run the example to see usage:

```bash
python rl/example_policy_usage.py
```

## Requirements

- PyTorch >= 2.0.0
- NumPy
- Python >= 3.8

## Files

- `policy_network.py`: Main PolicyNetwork implementation
- `test_policy_network.py`: Comprehensive test suite
- `example_policy_usage.py`: Usage examples and demonstrations
- `README_policy_network.md`: This documentation

## Next Steps

This PolicyNetwork will be integrated with:
1. TradingEnvironment (Task 1) - for state representation
2. RLTradingAgent (Task 3) - for action selection and training
3. RLBacktester (Task 4) - for backtesting with RL decisions
4. TrainingManager (Task 5) - for training orchestration

The network is ready for integration with the broader RL trading system.