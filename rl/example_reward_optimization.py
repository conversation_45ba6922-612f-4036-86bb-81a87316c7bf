# example_reward_optimization.py
# 奖励函数优化使用示例

import numpy as np
import pandas as pd
from typing import Dict, List
import matplotlib.pyplot as plt

from .reward_functions import (
    MultiObjectiveRewardFunction, 
    RewardConfig, 
    create_default_configs,
    MarketRegime
)
from .reward_config import (
    RewardConfigManager,
    create_template_config,
    REWARD_CONFIG_TEMPLATES
)
from .test_reward_functions import RewardFunctionAnalyzer

def demonstrate_basic_usage():
    """演示基础使用方法"""
    print("=== 奖励函数基础使用演示 ===")
    
    # 1. 创建默认配置
    config = RewardConfig()
    reward_func = MultiObjectiveRewardFunction(config)
    
    # 2. 模拟交易结果
    trade_result = {
        'pnl': 25.0,
        'pnl_pct': 0.025,  # 2.5%收益
        'max_loss_pct': -0.005,  # 最大亏损0.5%
        'hold_time_minutes': 75,
        'exit_reason': 'take_profit'
    }
    
    # 3. 模拟市场状态
    market_state = {
        'price_change_1h': 0.01,  # 1小时上涨1%
        'price_change_4h': 0.02,  # 4小时上涨2%
        'volatility_recent': 0.03  # 3%波动率
    }
    
    # 4. 模拟投资组合指标
    portfolio_metrics = {
        'recent_win_rate': 0.7,  # 70%胜率
        'consecutive_wins': 2,
        'consecutive_losses': 0,
        'total_trades': 50,
        'current_drawdown': 0.02,  # 2%回撤
        'recent_volatility': 0.025,
        'sharpe_ratio': 1.5
    }
    
    # 5. 计算奖励
    reward_breakdown = reward_func.calculate_reward(
        trade_result, market_state, portfolio_metrics
    )
    
    print("交易结果:")
    print(f"  PnL: ${trade_result['pnl']:.2f} ({trade_result['pnl_pct']*100:.2f}%)")
    print(f"  持仓时间: {trade_result['hold_time_minutes']} 分钟")
    print(f"  退出原因: {trade_result['exit_reason']}")
    
    print("\n奖励分解:")
    for key, value in reward_breakdown.items():
        if isinstance(value, (int, float)):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")
    
    print(f"\n总奖励: {reward_breakdown['total_reward']:.4f}")

def demonstrate_config_comparison():
    """演示不同配置的比较"""
    print("\n=== 不同奖励配置比较演示 ===")
    
    # 获取所有预设配置
    configs = create_default_configs()
    
    # 添加模板配置
    for template_name in ['day_trading', 'swing_trading']:
        configs[template_name] = create_template_config(template_name)
    
    # 创建测试场景
    test_scenarios = []
    for i in range(20):
        pnl_pct = np.random.normal(0.01, 0.02)  # 平均1%收益
        
        scenario = {
            'trade_result': {
                'pnl': pnl_pct * 1000,
                'pnl_pct': pnl_pct,
                'max_loss_pct': min(pnl_pct, np.random.uniform(-0.03, 0)),
                'hold_time_minutes': np.random.randint(30, 180),
                'exit_reason': np.random.choice(['take_profit', 'stop_loss', 'timeout'])
            },
            'market_state': {
                'price_change_1h': np.random.normal(0, 0.015),
                'price_change_4h': np.random.normal(0, 0.025),
                'volatility_recent': np.random.uniform(0.02, 0.06)
            },
            'portfolio_metrics': {
                'recent_win_rate': np.random.uniform(0.4, 0.8),
                'consecutive_wins': np.random.randint(0, 5),
                'consecutive_losses': np.random.randint(0, 3),
                'total_trades': np.random.randint(20, 100),
                'current_drawdown': np.random.uniform(0, 0.1),
                'recent_volatility': np.random.uniform(0.02, 0.05),
                'sharpe_ratio': np.random.normal(1.0, 0.3)
            }
        }
        test_scenarios.append(scenario)
    
    # 测试每个配置
    results = {}
    for config_name, config in configs.items():
        reward_func = MultiObjectiveRewardFunction(config)
        rewards = []
        
        for scenario in test_scenarios:
            reward_breakdown = reward_func.calculate_reward(
                scenario['trade_result'],
                scenario['market_state'],
                scenario['portfolio_metrics']
            )
            rewards.append(reward_breakdown['total_reward'])
        
        results[config_name] = {
            'mean_reward': np.mean(rewards),
            'std_reward': np.std(rewards),
            'sharpe_ratio': np.mean(rewards) / (np.std(rewards) + 1e-8)
        }
    
    # 显示结果
    print("配置比较结果:")
    print(f"{'配置名称':<15} {'平均奖励':<10} {'标准差':<10} {'夏普比率':<10}")
    print("-" * 50)
    
    # 按夏普比率排序
    sorted_results = sorted(results.items(), key=lambda x: x[1]['sharpe_ratio'], reverse=True)
    
    for config_name, metrics in sorted_results:
        print(f"{config_name:<15} {metrics['mean_reward']:<10.4f} "
              f"{metrics['std_reward']:<10.4f} {metrics['sharpe_ratio']:<10.4f}")

def demonstrate_market_regime_adaptation():
    """演示市场状态适应性"""
    print("\n=== 市场状态适应性演示 ===")
    
    # 创建平衡配置
    config = create_default_configs()['balanced']
    reward_func = MultiObjectiveRewardFunction(config)
    
    # 定义不同市场状态的场景
    market_scenarios = {
        '牛市趋势': {
            'price_change_1h': 0.025,
            'price_change_4h': 0.04,
            'volatility_recent': 0.02
        },
        '熊市趋势': {
            'price_change_1h': -0.025,
            'price_change_4h': -0.04,
            'volatility_recent': 0.03
        },
        '横盘整理': {
            'price_change_1h': 0.002,
            'price_change_4h': -0.001,
            'volatility_recent': 0.015
        },
        '高波动': {
            'price_change_1h': 0.01,
            'price_change_4h': 0.005,
            'volatility_recent': 0.08
        }
    }
    
    # 固定的交易结果和投资组合指标
    base_trade = {
        'pnl': 20.0,
        'pnl_pct': 0.02,
        'max_loss_pct': -0.005,
        'hold_time_minutes': 90,
        'exit_reason': 'take_profit'
    }
    
    base_portfolio = {
        'recent_win_rate': 0.6,
        'consecutive_wins': 1,
        'consecutive_losses': 0,
        'total_trades': 30,
        'current_drawdown': 0.03,
        'recent_volatility': 0.025,
        'sharpe_ratio': 1.0
    }
    
    print("相同交易在不同市场状态下的奖励:")
    print(f"{'市场状态':<10} {'总奖励':<10} {'市场调整':<10} {'检测状态':<15}")
    print("-" * 50)
    
    for scenario_name, market_state in market_scenarios.items():
        reward_breakdown = reward_func.calculate_reward(
            base_trade, market_state, base_portfolio
        )
        
        print(f"{scenario_name:<10} {reward_breakdown['total_reward']:<10.4f} "
              f"{reward_breakdown['regime_multiplier']:<10.2f} "
              f"{reward_breakdown['market_regime']:<15}")

def demonstrate_adaptive_learning():
    """演示自适应学习"""
    print("\n=== 自适应学习演示 ===")
    
    config = RewardConfig(adaptive_learning_rate=0.05)
    reward_func = MultiObjectiveRewardFunction(config)
    
    print("初始权重:")
    print(f"  PnL权重: {reward_func.adaptive_weights['pnl']:.3f}")
    print(f"  风险权重: {reward_func.adaptive_weights['risk']:.3f}")
    print(f"  效率权重: {reward_func.adaptive_weights['efficiency']:.3f}")
    
    # 模拟一系列亏损交易
    print("\n模拟连续亏损交易...")
    for i in range(15):
        losing_trade = {
            'pnl': -10.0 - i * 2,
            'pnl_pct': -0.01 - i * 0.002,
            'max_loss_pct': -0.02 - i * 0.003,
            'hold_time_minutes': 120,
            'exit_reason': 'stop_loss'
        }
        
        market_state = {
            'price_change_1h': -0.01,
            'price_change_4h': -0.015,
            'volatility_recent': 0.04
        }
        
        portfolio_metrics = {
            'recent_win_rate': max(0.3, 0.6 - i * 0.02),
            'consecutive_wins': 0,
            'consecutive_losses': i + 1,
            'total_trades': 20 + i,
            'current_drawdown': min(0.15, 0.02 + i * 0.01),
            'recent_volatility': 0.03,
            'sharpe_ratio': max(-1.0, 1.0 - i * 0.1)
        }
        
        reward_breakdown = reward_func.calculate_reward(
            losing_trade, market_state, portfolio_metrics
        )
        
        if i % 5 == 4:  # 每5次显示一次权重变化
            print(f"第{i+1}次交易后权重:")
            print(f"  PnL权重: {reward_func.adaptive_weights['pnl']:.3f}")
            print(f"  风险权重: {reward_func.adaptive_weights['risk']:.3f}")
            print(f"  效率权重: {reward_func.adaptive_weights['efficiency']:.3f}")

def run_comprehensive_example():
    """运行综合示例"""
    print("=== 奖励函数优化综合示例 ===\n")
    
    # 1. 基础使用
    demonstrate_basic_usage()
    
    # 2. 配置比较
    demonstrate_config_comparison()
    
    # 3. 市场状态适应
    demonstrate_market_regime_adaptation()
    
    # 4. 自适应学习
    demonstrate_adaptive_learning()
    
    print("\n=== 运行完整测试分析 ===")
    
    # 5. 运行完整的测试分析（可选，比较耗时）
    try:
        analyzer = RewardFunctionAnalyzer()
        
        # 生成少量测试场景进行快速演示
        test_scenarios = analyzer.generate_test_scenarios(50)
        
        # 获取几个主要配置进行比较
        main_configs = {
            'conservative': create_default_configs()['conservative'],
            'balanced': create_default_configs()['balanced'],
            'aggressive': create_default_configs()['aggressive']
        }
        
        # 比较市场状态表现
        regime_performance = analyzer.compare_market_regime_performance(
            main_configs, test_scenarios
        )
        
        print("\n主要配置在不同市场状态下的表现:")
        for config_name, regimes in regime_performance.items():
            print(f"\n{config_name}配置:")
            for regime, metrics in regimes.items():
                if metrics['num_scenarios'] > 0:
                    print(f"  {regime}: 平均奖励={metrics['mean_reward']:.4f}, "
                          f"夏普比率={metrics['reward_sharpe']:.4f}")
        
    except Exception as e:
        print(f"完整测试分析跳过: {e}")
    
    print("\n=== 示例完成 ===")
    print("奖励函数优化系统已准备就绪！")
    print("可以通过以下方式使用:")
    print("1. 使用预设配置: conservative, balanced, aggressive")
    print("2. 使用模板配置: day_trading, swing_trading, scalping, trend_following")
    print("3. 创建自定义配置并测试效果")
    print("4. 运行完整的测试分析来优化参数")

if __name__ == "__main__":
    run_comprehensive_example()