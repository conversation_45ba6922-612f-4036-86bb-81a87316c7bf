# test_reward_optimization.py
# 奖励函数优化系统测试

import unittest
import numpy as np
import pandas as pd
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rl.reward_functions import (
    MultiObjectiveRewardFunction, 
    RewardConfig, 
    create_default_configs,
    MarketRegimeDetector,
    MarketRegime
)
from rl.reward_config import (
    RewardConfigManager,
    create_template_config,
    validate_reward_config
)
from rl.trading_environment import TradingEnvironment, MarketState, TradingAction

class TestRewardFunctions(unittest.TestCase):
    """测试奖励函数系统"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = RewardConfig()
        self.reward_func = MultiObjectiveRewardFunction(self.config)
        
        # 标准测试数据
        self.test_trade = {
            'pnl': 25.0,
            'pnl_pct': 0.025,
            'max_loss_pct': -0.005,
            'hold_time_minutes': 75,
            'exit_reason': 'take_profit'
        }
        
        self.test_market = {
            'price_change_1h': 0.01,
            'price_change_4h': 0.02,
            'volatility_recent': 0.03
        }
        
        self.test_portfolio = {
            'recent_win_rate': 0.7,
            'consecutive_wins': 2,
            'consecutive_losses': 0,
            'total_trades': 50,
            'current_drawdown': 0.02,
            'recent_volatility': 0.025,
            'sharpe_ratio': 1.5
        }
    
    def test_basic_reward_calculation(self):
        """测试基础奖励计算"""
        reward_breakdown = self.reward_func.calculate_reward(
            self.test_trade, self.test_market, self.test_portfolio
        )
        
        # 检查返回的奖励分解
        self.assertIn('total_reward', reward_breakdown)
        self.assertIn('pnl_reward', reward_breakdown)
        self.assertIn('risk_penalty', reward_breakdown)
        self.assertIn('efficiency_reward', reward_breakdown)
        self.assertIn('market_regime', reward_breakdown)
        
        # 检查奖励值的合理性
        self.assertIsInstance(reward_breakdown['total_reward'], (int, float))
        self.assertGreater(reward_breakdown['pnl_reward'], 0)  # 盈利交易应该有正奖励
    
    def test_market_regime_detection(self):
        """测试市场状态检测"""
        detector = MarketRegimeDetector()
        
        # 测试不同市场状态
        test_cases = [
            ({'price_change_1h': 0.03, 'price_change_4h': 0.02, 'volatility_recent': 0.02}, MarketRegime.TRENDING_UP),
            ({'price_change_1h': -0.03, 'price_change_4h': -0.02, 'volatility_recent': 0.02}, MarketRegime.TRENDING_DOWN),
            ({'price_change_1h': 0.001, 'price_change_4h': 0.001, 'volatility_recent': 0.02}, MarketRegime.SIDEWAYS),
            ({'price_change_1h': 0.01, 'price_change_4h': 0.01, 'volatility_recent': 0.08}, MarketRegime.HIGH_VOLATILITY),
            ({'price_change_1h': 0.001, 'price_change_4h': 0.001, 'volatility_recent': 0.005}, MarketRegime.LOW_VOLATILITY)
        ]
        
        for market_state, expected_regime in test_cases:
            detected_regime = detector.detect_regime(market_state)
            self.assertEqual(detected_regime, expected_regime)
    
    def test_different_configs(self):
        """测试不同配置的奖励函数"""
        configs = create_default_configs()
        
        for config_name, config in configs.items():
            with self.subTest(config=config_name):
                reward_func = MultiObjectiveRewardFunction(config)
                reward_breakdown = reward_func.calculate_reward(
                    self.test_trade, self.test_market, self.test_portfolio
                )
                
                # 每个配置都应该返回有效的奖励
                self.assertIsInstance(reward_breakdown['total_reward'], (int, float))
                self.assertIn('market_regime', reward_breakdown)
    
    def test_adaptive_learning(self):
        """测试自适应学习功能"""
        config = RewardConfig(adaptive_learning_rate=0.2)  # 更高的学习率
        reward_func = MultiObjectiveRewardFunction(config)
        
        # 记录初始权重
        initial_weights = reward_func.adaptive_weights.copy()
        
        # 模拟多次亏损交易，确保满足调整条件
        for i in range(10):
            losing_trade = {
                'pnl': -20.0,  # 更大的亏损
                'pnl_pct': -0.02,
                'max_loss_pct': -0.03,
                'hold_time_minutes': 120,
                'exit_reason': 'stop_loss'
            }
            
            portfolio_metrics = self.test_portfolio.copy()
            portfolio_metrics['consecutive_losses'] = i + 1
            
            reward_breakdown = reward_func.calculate_reward(losing_trade, self.test_market, portfolio_metrics)
            
            # 确保奖励为负（触发调整条件）
            self.assertLess(reward_breakdown['total_reward'], 0)
        
        # 检查权重是否发生了调整
        final_weights = reward_func.adaptive_weights
        
        # 连续亏损后，风险权重应该增加
        self.assertGreater(final_weights['risk'], initial_weights['risk'])
    
    def test_reward_components(self):
        """测试奖励组件的计算"""
        # 测试盈利交易
        profit_trade = {
            'pnl': 50.0,
            'pnl_pct': 0.05,
            'max_loss_pct': -0.01,
            'hold_time_minutes': 60,
            'exit_reason': 'take_profit'
        }
        
        reward_breakdown = self.reward_func.calculate_reward(
            profit_trade, self.test_market, self.test_portfolio
        )
        
        # 盈利交易应该有正的PnL奖励
        self.assertGreater(reward_breakdown['pnl_reward'], 0)
        
        # 测试亏损交易
        loss_trade = {
            'pnl': -30.0,
            'pnl_pct': -0.03,
            'max_loss_pct': -0.05,
            'hold_time_minutes': 180,
            'exit_reason': 'stop_loss'
        }
        
        reward_breakdown = self.reward_func.calculate_reward(
            loss_trade, self.test_market, self.test_portfolio
        )
        
        # 亏损交易应该有负的PnL奖励
        self.assertLess(reward_breakdown['pnl_reward'], 0)
        # 应该有风险惩罚
        self.assertGreater(reward_breakdown['risk_penalty'], 0)

class TestRewardConfig(unittest.TestCase):
    """测试奖励配置系统"""
    
    def setUp(self):
        """设置测试环境"""
        self.config_manager = RewardConfigManager("test_configs")
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        if os.path.exists("test_configs"):
            shutil.rmtree("test_configs")
    
    def test_config_creation_and_loading(self):
        """测试配置创建和加载"""
        # 创建自定义配置
        custom_config = self.config_manager.create_custom_config(
            name="test_config",
            base_config="balanced",
            modifications={'pnl_weight': 1.5}
        )
        
        self.assertEqual(custom_config.pnl_weight, 1.5)
        
        # 加载配置
        loaded_config = self.config_manager.load_config("test_config")
        self.assertIsNotNone(loaded_config)
        self.assertEqual(loaded_config.pnl_weight, 1.5)
    
    def test_template_configs(self):
        """测试模板配置"""
        template_names = ['day_trading', 'swing_trading', 'scalping', 'trend_following']
        
        for template_name in template_names:
            with self.subTest(template=template_name):
                config = create_template_config(template_name)
                self.assertIsNotNone(config)
                self.assertIsInstance(config, RewardConfig)
    
    def test_config_validation(self):
        """测试配置验证"""
        # 有效配置
        valid_config = RewardConfig()
        issues = validate_reward_config(valid_config)
        self.assertEqual(len(issues), 0)
        
        # 无效配置
        invalid_config = RewardConfig(pnl_weight=-1.0)  # 负权重
        issues = validate_reward_config(invalid_config)
        self.assertGreater(len(issues), 0)
        self.assertIn('pnl_weight', issues)

class TestTradingEnvironmentIntegration(unittest.TestCase):
    """测试与交易环境的集成"""
    
    def setUp(self):
        """设置测试环境"""
        config = {
            'initial_capital': 10000,
            'transaction_cost': 0.001,
            'max_position_size': 0.05,
            'max_positions': 3,
            'reward_config': {
                'pnl_weight': 1.0,
                'risk_penalty': 0.5,
                'time_efficiency': 0.1,
                'max_drawdown_penalty': 2.0
            }
        }
        self.env = TradingEnvironment(config)
    
    def test_reward_calculation_integration(self):
        """测试奖励计算集成"""
        # 创建市场状态
        market_state = MarketState(
            model_signal=1,
            signal_confidence=0.75,
            signal_probability=0.82,
            current_price=2500.0,
            price_change_1h=0.02,
            price_change_4h=-0.01,
            volatility_recent=0.03,
            cash_ratio=0.9,
            position_count=0,
            unrealized_pnl=0.0,
            recent_win_rate=0.6,
            consecutive_losses=0,
            hour=14,
            day_of_week=2,
            is_good_trading_time=True
        )
        
        # 执行交易
        action = TradingAction(
            enter_trade=True,
            position_size=0.03,
            stop_loss_pct=0.025,
            take_profit_pct=0.02,
            max_hold_time=120
        )
        
        result = self.env.execute_action(action, market_state, pd.Timestamp.now(), 0)
        self.assertTrue(result['trade_executed'])
        
        # 模拟价格变动并完成交易
        new_price = 2550.0  # 2%上涨，触发止盈
        completed_trades = self.env.update_positions(new_price, pd.Timestamp.now(), 120)
        
        if completed_trades:
            # 测试奖励计算
            reward = self.env.calculate_reward(completed_trades, market_state)
            self.assertIsInstance(reward, (int, float))
            self.assertGreater(reward, 0)  # 盈利交易应该有正奖励

def run_all_tests():
    """运行所有测试"""
    print("开始运行奖励函数优化系统测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestRewardFunctions,
        TestRewardConfig,
        TestTradingEnvironmentIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！")
        print(f"运行了 {result.testsRun} 个测试")
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)