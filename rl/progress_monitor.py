#!/usr/bin/env python3
"""
Progress Monitoring Utilities for RL Training

Provides real-time progress monitoring, interruption handling, and training statistics.
"""

import time
import sys
import threading
from typing import Dict, Any, Optional, Callable
from datetime import datetime, timedelta
import json
from pathlib import Path


class ProgressMonitor:
    """训练进度监控器"""
    
    def __init__(self, total_episodes: int, log_dir: str, quiet: bool = False):
        self.total_episodes = total_episodes
        self.log_dir = Path(log_dir)
        self.quiet = quiet
        
        # 进度状态
        self.current_episode = 0
        self.start_time = None
        self.last_update_time = None
        
        # 统计信息
        self.episode_times = []
        self.episode_rewards = []
        self.episode_returns = []
        self.best_performance = -float('inf')
        self.best_episode = 0
        
        # 中断标志
        self.interrupted = False
        
        # 回调函数
        self.callbacks = []
    
    def start(self):
        """开始监控"""
        self.start_time = time.time()
        self.last_update_time = self.start_time
        
        if not self.quiet:
            print(f"🚀 开始训练 {self.total_episodes} episodes...")
            print(f"📊 进度监控已启动")
    
    def update(self, episode: int, metrics: Dict[str, Any]):
        """更新进度"""
        self.current_episode = episode
        current_time = time.time()
        
        # 记录统计信息
        episode_time = current_time - self.last_update_time
        self.episode_times.append(episode_time)
        
        if 'total_reward' in metrics:
            self.episode_rewards.append(metrics['total_reward'])
        
        if 'total_return' in metrics:
            self.episode_returns.append(metrics['total_return'])
            
            # 更新最佳性能
            if metrics['total_return'] > self.best_performance:
                self.best_performance = metrics['total_return']
                self.best_episode = episode
        
        # 显示进度
        if not self.quiet:
            self._display_progress(episode, metrics, current_time)
        
        # 执行回调
        for callback in self.callbacks:
            callback(episode, metrics)
        
        self.last_update_time = current_time
    
    def _display_progress(self, episode: int, metrics: Dict[str, Any], current_time: float):
        """显示进度信息"""
        # 计算进度百分比
        progress = episode / self.total_episodes
        
        # 计算时间统计
        elapsed_time = current_time - self.start_time
        avg_episode_time = elapsed_time / max(episode, 1)
        remaining_episodes = self.total_episodes - episode
        eta = remaining_episodes * avg_episode_time
        
        # 创建进度条
        bar_length = 40
        filled_length = int(bar_length * progress)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        
        # 格式化时间
        elapsed_str = self._format_time(elapsed_time)
        eta_str = self._format_time(eta)
        
        # 格式化指标
        reward = metrics.get('total_reward', 0)
        return_pct = metrics.get('total_return', 0) * 100
        win_rate = metrics.get('win_rate', 0) * 100
        
        # 打印进度行
        progress_line = (
            f"\r📈 Episode {episode:4d}/{self.total_episodes} "
            f"[{bar}] {progress:.1%} "
            f"| ⏱️ {elapsed_str} < {eta_str} "
            f"| 💰 {return_pct:+6.2f}% "
            f"| 🎯 {win_rate:5.1f}% "
            f"| 🏆 {self.best_performance*100:+6.2f}%"
        )
        
        print(progress_line, end='', flush=True)
        
        # 每100个episode换行一次
        if episode % 100 == 0:
            print()
    
    def _format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        if seconds < 60:
            return f"{seconds:.0f}s"
        elif seconds < 3600:
            return f"{seconds/60:.0f}m{seconds%60:.0f}s"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h{minutes:02d}m"
    
    def add_callback(self, callback: Callable[[int, Dict[str, Any]], None]):
        """添加进度回调函数"""
        self.callbacks.append(callback)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取训练统计信息"""
        if not self.episode_times:
            return {}
        
        current_time = time.time()
        total_time = current_time - self.start_time if self.start_time else 0
        
        return {
            'total_episodes': self.current_episode,
            'total_time': total_time,
            'avg_episode_time': sum(self.episode_times) / len(self.episode_times),
            'best_performance': self.best_performance,
            'best_episode': self.best_episode,
            'avg_reward': sum(self.episode_rewards) / len(self.episode_rewards) if self.episode_rewards else 0,
            'avg_return': sum(self.episode_returns) / len(self.episode_returns) if self.episode_returns else 0,
            'episodes_per_hour': self.current_episode / (total_time / 3600) if total_time > 0 else 0
        }
    
    def save_progress(self):
        """保存进度信息"""
        progress_file = self.log_dir / "training_progress.json"
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        progress_data = {
            'timestamp': datetime.now().isoformat(),
            'current_episode': self.current_episode,
            'total_episodes': self.total_episodes,
            'statistics': self.get_statistics(),
            'interrupted': self.interrupted
        }
        
        with open(progress_file, 'w') as f:
            json.dump(progress_data, f, indent=2)
    
    def interrupt(self):
        """标记为中断"""
        self.interrupted = True
        if not self.quiet:
            print(f"\n⚠️  训练在第 {self.current_episode} episode 被中断")
        self.save_progress()
    
    def finish(self):
        """完成监控"""
        if not self.quiet:
            print(f"\n✅ 训练完成！")
            
            stats = self.get_statistics()
            print(f"📊 训练统计:")
            print(f"  总时间: {self._format_time(stats['total_time'])}")
            print(f"  平均每episode: {stats['avg_episode_time']:.2f}s")
            print(f"  训练速度: {stats['episodes_per_hour']:.1f} episodes/小时")
            print(f"  最佳性能: {stats['best_performance']:.2%} (Episode {stats['best_episode']})")
        
        self.save_progress()


class TrainingInterruptHandler:
    """训练中断处理器"""
    
    def __init__(self, progress_monitor: ProgressMonitor):
        self.progress_monitor = progress_monitor
        self.interrupted = False
        self._setup_handlers()
    
    def _setup_handlers(self):
        """设置信号处理器"""
        import signal
        
        def signal_handler(signum, frame):
            print(f"\n⚠️  收到中断信号 ({signum})")
            self.interrupt()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def interrupt(self):
        """处理中断"""
        if not self.interrupted:
            self.interrupted = True
            self.progress_monitor.interrupt()
            print("正在保存当前进度...")
    
    def is_interrupted(self) -> bool:
        """检查是否被中断"""
        return self.interrupted


class LiveMetricsDisplay:
    """实时指标显示器"""
    
    def __init__(self, update_interval: float = 1.0):
        self.update_interval = update_interval
        self.metrics_history = []
        self.running = False
        self.thread = None
    
    def start(self):
        """开始显示"""
        self.running = True
        self.thread = threading.Thread(target=self._display_loop, daemon=True)
        self.thread.start()
    
    def stop(self):
        """停止显示"""
        self.running = False
        if self.thread:
            self.thread.join()
    
    def update_metrics(self, metrics: Dict[str, Any]):
        """更新指标"""
        self.metrics_history.append({
            'timestamp': time.time(),
            **metrics
        })
        
        # 保持最近1000条记录
        if len(self.metrics_history) > 1000:
            self.metrics_history = self.metrics_history[-1000:]
    
    def _display_loop(self):
        """显示循环"""
        while self.running:
            if self.metrics_history:
                self._update_display()
            time.sleep(self.update_interval)
    
    def _update_display(self):
        """更新显示"""
        if not self.metrics_history:
            return
        
        # 获取最近的指标
        recent_metrics = self.metrics_history[-10:]  # 最近10条
        
        # 计算移动平均
        if len(recent_metrics) > 1:
            avg_reward = sum(m.get('total_reward', 0) for m in recent_metrics) / len(recent_metrics)
            avg_return = sum(m.get('total_return', 0) for m in recent_metrics) / len(recent_metrics)
            
            # 清除当前行并显示新指标
            print(f"\r📊 实时指标 | 平均奖励: {avg_reward:8.4f} | 平均收益: {avg_return:+7.2%}", end='', flush=True)


def create_progress_callback(progress_monitor: ProgressMonitor, 
                           live_display: Optional[LiveMetricsDisplay] = None) -> Callable:
    """创建进度回调函数"""
    
    def progress_callback(episode: int, metrics: Dict[str, Any]):
        # 更新进度监控
        progress_monitor.update(episode, metrics)
        
        # 更新实时显示
        if live_display:
            live_display.update_metrics(metrics)
        
        # 定期保存进度
        if episode % 50 == 0:
            progress_monitor.save_progress()
    
    return progress_callback


if __name__ == "__main__":
    # 测试进度监控器
    import random
    
    print("测试进度监控器")
    
    # 创建监控器
    monitor = ProgressMonitor(total_episodes=100, log_dir="test_logs")
    interrupt_handler = TrainingInterruptHandler(monitor)
    live_display = LiveMetricsDisplay()
    
    # 创建回调
    callback = create_progress_callback(monitor, live_display)
    
    # 开始监控
    monitor.start()
    live_display.start()
    
    try:
        # 模拟训练过程
        for episode in range(1, 101):
            # 模拟训练指标
            metrics = {
                'total_reward': random.uniform(-1, 1),
                'total_return': random.uniform(-0.1, 0.1),
                'win_rate': random.uniform(0.3, 0.7),
                'num_trades': random.randint(5, 20)
            }
            
            # 更新进度
            callback(episode, metrics)
            
            # 模拟训练时间
            time.sleep(0.1)
            
            # 检查中断
            if interrupt_handler.is_interrupted():
                break
    
    except KeyboardInterrupt:
        interrupt_handler.interrupt()
    
    finally:
        # 停止显示
        live_display.stop()
        monitor.finish()
        
        # 显示统计
        stats = monitor.get_statistics()
        print(f"\n最终统计: {stats}")
    
    print("进度监控器测试完成！")