"""
RL Backtest Integration

This module shows how to integrate the RL trading agent with existing backtesting systems.
It provides a wrapper that can replace the traditional rule-based trading logic.
"""

import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
import json

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rl.rl_trading_agent import RLTradingAgent
from rl.trading_environment import TradingEnvironment, MarketState, TradingAction


class RLBacktestWrapper:
    """
    RL回测包装器
    
    将RL代理集成到现有回测系统中，提供与传统策略相同的接口
    """
    
    def __init__(self, rl_agent_path: str, config: Dict):
        """
        初始化RL回测包装器
        
        Args:
            rl_agent_path: 训练好的RL代理模型路径
            config: 回测配置
        """
        self.config = config
        
        # 加载训练好的RL代理
        self.rl_agent = RLTradingAgent.load_model(rl_agent_path)
        self.rl_agent.set_training_mode(False)  # 设置为评估模式
        
        # 创建环境用于状态转换
        self.env = TradingEnvironment(config)
        
        # 交易统计
        self.trade_count = 0
        self.decision_history = []
        
        print(f"RL回测包装器初始化完成")
        print(f"已加载RL代理: {rl_agent_path}")
    
    def should_enter_trade(self, signal: int, confidence: float, 
                          market_data: Dict, portfolio_state: Dict) -> bool:
        """
        决定是否进入交易（替换传统的规则判断）
        
        Args:
            signal: 现有模型的信号 (0 or 1)
            confidence: 信号置信度 (0-1)
            market_data: 市场数据字典
            portfolio_state: 投资组合状态
            
        Returns:
            是否应该进入交易
        """
        # 构建市场状态
        market_state = self._build_market_state(signal, confidence, market_data, portfolio_state)
        
        # 获取状态向量
        state_vector = self.env.get_state_vector(market_state)
        
        # RL代理决策
        action, _, _ = self.rl_agent.get_action(state_vector, deterministic=True)
        
        # 记录决策历史
        self.decision_history.append({
            'signal': signal,
            'confidence': confidence,
            'rl_decision': action['enter_trade'],
            'rl_position_size': action['position_size'],
            'market_price': market_data.get('current_price', 0)
        })
        
        return action['enter_trade']
    
    def get_position_size(self, signal: int, confidence: float,
                         market_data: Dict, portfolio_state: Dict) -> float:
        """
        获取仓位大小（替换固定百分比）
        
        Returns:
            仓位大小 (0-1之间的比例)
        """
        # 构建市场状态
        market_state = self._build_market_state(signal, confidence, market_data, portfolio_state)
        
        # 获取状态向量
        state_vector = self.env.get_state_vector(market_state)
        
        # RL代理决策
        action, _, _ = self.rl_agent.get_action(state_vector, deterministic=True)
        
        return action['position_size']
    
    def get_stop_loss_take_profit(self, signal: int, confidence: float,
                                 market_data: Dict, portfolio_state: Dict) -> tuple:
        """
        获取止损止盈设置（替换固定百分比）
        
        Returns:
            (stop_loss_pct, take_profit_pct) 元组
        """
        # 构建市场状态
        market_state = self._build_market_state(signal, confidence, market_data, portfolio_state)
        
        # 获取状态向量
        state_vector = self.env.get_state_vector(market_state)
        
        # RL代理决策
        action, _, _ = self.rl_agent.get_action(state_vector, deterministic=True)
        
        return action['stop_loss_pct'], action['take_profit_pct']
    
    def get_max_hold_time(self, signal: int, confidence: float,
                         market_data: Dict, portfolio_state: Dict) -> int:
        """
        获取最大持仓时间（替换固定时间）
        
        Returns:
            最大持仓时间（分钟）
        """
        # 构建市场状态
        market_state = self._build_market_state(signal, confidence, market_data, portfolio_state)
        
        # 获取状态向量
        state_vector = self.env.get_state_vector(market_state)
        
        # RL代理决策
        action, _, _ = self.rl_agent.get_action(state_vector, deterministic=True)
        
        return action['max_hold_time']
    
    def make_trading_decision(self, signal: int, confidence: float,
                            market_data: Dict, portfolio_state: Dict) -> Dict:
        """
        完整的交易决策（一次性获取所有参数）
        
        Returns:
            包含所有交易参数的字典
        """
        # 构建市场状态
        market_state = self._build_market_state(signal, confidence, market_data, portfolio_state)
        
        # 获取状态向量
        state_vector = self.env.get_state_vector(market_state)
        
        # RL代理决策
        action, log_prob, value = self.rl_agent.get_action(state_vector, deterministic=True)
        
        # 记录决策
        decision = {
            'enter_trade': action['enter_trade'],
            'position_size': action['position_size'],
            'stop_loss_pct': action['stop_loss_pct'],
            'take_profit_pct': action['take_profit_pct'],
            'max_hold_time': action['max_hold_time'],
            'confidence_score': value,  # RL代理的价值估计作为置信度
            'signal': signal,
            'original_confidence': confidence
        }
        
        self.decision_history.append(decision)
        return decision
    
    def _build_market_state(self, signal: int, confidence: float,
                           market_data: Dict, portfolio_state: Dict) -> MarketState:
        """构建MarketState对象"""
        return MarketState(
            model_signal=signal,
            signal_confidence=confidence,
            signal_probability=market_data.get('signal_probability', confidence),
            current_price=market_data.get('current_price', 2500.0),
            price_change_1h=market_data.get('price_change_1h', 0.0),
            price_change_4h=market_data.get('price_change_4h', 0.0),
            volatility_recent=market_data.get('volatility_recent', 0.02),
            cash_ratio=portfolio_state.get('cash_ratio', 0.9),
            position_count=portfolio_state.get('position_count', 0),
            unrealized_pnl=portfolio_state.get('unrealized_pnl', 0.0),
            recent_win_rate=portfolio_state.get('recent_win_rate', 0.5),
            consecutive_losses=portfolio_state.get('consecutive_losses', 0),
            hour=market_data.get('hour', 12),
            day_of_week=market_data.get('day_of_week', 1),
            is_good_trading_time=market_data.get('is_good_trading_time', True)
        )
    
    def get_decision_stats(self) -> Dict:
        """获取决策统计信息"""
        if not self.decision_history:
            return {}
        
        decisions = pd.DataFrame(self.decision_history)
        
        stats = {
            'total_decisions': len(decisions),
            'enter_trade_rate': decisions['enter_trade'].mean() if 'enter_trade' in decisions else 0,
            'avg_position_size': decisions['position_size'].mean() if 'position_size' in decisions else 0,
            'avg_stop_loss': decisions['stop_loss_pct'].mean() if 'stop_loss_pct' in decisions else 0,
            'avg_take_profit': decisions['take_profit_pct'].mean() if 'take_profit_pct' in decisions else 0,
            'avg_hold_time': decisions['max_hold_time'].mean() if 'max_hold_time' in decisions else 0
        }
        
        return stats
    
    def save_decision_history(self, filepath: str):
        """保存决策历史"""
        with open(filepath, 'w') as f:
            json.dump(self.decision_history, f, indent=2, default=str)
        print(f"决策历史已保存到: {filepath}")


def create_rl_backtest_config() -> Dict:
    """创建RL回测的默认配置"""
    return {
        'initial_capital': 10000,
        'transaction_cost': 0.001,
        'max_position_size': 0.1,
        'max_positions': 5,
        'max_drawdown_limit': 0.2,
        'reward_config': {
            'pnl_weight': 1.0,
            'risk_penalty': 0.5,
            'time_efficiency': 0.1,
            'max_drawdown_penalty': 2.0
        }
    }


def example_backtest_integration():
    """示例：如何在现有回测系统中使用RL代理"""
    print("=== RL回测集成示例 ===")
    
    # 假设我们有一个训练好的RL代理（这里创建一个新的用于演示）
    from rl.rl_trading_agent import create_default_rl_agent
    
    # 创建并保存一个示例代理
    agent = create_default_rl_agent()
    agent.save_model("example_rl_agent")
    
    # 创建RL回测包装器
    config = create_rl_backtest_config()
    rl_wrapper = RLBacktestWrapper("example_rl_agent", config)
    
    # 模拟回测过程
    print("\n模拟回测过程...")
    
    for i in range(100):
        # 模拟现有模型的信号
        signal = np.random.choice([0, 1])
        confidence = np.random.uniform(0.5, 0.9)
        
        # 模拟市场数据
        market_data = {
            'current_price': 2500 + np.random.normal(0, 50),
            'price_change_1h': np.random.normal(0, 0.02),
            'price_change_4h': np.random.normal(0, 0.05),
            'volatility_recent': np.random.uniform(0.01, 0.05),
            'hour': i % 24,
            'day_of_week': (i // 24) % 7,
            'is_good_trading_time': np.random.choice([True, False], p=[0.7, 0.3])
        }
        
        # 模拟投资组合状态
        portfolio_state = {
            'cash_ratio': np.random.uniform(0.7, 1.0),
            'position_count': np.random.randint(0, 3),
            'unrealized_pnl': np.random.normal(0, 100),
            'recent_win_rate': np.random.uniform(0.3, 0.8),
            'consecutive_losses': np.random.randint(0, 3)
        }
        
        # 使用RL代理做决策
        decision = rl_wrapper.make_trading_decision(
            signal, confidence, market_data, portfolio_state
        )
        
        if i < 5:  # 打印前几个决策作为示例
            print(f"Step {i+1}: Signal={signal}, Confidence={confidence:.2f}")
            print(f"  RL Decision: Enter={decision['enter_trade']}, "
                  f"Size={decision['position_size']:.3f}, "
                  f"Stop={decision['stop_loss_pct']:.3f}")
    
    # 获取决策统计
    stats = rl_wrapper.get_decision_stats()
    print(f"\n决策统计:")
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")
    
    # 保存决策历史
    rl_wrapper.save_decision_history("rl_decision_history.json")
    
    # 清理示例文件
    try:
        os.remove("example_rl_agent.pth")
        os.remove("example_rl_agent_config.json")
    except:
        pass
    
    print("\n✅ RL回测集成示例完成")


def integrate_with_existing_backtest():
    """展示如何与现有回测系统集成的伪代码"""
    
    code_example = '''
# 现有回测系统集成示例

# 1. 在现有回测脚本中导入RL包装器
from rl.rl_backtest_integration import RLBacktestWrapper, create_rl_backtest_config

# 2. 初始化RL包装器
config = create_rl_backtest_config()
rl_wrapper = RLBacktestWrapper("path/to/trained_rl_agent", config)

# 3. 在回测循环中替换传统决策逻辑
def backtest_with_rl(data):
    for i, row in data.iterrows():
        # 获取现有模型的信号
        signal = get_model_signal(row)
        confidence = get_model_confidence(row)
        
        # 构建市场数据
        market_data = {
            'current_price': row['close'],
            'price_change_1h': row['price_change_1h'],
            'volatility_recent': row['volatility'],
            # ... 其他市场特征
        }
        
        # 构建投资组合状态
        portfolio_state = {
            'cash_ratio': current_cash / total_capital,
            'position_count': len(active_positions),
            # ... 其他投资组合状态
        }
        
        # 使用RL代理决策（替换原来的固定规则）
        decision = rl_wrapper.make_trading_decision(
            signal, confidence, market_data, portfolio_state
        )
        
        # 根据RL决策执行交易
        if decision['enter_trade']:
            execute_trade(
                size=decision['position_size'],
                stop_loss=decision['stop_loss_pct'],
                take_profit=decision['take_profit_pct'],
                max_hold_time=decision['max_hold_time']
            )

# 4. 分析RL决策效果
stats = rl_wrapper.get_decision_stats()
print("RL决策统计:", stats)
'''
    
    print("=== 现有回测系统集成指南 ===")
    print(code_example)
    
    print("\n集成步骤:")
    print("1. 训练RL代理并保存模型")
    print("2. 创建RLBacktestWrapper实例")
    print("3. 在回测循环中调用RL决策方法")
    print("4. 分析RL决策的效果和统计")


if __name__ == "__main__":
    # 运行集成示例
    example_backtest_integration()
    
    print("\n" + "="*50)
    
    # 显示集成指南
    integrate_with_existing_backtest()
    
    print("\n" + "="*50)
    print("🎯 RL回测集成完成！")
    print("现在你可以:")
    print("1. 训练自己的RL代理")
    print("2. 使用RLBacktestWrapper替换现有的交易逻辑")
    print("3. 对比RL策略与传统策略的效果")
    print("="*50)