#!/usr/bin/env python3
"""
RL Trading Agent Training Script

Enhanced command-line interface for training the RL trading agent using existing models.
Supports multiple coins, timeframes, progress monitoring, and checkpoint recovery.
"""

import argparse
import sys
import os
import signal
import time
import json
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any

# Add the parent directory to the path to import modules
sys.path.append(str(Path(__file__).parent.parent))

from rl.training_manager import TrainingManager
from rl.training_config import FullTrainingConfig, TrainingConfigManager, create_config_from_dict
from rl.config_validator import ConfigValidator


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="强化学习交易代理训练脚本 - 支持多币种、多时间框架训练",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基础训练
  python -m rl.train_rl_agent --model-file models/eth_5m_model.joblib --config-file models/eth_5m_config.json
  
  # 使用预设配置
  python -m rl.train_rl_agent --model-file models/eth_5m_model.joblib --config-file models/eth_5m_config.json --training-config conservative
  
  # 自定义参数
  python -m rl.train_rl_agent --model-file models/btc_5m_model.joblib --config-file models/btc_5m_config.json --episodes 2000 --learning-rate 0.0001
  
  # 从检查点恢复
  python -m rl.train_rl_agent --resume rl_training_logs_ETH_5m_20240101_120000/checkpoints/rl_agent_episode_500
  
  # 批量训练多个模型
  python -m rl.train_rl_agent --batch-mode --models-dir models/ --pattern "*_5m_*"
        """
    )
    
    # 模型参数组
    model_group = parser.add_argument_group('模型配置', '指定要使用的现有模型')
    model_group.add_argument(
        "--model-file", 
        help="现有模型文件路径 (例如: models/eth_5m_model.joblib)"
    )
    
    model_group.add_argument(
        "--config-file",
        help="现有配置文件路径 (例如: models/eth_5m_config.json)"
    )
    
    model_group.add_argument(
        "--auto-detect",
        action="store_true",
        help="自动检测并选择可用的模型文件"
    )
    
    # 批量模式
    batch_group = parser.add_argument_group('批量训练', '批量训练多个模型')
    batch_group.add_argument(
        "--batch-mode",
        action="store_true",
        help="启用批量训练模式"
    )
    
    batch_group.add_argument(
        "--models-dir",
        default="models/",
        help="模型目录路径 (批量模式)"
    )
    
    batch_group.add_argument(
        "--pattern",
        default="*_model.joblib",
        help="模型文件匹配模式 (批量模式)"
    )
    
    # 训练配置
    config_group = parser.add_argument_group('训练配置', '训练相关参数')
    config_group.add_argument(
        "--training-config",
        help="预设训练配置名称 (conservative, aggressive, day_trading, swing_trading, scalping)"
    )
    
    config_group.add_argument(
        "--config-dir",
        default="rl/configs",
        help="训练配置目录"
    )
    
    # 数据参数
    data_group = parser.add_argument_group('数据配置', '数据源和币种配置')
    data_group.add_argument(
        "--db-path",
        default="coin_data.db",
        help="SQLite数据库路径"
    )
    
    data_group.add_argument(
        "--coin",
        help="币种名称 (ETH, BTC, DOT, UNI, SUI, ENA, LINK)"
    )
    
    data_group.add_argument(
        "--interval", 
        help="时间间隔 (1m, 5m, 15m, 30m, 1h, 4h)"
    )
    
    data_group.add_argument(
        "--market",
        default="spot",
        choices=["spot", "futures"],
        help="市场类型"
    )
    
    # 训练参数覆盖
    training_group = parser.add_argument_group('训练参数', '覆盖默认训练参数')
    training_group.add_argument(
        "--episodes",
        type=int,
        help="训练episode数量"
    )
    
    training_group.add_argument(
        "--episode-length",
        type=int,
        help="每个episode的时间步数"
    )
    
    training_group.add_argument(
        "--learning-rate",
        type=float,
        help="学习率"
    )
    
    training_group.add_argument(
        "--batch-size",
        type=int,
        help="批次大小"
    )
    
    training_group.add_argument(
        "--update-frequency",
        type=int,
        help="策略更新频率（每N步更新一次）"
    )
    
    # 环境参数覆盖
    env_group = parser.add_argument_group('环境参数', '覆盖默认环境参数')
    env_group.add_argument(
        "--initial-capital",
        type=float,
        help="初始资金"
    )
    
    env_group.add_argument(
        "--transaction-cost",
        type=float,
        help="交易手续费率"
    )
    
    env_group.add_argument(
        "--max-position-size",
        type=float,
        help="最大单笔仓位比例"
    )
    
    env_group.add_argument(
        "--max-positions",
        type=int,
        help="最大同时持仓数"
    )
    
    # 数据分割参数
    data_split_group = parser.add_argument_group('数据分割', '训练/验证/测试数据分割')
    data_split_group.add_argument(
        "--train-ratio",
        type=float,
        help="训练集比例"
    )
    
    data_split_group.add_argument(
        "--val-ratio",
        type=float,
        help="验证集比例"
    )
    
    data_split_group.add_argument(
        "--test-ratio",
        type=float,
        help="测试集比例"
    )
    
    # 日志和监控
    logging_group = parser.add_argument_group('日志和监控', '训练过程监控和日志')
    logging_group.add_argument(
        "--log-dir",
        help="日志目录（默认自动生成）"
    )
    
    logging_group.add_argument(
        "--save-frequency",
        type=int,
        help="模型保存频率（每N个episode）"
    )
    
    logging_group.add_argument(
        "--eval-frequency",
        type=int,
        help="评估频率（每N个episode）"
    )
    
    logging_group.add_argument(
        "--progress-bar",
        action="store_true",
        default=True,
        help="显示训练进度条"
    )
    
    logging_group.add_argument(
        "--no-progress-bar",
        action="store_false",
        dest="progress_bar",
        help="禁用训练进度条"
    )
    
    logging_group.add_argument(
        "--tensorboard",
        action="store_true",
        help="启用TensorBoard日志"
    )
    
    logging_group.add_argument(
        "--wandb",
        help="Weights & Biases项目名称"
    )
    
    # 恢复和控制选项
    control_group = parser.add_argument_group('训练控制', '训练控制和恢复选项')
    control_group.add_argument(
        "--resume",
        help="从检查点恢复训练（指定检查点路径）"
    )
    
    control_group.add_argument(
        "--auto-resume",
        action="store_true",
        help="自动从最新检查点恢复训练"
    )
    
    control_group.add_argument(
        "--dry-run",
        action="store_true",
        help="仅验证配置，不实际训练"
    )
    
    control_group.add_argument(
        "--validate-only",
        action="store_true",
        help="仅验证配置和数据，不开始训练"
    )
    
    control_group.add_argument(
        "--max-time",
        type=float,
        help="最大训练时间（小时）"
    )
    
    control_group.add_argument(
        "--early-stop",
        action="store_true",
        help="启用早停机制"
    )
    
    control_group.add_argument(
        "--patience",
        type=int,
        help="早停耐心值（episode数）"
    )
    
    # 输出和报告
    output_group = parser.add_argument_group('输出选项', '输出格式和报告')
    output_group.add_argument(
        "--quiet",
        action="store_true",
        help="静默模式，减少输出"
    )
    
    output_group.add_argument(
        "--verbose",
        action="store_true",
        help="详细模式，增加输出"
    )
    
    output_group.add_argument(
        "--json-output",
        help="将结果输出为JSON文件"
    )
    
    output_group.add_argument(
        "--plot-results",
        action="store_true",
        default=True,
        help="生成训练结果图表"
    )
    
    output_group.add_argument(
        "--no-plot",
        action="store_false",
        dest="plot_results",
        help="不生成训练结果图表"
    )
    
    return parser.parse_args()


def detect_available_models(models_dir: str = "models/", pattern: str = "*_model.joblib") -> list:
    """检测可用的模型文件"""
    models_path = Path(models_dir)
    if not models_path.exists():
        return []
    
    model_files = list(models_path.glob(pattern))
    available_models = []
    
    for model_file in model_files:
        # 查找对应的配置文件
        config_file = model_file.with_name(model_file.name.replace('_model.joblib', '_config.json'))
        if config_file.exists():
            available_models.append({
                'model_file': str(model_file),
                'config_file': str(config_file),
                'name': model_file.stem.replace('_model', '')
            })
    
    return available_models


def auto_detect_model_config(args) -> tuple:
    """自动检测模型和配置文件"""
    if args.auto_detect:
        available_models = detect_available_models(args.models_dir, args.pattern)
        
        if not available_models:
            print("❌ 未找到可用的模型文件")
            return None, None
        
        print("🔍 检测到以下可用模型:")
        for i, model in enumerate(available_models, 1):
            print(f"  {i}. {model['name']}")
        
        try:
            choice = input(f"\n请选择模型 (1-{len(available_models)}): ").strip()
            idx = int(choice) - 1
            if 0 <= idx < len(available_models):
                selected = available_models[idx]
                return selected['model_file'], selected['config_file']
            else:
                print("❌ 无效选择")
                return None, None
        except (ValueError, KeyboardInterrupt):
            print("❌ 选择已取消")
            return None, None
    
    return args.model_file, args.config_file


def validate_arguments(args):
    """验证命令行参数"""
    errors = []
    warnings = []
    
    # 批量模式验证
    if args.batch_mode:
        if not Path(args.models_dir).exists():
            errors.append(f"模型目录不存在: {args.models_dir}")
        else:
            available_models = detect_available_models(args.models_dir, args.pattern)
            if not available_models:
                errors.append(f"在目录 {args.models_dir} 中未找到匹配模式 {args.pattern} 的模型")
        return errors, warnings
    
    # 恢复模式验证
    if args.resume:
        if not Path(args.resume).exists():
            errors.append(f"检查点文件不存在: {args.resume}")
        return errors, warnings
    
    # 自动检测模式
    if args.auto_detect:
        return errors, warnings  # 在后续处理中验证
    
    # 标准模式验证
    if not args.model_file:
        errors.append("必须指定 --model-file 或使用 --auto-detect")
    elif not os.path.exists(args.model_file):
        errors.append(f"模型文件不存在: {args.model_file}")
    
    if not args.config_file:
        errors.append("必须指定 --config-file 或使用 --auto-detect")
    elif not os.path.exists(args.config_file):
        errors.append(f"配置文件不存在: {args.config_file}")
    
    if not os.path.exists(args.db_path):
        errors.append(f"数据库文件不存在: {args.db_path}")
    
    # 检查数值范围（仅当指定时）
    if args.train_ratio is not None and args.val_ratio is not None and args.test_ratio is not None:
        if abs(args.train_ratio + args.val_ratio + args.test_ratio - 1.0) > 1e-6:
            errors.append("训练集、验证集、测试集比例之和必须等于1.0")
    
    if args.learning_rate is not None and args.learning_rate <= 0:
        errors.append("学习率必须大于0")
    
    if args.initial_capital is not None and args.initial_capital <= 0:
        errors.append("初始资金必须大于0")
    
    if args.max_position_size is not None and not (0 < args.max_position_size <= 1.0):
        errors.append("最大仓位比例必须在(0, 1]范围内")
    
    if args.episodes is not None and args.episodes <= 0:
        errors.append("训练episode数量必须大于0")
    
    if args.episode_length is not None and args.episode_length <= 0:
        errors.append("Episode长度必须大于0")
    
    # 警告检查
    if args.learning_rate is not None and args.learning_rate > 0.01:
        warnings.append("学习率较高，可能导致训练不稳定")
    
    if args.max_position_size is not None and args.max_position_size > 0.2:
        warnings.append("最大仓位较大，风险较高")
    
    return errors, warnings


def extract_coin_interval_from_model(model_file: str) -> tuple:
    """从模型文件名提取币种和时间间隔"""
    model_name = Path(model_file).stem.replace('_model', '')
    
    # 常见模式匹配
    parts = model_name.split('_')
    coin = None
    interval = None
    
    for part in parts:
        if part.upper() in ['ETH', 'BTC', 'DOT', 'UNI', 'SUI', 'ENA', 'LINK']:
            coin = part.upper()
        elif part in ['1m', '5m', '15m', '30m', '1h', '4h']:
            interval = part
    
    return coin, interval


def create_training_config(args, model_file: str, config_file: str) -> FullTrainingConfig:
    """根据命令行参数创建训练配置"""
    # 创建配置管理器
    config_manager = TrainingConfigManager(args.config_dir)
    
    # 加载基础配置
    if args.training_config:
        base_config = config_manager.load_config(args.training_config)
        if not base_config:
            print(f"⚠️  未找到训练配置 '{args.training_config}'，使用默认配置")
            base_config = config_manager.load_config("conservative")
    else:
        base_config = config_manager.load_config("conservative")
    
    if not base_config:
        # 创建默认配置
        from rl.training_config import FullTrainingConfig
        base_config = FullTrainingConfig()
    
    # 从模型文件名提取币种和时间间隔
    detected_coin, detected_interval = extract_coin_interval_from_model(model_file)
    
    # 应用命令行参数覆盖
    config = base_config
    
    # 数据配置
    config.data.db_path = args.db_path
    config.data.coin = args.coin or detected_coin or "ETH"
    config.data.interval = args.interval or detected_interval or "5m"
    config.data.market = args.market
    
    if args.train_ratio is not None:
        config.data.train_ratio = args.train_ratio
    if args.val_ratio is not None:
        config.data.val_ratio = args.val_ratio
    if args.test_ratio is not None:
        config.data.test_ratio = args.test_ratio
    
    # 模型配置
    config.model.model_file = model_file
    config.model.config_file = config_file
    
    # 环境配置
    if args.initial_capital is not None:
        config.environment.initial_capital = args.initial_capital
    if args.transaction_cost is not None:
        config.environment.transaction_cost = args.transaction_cost
    if args.max_position_size is not None:
        config.environment.max_position_size = args.max_position_size
    if args.max_positions is not None:
        config.environment.max_positions = args.max_positions
    
    # RL配置
    if args.learning_rate is not None:
        config.rl.learning_rate = args.learning_rate
    if args.batch_size is not None:
        config.rl.batch_size = args.batch_size
    if args.update_frequency is not None:
        config.training.update_frequency = args.update_frequency
    
    # 训练配置
    if args.episodes is not None:
        config.training.episodes = args.episodes
    if args.episode_length is not None:
        config.training.episode_length = args.episode_length
    if args.save_frequency is not None:
        config.training.save_frequency = args.save_frequency
    if args.eval_frequency is not None:
        config.training.eval_frequency = args.eval_frequency
    if args.max_time is not None:
        config.training.max_training_time_hours = args.max_time
    if args.patience is not None:
        config.training.early_stopping_patience = args.patience
    
    # 日志配置
    if args.log_dir:
        config.logging.log_dir = args.log_dir
    else:
        # 生成日志目录名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        config.logging.log_dir = f"rl_training_logs_{config.data.coin}_{config.data.interval}_{timestamp}"
    
    config.logging.tensorboard_logging = args.tensorboard
    if args.wandb:
        config.logging.wandb_logging = True
        config.logging.wandb_project = args.wandb
    
    # 更新配置名称和描述
    config.config_name = f"cli_generated_{config.data.coin}_{config.data.interval}"
    config.description = f"通过CLI生成的{config.data.coin} {config.data.interval}训练配置"
    
    return config


def print_config_summary(config: FullTrainingConfig):
    """打印配置摘要"""
    print("=" * 70)
    print("🚀 RL交易代理训练配置")
    print("=" * 70)
    print(f"📊 数据源: {config.data.db_path}")
    print(f"💰 币种: {config.data.coin} ({config.data.interval}, {config.data.market})")
    print(f"🤖 现有模型: {config.model.model_file}")
    print(f"⚙️  现有配置: {config.model.config_file}")
    print(f"📝 训练配置: {config.config_name}")
    print()
    print(f"🎯 训练参数:")
    print(f"  Episodes: {config.training.episodes:,}")
    print(f"  Episode长度: {config.training.episode_length:,}")
    print(f"  学习率: {config.rl.learning_rate:.6f}")
    print(f"  批次大小: {config.rl.batch_size}")
    print(f"  更新频率: {config.training.update_frequency}")
    print(f"  最大训练时间: {config.training.max_training_time_hours:.1f}小时")
    print()
    print(f"🏦 环境参数:")
    print(f"  初始资金: ${config.environment.initial_capital:,.2f}")
    print(f"  交易成本: {config.environment.transaction_cost:.4f}")
    print(f"  最大仓位: {config.environment.max_position_size:.1%}")
    print(f"  最大持仓数: {config.environment.max_positions}")
    print()
    print(f"🎲 奖励配置:")
    print(f"  PnL权重: {config.reward.pnl_weight:.2f}")
    print(f"  风险惩罚: {config.reward.risk_penalty_weight:.2f}")
    print(f"  最大回撤阈值: {config.reward.max_drawdown_threshold:.1%}")
    print()
    print(f"📈 数据分割:")
    print(f"  训练集: {config.data.train_ratio:.1%}")
    print(f"  验证集: {config.data.val_ratio:.1%}")
    print(f"  测试集: {config.data.test_ratio:.1%}")
    print()
    print(f"📁 日志目录: {config.logging.log_dir}")
    if config.logging.tensorboard_logging:
        print(f"📊 TensorBoard: 启用")
    if config.logging.wandb_logging:
        print(f"🔗 W&B项目: {config.logging.wandb_project}")
    print("=" * 70)


def setup_signal_handlers():
    """设置信号处理器以支持优雅中断"""
    interrupted = {'value': False}
    
    def signal_handler(signum, frame):
        print(f"\n⚠️  收到中断信号 ({signum})，正在优雅停止训练...")
        interrupted['value'] = True
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    return interrupted


def find_latest_checkpoint(log_dir: str) -> Optional[str]:
    """查找最新的检查点文件"""
    checkpoints_dir = Path(log_dir) / "checkpoints"
    if not checkpoints_dir.exists():
        return None
    
    checkpoint_files = list(checkpoints_dir.glob("rl_agent_episode_*"))
    if not checkpoint_files:
        return None
    
    # 按episode数排序，返回最新的
    def extract_episode(path):
        try:
            return int(path.name.split('_')[-1])
        except ValueError:
            return 0
    
    latest = max(checkpoint_files, key=extract_episode)
    return str(latest)


def train_single_model(args, model_file: str, config_file: str) -> Dict[str, Any]:
    """训练单个模型"""
    print(f"\n🎯 开始训练模型: {Path(model_file).name}")
    
    # 创建配置
    config = create_training_config(args, model_file, config_file)
    
    # 验证配置
    if not args.quiet:
        validator = ConfigValidator()
        issues = validator.validate_config(config)
        
        if issues['errors']:
            print("❌ 配置验证失败:")
            for error in issues['errors']:
                print(f"  - {error}")
            return {'success': False, 'error': 'Configuration validation failed'}
        
        if issues['warnings'] and args.verbose:
            print("⚠️  配置警告:")
            for warning in issues['warnings']:
                print(f"  - {warning}")
    
    # 打印配置摘要
    if not args.quiet:
        print_config_summary(config)
    
    # 干运行模式
    if args.dry_run or args.validate_only:
        print("✅ 配置验证通过（验证模式）")
        return {'success': True, 'mode': 'validation'}
    
    # 确认开始训练
    if not args.quiet and not args.batch_mode:
        try:
            response = input("\n是否开始训练？(y/N): ").strip().lower()
            if response not in ['y', 'yes']:
                print("训练已取消")
                return {'success': False, 'error': 'User cancelled'}
        except KeyboardInterrupt:
            print("\n训练已取消")
            return {'success': False, 'error': 'User cancelled'}
    
    # 设置信号处理
    interrupted = setup_signal_handlers()
    
    try:
        # 创建训练管理器
        if not args.quiet:
            print("\n🔧 初始化训练管理器...")
        
        trainer = TrainingManager(config)
        
        # 检查恢复选项
        if args.auto_resume:
            latest_checkpoint = find_latest_checkpoint(config.logging.log_dir)
            if latest_checkpoint:
                print(f"🔄 从检查点恢复: {latest_checkpoint}")
                trainer.load_checkpoint(latest_checkpoint)
        
        # 开始训练
        if not args.quiet:
            print("🚀 开始训练...")
        
        start_time = time.time()
        results = trainer.train(
            progress_callback=None if args.quiet else lambda episode, metrics: print(f"Episode {episode}: {metrics}"),
            interrupted_flag=interrupted
        )
        
        training_time = time.time() - start_time
        
        # 训练完成
        if not args.quiet:
            print("\n" + "=" * 70)
            print("🎉 训练完成！")
            print("=" * 70)
            print(f"⏱️  训练时间: {training_time:.2f} 秒")
            print(f"🏆 最佳性能: {results['best_performance']:.2%}")
            print(f"💾 最佳模型: {results['best_model_path']}")
            
            if 'final_test_metrics' in results:
                print("\n📊 最终测试集性能:")
                test_metrics = results['final_test_metrics']
                print(f"  平均收益率: {test_metrics['avg_return']:.2%}")
                print(f"  平均胜率: {test_metrics['avg_win_rate']:.2%}")
                print(f"  平均夏普比率: {test_metrics['avg_sharpe_ratio']:.3f}")
                print(f"  平均最大回撤: {test_metrics['avg_max_drawdown']:.2%}")
                print(f"  平均交易次数: {test_metrics['avg_num_trades']:.1f}")
            
            print(f"\n📁 日志和模型已保存到: {config.logging.log_dir}")
            print("=" * 70)
        
        # 保存结果到JSON
        if args.json_output:
            results['training_time'] = training_time
            results['config'] = config.__dict__
            with open(args.json_output, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            print(f"📄 结果已保存到: {args.json_output}")
        
        return {
            'success': True,
            'results': results,
            'training_time': training_time,
            'model_file': model_file,
            'config_file': config_file
        }
        
    except KeyboardInterrupt:
        print("\n⚠️  训练被用户中断")
        return {'success': False, 'error': 'User interrupted'}
    except Exception as e:
        print(f"\n❌ 训练过程中发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return {'success': False, 'error': str(e)}


def main():
    """主函数"""
    if not sys.argv[1:]:  # 如果没有参数，显示帮助
        print("RL交易代理训练脚本")
        print("使用 --help 查看详细帮助信息")
        sys.exit(0)
    
    # 解析参数
    args = parse_arguments()
    
    # 设置日志级别
    if args.quiet:
        import logging
        logging.getLogger().setLevel(logging.ERROR)
    elif args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    if not args.quiet:
        print("🚀 RL交易代理训练脚本")
        print("=" * 70)
    
    # 批量训练模式
    if args.batch_mode:
        return handle_batch_training(args)
    
    # 恢复训练模式
    if args.resume:
        return handle_resume_training(args)
    
    # 标准训练模式
    return handle_standard_training(args)


def handle_batch_training(args):
    """处理批量训练"""
    print("📦 批量训练模式")
    print("-" * 50)
    
    # 验证参数
    errors, warnings = validate_arguments(args)
    if errors:
        print("❌ 参数验证失败:")
        for error in errors:
            print(f"  - {error}")
        sys.exit(1)
    
    # 获取可用模型
    available_models = detect_available_models(args.models_dir, args.pattern)
    
    if not available_models:
        print(f"❌ 在目录 {args.models_dir} 中未找到匹配的模型")
        sys.exit(1)
    
    print(f"🔍 找到 {len(available_models)} 个模型:")
    for model in available_models:
        print(f"  • {model['name']}")
    
    # 确认批量训练
    if not args.quiet:
        try:
            response = input(f"\n是否开始批量训练 {len(available_models)} 个模型？(y/N): ").strip().lower()
            if response not in ['y', 'yes']:
                print("批量训练已取消")
                return
        except KeyboardInterrupt:
            print("\n批量训练已取消")
            return
    
    # 执行批量训练
    results = []
    successful = 0
    failed = 0
    
    for i, model in enumerate(available_models, 1):
        print(f"\n{'='*20} 模型 {i}/{len(available_models)} {'='*20}")
        
        result = train_single_model(args, model['model_file'], model['config_file'])
        results.append({
            'model_name': model['name'],
            'model_file': model['model_file'],
            'config_file': model['config_file'],
            **result
        })
        
        if result['success']:
            successful += 1
            print(f"✅ {model['name']} 训练成功")
        else:
            failed += 1
            print(f"❌ {model['name']} 训练失败: {result.get('error', 'Unknown error')}")
    
    # 批量训练总结
    print(f"\n{'='*70}")
    print("📊 批量训练总结")
    print(f"{'='*70}")
    print(f"总模型数: {len(available_models)}")
    print(f"成功: {successful}")
    print(f"失败: {failed}")
    print(f"成功率: {successful/len(available_models):.1%}")
    
    if args.json_output:
        with open(args.json_output, 'w') as f:
            json.dump({
                'batch_results': results,
                'summary': {
                    'total': len(available_models),
                    'successful': successful,
                    'failed': failed,
                    'success_rate': successful/len(available_models)
                }
            }, f, indent=2, default=str)
        print(f"📄 批量结果已保存到: {args.json_output}")


def handle_resume_training(args):
    """处理恢复训练"""
    print("🔄 恢复训练模式")
    print("-" * 50)
    
    checkpoint_path = Path(args.resume)
    if not checkpoint_path.exists():
        print(f"❌ 检查点文件不存在: {args.resume}")
        sys.exit(1)
    
    print(f"📂 检查点路径: {args.resume}")
    
    try:
        # 加载检查点元数据
        metadata_file = checkpoint_path.with_suffix('.json')
        if metadata_file.exists():
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
            
            print(f"📊 检查点信息:")
            print(f"  Episode: {metadata.get('episode', 'Unknown')}")
            print(f"  性能: {metadata.get('performance', 'Unknown')}")
            print(f"  时间: {metadata.get('timestamp', 'Unknown')}")
        
        # 确认恢复
        if not args.quiet:
            try:
                response = input("\n是否从此检查点恢复训练？(y/N): ").strip().lower()
                if response not in ['y', 'yes']:
                    print("恢复训练已取消")
                    return
            except KeyboardInterrupt:
                print("\n恢复训练已取消")
                return
        
        # 创建训练管理器并恢复
        print("🔧 初始化训练管理器...")
        
        # 从检查点恢复配置
        if metadata_file.exists():
            config_dict = metadata.get('config', {})
            config = create_config_from_dict(config_dict)
        else:
            print("⚠️  未找到配置元数据，使用默认配置")
            config = FullTrainingConfig()
        
        trainer = TrainingManager(config)
        trainer.load_checkpoint(args.resume)
        
        # 继续训练
        print("🚀 恢复训练...")
        results = trainer.train()
        
        print("✅ 恢复训练完成")
        return results
        
    except Exception as e:
        print(f"❌ 恢复训练失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def handle_standard_training(args):
    """处理标准训练"""
    # 验证参数
    errors, warnings = validate_arguments(args)
    if errors:
        print("❌ 参数验证失败:")
        for error in errors:
            print(f"  - {error}")
        sys.exit(1)
    
    if warnings and not args.quiet:
        print("⚠️  参数警告:")
        for warning in warnings:
            print(f"  - {warning}")
        print()
    
    # 获取模型和配置文件
    model_file, config_file = auto_detect_model_config(args)
    
    if not model_file or not config_file:
        print("❌ 无法确定模型和配置文件")
        sys.exit(1)
    
    # 训练单个模型
    result = train_single_model(args, model_file, config_file)
    
    if result['success']:
        if not args.quiet:
            print("🎉 训练成功完成！")
        sys.exit(0)
    else:
        print(f"❌ 训练失败: {result.get('error', 'Unknown error')}")
        sys.exit(1)


if __name__ == "__main__":
    main()