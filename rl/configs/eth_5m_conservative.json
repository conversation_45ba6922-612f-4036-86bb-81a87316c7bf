{"name": "ETH 5m Conservative Strategy", "description": "Conservative RL trading strategy for ETH 5-minute timeframe with lower risk parameters", "symbol": "ETH", "timeframe": "5m", "model_file": "models/eth_5m_model.joblib", "config_file": "models/eth_5m_config.json", "training": {"episodes": 1500, "episode_length": 800, "learning_rate": 0.0002, "batch_size": 32, "update_frequency": 150, "gamma": 0.95, "clip_epsilon": 0.15, "entropy_coef": 0.005, "value_loss_coef": 0.3, "max_grad_norm": 0.5}, "environment": {"initial_capital": 10000, "transaction_cost": 0.001, "slippage": 0.0005, "max_position_size": 0.05, "min_position_size": 0.005, "max_positions": 3, "max_drawdown_limit": 0.15, "stop_loss_range": [0.01, 0.03], "take_profit_range": [0.015, 0.04], "hold_time_range": [30, 180]}, "reward": {"profit_weight": 0.8, "risk_weight": 1.2, "efficiency_weight": 0.05, "drawdown_penalty": 3.0, "transaction_cost_penalty": 0.2, "consistency_bonus": 0.1, "win_rate_bonus": 0.15}, "data": {"train_start": "2023-01-01", "train_end": "2024-06-30", "val_start": "2024-07-01", "val_end": "2024-09-30", "test_start": "2024-10-01", "test_end": "2024-12-31", "min_signal_confidence": 0.6, "use_market_hours_filter": true}, "risk_management": {"max_daily_trades": 10, "max_consecutive_losses": 3, "position_sizing_method": "kelly_fraction", "volatility_adjustment": true, "correlation_limit": 0.7}, "evaluation": {"benchmark_strategies": ["buy_hold", "simple_momentum", "mean_reversion"], "metrics": ["total_return", "sharpe_ratio", "max_drawdown", "win_rate", "profit_factor"], "rolling_window": 30}}