{"name": "Multi-Coin Diversified Strategy", "description": "Diversified RL trading strategy across multiple cryptocurrencies with portfolio-level risk management", "symbols": ["ETH", "BTC", "DOT", "UNI"], "timeframe": "15m", "model_files": {"ETH": "models/eth_15m_model.joblib", "BTC": "models/btc_15m_model.joblib", "DOT": "models/dot_15m_model.joblib", "UNI": "models/uni_15m_model.joblib"}, "config_files": {"ETH": "models/eth_15m_config.json", "BTC": "models/btc_15m_config.json", "DOT": "models/dot_15m_config.json", "UNI": "models/uni_15m_config.json"}, "training": {"episodes": 1800, "episode_length": 1500, "learning_rate": 0.0002, "batch_size": 96, "update_frequency": 120, "gamma": 0.98, "clip_epsilon": 0.18, "entropy_coef": 0.008, "value_loss_coef": 0.4, "max_grad_norm": 0.6}, "environment": {"initial_capital": 50000, "transaction_cost": 0.001, "slippage": 0.0007, "max_position_size_per_asset": 0.06, "max_total_exposure": 0.3, "min_position_size": 0.003, "max_positions_per_asset": 2, "max_total_positions": 6, "max_drawdown_limit": 0.2, "stop_loss_range": [0.015, 0.045], "take_profit_range": [0.02, 0.055], "hold_time_range": [60, 360]}, "reward": {"profit_weight": 1.0, "risk_weight": 1.0, "efficiency_weight": 0.08, "drawdown_penalty": 2.5, "transaction_cost_penalty": 0.12, "diversification_bonus": 0.15, "correlation_penalty": 0.2, "portfolio_balance_bonus": 0.1}, "data": {"train_start": "2023-01-01", "train_end": "2024-06-30", "val_start": "2024-07-01", "val_end": "2024-09-30", "test_start": "2024-10-01", "test_end": "2024-12-31", "min_signal_confidence": 0.5, "use_market_hours_filter": false, "correlation_lookback": 168}, "portfolio_management": {"rebalancing_frequency": "daily", "max_asset_weight": 0.4, "min_asset_weight": 0.1, "correlation_threshold": 0.85, "volatility_targeting": true, "target_volatility": 0.15}, "risk_management": {"max_daily_trades": 20, "max_consecutive_losses": 4, "position_sizing_method": "risk_parity", "volatility_adjustment": true, "correlation_limit": 0.75, "sector_exposure_limit": 0.6}, "evaluation": {"benchmark_strategies": ["equal_weight_buy_hold", "momentum_portfolio", "mean_reversion_portfolio"], "metrics": ["total_return", "sharpe_ratio", "max_drawdown", "win_rate", "profit_factor", "information_ratio"], "rolling_window": 30, "attribution_analysis": true}}