{"name": "BTC 15m Balanced Strategy", "description": "Balanced RL trading strategy for BTC 15-minute timeframe with moderate risk parameters", "symbol": "BTC", "timeframe": "15m", "model_file": "models/btc_15m_model.joblib", "config_file": "models/btc_15m_config.json", "training": {"episodes": 1200, "episode_length": 1000, "learning_rate": 0.0003, "batch_size": 48, "update_frequency": 100, "gamma": 0.97, "clip_epsilon": 0.2, "entropy_coef": 0.01, "value_loss_coef": 0.5, "max_grad_norm": 0.7}, "environment": {"initial_capital": 10000, "transaction_cost": 0.001, "slippage": 0.0006, "max_position_size": 0.08, "min_position_size": 0.002, "max_positions": 5, "max_drawdown_limit": 0.18, "stop_loss_range": [0.012, 0.04], "take_profit_range": [0.018, 0.05], "hold_time_range": [45, 240]}, "reward": {"profit_weight": 1.0, "risk_weight": 0.8, "efficiency_weight": 0.1, "drawdown_penalty": 2.0, "transaction_cost_penalty": 0.1, "consistency_bonus": 0.08, "trend_following_bonus": 0.12}, "data": {"train_start": "2023-01-01", "train_end": "2024-06-30", "val_start": "2024-07-01", "val_end": "2024-09-30", "test_start": "2024-10-01", "test_end": "2024-12-31", "min_signal_confidence": 0.55, "use_market_hours_filter": false, "volatility_filter_threshold": 0.02}, "risk_management": {"max_daily_trades": 15, "max_consecutive_losses": 4, "position_sizing_method": "volatility_adjusted", "volatility_adjustment": true, "correlation_limit": 0.8, "drawdown_scaling": true}, "evaluation": {"benchmark_strategies": ["buy_hold", "simple_momentum", "rsi_mean_reversion"], "metrics": ["total_return", "sharpe_ratio", "max_drawdown", "win_rate", "profit_factor", "recovery_factor"], "rolling_window": 21}}