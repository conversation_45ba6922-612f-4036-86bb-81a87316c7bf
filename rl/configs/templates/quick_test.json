{"data": {"db_path": "coin_data.db", "coin": "ETH", "interval": "5m", "market": "spot", "train_ratio": 0.8, "val_ratio": 0.1, "test_ratio": 0.1}, "model": {"model_file": null, "config_file": null, "state_dim": 15, "hidden_dim": 128, "num_layers": 2, "activation": "relu", "dropout_rate": 0.05}, "environment": {"initial_capital": 5000.0, "transaction_cost": 0.001, "max_position_size": 0.05, "max_positions": 3, "slippage": 0.0001, "min_trade_amount": 10.0}, "reward": {"pnl_weight": 1.0, "risk_penalty_weight": 0.3, "time_efficiency_weight": 0.05, "drawdown_penalty_weight": 1.5, "sharpe_bonus_weight": 0.2, "max_drawdown_threshold": 0.08, "volatility_penalty_weight": 0.1, "win_rate_bonus_weight": 0.3, "consecutive_win_bonus": 0.05, "trade_frequency_penalty": 0.02, "market_regime_adjustments": {"trending_up": 1.1, "trending_down": 0.9, "sideways": 1.0, "high_volatility": 0.8, "low_volatility": 1.0}, "adaptive_learning_rate": 0.005, "performance_window": 20}, "rl": {"learning_rate": 0.001, "batch_size": 32, "gamma": 0.98, "clip_epsilon": 0.3, "value_loss_coef": 0.4, "entropy_coef": 0.02, "gae_lambda": 0.9, "update_epochs": 2, "max_grad_norm": 1.0, "buffer_size": 5000, "exploration_noise": 0.2, "epsilon_start": 1.2, "epsilon_end": 0.2, "epsilon_decay": 0.99}, "training": {"episodes": 200, "episode_length": 300, "update_frequency": 50, "eval_frequency": 25, "save_frequency": 50, "early_stopping_patience": 50, "early_stopping_threshold": 0.01, "max_training_time_hours": 2.0, "resume_from_checkpoint": false, "checkpoint_path": null}, "logging": {"log_dir": "rl_training_logs/quick_test", "log_level": "DEBUG", "save_plots": true, "plot_frequency": 25, "tensorboard_logging": false, "wandb_logging": false, "wandb_project": null}, "config_name": "quick_test", "description": "快速测试配置，用于验证系统功能和调试", "version": "1.0", "created_by": "development_team"}