# reward_config.py
# 奖励函数配置管理系统

import json
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from .reward_functions import RewardConfig, create_default_configs

class RewardConfigManager:
    """奖励函数配置管理器"""
    
    def __init__(self, config_dir: str = "rl/configs"):
        self.config_dir = config_dir
        self.ensure_config_dir()
        self.load_default_configs()
    
    def ensure_config_dir(self):
        """确保配置目录存在"""
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
    
    def load_default_configs(self):
        """加载默认配置"""
        default_configs = create_default_configs()
        
        for name, config in default_configs.items():
            config_path = os.path.join(self.config_dir, f"{name}_reward_config.json")
            if not os.path.exists(config_path):
                self.save_config(name, config)
    
    def save_config(self, name: str, config: RewardConfig):
        """保存配置到文件"""
        config_dict = asdict(config)
        config_path = os.path.join(self.config_dir, f"{name}_reward_config.json")
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
        
        print(f"奖励配置已保存: {config_path}")
    
    def load_config(self, name: str) -> Optional[RewardConfig]:
        """从文件加载配置"""
        config_path = os.path.join(self.config_dir, f"{name}_reward_config.json")
        
        if not os.path.exists(config_path):
            print(f"配置文件不存在: {config_path}")
            return None
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            
            return RewardConfig(**config_dict)
        except Exception as e:
            print(f"加载配置失败: {e}")
            return None
    
    def list_configs(self) -> List[str]:
        """列出所有可用配置"""
        configs = []
        for filename in os.listdir(self.config_dir):
            if filename.endswith('_reward_config.json'):
                config_name = filename.replace('_reward_config.json', '')
                configs.append(config_name)
        return configs
    
    def create_custom_config(self, name: str, base_config: str = 'balanced', 
                           modifications: Dict[str, Any] = None) -> RewardConfig:
        """创建自定义配置"""
        # 加载基础配置
        base = self.load_config(base_config)
        if base is None:
            base = RewardConfig()  # 使用默认配置
        
        # 应用修改
        if modifications:
            config_dict = asdict(base)
            config_dict.update(modifications)
            custom_config = RewardConfig(**config_dict)
        else:
            custom_config = base
        
        # 保存自定义配置
        self.save_config(name, custom_config)
        
        return custom_config
    
    def get_config_comparison(self, config_names: List[str]) -> Dict:
        """比较多个配置"""
        comparison = {}
        
        for name in config_names:
            config = self.load_config(name)
            if config:
                comparison[name] = asdict(config)
        
        return comparison

def create_reward_config_from_dict(config_dict: Dict) -> RewardConfig:
    """从字典创建奖励配置"""
    # 提取基础参数
    base_params = {
        'pnl_weight': config_dict.get('pnl_weight', 1.0),
        'risk_penalty_weight': config_dict.get('risk_penalty_weight', 0.5),
        'time_efficiency_weight': config_dict.get('time_efficiency_weight', 0.1),
        'drawdown_penalty_weight': config_dict.get('drawdown_penalty_weight', 2.0),
        'sharpe_bonus_weight': config_dict.get('sharpe_bonus_weight', 0.3),
        'max_drawdown_threshold': config_dict.get('max_drawdown_threshold', 0.05),
        'volatility_penalty_weight': config_dict.get('volatility_penalty_weight', 0.2),
        'win_rate_bonus_weight': config_dict.get('win_rate_bonus_weight', 0.4),
        'consecutive_win_bonus': config_dict.get('consecutive_win_bonus', 0.1),
        'trade_frequency_penalty': config_dict.get('trade_frequency_penalty', 0.05),
        'adaptive_learning_rate': config_dict.get('adaptive_learning_rate', 0.01),
        'performance_window': config_dict.get('performance_window', 50)
    }
    
    # 处理市场状态调整参数
    if 'market_regime_adjustments' in config_dict:
        base_params['market_regime_adjustments'] = config_dict['market_regime_adjustments']
    
    return RewardConfig(**base_params)

def validate_reward_config(config: RewardConfig) -> Dict[str, str]:
    """验证奖励配置的合理性"""
    issues = {}
    
    # 检查权重范围
    if config.pnl_weight < 0 or config.pnl_weight > 5.0:
        issues['pnl_weight'] = "PnL权重应在0-5.0之间"
    
    if config.risk_penalty_weight < 0 or config.risk_penalty_weight > 10.0:
        issues['risk_penalty_weight'] = "风险惩罚权重应在0-10.0之间"
    
    if config.time_efficiency_weight < 0 or config.time_efficiency_weight > 1.0:
        issues['time_efficiency_weight'] = "时间效率权重应在0-1.0之间"
    
    # 检查阈值参数
    if config.max_drawdown_threshold < 0.01 or config.max_drawdown_threshold > 0.2:
        issues['max_drawdown_threshold'] = "最大回撤阈值应在1%-20%之间"
    
    # 检查学习参数
    if config.adaptive_learning_rate < 0.001 or config.adaptive_learning_rate > 0.1:
        issues['adaptive_learning_rate'] = "自适应学习率应在0.001-0.1之间"
    
    if config.performance_window < 10 or config.performance_window > 200:
        issues['performance_window'] = "性能窗口应在10-200之间"
    
    return issues

# 预定义的配置模板
REWARD_CONFIG_TEMPLATES = {
    "day_trading": {
        "description": "适合日内交易的配置",
        "pnl_weight": 1.2,
        "risk_penalty_weight": 0.8,
        "time_efficiency_weight": 0.3,
        "drawdown_penalty_weight": 1.5,
        "sharpe_bonus_weight": 0.4,
        "win_rate_bonus_weight": 0.5,
        "consecutive_win_bonus": 0.08,
        "trade_frequency_penalty": 0.02,
        "max_drawdown_threshold": 0.03
    },
    
    "swing_trading": {
        "description": "适合波段交易的配置", 
        "pnl_weight": 1.0,
        "risk_penalty_weight": 0.6,
        "time_efficiency_weight": 0.05,
        "drawdown_penalty_weight": 2.5,
        "sharpe_bonus_weight": 0.5,
        "win_rate_bonus_weight": 0.6,
        "consecutive_win_bonus": 0.12,
        "trade_frequency_penalty": 0.08,
        "max_drawdown_threshold": 0.08
    },
    
    "scalping": {
        "description": "适合剥头皮交易的配置",
        "pnl_weight": 0.8,
        "risk_penalty_weight": 1.2,
        "time_efficiency_weight": 0.5,
        "drawdown_penalty_weight": 3.0,
        "sharpe_bonus_weight": 0.3,
        "win_rate_bonus_weight": 0.7,
        "consecutive_win_bonus": 0.05,
        "trade_frequency_penalty": 0.01,
        "max_drawdown_threshold": 0.02
    },
    
    "trend_following": {
        "description": "适合趋势跟踪的配置",
        "pnl_weight": 1.5,
        "risk_penalty_weight": 0.4,
        "time_efficiency_weight": 0.08,
        "drawdown_penalty_weight": 1.8,
        "sharpe_bonus_weight": 0.6,
        "win_rate_bonus_weight": 0.3,
        "consecutive_win_bonus": 0.15,
        "trade_frequency_penalty": 0.1,
        "max_drawdown_threshold": 0.12
    }
}

def create_template_config(template_name: str) -> Optional[RewardConfig]:
    """根据模板创建配置"""
    if template_name not in REWARD_CONFIG_TEMPLATES:
        print(f"未知模板: {template_name}")
        print(f"可用模板: {list(REWARD_CONFIG_TEMPLATES.keys())}")
        return None
    
    template = REWARD_CONFIG_TEMPLATES[template_name]
    return create_reward_config_from_dict(template)

if __name__ == "__main__":
    # 测试配置管理系统
    print("测试奖励配置管理系统")
    
    # 创建配置管理器
    manager = RewardConfigManager()
    
    # 列出所有配置
    configs = manager.list_configs()
    print(f"可用配置: {configs}")
    
    # 创建自定义配置
    custom_config = manager.create_custom_config(
        name="test_custom",
        base_config="balanced",
        modifications={
            'pnl_weight': 1.2,
            'risk_penalty_weight': 0.8
        }
    )
    
    print(f"创建自定义配置: {custom_config}")
    
    # 验证配置
    issues = validate_reward_config(custom_config)
    if issues:
        print(f"配置问题: {issues}")
    else:
        print("配置验证通过")
    
    # 测试模板配置
    for template_name in REWARD_CONFIG_TEMPLATES.keys():
        template_config = create_template_config(template_name)
        print(f"模板 {template_name}: {template_config.pnl_weight}")
    
    print("配置管理系统测试完成！")