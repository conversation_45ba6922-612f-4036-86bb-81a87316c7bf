#!/usr/bin/env python3
# run_rl_backtest.py
# 运行 RL 回测的主脚本 - 集成到现有回测系统

import sys
import os
import argparse
import pandas as pd
import numpy as np
from datetime import datetime
import sqlite3

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rl.rl_backtester import RLBacktester, create_rl_backtester
from backtest_money_quick import (
    load_data_from_sqlite, parse_time_input, load_chushou_config,
    finalize_and_report, format_beijing_time
)

try:
    from model_utils_815 import get_coin_config
    from analyze_money import analyze_and_plot_results
except ImportError:
    print("警告: 无法导入 model_utils 或 analyze_money，某些功能可能不可用")
    def get_coin_config(coin): 
        return {'model_basename': f"{coin.lower()}_5m", 'api_symbol': coin.upper()}
    def analyze_and_plot_results(df, basename): 
        print(f"分析结果: {basename}")


def run_rl_backtest(model_file: str, config_file: str, rl_agent_path: str, 
                   df: pd.DataFrame, **kwargs) -> RLBacktester:
    """
    运行 RL 回测
    
    Args:
        model_file: 现有预测模型文件
        config_file: 现有模型配置文件
        rl_agent_path: RL 代理模型路径
        df: 历史数据 DataFrame
        **kwargs: 其他回测参数
        
    Returns:
        完成回测的 RLBacktester 实例
    """
    print("=== 开始 RL 强化学习回测 ===")
    
    # 创建 RL 回测器
    rl_backtester = create_rl_backtester(
        model_file=model_file,
        config_file=config_file,
        rl_agent_path=rl_agent_path,
        initial_capital=kwargs['initial_capital'],
        risk_per_trade_pct=kwargs['risk_per_trade_pct'],
        price_multiplier=kwargs.get('price_multiplier', 1.0),
        stop_loss_pct=kwargs.get('stop_loss_pct'),
        rl_config=kwargs.get('rl_config')
    )
    
    if not rl_backtester.rl_agent:
        print("❌ RL 代理加载失败，退出")
        return None
    
    # 加载出手时间配置
    time_filter = load_chushou_config(kwargs.get('chushou_file', 'chushou.json'))
    
    # 使用快速回测方法（预计算特征）
    print("正在预先计算所有时间序列的特征，请稍候...")
    
    try:
        from model_utils_815 import calculate_features
        features_df = calculate_features(df, timeframe=rl_backtester.config['timeframe_minutes'])
        print("特征计算完成。")
    except ImportError:
        print("警告: 无法导入 calculate_features，使用原始数据")
        features_df = df.copy()
    
    # 找到第一个可以开始预测的有效点
    valid_features_df = features_df.dropna(subset=rl_backtester.config.get('feature_list', ['close']))
    if valid_features_df.empty:
        print("❌ 计算特征后没有剩下任何有效数据行，无法进行回测。")
        return None
    
    first_valid_index_pos = df.index.get_loc(valid_features_df.index[0])
    print(f"\n从索引 {first_valid_index_pos} 开始预测 (时间: {format_beijing_time(valid_features_df.index[0])})")
    print(f"将同时保持最多 {kwargs['max_active_predictions']} 笔活跃的投资。")
    print(f"🤖 使用 RL 代理进行智能决策")
    
    # 回测循环
    skipped_predictions = 0
    
    for i in range(first_valid_index_pos, len(df)):
        current_timestamp = df.index[i]
        current_price = df.iloc[i]['close']
        
        # 检查现有预测的状态
        rl_backtester.check_predictions(current_price, current_timestamp, i)
        
        # 检查是否可以进行新预测
        active_count = len([p for p in rl_backtester.active_predictions.values() 
                           if p['status'] == 'active'])
        
        if active_count < kwargs['max_active_predictions']:
            # 直接从预计算的特征DataFrame中获取当前行的特征
            if current_timestamp in features_df.index:
                latest_features_series = features_df.loc[current_timestamp]
                
                # 使用现有模型进行预测
                guess, probability, pred_price = rl_backtester.make_prediction_from_features(latest_features_series)
                
                if guess is not None:
                    # 检查是否在允许的交易时间内
                    from backtest_money_quick import is_good_time_to_trade
                    if is_good_time_to_trade(current_timestamp, time_filter):
                        # 使用 RL 回测器的 add_prediction（内部会调用 RL 代理）
                        rl_backtester.add_prediction(guess, probability, pred_price, current_timestamp, i)
                    else:
                        skipped_predictions += 1
    
    # 结束所有仍在活跃的预测
    final_timestamp, final_price, final_idx = df.index[-1], df.iloc[-1]['close'], len(df) - 1
    for pred_id in list(rl_backtester.active_predictions.keys()):
        rl_backtester.complete_prediction(pred_id, -1, final_price, final_timestamp, final_idx, "数据结束-超时")
    
    if time_filter and skipped_predictions > 0:
        print(f"\n⏰ 因时间过滤跳过了 {skipped_predictions} 个潜在预测信号")
    
    return rl_backtester


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="使用 RL 代理对 SQLite 历史数据进行回测")
    
    # 基础参数
    parser.add_argument("--coin", default="ETH", help="交易对，例如 BTC")
    parser.add_argument("--interval", default="5m", help="K线间隔，例如 5m, 15m")
    parser.add_argument("--market", default="spot", choices=["spot", "futures"], help="市场类型")
    parser.add_argument("--db", default="coin_data.db", help="SQLite数据库路径")
    
    # 时间范围
    parser.add_argument("--start-time", help="回测开始时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--end-time", help="回测结束时间(北京时间, YYYY-MM-DD HH:MM)")
    
    # 回测参数
    parser.add_argument("--initial-capital", type=float, default=10000, help="初始资金")
    parser.add_argument("--risk-per-trade", type=float, default=1.0, help="单次交易风险比例(%%)")
    parser.add_argument("--max-active-predictions", type=int, default=1000, help="最大同时活跃预测数")
    parser.add_argument("--stop-loss", type=float, help="传统止损百分比 (RL模式下会被动态调整)")
    
    # 模型文件
    parser.add_argument("--model-file", help="现有预测模型文件路径 (.joblib)")
    parser.add_argument("--config-file", help="现有模型配置文件路径 (.json)")
    parser.add_argument("--rl-agent", required=True, help="训练好的 RL 代理模型路径")
    
    # 其他选项
    parser.add_argument("--chushou-file", default="chushou.json", help="出手时间配置文件路径")
    parser.add_argument("--use-chushou", action='store_true', help="启用出手时间过滤")
    parser.add_argument("--save-decisions", help="保存 RL 决策历史的文件路径")
    
    args = parser.parse_args()
    
    # 解析时间
    start_time = parse_time_input(args.start_time) if args.start_time else None
    end_time = parse_time_input(args.end_time) if args.end_time else None
    
    # 获取币种配置
    coin_config = get_coin_config(args.coin)
    if coin_config is None:
        print(f"❌ 无法获取 {args.coin} 的配置")
        return
    
    # 确定模型文件路径
    model_file = args.model_file if args.model_file else f"models/{coin_config['model_basename']}_model.joblib"
    config_file = args.config_file if args.config_file else f"models/{coin_config['model_basename']}_config.json"
    
    # 检查文件是否存在
    if not os.path.exists(model_file):
        print(f"❌ 模型文件不存在: {model_file}")
        return
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return
    
    if not os.path.exists(f"{args.rl_agent}.pth"):
        print(f"❌ RL 代理文件不存在: {args.rl_agent}.pth")
        return
    
    print(f"📊 加载数据: {args.coin} {args.interval} {args.market}")
    print(f"🤖 RL 代理: {args.rl_agent}")
    print(f"📈 预测模型: {model_file}")
    
    # 加载数据
    df = load_data_from_sqlite(
        args.db, coin_config['api_symbol'], args.interval, args.market,
        price_multiplier=1.0, start_time=start_time, end_time=end_time
    )
    
    if df is None or df.empty:
        print("❌ 未加载到任何数据，程序退出。")
        return
    
    print(f"✅ 已加载 {len(df)} 条数据")
    print(f"时间范围: {format_beijing_time(df.index[0])} 到 {format_beijing_time(df.index[-1])}")
    
    # 准备回测参数
    backtest_params = {
        'initial_capital': args.initial_capital,
        'risk_per_trade_pct': args.risk_per_trade,
        'price_multiplier': 1.0,
        'stop_loss_pct': args.stop_loss,
        'max_active_predictions': args.max_active_predictions,
        'chushou_file': args.chushou_file if args.use_chushou else None,
        'rl_config': {
            'initial_capital': args.initial_capital,
            'transaction_cost': 0.001,
            'max_position_size': 0.1,
            'max_positions': 5,
            'max_drawdown_limit': 0.2
        }
    }
    
    # 运行 RL 回测
    rl_backtester = run_rl_backtest(
        model_file=model_file,
        config_file=config_file,
        rl_agent_path=args.rl_agent,
        df=df,
        **backtest_params
    )
    
    if rl_backtester is None:
        print("❌ RL 回测失败")
        return
    
    # 打印结果
    print("\n" + "="*60)
    finalize_and_report(rl_backtester, args.initial_capital, args.stop_loss)
    
    # 打印 RL 特有的统计信息
    rl_backtester.print_rl_summary()
    
    # 保存 RL 决策历史
    if args.save_decisions:
        rl_backtester.save_rl_decisions(args.save_decisions)
    else:
        # 默认保存到带时间戳的文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        decision_file = f"rl_decisions_{args.coin}_{args.interval}_{timestamp}.csv"
        rl_backtester.save_rl_decisions(decision_file)
    
    # 分析结果
    if rl_backtester.completed_predictions:
        results_df = pd.DataFrame(rl_backtester.completed_predictions)
        results_df['_sort_time'] = pd.to_datetime(results_df['StartTimestamp'].str.replace(' UTC\+8', '', regex=True))
        results_df = results_df.sort_values('_sort_time').drop('_sort_time', axis=1)
        
        # 保存结果
        output_filename = f"rl_backtest_{args.coin}_{args.interval}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        results_df.to_csv(output_filename, index=False, float_format='%.4f')
        print(f"\n📊 RL 回测结果已保存到: {output_filename}")
        
        # 生成分析图表
        try:
            analyze_and_plot_results(results_df, f"rl_backtest_{args.coin}_{args.interval}")
        except Exception as e:
            print(f"⚠️ 生成分析图表时出错: {e}")
    
    print("\n🎉 RL 回测完成！")
    print("\n📈 RL 回测特点:")
    print("  • 动态仓位管理 - AI 决定每次用多少资金")
    print("  • 智能止盈止损 - 根据市场情况调整")
    print("  • 进场时机优化 - 不是每个信号都跟随")
    print("  • 风险控制 - 连续亏损时减少仓位")


if __name__ == "__main__":
    main()