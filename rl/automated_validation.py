#!/usr/bin/env python3
"""
Automated Model Validation Workflow

This module provides automated validation and testing of RL trading models
to ensure they meet production readiness standards.

Usage:
    python rl/automated_validation.py --model models/eth_5m_rl_model.pth
    python rl/automated_validation.py --config rl/configs/eth_5m_conservative.json --validate_all
"""

import os
import sys
import json
import logging
import argparse
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from rl.rl_trading_agent import RLTradingAgent
from rl.rl_backtester import RLBacktester
from rl.performance_evaluator import PerformanceEvaluator
from rl.benchmark_comparison import BenchmarkComparison
from rl.config_validator import ConfigValidator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ModelValidator:
    """Comprehensive model validation system"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.validation_results = {}
        self.validation_passed = True
        
        # Validation thresholds
        self.thresholds = {
            'min_sharpe_ratio': 1.0,
            'max_drawdown': 0.25,
            'min_win_rate': 0.35,
            'min_profit_factor': 1.2,
            'min_total_return': 0.05,  # 5% minimum return
            'max_volatility': 0.4,
            'min_trades': 50,  # Minimum number of trades for statistical significance
            'max_correlation_with_market': 0.8
        }
        
    def validate_model(self, model_path: str) -> Dict[str, Any]:
        """Run comprehensive model validation"""
        
        logger.info(f"Starting validation for model: {model_path}")
        
        try:
            # Load model
            model = self.load_model(model_path)
            
            # Run validation tests
            self.test_model_loading(model_path)
            self.test_prediction_stability(model)
            self.test_performance_metrics(model)
            self.test_risk_metrics(model)
            self.test_statistical_significance(model)
            self.test_robustness(model)
            self.test_benchmark_comparison(model)
            
            # Generate validation report
            self.generate_validation_report()
            
            return {
                'validation_passed': self.validation_passed,
                'results': self.validation_results,
                'model_path': model_path,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Validation failed: {e}")
            return {
                'validation_passed': False,
                'error': str(e),
                'model_path': model_path,
                'timestamp': datetime.now().isoformat()
            }
    
    def load_model(self, model_path: str):
        """Load and validate model file"""
        
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")
            
        try:
            model = RLTradingAgent.load(model_path)
            logger.info("Model loaded successfully")
            return model
        except Exception as e:
            raise ValueError(f"Failed to load model: {e}")
    
    def test_model_loading(self, model_path: str):
        """Test 1: Model Loading and Basic Functionality"""
        
        logger.info("Running Test 1: Model Loading")
        
        test_results = {
            'test_name': 'Model Loading',
            'passed': True,
            'details': {}
        }
        
        try:
            # Check file exists and is readable
            if not os.path.exists(model_path):
                test_results['passed'] = False
                test_results['details']['file_exists'] = False
            else:
                test_results['details']['file_exists'] = True
                test_results['details']['file_size'] = os.path.getsize(model_path)
            
            # Check model can be loaded
            model = RLTradingAgent.load(model_path)
            test_results['details']['model_loadable'] = True
            
            # Check model has required methods
            required_methods = ['get_action', 'predict', 'evaluate']
            for method in required_methods:
                if hasattr(model, method):
                    test_results['details'][f'has_{method}'] = True
                else:
                    test_results['passed'] = False
                    test_results['details'][f'has_{method}'] = False
            
        except Exception as e:
            test_results['passed'] = False
            test_results['details']['error'] = str(e)
        
        self.validation_results['model_loading'] = test_results
        if not test_results['passed']:
            self.validation_passed = False
    
    def test_prediction_stability(self, model):
        """Test 2: Prediction Stability"""
        
        logger.info("Running Test 2: Prediction Stability")
        
        test_results = {
            'test_name': 'Prediction Stability',
            'passed': True,
            'details': {}
        }
        
        try:
            # Generate sample market states
            sample_states = self.generate_sample_states(100)
            
            # Test prediction consistency
            predictions_1 = []
            predictions_2 = []
            
            for state in sample_states:
                pred_1 = model.predict(state)
                pred_2 = model.predict(state)  # Same state, should give same prediction
                
                predictions_1.append(pred_1)
                predictions_2.append(pred_2)
            
            # Calculate prediction consistency
            consistency_scores = []
            for p1, p2 in zip(predictions_1, predictions_2):
                if isinstance(p1, dict) and isinstance(p2, dict):
                    # Compare action probabilities
                    consistency = 1.0 - abs(p1.get('action_prob', 0) - p2.get('action_prob', 0))
                else:
                    consistency = 1.0 if p1 == p2 else 0.0
                consistency_scores.append(consistency)
            
            avg_consistency = np.mean(consistency_scores)
            test_results['details']['prediction_consistency'] = avg_consistency
            
            # Test should pass if predictions are >95% consistent
            if avg_consistency < 0.95:
                test_results['passed'] = False
                test_results['details']['consistency_threshold'] = 0.95
            
            # Test prediction range
            action_probs = [p.get('action_prob', 0) for p in predictions_1 if isinstance(p, dict)]
            if action_probs:
                test_results['details']['min_action_prob'] = min(action_probs)
                test_results['details']['max_action_prob'] = max(action_probs)
                test_results['details']['mean_action_prob'] = np.mean(action_probs)
            
        except Exception as e:
            test_results['passed'] = False
            test_results['details']['error'] = str(e)
        
        self.validation_results['prediction_stability'] = test_results
        if not test_results['passed']:
            self.validation_passed = False
    
    def test_performance_metrics(self, model):
        """Test 3: Performance Metrics"""
        
        logger.info("Running Test 3: Performance Metrics")
        
        test_results = {
            'test_name': 'Performance Metrics',
            'passed': True,
            'details': {}
        }
        
        try:
            # Run backtest
            backtester = RLBacktester(model, self.config)
            
            # Use validation period for testing
            backtest_results = backtester.run_backtest(
                start_date=self.config['data']['val_start'],
                end_date=self.config['data']['val_end']
            )
            
            # Extract key metrics
            metrics = backtest_results.get('metrics', {})
            
            # Test Sharpe ratio
            sharpe_ratio = metrics.get('sharpe_ratio', 0)
            test_results['details']['sharpe_ratio'] = sharpe_ratio
            if sharpe_ratio < self.thresholds['min_sharpe_ratio']:
                test_results['passed'] = False
                test_results['details']['sharpe_ratio_failed'] = True
            
            # Test maximum drawdown
            max_drawdown = metrics.get('max_drawdown', 1.0)
            test_results['details']['max_drawdown'] = max_drawdown
            if max_drawdown > self.thresholds['max_drawdown']:
                test_results['passed'] = False
                test_results['details']['max_drawdown_failed'] = True
            
            # Test win rate
            win_rate = metrics.get('win_rate', 0)
            test_results['details']['win_rate'] = win_rate
            if win_rate < self.thresholds['min_win_rate']:
                test_results['passed'] = False
                test_results['details']['win_rate_failed'] = True
            
            # Test profit factor
            profit_factor = metrics.get('profit_factor', 0)
            test_results['details']['profit_factor'] = profit_factor
            if profit_factor < self.thresholds['min_profit_factor']:
                test_results['passed'] = False
                test_results['details']['profit_factor_failed'] = True
            
            # Test total return
            total_return = metrics.get('total_return', 0)
            test_results['details']['total_return'] = total_return
            if total_return < self.thresholds['min_total_return']:
                test_results['passed'] = False
                test_results['details']['total_return_failed'] = True
            
        except Exception as e:
            test_results['passed'] = False
            test_results['details']['error'] = str(e)
        
        self.validation_results['performance_metrics'] = test_results
        if not test_results['passed']:
            self.validation_passed = False
    
    def test_risk_metrics(self, model):
        """Test 4: Risk Metrics"""
        
        logger.info("Running Test 4: Risk Metrics")
        
        test_results = {
            'test_name': 'Risk Metrics',
            'passed': True,
            'details': {}
        }
        
        try:
            # Run backtest for risk analysis
            backtester = RLBacktester(model, self.config)
            backtest_results = backtester.run_backtest(
                start_date=self.config['data']['val_start'],
                end_date=self.config['data']['val_end']
            )
            
            # Calculate risk metrics
            returns = backtest_results.get('daily_returns', [])
            if returns:
                returns_series = pd.Series(returns)
                
                # Volatility test
                volatility = returns_series.std() * np.sqrt(252)  # Annualized
                test_results['details']['volatility'] = volatility
                if volatility > self.thresholds['max_volatility']:
                    test_results['passed'] = False
                    test_results['details']['volatility_failed'] = True
                
                # Value at Risk (95%)
                var_95 = np.percentile(returns, 5)
                test_results['details']['var_95'] = var_95
                
                # Expected Shortfall
                expected_shortfall = returns_series[returns_series <= var_95].mean()
                test_results['details']['expected_shortfall'] = expected_shortfall
                
                # Skewness and Kurtosis
                test_results['details']['skewness'] = returns_series.skew()
                test_results['details']['kurtosis'] = returns_series.kurtosis()
                
                # Maximum consecutive losses
                consecutive_losses = self.calculate_max_consecutive_losses(returns)
                test_results['details']['max_consecutive_losses'] = consecutive_losses
                
        except Exception as e:
            test_results['passed'] = False
            test_results['details']['error'] = str(e)
        
        self.validation_results['risk_metrics'] = test_results
        if not test_results['passed']:
            self.validation_passed = False
    
    def test_statistical_significance(self, model):
        """Test 5: Statistical Significance"""
        
        logger.info("Running Test 5: Statistical Significance")
        
        test_results = {
            'test_name': 'Statistical Significance',
            'passed': True,
            'details': {}
        }
        
        try:
            # Run backtest
            backtester = RLBacktester(model, self.config)
            backtest_results = backtester.run_backtest(
                start_date=self.config['data']['val_start'],
                end_date=self.config['data']['val_end']
            )
            
            # Check number of trades
            trades = backtest_results.get('trades', [])
            num_trades = len(trades)
            test_results['details']['num_trades'] = num_trades
            
            if num_trades < self.thresholds['min_trades']:
                test_results['passed'] = False
                test_results['details']['insufficient_trades'] = True
            
            # T-test for returns significance
            if trades:
                trade_returns = [trade.get('return_pct', 0) for trade in trades]
                
                # One-sample t-test against zero
                from scipy import stats
                t_stat, p_value = stats.ttest_1samp(trade_returns, 0)
                
                test_results['details']['t_statistic'] = t_stat
                test_results['details']['p_value'] = p_value
                test_results['details']['significant_at_5pct'] = p_value < 0.05
                
                # Mean return and confidence interval
                mean_return = np.mean(trade_returns)
                std_error = stats.sem(trade_returns)
                confidence_interval = stats.t.interval(0.95, len(trade_returns)-1, 
                                                     loc=mean_return, scale=std_error)
                
                test_results['details']['mean_return'] = mean_return
                test_results['details']['confidence_interval'] = confidence_interval
            
        except Exception as e:
            test_results['passed'] = False
            test_results['details']['error'] = str(e)
        
        self.validation_results['statistical_significance'] = test_results
        if not test_results['passed']:
            self.validation_passed = False
    
    def test_robustness(self, model):
        """Test 6: Model Robustness"""
        
        logger.info("Running Test 6: Model Robustness")
        
        test_results = {
            'test_name': 'Model Robustness',
            'passed': True,
            'details': {}
        }
        
        try:
            # Test with different market conditions
            robustness_results = []
            
            # Test periods with different characteristics
            test_periods = [
                ('2024-01-01', '2024-03-31', 'Q1_2024'),
                ('2024-04-01', '2024-06-30', 'Q2_2024'),
                ('2024-07-01', '2024-09-30', 'Q3_2024')
            ]
            
            backtester = RLBacktester(model, self.config)
            
            for start, end, period_name in test_periods:
                try:
                    period_results = backtester.run_backtest(start_date=start, end_date=end)
                    period_metrics = period_results.get('metrics', {})
                    
                    robustness_results.append({
                        'period': period_name,
                        'sharpe_ratio': period_metrics.get('sharpe_ratio', 0),
                        'max_drawdown': period_metrics.get('max_drawdown', 1),
                        'total_return': period_metrics.get('total_return', 0)
                    })
                    
                except Exception as e:
                    logger.warning(f"Failed to test period {period_name}: {e}")
            
            # Analyze consistency across periods
            if robustness_results:
                sharpe_ratios = [r['sharpe_ratio'] for r in robustness_results]
                sharpe_std = np.std(sharpe_ratios)
                
                test_results['details']['robustness_results'] = robustness_results
                test_results['details']['sharpe_ratio_std'] = sharpe_std
                test_results['details']['consistent_performance'] = sharpe_std < 0.5
                
                # Check if performance is consistently positive
                positive_periods = sum(1 for r in robustness_results if r['total_return'] > 0)
                test_results['details']['positive_periods'] = positive_periods
                test_results['details']['total_periods'] = len(robustness_results)
                
                if positive_periods < len(robustness_results) * 0.6:  # 60% threshold
                    test_results['passed'] = False
                    test_results['details']['insufficient_positive_periods'] = True
            
        except Exception as e:
            test_results['passed'] = False
            test_results['details']['error'] = str(e)
        
        self.validation_results['robustness'] = test_results
        if not test_results['passed']:
            self.validation_passed = False
    
    def test_benchmark_comparison(self, model):
        """Test 7: Benchmark Comparison"""
        
        logger.info("Running Test 7: Benchmark Comparison")
        
        test_results = {
            'test_name': 'Benchmark Comparison',
            'passed': True,
            'details': {}
        }
        
        try:
            # Run RL model backtest
            backtester = RLBacktester(model, self.config)
            rl_results = backtester.run_backtest(
                start_date=self.config['data']['val_start'],
                end_date=self.config['data']['val_end']
            )
            
            # Compare with benchmarks
            benchmark_comparison = BenchmarkComparison(self.config)
            comparison_results = benchmark_comparison.compare_strategies(
                rl_results=rl_results,
                start_date=self.config['data']['val_start'],
                end_date=self.config['data']['val_end']
            )
            
            # Extract comparison metrics
            rl_sharpe = rl_results.get('metrics', {}).get('sharpe_ratio', 0)
            
            benchmark_sharpes = {}
            for strategy, results in comparison_results.items():
                if strategy != 'rl_strategy':
                    benchmark_sharpes[strategy] = results.get('sharpe_ratio', 0)
            
            test_results['details']['rl_sharpe_ratio'] = rl_sharpe
            test_results['details']['benchmark_sharpes'] = benchmark_sharpes
            
            # Check if RL outperforms at least one benchmark
            if benchmark_sharpes:
                best_benchmark_sharpe = max(benchmark_sharpes.values())
                test_results['details']['best_benchmark_sharpe'] = best_benchmark_sharpe
                test_results['details']['outperforms_best_benchmark'] = rl_sharpe > best_benchmark_sharpe
                
                if rl_sharpe <= best_benchmark_sharpe:
                    test_results['passed'] = False
                    test_results['details']['underperforms_benchmarks'] = True
            
        except Exception as e:
            test_results['passed'] = False
            test_results['details']['error'] = str(e)
        
        self.validation_results['benchmark_comparison'] = test_results
        if not test_results['passed']:
            self.validation_passed = False
    
    def generate_sample_states(self, n_samples: int) -> List[Dict]:
        """Generate sample market states for testing"""
        
        np.random.seed(42)  # For reproducibility
        
        sample_states = []
        for _ in range(n_samples):
            state = {
                'price': np.random.uniform(1000, 5000),
                'volume': np.random.uniform(100, 10000),
                'rsi': np.random.uniform(20, 80),
                'macd': np.random.uniform(-50, 50),
                'bb_position': np.random.uniform(0, 1),
                'volatility': np.random.uniform(0.01, 0.1)
            }
            sample_states.append(state)
        
        return sample_states
    
    def calculate_max_consecutive_losses(self, returns: List[float]) -> int:
        """Calculate maximum consecutive losses"""
        
        max_consecutive = 0
        current_consecutive = 0
        
        for ret in returns:
            if ret < 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def generate_validation_report(self):
        """Generate comprehensive validation report"""
        
        report_path = f"validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        with open(report_path, 'w') as f:
            f.write("# Model Validation Report\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**Overall Result:** {'✅ PASSED' if self.validation_passed else '❌ FAILED'}\n\n")
            
            # Summary table
            f.write("## Validation Summary\n\n")
            f.write("| Test | Status | Key Metrics |\n")
            f.write("|------|--------|-------------|\n")
            
            for test_name, results in self.validation_results.items():
                status = "✅ PASS" if results['passed'] else "❌ FAIL"
                key_metrics = self.extract_key_metrics(results)
                f.write(f"| {results['test_name']} | {status} | {key_metrics} |\n")
            
            f.write("\n")
            
            # Detailed results
            f.write("## Detailed Results\n\n")
            for test_name, results in self.validation_results.items():
                f.write(f"### {results['test_name']}\n\n")
                f.write(f"**Status:** {'✅ PASSED' if results['passed'] else '❌ FAILED'}\n\n")
                
                if 'details' in results:
                    f.write("**Details:**\n")
                    for key, value in results['details'].items():
                        f.write(f"- {key}: {value}\n")
                f.write("\n")
            
            # Recommendations
            f.write("## Recommendations\n\n")
            recommendations = self.generate_recommendations()
            for rec in recommendations:
                f.write(f"- {rec}\n")
        
        logger.info(f"Validation report generated: {report_path}")
    
    def extract_key_metrics(self, results: Dict) -> str:
        """Extract key metrics for summary table"""
        
        details = results.get('details', {})
        
        if 'sharpe_ratio' in details:
            return f"Sharpe: {details['sharpe_ratio']:.2f}"
        elif 'prediction_consistency' in details:
            return f"Consistency: {details['prediction_consistency']:.2%}"
        elif 'num_trades' in details:
            return f"Trades: {details['num_trades']}"
        else:
            return "See details"
    
    def generate_recommendations(self) -> List[str]:
        """Generate recommendations based on validation results"""
        
        recommendations = []
        
        if not self.validation_passed:
            recommendations.append("❌ Model failed validation and should not be deployed to production")
        else:
            recommendations.append("✅ Model passed all validation tests and is ready for production")
        
        # Specific recommendations based on test results
        for test_name, results in self.validation_results.items():
            if not results['passed']:
                if test_name == 'performance_metrics':
                    recommendations.append("Consider retraining with different hyperparameters to improve performance metrics")
                elif test_name == 'robustness':
                    recommendations.append("Model shows inconsistent performance across different periods - consider ensemble methods")
                elif test_name == 'statistical_significance':
                    recommendations.append("Insufficient trading activity - consider adjusting entry/exit criteria")
        
        return recommendations

class BatchValidator:
    """Batch validation for multiple models"""
    
    def __init__(self, config_dir: str = "rl/configs"):
        self.config_dir = Path(config_dir)
        
    def validate_all_models(self) -> Dict[str, Any]:
        """Validate all models in the configs directory"""
        
        results = {}
        
        # Find all config files
        config_files = list(self.config_dir.glob("*.json"))
        
        for config_file in config_files:
            logger.info(f"Validating model from config: {config_file}")
            
            try:
                # Load config
                with open(config_file, 'r') as f:
                    config = json.load(f)
                
                # Find corresponding model file
                model_file = config.get('model_file')
                if model_file and os.path.exists(model_file):
                    # Run validation
                    validator = ModelValidator(config)
                    validation_result = validator.validate_model(model_file)
                    results[str(config_file)] = validation_result
                else:
                    results[str(config_file)] = {
                        'validation_passed': False,
                        'error': f"Model file not found: {model_file}"
                    }
                    
            except Exception as e:
                results[str(config_file)] = {
                    'validation_passed': False,
                    'error': str(e)
                }
        
        return results

def main():
    parser = argparse.ArgumentParser(description="Automated Model Validation")
    parser.add_argument("--model", help="Path to model file to validate")
    parser.add_argument("--config", help="Path to config file")
    parser.add_argument("--validate_all", action="store_true", help="Validate all models")
    parser.add_argument("--output_dir", default="validation_results", help="Output directory for results")
    
    args = parser.parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    if args.validate_all:
        # Batch validation
        batch_validator = BatchValidator()
        results = batch_validator.validate_all_models()
        
        # Save batch results
        batch_results_file = output_dir / f"batch_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(batch_results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"Batch validation results saved to: {batch_results_file}")
        
        # Print summary
        passed = sum(1 for r in results.values() if r.get('validation_passed', False))
        total = len(results)
        print(f"\nBatch Validation Summary: {passed}/{total} models passed validation")
        
    else:
        # Single model validation
        if not args.model or not args.config:
            print("Error: --model and --config are required for single model validation")
            return
        
        # Load config
        with open(args.config, 'r') as f:
            config = json.load(f)
        
        # Run validation
        validator = ModelValidator(config)
        result = validator.validate_model(args.model)
        
        # Save results
        results_file = output_dir / f"validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(result, f, indent=2)
        
        logger.info(f"Validation results saved to: {results_file}")
        
        # Print result
        status = "PASSED" if result['validation_passed'] else "FAILED"
        print(f"\nValidation Result: {status}")

if __name__ == "__main__":
    main()