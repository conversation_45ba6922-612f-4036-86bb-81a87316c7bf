# config_validator.py
# 配置验证工具

import json
import os
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import numpy as np

from .training_config import FullTrainingConfig, TrainingConfigManager


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.validation_rules = self._create_validation_rules()
    
    def _create_validation_rules(self) -> Dict[str, Dict]:
        """创建验证规则"""
        return {
            'data': {
                'train_ratio': {'min': 0.5, 'max': 0.9, 'type': float},
                'val_ratio': {'min': 0.05, 'max': 0.3, 'type': float},
                'test_ratio': {'min': 0.05, 'max': 0.3, 'type': float},
                'coin': {'choices': ['ETH', 'BTC', 'DOT', 'UNI', 'SUI', 'ENA', 'LINK'], 'type': str},
                'interval': {'choices': ['1m', '5m', '15m', '30m', '1h', '4h'], 'type': str},
                'market': {'choices': ['spot', 'futures'], 'type': str}
            },
            'model': {
                'state_dim': {'min': 10, 'max': 100, 'type': int},
                'hidden_dim': {'min': 64, 'max': 1024, 'type': int},
                'num_layers': {'min': 1, 'max': 8, 'type': int},
                'dropout_rate': {'min': 0.0, 'max': 0.5, 'type': float}
            },
            'environment': {
                'initial_capital': {'min': 1000.0, 'max': 1000000.0, 'type': float},
                'transaction_cost': {'min': 0.0001, 'max': 0.01, 'type': float},
                'max_position_size': {'min': 0.01, 'max': 0.5, 'type': float},
                'max_positions': {'min': 1, 'max': 20, 'type': int},
                'slippage': {'min': 0.0, 'max': 0.01, 'type': float},
                'min_trade_amount': {'min': 1.0, 'max': 1000.0, 'type': float}
            },
            'reward': {
                'pnl_weight': {'min': 0.1, 'max': 5.0, 'type': float},
                'risk_penalty_weight': {'min': 0.0, 'max': 10.0, 'type': float},
                'time_efficiency_weight': {'min': 0.0, 'max': 1.0, 'type': float},
                'drawdown_penalty_weight': {'min': 0.0, 'max': 10.0, 'type': float},
                'sharpe_bonus_weight': {'min': 0.0, 'max': 2.0, 'type': float},
                'max_drawdown_threshold': {'min': 0.01, 'max': 0.3, 'type': float},
                'volatility_penalty_weight': {'min': 0.0, 'max': 2.0, 'type': float},
                'win_rate_bonus_weight': {'min': 0.0, 'max': 2.0, 'type': float},
                'consecutive_win_bonus': {'min': 0.0, 'max': 0.5, 'type': float},
                'trade_frequency_penalty': {'min': 0.0, 'max': 0.5, 'type': float},
                'adaptive_learning_rate': {'min': 0.001, 'max': 0.1, 'type': float},
                'performance_window': {'min': 10, 'max': 500, 'type': int}
            },
            'rl': {
                'learning_rate': {'min': 1e-6, 'max': 0.01, 'type': float},
                'batch_size': {'min': 8, 'max': 512, 'type': int},
                'gamma': {'min': 0.9, 'max': 0.999, 'type': float},
                'clip_epsilon': {'min': 0.05, 'max': 0.5, 'type': float},
                'value_loss_coef': {'min': 0.1, 'max': 2.0, 'type': float},
                'entropy_coef': {'min': 0.001, 'max': 0.1, 'type': float},
                'gae_lambda': {'min': 0.8, 'max': 0.99, 'type': float},
                'update_epochs': {'min': 1, 'max': 20, 'type': int},
                'max_grad_norm': {'min': 0.1, 'max': 10.0, 'type': float},
                'buffer_size': {'min': 1000, 'max': 100000, 'type': int},
                'exploration_noise': {'min': 0.01, 'max': 1.0, 'type': float},
                'epsilon_start': {'min': 0.1, 'max': 2.0, 'type': float},
                'epsilon_end': {'min': 0.01, 'max': 0.5, 'type': float},
                'epsilon_decay': {'min': 0.9, 'max': 0.9999, 'type': float}
            },
            'training': {
                'episodes': {'min': 10, 'max': 10000, 'type': int},
                'episode_length': {'min': 50, 'max': 5000, 'type': int},
                'update_frequency': {'min': 10, 'max': 1000, 'type': int},
                'eval_frequency': {'min': 5, 'max': 500, 'type': int},
                'save_frequency': {'min': 10, 'max': 1000, 'type': int},
                'early_stopping_patience': {'min': 10, 'max': 1000, 'type': int},
                'early_stopping_threshold': {'min': 1e-6, 'max': 0.1, 'type': float},
                'max_training_time_hours': {'min': 0.1, 'max': 168.0, 'type': float}
            }
        }
    
    def validate_config(self, config: FullTrainingConfig) -> Dict[str, List[str]]:
        """全面验证配置"""
        issues = {
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        # 基础验证
        self._validate_basic_constraints(config, issues)
        
        # 逻辑一致性验证
        self._validate_logical_consistency(config, issues)
        
        # 性能和稳定性验证
        self._validate_performance_stability(config, issues)
        
        # 最佳实践建议
        self._suggest_best_practices(config, issues)
        
        return issues
    
    def _validate_basic_constraints(self, config: FullTrainingConfig, issues: Dict):
        """验证基础约束"""
        # 验证数据配置
        data_dict = config.data.__dict__
        for field, value in data_dict.items():
            if field in self.validation_rules['data']:
                rule = self.validation_rules['data'][field]
                self._check_field_constraint(f"data.{field}", value, rule, issues)
        
        # 验证数据分割比例
        total_ratio = config.data.train_ratio + config.data.val_ratio + config.data.test_ratio
        if abs(total_ratio - 1.0) > 1e-6:
            issues['errors'].append(f"数据分割比例之和必须等于1.0，当前为{total_ratio:.4f}")
        
        # 验证模型配置
        model_dict = config.model.__dict__
        for field, value in model_dict.items():
            if field in self.validation_rules['model'] and value is not None:
                rule = self.validation_rules['model'][field]
                self._check_field_constraint(f"model.{field}", value, rule, issues)
        
        # 验证环境配置
        env_dict = config.environment.__dict__
        for field, value in env_dict.items():
            if field in self.validation_rules['environment']:
                rule = self.validation_rules['environment'][field]
                self._check_field_constraint(f"environment.{field}", value, rule, issues)
        
        # 验证奖励配置
        reward_dict = config.reward.__dict__
        for field, value in reward_dict.items():
            if field in self.validation_rules['reward'] and not isinstance(value, dict):
                rule = self.validation_rules['reward'][field]
                self._check_field_constraint(f"reward.{field}", value, rule, issues)
        
        # 验证RL配置
        rl_dict = config.rl.__dict__
        for field, value in rl_dict.items():
            if field in self.validation_rules['rl']:
                rule = self.validation_rules['rl'][field]
                self._check_field_constraint(f"rl.{field}", value, rule, issues)
        
        # 验证训练配置
        training_dict = config.training.__dict__
        for field, value in training_dict.items():
            if field in self.validation_rules['training'] and value is not None:
                rule = self.validation_rules['training'][field]
                self._check_field_constraint(f"training.{field}", value, rule, issues)
    
    def _check_field_constraint(self, field_name: str, value: Any, rule: Dict, issues: Dict):
        """检查字段约束"""
        # 类型检查
        if 'type' in rule and not isinstance(value, rule['type']):
            issues['errors'].append(f"{field_name}: 类型错误，期望{rule['type'].__name__}，实际{type(value).__name__}")
            return
        
        # 范围检查
        if 'min' in rule and value < rule['min']:
            issues['errors'].append(f"{field_name}: 值{value}小于最小值{rule['min']}")
        
        if 'max' in rule and value > rule['max']:
            issues['errors'].append(f"{field_name}: 值{value}大于最大值{rule['max']}")
        
        # 选择检查
        if 'choices' in rule and value not in rule['choices']:
            issues['errors'].append(f"{field_name}: 值'{value}'不在允许的选择中{rule['choices']}")
    
    def _validate_logical_consistency(self, config: FullTrainingConfig, issues: Dict):
        """验证逻辑一致性"""
        # 检查epsilon衰减
        if config.rl.epsilon_start <= config.rl.epsilon_end:
            issues['errors'].append("epsilon_start必须大于epsilon_end")
        
        # 检查更新频率
        if config.training.update_frequency >= config.training.episode_length:
            issues['warnings'].append("更新频率不应大于等于episode长度")
        
        # 检查评估频率
        if config.training.eval_frequency > config.training.episodes / 5:
            issues['warnings'].append("评估频率过高，可能影响训练效率")
        
        # 检查批次大小与缓冲区大小
        if config.rl.batch_size > config.rl.buffer_size / 10:
            issues['warnings'].append("批次大小相对于缓冲区大小过大")
        
        # 检查最大仓位与最大持仓数
        max_total_position = config.environment.max_position_size * config.environment.max_positions
        if max_total_position > 1.0:
            issues['errors'].append(f"最大总仓位{max_total_position:.2%}超过100%")
        
        # 检查交易成本与滑点
        total_cost = config.environment.transaction_cost + config.environment.slippage
        if total_cost > 0.01:
            issues['warnings'].append(f"总交易成本{total_cost:.4f}过高，可能影响盈利能力")
        
        # 检查奖励权重平衡
        total_penalty_weight = (config.reward.risk_penalty_weight + 
                               config.reward.drawdown_penalty_weight + 
                               config.reward.volatility_penalty_weight)
        if total_penalty_weight > config.reward.pnl_weight * 3:
            issues['warnings'].append("惩罚权重过高，可能导致过于保守的策略")
    
    def _validate_performance_stability(self, config: FullTrainingConfig, issues: Dict):
        """验证性能和稳定性"""
        # 检查学习率稳定性
        if config.rl.learning_rate > 0.001:
            issues['warnings'].append("学习率较高，可能导致训练不稳定")
        
        # 检查网络复杂度
        total_params = config.model.state_dim * config.model.hidden_dim
        for _ in range(config.model.num_layers - 1):
            total_params += config.model.hidden_dim * config.model.hidden_dim
        
        if total_params > 1000000:
            issues['warnings'].append(f"网络参数量{total_params}过大，可能导致过拟合")
        
        # 检查训练时长合理性
        estimated_time = (config.training.episodes * config.training.episode_length * 
                         config.rl.update_epochs / 10000)  # 粗略估计
        if estimated_time > config.training.max_training_time_hours:
            issues['warnings'].append(f"估计训练时间{estimated_time:.1f}小时超过最大限制")
        
        # 检查内存使用
        estimated_memory = (config.rl.buffer_size * config.model.state_dim * 4 / 1024 / 1024)  # MB
        if estimated_memory > 1000:
            issues['warnings'].append(f"估计内存使用{estimated_memory:.0f}MB较高")
    
    def _suggest_best_practices(self, config: FullTrainingConfig, issues: Dict):
        """提供最佳实践建议"""
        # 根据交易频率建议
        if config.data.interval == "1m":
            if config.reward.time_efficiency_weight < 0.2:
                issues['suggestions'].append("1分钟数据建议增加时间效率权重")
            if config.training.episode_length > 500:
                issues['suggestions'].append("高频交易建议使用较短的episode长度")
        
        elif config.data.interval in ["15m", "30m", "1h"]:
            if config.reward.time_efficiency_weight > 0.1:
                issues['suggestions'].append("长周期交易建议降低时间效率权重")
            if config.environment.max_positions < 3:
                issues['suggestions'].append("波段交易建议增加最大持仓数")
        
        # 根据风险偏好建议
        risk_score = self._calculate_risk_score(config)
        if risk_score > 7:
            issues['suggestions'].append("当前配置风险较高，建议降低最大仓位或增加风险惩罚")
        elif risk_score < 3:
            issues['suggestions'].append("当前配置过于保守，可以适当增加仓位或降低风险惩罚")
        
        # 训练效率建议
        if config.rl.batch_size < 32:
            issues['suggestions'].append("建议增加批次大小以提高训练稳定性")
        
        if config.training.update_frequency < 50:
            issues['suggestions'].append("更新频率过高可能影响样本效率")
        
        # 模型复杂度建议
        if config.model.hidden_dim < 128:
            issues['suggestions'].append("隐藏层维度较小，可能限制模型表达能力")
        elif config.model.hidden_dim > 512:
            issues['suggestions'].append("隐藏层维度较大，注意防止过拟合")
    
    def _calculate_risk_score(self, config: FullTrainingConfig) -> int:
        """计算风险评分 (0-10)"""
        score = 0
        
        # 仓位大小影响 (0-3分)
        if config.environment.max_position_size > 0.15:
            score += 3
        elif config.environment.max_position_size > 0.1:
            score += 2
        elif config.environment.max_position_size > 0.05:
            score += 1
        
        # 回撤容忍度影响 (0-3分)
        if config.reward.max_drawdown_threshold > 0.08:
            score += 3
        elif config.reward.max_drawdown_threshold > 0.05:
            score += 2
        elif config.reward.max_drawdown_threshold > 0.03:
            score += 1
        
        # 风险惩罚权重影响 (0-2分)
        if config.reward.risk_penalty_weight < 0.3:
            score += 2
        elif config.reward.risk_penalty_weight < 0.5:
            score += 1
        
        # 持仓数量影响 (0-2分)
        if config.environment.max_positions > 8:
            score += 2
        elif config.environment.max_positions > 5:
            score += 1
        
        return min(score, 10)
    
    def generate_validation_report(self, config: FullTrainingConfig) -> str:
        """生成验证报告"""
        issues = self.validate_config(config)
        
        report = f"配置验证报告 - {config.config_name}\n"
        report += "=" * 50 + "\n\n"
        
        # 基本信息
        report += f"配置名称: {config.config_name}\n"
        report += f"描述: {config.description}\n"
        report += f"版本: {config.version}\n"
        report += f"风险评分: {self._calculate_risk_score(config)}/10\n\n"
        
        # 错误
        if issues['errors']:
            report += "❌ 错误 (必须修复):\n"
            for i, error in enumerate(issues['errors'], 1):
                report += f"  {i}. {error}\n"
            report += "\n"
        
        # 警告
        if issues['warnings']:
            report += "⚠️  警告 (建议修复):\n"
            for i, warning in enumerate(issues['warnings'], 1):
                report += f"  {i}. {warning}\n"
            report += "\n"
        
        # 建议
        if issues['suggestions']:
            report += "💡 建议 (可选优化):\n"
            for i, suggestion in enumerate(issues['suggestions'], 1):
                report += f"  {i}. {suggestion}\n"
            report += "\n"
        
        # 总结
        if not issues['errors']:
            report += "✅ 配置验证通过，可以开始训练\n"
        else:
            report += "❌ 配置验证失败，请修复错误后重试\n"
        
        return report


def validate_config_file(config_path: str) -> bool:
    """验证配置文件"""
    try:
        manager = TrainingConfigManager()
        config = manager._load_config_from_file(Path(config_path))
        
        if config is None:
            print(f"无法加载配置文件: {config_path}")
            return False
        
        validator = ConfigValidator()
        report = validator.generate_validation_report(config)
        print(report)
        
        issues = validator.validate_config(config)
        return len(issues['errors']) == 0
        
    except Exception as e:
        print(f"验证配置文件时出错: {e}")
        return False


if __name__ == "__main__":
    # 测试配置验证器
    print("测试配置验证器")
    
    # 创建配置管理器和验证器
    manager = TrainingConfigManager()
    validator = ConfigValidator()
    
    # 测试默认配置
    default_config = manager.load_config("default")
    if default_config:
        report = validator.generate_validation_report(default_config)
        print(report)
    
    print("配置验证器测试完成！")