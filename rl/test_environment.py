# test_environment.py
# 测试交易环境的基本功能

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from trading_environment import TradingEnvironment, MarketState, TradingAction

def test_basic_functionality():
    """测试环境基本功能"""
    print("=== 测试交易环境基本功能 ===")
    
    # 创建环境
    config = {
        'initial_capital': 10000,
        'transaction_cost': 0.001,
        'max_position_size': 0.05,
        'max_positions': 3,
        'reward_config': {
            'pnl_weight': 1.0,
            'risk_penalty': 0.5,
            'time_efficiency': 0.1,
            'max_drawdown_penalty': 2.0
        }
    }
    
    env = TradingEnvironment(config)
    
    # 测试1: 状态向量转换
    print("\n1. 测试状态向量转换")
    market_state = MarketState(
        model_signal=1,
        signal_confidence=0.75,
        signal_probability=0.82,
        current_price=2500.0,
        price_change_1h=0.02,
        price_change_4h=-0.01,
        volatility_recent=0.03,
        cash_ratio=0.9,
        position_count=0,
        unrealized_pnl=0.0,
        recent_win_rate=0.6,
        consecutive_losses=0,
        hour=14,
        day_of_week=2,
        is_good_trading_time=True
    )
    
    state_vector = env.get_state_vector(market_state)
    print(f"状态向量维度: {len(state_vector)}")
    print(f"预期维度: {env.get_state_dim()}")
    assert len(state_vector) == env.get_state_dim(), "状态向量维度不匹配"
    print("✅ 状态向量转换正常")
    
    # 测试2: 交易执行
    print("\n2. 测试交易执行")
    action = TradingAction(
        enter_trade=True,
        position_size=0.03,
        stop_loss_pct=0.025,
        take_profit_pct=0.02,
        max_hold_time=120
    )
    
    result = env.execute_action(action, market_state, pd.Timestamp.now(), 0)
    print(f"交易执行结果: {result}")
    assert result['trade_executed'], "交易应该被执行"
    assert len(env.active_positions) == 1, "应该有一个活跃仓位"
    print("✅ 交易执行正常")
    
    # 测试3: 仓位更新 - 止盈
    print("\n3. 测试止盈")
    # 模拟价格上涨触发止盈
    new_price = 2500.0 * 1.025  # 上涨2.5%，触发2%止盈
    completed_trades = env.update_positions(new_price, pd.Timestamp.now(), 10)
    
    print(f"完成的交易数: {len(completed_trades)}")
    if completed_trades:
        trade = completed_trades[0]
        print(f"退出原因: {trade['exit_reason']}")
        print(f"盈亏: ${trade['pnl']:.2f}")
        assert trade['exit_reason'] == 'take_profit', "应该是止盈退出"
        assert trade['pnl'] > 0, "止盈应该是盈利的"
        print("✅ 止盈逻辑正常")
    
    # 测试4: 奖励计算
    print("\n4. 测试奖励计算")
    if completed_trades:
        reward = env.calculate_reward(completed_trades, market_state)
        print(f"奖励值: {reward:.4f}")
        assert isinstance(reward, float), "奖励应该是浮点数"
        print("✅ 奖励计算正常")
    
    # 测试5: 投资组合指标
    print("\n5. 测试投资组合指标")
    metrics = env.get_portfolio_metrics()
    print(f"投资组合指标: {metrics}")
    assert 'recent_win_rate' in metrics, "应该包含胜率指标"
    assert 'total_trades' in metrics, "应该包含交易总数"
    print("✅ 投资组合指标正常")

def test_risk_controls():
    """测试风险控制机制"""
    print("\n=== 测试风险控制机制 ===")
    
    config = {
        'initial_capital': 1000,
        'max_position_size': 0.1,
        'max_positions': 2
    }
    
    env = TradingEnvironment(config)
    
    market_state = MarketState(
        model_signal=1, signal_confidence=0.8, signal_probability=0.85,
        current_price=100.0, price_change_1h=0.01, price_change_4h=0.02,
        volatility_recent=0.02, cash_ratio=0.5, position_count=0,
        unrealized_pnl=0.0, recent_win_rate=0.6, consecutive_losses=0,
        hour=10, day_of_week=1, is_good_trading_time=True
    )
    
    # 测试1: 仓位大小限制
    print("\n1. 测试仓位大小限制")
    action = TradingAction(
        enter_trade=True,
        position_size=0.2,  # 超过最大限制
        stop_loss_pct=0.02,
        take_profit_pct=0.02,
        max_hold_time=120
    )
    
    validated_action = env.validate_action(action, market_state)
    print(f"原始仓位: {action.position_size}, 验证后: {validated_action.position_size}")
    assert validated_action.position_size <= env.max_position_size, "仓位应该被限制"
    print("✅ 仓位大小限制正常")
    
    # 测试2: 最大持仓数限制
    print("\n2. 测试最大持仓数限制")
    # 先开两个仓位
    for i in range(2):
        action = TradingAction(True, 0.05, 0.02, 0.02, 120)
        env.execute_action(action, market_state, pd.Timestamp.now(), i)
    
    # 尝试开第三个仓位
    action = TradingAction(True, 0.05, 0.02, 0.02, 120)
    validated_action = env.validate_action(action, market_state)
    print(f"当前持仓数: {len(env.active_positions)}, 是否允许进场: {validated_action.enter_trade}")
    assert not validated_action.enter_trade, "超过最大持仓数应该被拒绝"
    print("✅ 最大持仓数限制正常")

def test_signal_scenarios():
    """测试不同信号场景"""
    print("\n=== 测试不同信号场景 ===")
    
    env = TradingEnvironment({'initial_capital': 10000})
    
    # 测试看涨信号
    print("\n1. 测试看涨信号")
    bull_state = MarketState(
        model_signal=1, signal_confidence=0.8, signal_probability=0.85,
        current_price=100.0, price_change_1h=0.01, price_change_4h=0.02,
        volatility_recent=0.02, cash_ratio=0.9, position_count=0,
        unrealized_pnl=0.0, recent_win_rate=0.6, consecutive_losses=0,
        hour=10, day_of_week=1, is_good_trading_time=True
    )
    
    action = TradingAction(True, 0.05, 0.02, 0.02, 120)
    result = env.execute_action(action, bull_state, pd.Timestamp.now(), 0)
    
    if result['trade_executed']:
        pos = list(env.active_positions.values())[0]
        print(f"看涨仓位 - 止盈价: {pos['take_profit_price']:.2f}, 止损价: {pos['stop_loss_price']:.2f}")
        assert pos['take_profit_price'] > pos['entry_price'], "看涨止盈价应该更高"
        assert pos['stop_loss_price'] < pos['entry_price'], "看涨止损价应该更低"
        print("✅ 看涨信号处理正常")
    
    env.reset()
    
    # 测试看跌信号
    print("\n2. 测试看跌信号")
    bear_state = MarketState(
        model_signal=0, signal_confidence=0.75, signal_probability=0.2,
        current_price=100.0, price_change_1h=-0.01, price_change_4h=-0.02,
        volatility_recent=0.03, cash_ratio=0.9, position_count=0,
        unrealized_pnl=0.0, recent_win_rate=0.5, consecutive_losses=1,
        hour=15, day_of_week=3, is_good_trading_time=True
    )
    
    result = env.execute_action(action, bear_state, pd.Timestamp.now(), 0)
    
    if result['trade_executed']:
        pos = list(env.active_positions.values())[0]
        print(f"看跌仓位 - 止盈价: {pos['take_profit_price']:.2f}, 止损价: {pos['stop_loss_price']:.2f}")
        assert pos['take_profit_price'] < pos['entry_price'], "看跌止盈价应该更低"
        assert pos['stop_loss_price'] > pos['entry_price'], "看跌止损价应该更高"
        print("✅ 看跌信号处理正常")

if __name__ == "__main__":
    try:
        test_basic_functionality()
        test_risk_controls()
        test_signal_scenarios()
        print("\n🎉 所有测试通过！交易环境工作正常。")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()