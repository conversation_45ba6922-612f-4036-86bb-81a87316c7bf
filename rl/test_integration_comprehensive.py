"""
Comprehensive integration tests for the RL trading system.
Tests the complete workflow from data loading to model training and evaluation.
"""

import unittest
import tempfile
import shutil
import os
import json
import sqlite3
import pandas as pd
import numpy as np
import joblib
from pathlib import Path
from unittest.mock import patch, MagicMock

from .training_manager import TrainingManager, TrainingConfig
from .trading_environment import TradingEnvironment
from .rl_trading_agent import RLTradingAgent
from .policy_network import PolicyNetwork
from .performance_optimizer import PerformanceProfiler


class TestCompleteWorkflow(unittest.TestCase):
    """Test complete RL trading workflow"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.test_dir, "test_data.db")
        self.model_path = os.path.join(self.test_dir, "test_model.joblib")
        self.config_path = os.path.join(self.test_dir, "test_config.json")
        
        # Create test database
        self._create_test_database()
        
        # Create test model and config
        self._create_test_model()
        
        # Create training configuration
        self.training_config = self._create_training_config()
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def _create_test_database(self):
        """Create test SQLite database with sample data"""
        conn = sqlite3.connect(self.db_path)
        
        # Create sample data
        dates = pd.date_range('2023-01-01', periods=5000, freq='5T')
        np.random.seed(42)
        
        # Generate realistic price data
        price = 2000.0
        prices = [price]
        
        for _ in range(len(dates) - 1):
            change = np.random.normal(0, 0.002)  # 0.2% volatility
            price *= (1 + change)
            prices.append(price)
        
        df = pd.DataFrame({
            'timestamp': [int(d.timestamp()) for d in dates],
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.001))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.001))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 10000, len(dates))
        })
        
        # Create table
        df.to_sql('ETH_5m_spot', conn, if_exists='replace', index=False)
        conn.close()
    
    def _create_test_model(self):
        """Create test model and configuration"""
        # Create dummy model (simple classifier)
        from sklearn.ensemble import RandomForestClassifier
        
        model = RandomForestClassifier(n_estimators=10, random_state=42)
        
        # Create dummy training data
        X = np.random.randn(1000, 20)
        y = np.random.randint(0, 2, 1000)
        model.fit(X, y)
        
        # Save model
        joblib.dump(model, self.model_path)
        
        # Create config
        config = {
            'timeframe_minutes': 5,
            'best_threshold': 0.6,
            'feature_list': [f'feature_{i}' for i in range(20)]
        }
        
        with open(self.config_path, 'w') as f:
            json.dump(config, f)
    
    def _create_training_config(self):
        """Create training configuration"""
        class MockConfig:
            def __init__(self):
                # Data config
                self.data = type('obj', (object,), {
                    'db_path': self.db_path,
                    'coin': 'ETH',
                    'interval': '5m',
                    'market': 'spot',
                    'train_ratio': 0.7,
                    'val_ratio': 0.15,
                    'test_ratio': 0.15
                })()
                
                # Model config
                self.model = type('obj', (object,), {
                    'model_file': self.model_path,
                    'config_file': self.config_path,
                    'hidden_dim': 128
                })()
                
                # Training config
                self.training = type('obj', (object,), {
                    'episodes': 10,
                    'episode_length': 100,
                    'update_frequency': 10
                })()
                
                # Environment config
                self.environment = type('obj', (object,), {
                    'initial_capital': 10000.0,
                    'transaction_cost': 0.001,
                    'max_position_size': 0.1,
                    'max_positions': 5
                })()
                
                # RL config
                self.rl = type('obj', (object,), {
                    'learning_rate': 3e-4,
                    'batch_size': 32,
                    'gamma': 0.99,
                    'clip_epsilon': 0.2,
                    'value_loss_coef': 0.5,
                    'entropy_coef': 0.01,
                    'gae_lambda': 0.95,
                    'update_epochs': 4,
                    'max_grad_norm': 0.5,
                    'buffer_size': 10000,
                    'exploration_noise': 0.1,
                    'epsilon_start': 1.0,
                    'epsilon_end': 0.1,
                    'epsilon_decay': 0.995
                })()
                
                # Reward config
                self.reward = type('obj', (object,), {
                    'pnl_weight': 1.0,
                    'risk_penalty_weight': 0.5,
                    'time_efficiency_weight': 0.1,
                    'drawdown_penalty_weight': 0.3
                })()
                
                # Logging config
                self.logging = type('obj', (object,), {
                    'log_dir': os.path.join(self.test_dir, 'logs')
                })()
                
                # Performance config
                self.use_multiprocessing = False
                self.num_workers = 2
        
        return MockConfig()
    
    def test_training_manager_initialization(self):
        """Test TrainingManager initialization"""
        # Mock the calculate_features function to avoid import issues
        with patch('rl.training_manager.calculate_features') as mock_calc_features:
            mock_calc_features.return_value = pd.DataFrame({
                'close': np.random.randn(100),
                **{f'feature_{i}': np.random.randn(100) for i in range(20)}
            })
            
            manager = TrainingManager(self.training_config)
            
            # Check initialization
            self.assertIsNotNone(manager.train_data)
            self.assertIsNotNone(manager.val_data)
            self.assertIsNotNone(manager.test_data)
            self.assertIsNotNone(manager.env)
            self.assertIsNotNone(manager.agent)
    
    def test_data_loading_and_splitting(self):
        """Test data loading and time-series splitting"""
        with patch('rl.training_manager.calculate_features') as mock_calc_features:
            mock_calc_features.return_value = pd.DataFrame({
                'close': np.random.randn(100),
                **{f'feature_{i}': np.random.randn(100) for i in range(20)}
            })
            
            manager = TrainingManager(self.training_config)
            
            # Check data splits
            total_len = len(manager.train_data) + len(manager.val_data) + len(manager.test_data)
            self.assertGreater(total_len, 0)
            
            # Check time ordering (train < val < test)
            if len(manager.train_data) > 0 and len(manager.val_data) > 0:
                self.assertLess(manager.train_data.index[-1], manager.val_data.index[0])
            
            if len(manager.val_data) > 0 and len(manager.test_data) > 0:
                self.assertLess(manager.val_data.index[-1], manager.test_data.index[0])
    
    def test_signal_precomputation(self):
        """Test signal precomputation from existing model"""
        with patch('rl.training_manager.calculate_features') as mock_calc_features:
            # Create mock features that match the model's expected input
            mock_features = pd.DataFrame({
                'close': np.random.randn(100),
                **{f'feature_{i}': np.random.randn(100) for i in range(20)}
            })
            mock_calc_features.return_value = mock_features
            
            manager = TrainingManager(self.training_config)
            
            # Check that signals were computed
            self.assertIsNotNone(manager.train_signals)
            self.assertIsNotNone(manager.val_signals)
            self.assertIsNotNone(manager.test_signals)
            
            # Check signal structure
            if not manager.train_signals.empty:
                self.assertIn('signal', manager.train_signals.columns)
                self.assertIn('probability', manager.train_signals.columns)
                self.assertIn('confidence', manager.train_signals.columns)
    
    def test_episode_execution(self):
        """Test episode execution"""
        with patch('rl.training_manager.calculate_features') as mock_calc_features:
            mock_calc_features.return_value = pd.DataFrame({
                'close': np.random.randn(100),
                **{f'feature_{i}': np.random.randn(100) for i in range(20)}
            })
            
            manager = TrainingManager(self.training_config)
            
            # Run a single episode
            if not manager.train_data.empty and manager.train_signals is not None:
                result = manager.run_episode(
                    manager.train_data, 
                    manager.train_signals, 
                    training=True
                )
                
                # Check result structure
                self.assertIsNotNone(result.episode_id)
                self.assertIsInstance(result.total_reward, (int, float))
                self.assertIsInstance(result.total_return, (int, float))
                self.assertIsInstance(result.num_trades, int)
                self.assertGreaterEqual(result.win_rate, 0.0)
                self.assertLessEqual(result.win_rate, 1.0)
    
    def test_training_loop(self):
        """Test basic training loop"""
        with patch('rl.training_manager.calculate_features') as mock_calc_features:
            mock_calc_features.return_value = pd.DataFrame({
                'close': np.random.randn(100),
                **{f'feature_{i}': np.random.randn(100) for i in range(20)}
            })
            
            manager = TrainingManager(self.training_config)
            
            # Run short training
            initial_episode = manager.current_episode
            
            # Mock the train method to avoid full training
            with patch.object(manager, 'run_episode') as mock_run_episode:
                from .training_manager import EpisodeResult
                
                mock_run_episode.return_value = EpisodeResult(
                    episode_id=1,
                    total_reward=0.1,
                    total_return=0.05,
                    num_trades=5,
                    win_rate=0.6,
                    max_drawdown=0.02,
                    sharpe_ratio=1.2,
                    episode_length=100,
                    training_time=1.0
                )
                
                # This would normally call manager.train(), but we'll simulate it
                for i in range(3):
                    result = manager.run_episode(
                        manager.train_data, 
                        manager.train_signals, 
                        training=True
                    )
                    manager.logger.log_episode(result)
                    manager.current_episode += 1
                
                # Check that episodes were logged
                self.assertEqual(len(manager.logger.episode_results), 3)
    
    def test_performance_monitoring(self):
        """Test performance monitoring integration"""
        with patch('rl.training_manager.calculate_features') as mock_calc_features:
            mock_calc_features.return_value = pd.DataFrame({
                'close': np.random.randn(100),
                **{f'feature_{i}': np.random.randn(100) for i in range(20)}
            })
            
            manager = TrainingManager(self.training_config)
            
            # Test performance monitoring
            metrics = manager.monitor_training_performance()
            self.assertIsInstance(metrics, dict)
            
            # Test performance optimization
            manager.optimize_training_performance()
            
            # Should not raise exceptions
            self.assertTrue(True)
    
    def test_model_saving_and_loading(self):
        """Test model checkpoint saving and loading"""
        with patch('rl.training_manager.calculate_features') as mock_calc_features:
            mock_calc_features.return_value = pd.DataFrame({
                'close': np.random.randn(100),
                **{f'feature_{i}': np.random.randn(100) for i in range(20)}
            })
            
            manager = TrainingManager(self.training_config)
            
            # Save checkpoint
            checkpoint_path = manager.save_checkpoint(1, 0.1)
            
            # Check that checkpoint was created
            if checkpoint_path:
                self.assertTrue(os.path.exists(checkpoint_path))
    
    def test_evaluation_workflow(self):
        """Test model evaluation workflow"""
        with patch('rl.training_manager.calculate_features') as mock_calc_features:
            mock_calc_features.return_value = pd.DataFrame({
                'close': np.random.randn(100),
                **{f'feature_{i}': np.random.randn(100) for i in range(20)}
            })
            
            manager = TrainingManager(self.training_config)
            
            # Mock evaluation
            with patch.object(manager, 'run_episode') as mock_run_episode:
                from .training_manager import EpisodeResult
                
                mock_run_episode.return_value = EpisodeResult(
                    episode_id=1,
                    total_reward=0.1,
                    total_return=0.05,
                    num_trades=5,
                    win_rate=0.6,
                    max_drawdown=0.02,
                    sharpe_ratio=1.2,
                    episode_length=100,
                    training_time=1.0
                )
                
                # Run evaluation
                eval_metrics = manager.evaluate_agent(
                    manager.val_data, 
                    manager.val_signals, 
                    num_episodes=3
                )
                
                # Check evaluation metrics
                self.assertIn('avg_reward', eval_metrics)
                self.assertIn('avg_return', eval_metrics)
                self.assertIn('avg_win_rate', eval_metrics)
                self.assertIn('avg_sharpe_ratio', eval_metrics)


class TestMarketConditionStability(unittest.TestCase):
    """Test model stability under different market conditions"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def _create_market_scenario_data(self, scenario: str, length: int = 1000):
        """Create data for different market scenarios"""
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=length, freq='5T')
        
        if scenario == 'trending_up':
            # Strong upward trend
            trend = np.linspace(0, 0.5, length)  # 50% growth over period
            noise = np.random.normal(0, 0.01, length)  # 1% volatility
            returns = trend + noise
            
        elif scenario == 'trending_down':
            # Strong downward trend
            trend = np.linspace(0, -0.3, length)  # 30% decline over period
            noise = np.random.normal(0, 0.01, length)
            returns = trend + noise
            
        elif scenario == 'sideways':
            # Sideways market with mean reversion
            returns = np.random.normal(0, 0.005, length)  # Low volatility
            
        elif scenario == 'high_volatility':
            # High volatility market
            returns = np.random.normal(0, 0.03, length)  # 3% volatility
            
        elif scenario == 'crash':
            # Market crash scenario
            normal_returns = np.random.normal(0, 0.01, length - 100)
            crash_returns = np.random.normal(-0.05, 0.02, 100)  # Severe decline
            returns = np.concatenate([normal_returns, crash_returns])
            
        else:
            # Default random walk
            returns = np.random.normal(0, 0.01, length)
        
        # Convert returns to prices
        price = 2000.0
        prices = [price]
        
        for ret in returns:
            price *= (1 + ret)
            prices.append(price)
        
        prices = prices[:-1]  # Remove extra element
        
        # Create OHLCV data
        df = pd.DataFrame({
            'timestamp': [int(d.timestamp()) for d in dates],
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.002))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.002))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 10000, len(dates))
        })
        
        return df
    
    def test_trending_market_stability(self):
        """Test model stability in trending markets"""
        # Create trending up data
        up_data = self._create_market_scenario_data('trending_up')
        
        # Create trending down data
        down_data = self._create_market_scenario_data('trending_down')
        
        # Test that data was created correctly
        up_returns = up_data['close'].pct_change().dropna()
        down_returns = down_data['close'].pct_change().dropna()
        
        # Up trend should have positive mean return
        self.assertGreater(up_returns.mean(), 0)
        
        # Down trend should have negative mean return
        self.assertLess(down_returns.mean(), 0)
    
    def test_high_volatility_stability(self):
        """Test model stability in high volatility conditions"""
        # Create high volatility data
        volatile_data = self._create_market_scenario_data('high_volatility')
        
        # Calculate volatility
        returns = volatile_data['close'].pct_change().dropna()
        volatility = returns.std()
        
        # Should have higher volatility than normal
        self.assertGreater(volatility, 0.02)  # Should be > 2%
    
    def test_crash_scenario_stability(self):
        """Test model stability during market crashes"""
        # Create crash scenario data
        crash_data = self._create_market_scenario_data('crash')
        
        # Check that there's a significant decline in the last part
        first_half = crash_data['close'].iloc[:500]
        second_half = crash_data['close'].iloc[500:]
        
        decline = (second_half.iloc[-1] - second_half.iloc[0]) / second_half.iloc[0]
        self.assertLess(decline, -0.1)  # Should decline by more than 10%
    
    def test_sideways_market_stability(self):
        """Test model stability in sideways markets"""
        # Create sideways market data
        sideways_data = self._create_market_scenario_data('sideways')
        
        # Calculate total return
        total_return = (sideways_data['close'].iloc[-1] - sideways_data['close'].iloc[0]) / sideways_data['close'].iloc[0]
        
        # Should have low total return (close to zero)
        self.assertLess(abs(total_return), 0.1)  # Less than 10% total movement


class TestStressTests(unittest.TestCase):
    """Stress tests for the RL trading system"""
    
    def test_large_dataset_handling(self):
        """Test handling of large datasets"""
        # Create large dataset
        large_data = pd.DataFrame({
            'timestamp': range(100000),
            'open': np.random.randn(100000) + 2000,
            'high': np.random.randn(100000) + 2010,
            'low': np.random.randn(100000) + 1990,
            'close': np.random.randn(100000) + 2000,
            'volume': np.random.uniform(1000, 10000, 100000)
        })
        
        # Test that we can handle large datasets without memory issues
        self.assertEqual(len(large_data), 100000)
        
        # Test memory usage is reasonable
        memory_usage = large_data.memory_usage(deep=True).sum() / 1024 / 1024  # MB
        self.assertLess(memory_usage, 500)  # Should be less than 500MB
    
    def test_extreme_market_conditions(self):
        """Test handling of extreme market conditions"""
        # Test with extreme price movements
        extreme_data = pd.DataFrame({
            'timestamp': range(1000),
            'open': [2000] * 1000,
            'high': [10000] * 1000,  # 5x price increase
            'low': [100] * 1000,     # 95% price decrease
            'close': [2000] * 1000,
            'volume': [1000] * 1000
        })
        
        # Should handle extreme data without crashing
        self.assertEqual(len(extreme_data), 1000)
    
    def test_missing_data_handling(self):
        """Test handling of missing or corrupted data"""
        # Create data with missing values
        data_with_nans = pd.DataFrame({
            'timestamp': range(1000),
            'open': [2000 if i % 10 != 0 else np.nan for i in range(1000)],
            'high': [2010 if i % 15 != 0 else np.nan for i in range(1000)],
            'low': [1990 if i % 12 != 0 else np.nan for i in range(1000)],
            'close': [2000 if i % 8 != 0 else np.nan for i in range(1000)],
            'volume': [1000 if i % 20 != 0 else np.nan for i in range(1000)]
        })
        
        # Check that we have NaN values
        self.assertTrue(data_with_nans.isnull().any().any())
        
        # Test that we can clean the data
        cleaned_data = data_with_nans.dropna()
        self.assertFalse(cleaned_data.isnull().any().any())


if __name__ == '__main__':
    unittest.main()