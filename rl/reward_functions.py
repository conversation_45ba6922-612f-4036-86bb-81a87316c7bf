# reward_functions.py
# 强化学习交易奖励函数优化系统

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import json

class MarketRegime(Enum):
    """市场状态枚举"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"

@dataclass
class RewardConfig:
    """奖励函数配置"""
    # 基础权重
    pnl_weight: float = 1.0
    risk_penalty_weight: float = 0.5
    time_efficiency_weight: float = 0.1
    drawdown_penalty_weight: float = 2.0
    
    # 风险调整参数
    sharpe_bonus_weight: float = 0.3
    max_drawdown_threshold: float = 0.05  # 5%
    volatility_penalty_weight: float = 0.2
    
    # 效率奖励参数
    win_rate_bonus_weight: float = 0.4
    consecutive_win_bonus: float = 0.1
    trade_frequency_penalty: float = 0.05
    
    # 市场状态调整参数
    market_regime_adjustments: Dict[str, float] = None
    
    # 动态调整参数
    adaptive_learning_rate: float = 0.01
    performance_window: int = 50  # 用于计算动态调整的窗口
    
    def __post_init__(self):
        if self.market_regime_adjustments is None:
            self.market_regime_adjustments = {
                MarketRegime.TRENDING_UP.value: 1.2,
                MarketRegime.TRENDING_DOWN.value: 0.8,
                MarketRegime.SIDEWAYS.value: 0.9,
                MarketRegime.HIGH_VOLATILITY.value: 0.7,
                MarketRegime.LOW_VOLATILITY.value: 1.1
            }

class MultiObjectiveRewardFunction:
    """
    多目标奖励函数系统
    
    设计原则:
    1. 收益最大化 - 基础PnL奖励
    2. 风险最小化 - 回撤、波动率惩罚
    3. 效率优化 - 时间效率、胜率奖励
    4. 市场适应 - 根据市场状态调整奖励
    """
    
    def __init__(self, config: RewardConfig):
        self.config = config
        self.performance_history = []
        self.market_regime_detector = MarketRegimeDetector()
        
        # 动态调整参数
        self.adaptive_weights = {
            'pnl': config.pnl_weight,
            'risk': config.risk_penalty_weight,
            'efficiency': config.time_efficiency_weight
        }
        
        print("多目标奖励函数初始化完成")
        print(f"PnL权重: {config.pnl_weight}")
        print(f"风险惩罚权重: {config.risk_penalty_weight}")
        print(f"效率奖励权重: {config.time_efficiency_weight}")
    
    def calculate_reward(self, trade_result: Dict, market_state: Dict, 
                        portfolio_metrics: Dict) -> Dict:
        """
        计算多目标奖励
        
        Args:
            trade_result: 单笔交易结果
            market_state: 当前市场状态
            portfolio_metrics: 投资组合指标
            
        Returns:
            详细的奖励分解
        """
        # 检测市场状态
        current_regime = self.market_regime_detector.detect_regime(market_state)
        regime_multiplier = self.config.market_regime_adjustments.get(
            current_regime.value, 1.0
        )
        
        # 1. 基础收益奖励
        pnl_reward = self._calculate_pnl_reward(trade_result)
        
        # 2. 风险调整奖励
        risk_penalty = self._calculate_risk_penalty(trade_result, portfolio_metrics)
        
        # 3. 效率奖励
        efficiency_reward = self._calculate_efficiency_reward(
            trade_result, portfolio_metrics
        )
        
        # 4. 夏普比率奖励
        sharpe_bonus = self._calculate_sharpe_bonus(portfolio_metrics)
        
        # 5. 胜率奖励
        win_rate_bonus = self._calculate_win_rate_bonus(portfolio_metrics)
        
        # 6. 连续交易奖励/惩罚
        consecutive_bonus = self._calculate_consecutive_bonus(portfolio_metrics)
        
        # 7. 交易频率惩罚
        frequency_penalty = self._calculate_frequency_penalty(portfolio_metrics)
        
        # 应用动态权重
        weighted_pnl = pnl_reward * self.adaptive_weights['pnl']
        weighted_risk = risk_penalty * self.adaptive_weights['risk']
        weighted_efficiency = efficiency_reward * self.adaptive_weights['efficiency']
        
        # 计算总奖励
        base_reward = (
            weighted_pnl - 
            weighted_risk + 
            weighted_efficiency +
            sharpe_bonus * self.config.sharpe_bonus_weight +
            win_rate_bonus * self.config.win_rate_bonus_weight +
            consecutive_bonus * self.config.consecutive_win_bonus -
            frequency_penalty * self.config.trade_frequency_penalty
        )
        
        # 应用市场状态调整
        final_reward = base_reward * regime_multiplier
        
        # 记录性能历史用于动态调整
        self._update_performance_history(trade_result, final_reward)
        
        # 动态调整权重
        self._update_adaptive_weights()
        
        reward_breakdown = {
            'total_reward': final_reward,
            'pnl_reward': weighted_pnl,
            'risk_penalty': weighted_risk,
            'efficiency_reward': weighted_efficiency,
            'sharpe_bonus': sharpe_bonus,
            'win_rate_bonus': win_rate_bonus,
            'consecutive_bonus': consecutive_bonus,
            'frequency_penalty': frequency_penalty,
            'market_regime': current_regime.value,
            'regime_multiplier': regime_multiplier,
            'adaptive_weights': self.adaptive_weights.copy()
        }
        
        return reward_breakdown
    
    def _calculate_pnl_reward(self, trade_result: Dict) -> float:
        """计算基础PnL奖励"""
        pnl_pct = trade_result.get('pnl_pct', 0.0)
        
        # 非线性奖励：大盈利获得额外奖励，小亏损惩罚较轻
        if pnl_pct > 0:
            # 盈利时使用平方根函数，避免过度追求高风险
            reward = np.sqrt(pnl_pct) * 2.0
        else:
            # 亏损时使用线性惩罚
            reward = pnl_pct * 1.5
        
        return reward
    
    def _calculate_risk_penalty(self, trade_result: Dict, 
                               portfolio_metrics: Dict) -> float:
        """计算风险惩罚"""
        penalty = 0.0
        
        # 1. 最大回撤惩罚
        max_loss_pct = abs(trade_result.get('max_loss_pct', 0.0))
        if max_loss_pct > self.config.max_drawdown_threshold:
            penalty += (max_loss_pct - self.config.max_drawdown_threshold) * 5.0
        
        # 2. 基础风险惩罚（对所有亏损交易）
        if trade_result.get('pnl', 0.0) < 0:
            penalty += abs(max_loss_pct) * 2.0
        
        # 3. 投资组合回撤惩罚
        portfolio_drawdown = portfolio_metrics.get('current_drawdown', 0.0)
        if portfolio_drawdown > 0.1:  # 10%以上回撤
            penalty += portfolio_drawdown * self.config.drawdown_penalty_weight
        
        # 4. 波动率惩罚（基于最近交易的波动性）
        recent_volatility = portfolio_metrics.get('recent_volatility', 0.0)
        if recent_volatility > 0.05:  # 5%以上波动率
            penalty += recent_volatility * self.config.volatility_penalty_weight
        
        return penalty
    
    def _calculate_efficiency_reward(self, trade_result: Dict, 
                                   portfolio_metrics: Dict) -> float:
        """计算效率奖励"""
        # 时间效率：更快完成盈利交易获得奖励
        hold_time = trade_result.get('hold_time_minutes', 120)
        pnl = trade_result.get('pnl', 0.0)
        
        if pnl > 0:
            # 盈利交易的时间效率奖励
            time_efficiency = 1.0 / max(hold_time / 60.0, 0.5)  # 转换为小时
            return time_efficiency * 0.1
        else:
            # 亏损交易希望尽快止损
            if hold_time < 30:  # 30分钟内止损
                return 0.05
        
        return 0.0
    
    def _calculate_sharpe_bonus(self, portfolio_metrics: Dict) -> float:
        """计算夏普比率奖励"""
        sharpe_ratio = portfolio_metrics.get('sharpe_ratio', 0.0)
        
        if sharpe_ratio > 1.0:
            return (sharpe_ratio - 1.0) * 0.2
        elif sharpe_ratio < 0:
            return sharpe_ratio * 0.1  # 负夏普比率惩罚
        
        return 0.0
    
    def _calculate_win_rate_bonus(self, portfolio_metrics: Dict) -> float:
        """计算胜率奖励"""
        win_rate = portfolio_metrics.get('recent_win_rate', 0.5)
        
        # 胜率超过60%给予奖励
        if win_rate > 0.6:
            return (win_rate - 0.6) * 0.5
        # 胜率低于40%给予惩罚
        elif win_rate < 0.4:
            return (win_rate - 0.4) * 0.3
        
        return 0.0
    
    def _calculate_consecutive_bonus(self, portfolio_metrics: Dict) -> float:
        """计算连续交易奖励/惩罚"""
        consecutive_wins = portfolio_metrics.get('consecutive_wins', 0)
        consecutive_losses = portfolio_metrics.get('consecutive_losses', 0)
        
        # 连续盈利奖励（但有上限，避免过度自信）
        if consecutive_wins > 0:
            return min(consecutive_wins * 0.02, 0.1)
        
        # 连续亏损惩罚
        if consecutive_losses > 2:
            return -consecutive_losses * 0.03
        
        return 0.0
    
    def _calculate_frequency_penalty(self, portfolio_metrics: Dict) -> float:
        """计算交易频率惩罚"""
        total_trades = portfolio_metrics.get('total_trades', 0)
        
        # 过度交易惩罚
        if total_trades > 100:  # 假设100笔以上为过度交易
            return (total_trades - 100) * 0.001
        
        return 0.0
    
    def _update_performance_history(self, trade_result: Dict, reward: float):
        """更新性能历史"""
        self.performance_history.append({
            'pnl': trade_result.get('pnl', 0.0),
            'pnl_pct': trade_result.get('pnl_pct', 0.0),
            'reward': reward,
            'timestamp': pd.Timestamp.now()
        })
        
        # 保持固定窗口大小
        if len(self.performance_history) > self.config.performance_window:
            self.performance_history.pop(0)
    
    def _update_adaptive_weights(self):
        """动态调整权重"""
        if len(self.performance_history) < 5:  # 降低最小要求
            return
        
        # 计算最近性能
        recent_performance = self.performance_history[-5:]
        avg_pnl = np.mean([p['pnl'] for p in recent_performance])
        avg_reward = np.mean([p['reward'] for p in recent_performance])
        
        # 如果最近表现不佳，增加风险权重
        if avg_pnl < 0 and avg_reward < 0:
            adjustment = 1.0 + self.config.adaptive_learning_rate
            self.adaptive_weights['risk'] = min(
                self.adaptive_weights['risk'] * adjustment, 
                self.config.risk_penalty_weight * 2.0
            )
        # 如果表现良好，可以适当降低风险权重
        elif avg_pnl > 0 and avg_reward > 0:
            adjustment = 1.0 - self.config.adaptive_learning_rate * 0.5
            self.adaptive_weights['risk'] = max(
                self.adaptive_weights['risk'] * adjustment,
                self.config.risk_penalty_weight * 0.5
            )
    
    def get_config_dict(self) -> Dict:
        """获取当前配置"""
        return {
            'base_config': {
                'pnl_weight': self.config.pnl_weight,
                'risk_penalty_weight': self.config.risk_penalty_weight,
                'time_efficiency_weight': self.config.time_efficiency_weight,
                'drawdown_penalty_weight': self.config.drawdown_penalty_weight,
                'sharpe_bonus_weight': self.config.sharpe_bonus_weight,
                'win_rate_bonus_weight': self.config.win_rate_bonus_weight,
                'consecutive_win_bonus': self.config.consecutive_win_bonus,
                'trade_frequency_penalty': self.config.trade_frequency_penalty
            },
            'adaptive_weights': self.adaptive_weights.copy(),
            'market_regime_adjustments': self.config.market_regime_adjustments.copy()
        }

class MarketRegimeDetector:
    """市场状态检测器"""
    
    def detect_regime(self, market_state: Dict) -> MarketRegime:
        """检测当前市场状态"""
        price_change_1h = market_state.get('price_change_1h', 0.0)
        price_change_4h = market_state.get('price_change_4h', 0.0)
        volatility = market_state.get('volatility_recent', 0.0)
        
        # 高波动率检测
        if volatility > 0.05:  # 5%以上波动率
            return MarketRegime.HIGH_VOLATILITY
        elif volatility < 0.01:  # 1%以下波动率
            return MarketRegime.LOW_VOLATILITY
        
        # 趋势检测
        if price_change_1h > 0.02 and price_change_4h > 0.01:
            return MarketRegime.TRENDING_UP
        elif price_change_1h < -0.02 and price_change_4h < -0.01:
            return MarketRegime.TRENDING_DOWN
        else:
            return MarketRegime.SIDEWAYS

class RewardFunctionTester:
    """奖励函数测试器"""
    
    def __init__(self):
        self.test_scenarios = []
        self.results = []
    
    def add_test_scenario(self, name: str, config: RewardConfig, 
                         test_data: List[Dict]):
        """添加测试场景"""
        self.test_scenarios.append({
            'name': name,
            'config': config,
            'test_data': test_data
        })
    
    def run_tests(self) -> Dict:
        """运行所有测试场景"""
        results = {}
        
        for scenario in self.test_scenarios:
            print(f"测试场景: {scenario['name']}")
            
            reward_func = MultiObjectiveRewardFunction(scenario['config'])
            scenario_results = []
            
            for test_case in scenario['test_data']:
                trade_result = test_case['trade_result']
                market_state = test_case['market_state']
                portfolio_metrics = test_case['portfolio_metrics']
                
                reward_breakdown = reward_func.calculate_reward(
                    trade_result, market_state, portfolio_metrics
                )
                
                scenario_results.append({
                    'input': test_case,
                    'output': reward_breakdown
                })
            
            # 计算场景统计
            total_rewards = [r['output']['total_reward'] for r in scenario_results]
            results[scenario['name']] = {
                'results': scenario_results,
                'statistics': {
                    'mean_reward': np.mean(total_rewards),
                    'std_reward': np.std(total_rewards),
                    'min_reward': np.min(total_rewards),
                    'max_reward': np.max(total_rewards),
                    'total_tests': len(scenario_results)
                }
            }
            
            print(f"  平均奖励: {results[scenario['name']]['statistics']['mean_reward']:.4f}")
            print(f"  奖励标准差: {results[scenario['name']]['statistics']['std_reward']:.4f}")
        
        return results
    
    def compare_reward_functions(self, configs: Dict[str, RewardConfig], 
                               test_data: List[Dict]) -> Dict:
        """比较不同奖励函数的效果"""
        comparison_results = {}
        
        for name, config in configs.items():
            print(f"测试奖励函数: {name}")
            
            reward_func = MultiObjectiveRewardFunction(config)
            rewards = []
            
            for test_case in test_data:
                reward_breakdown = reward_func.calculate_reward(
                    test_case['trade_result'],
                    test_case['market_state'], 
                    test_case['portfolio_metrics']
                )
                rewards.append(reward_breakdown['total_reward'])
            
            comparison_results[name] = {
                'rewards': rewards,
                'mean': np.mean(rewards),
                'std': np.std(rewards),
                'sharpe': np.mean(rewards) / (np.std(rewards) + 1e-8)
            }
        
        return comparison_results

def create_default_configs() -> Dict[str, RewardConfig]:
    """创建默认的奖励函数配置"""
    configs = {}
    
    # 1. 保守型配置 - 重视风险控制
    configs['conservative'] = RewardConfig(
        pnl_weight=0.8,
        risk_penalty_weight=1.5,
        time_efficiency_weight=0.05,
        drawdown_penalty_weight=3.0,
        sharpe_bonus_weight=0.5,
        win_rate_bonus_weight=0.6,
        consecutive_win_bonus=0.05,
        trade_frequency_penalty=0.1
    )
    
    # 2. 激进型配置 - 追求高收益
    configs['aggressive'] = RewardConfig(
        pnl_weight=1.5,
        risk_penalty_weight=0.3,
        time_efficiency_weight=0.2,
        drawdown_penalty_weight=1.0,
        sharpe_bonus_weight=0.2,
        win_rate_bonus_weight=0.3,
        consecutive_win_bonus=0.15,
        trade_frequency_penalty=0.02
    )
    
    # 3. 平衡型配置 - 收益风险平衡
    configs['balanced'] = RewardConfig(
        pnl_weight=1.0,
        risk_penalty_weight=0.7,
        time_efficiency_weight=0.1,
        drawdown_penalty_weight=2.0,
        sharpe_bonus_weight=0.3,
        win_rate_bonus_weight=0.4,
        consecutive_win_bonus=0.1,
        trade_frequency_penalty=0.05
    )
    
    # 4. 效率型配置 - 重视交易效率
    configs['efficiency'] = RewardConfig(
        pnl_weight=0.9,
        risk_penalty_weight=0.5,
        time_efficiency_weight=0.3,
        drawdown_penalty_weight=1.5,
        sharpe_bonus_weight=0.4,
        win_rate_bonus_weight=0.5,
        consecutive_win_bonus=0.08,
        trade_frequency_penalty=0.08
    )
    
    return configs

if __name__ == "__main__":
    # 测试奖励函数系统
    print("测试多目标奖励函数系统")
    
    # 创建测试配置
    config = RewardConfig()
    reward_func = MultiObjectiveRewardFunction(config)
    
    # 创建测试数据
    test_trade = {
        'pnl': 50.0,
        'pnl_pct': 0.02,
        'max_loss_pct': -0.01,
        'hold_time_minutes': 90
    }
    
    test_market = {
        'price_change_1h': 0.015,
        'price_change_4h': 0.008,
        'volatility_recent': 0.025
    }
    
    test_portfolio = {
        'recent_win_rate': 0.65,
        'consecutive_wins': 3,
        'consecutive_losses': 0,
        'total_trades': 25,
        'current_drawdown': 0.02,
        'recent_volatility': 0.03,
        'sharpe_ratio': 1.2
    }
    
    # 计算奖励
    reward_breakdown = reward_func.calculate_reward(
        test_trade, test_market, test_portfolio
    )
    
    print("\n奖励分解:")
    for key, value in reward_breakdown.items():
        if isinstance(value, (int, float)):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value}")
    
    # 测试不同配置
    print("\n测试不同奖励函数配置:")
    configs = create_default_configs()
    
    tester = RewardFunctionTester()
    test_data = [{
        'trade_result': test_trade,
        'market_state': test_market,
        'portfolio_metrics': test_portfolio
    }]
    
    comparison = tester.compare_reward_functions(configs, test_data)
    
    for name, result in comparison.items():
        print(f"{name}: 奖励={result['mean']:.4f}, 夏普={result['sharpe']:.4f}")
    
    print("\n奖励函数系统测试完成！")