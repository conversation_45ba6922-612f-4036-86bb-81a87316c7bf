"""
Test file for PolicyNetwork implementation.

This file contains unit tests to verify the PolicyNetwork class works correctly
according to the requirements and design specifications.
"""

import torch
import numpy as np
import tempfile
import os
import sys
import unittest

# Add the parent directory to path to import the policy network
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rl.policy_network import PolicyNetwork, create_default_policy_network


class TestPolicyNetwork(unittest.TestCase):
    """Test cases for PolicyNetwork class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.state_dim = 20
        self.batch_size = 4
        self.network = create_default_policy_network(self.state_dim)
        self.sample_state = torch.randn(self.batch_size, self.state_dim)
        self.single_state = torch.randn(1, self.state_dim)
    
    def test_network_initialization(self):
        """Test that network initializes correctly."""
        self.assertEqual(self.network.state_dim, self.state_dim)
        self.assertEqual(self.network.hidden_dim, 256)
        self.assertEqual(self.network.dropout_rate, 0.1)
        
        # Check that all parameters are initialized
        for param in self.network.parameters():
            self.assertFalse(torch.isnan(param).any())
    
    def test_forward_pass(self):
        """Test forward pass produces correct output shapes."""
        outputs = self.network.forward(self.sample_state)
        
        # Check all expected outputs are present
        expected_keys = ['position_size_raw', 'stop_loss_raw', 'take_profit_raw', 
                        'hold_time_raw', 'enter_trade_logits']
        self.assertEqual(set(outputs.keys()), set(expected_keys))
        
        # Check output shapes
        self.assertEqual(outputs['position_size_raw'].shape, (self.batch_size, 1))
        self.assertEqual(outputs['stop_loss_raw'].shape, (self.batch_size, 1))
        self.assertEqual(outputs['take_profit_raw'].shape, (self.batch_size, 1))
        self.assertEqual(outputs['hold_time_raw'].shape, (self.batch_size, 1))
        self.assertEqual(outputs['enter_trade_logits'].shape, (self.batch_size, 2))
        
        # Check output ranges for sigmoid outputs (should be [0, 1])
        for key in ['position_size_raw', 'stop_loss_raw', 'take_profit_raw', 'hold_time_raw']:
            values = outputs[key]
            self.assertTrue(torch.all(values >= 0))
            self.assertTrue(torch.all(values <= 1))
    
    def test_get_actions_deterministic(self):
        """Test deterministic action generation."""
        actions = self.network.get_actions(self.single_state, deterministic=True)
        
        # Check all expected actions are present
        expected_keys = ['position_size', 'stop_loss_pct', 'take_profit_pct', 
                        'max_hold_time', 'enter_trade']
        self.assertEqual(set(actions.keys()), set(expected_keys))
        
        # Check action ranges
        self.assertTrue(0 <= actions['position_size'].item() <= 0.1)
        self.assertTrue(0.01 <= actions['stop_loss_pct'].item() <= 0.05)
        self.assertTrue(0.01 <= actions['take_profit_pct'].item() <= 0.04)
        self.assertTrue(60 <= actions['max_hold_time'].item() <= 240)
        self.assertIn(actions['enter_trade'].item(), [0, 1])
    
    def test_get_actions_stochastic(self):
        """Test stochastic action generation."""
        actions = self.network.get_actions(self.single_state, deterministic=False)
        
        # Should have same structure as deterministic
        expected_keys = ['position_size', 'stop_loss_pct', 'take_profit_pct', 
                        'max_hold_time', 'enter_trade']
        self.assertEqual(set(actions.keys()), set(expected_keys))
        
        # Check ranges are still valid
        self.assertTrue(0 <= actions['position_size'].item() <= 0.1)
        self.assertTrue(0.01 <= actions['stop_loss_pct'].item() <= 0.05)
        self.assertTrue(0.01 <= actions['take_profit_pct'].item() <= 0.04)
        self.assertTrue(60 <= actions['max_hold_time'].item() <= 240)
        self.assertIn(actions['enter_trade'].item(), [0, 1])
    
    def test_sample_action(self):
        """Test action sampling with log probabilities."""
        actions, log_probs = self.network.sample_action(self.single_state)
        
        # Check action structure
        expected_keys = ['position_size', 'stop_loss_pct', 'take_profit_pct', 
                        'max_hold_time', 'enter_trade']
        self.assertEqual(set(actions.keys()), set(expected_keys))
        self.assertEqual(set(log_probs.keys()), set(expected_keys))
        
        # Check action types and ranges
        self.assertIsInstance(actions['position_size'], float)
        self.assertIsInstance(actions['stop_loss_pct'], float)
        self.assertIsInstance(actions['take_profit_pct'], float)
        self.assertIsInstance(actions['max_hold_time'], int)
        self.assertIsInstance(actions['enter_trade'], bool)
        
        # Check ranges
        self.assertTrue(0 <= actions['position_size'] <= 0.1)
        self.assertTrue(0.01 <= actions['stop_loss_pct'] <= 0.05)
        self.assertTrue(0.01 <= actions['take_profit_pct'] <= 0.04)
        self.assertTrue(60 <= actions['max_hold_time'] <= 240)
        
        # Check log probabilities are tensors
        for key, log_prob in log_probs.items():
            self.assertIsInstance(log_prob, torch.Tensor)
            self.assertFalse(torch.isnan(log_prob).any())
    
    def test_get_log_prob(self):
        """Test log probability calculation."""
        # Sample some actions first
        actions, _ = self.network.sample_action(self.single_state)
        
        # Convert actions to tensors for log_prob calculation
        action_tensors = {
            'position_size': torch.tensor([actions['position_size']]),
            'enter_trade': torch.tensor([int(actions['enter_trade'])])
        }
        
        log_prob = self.network.get_log_prob(self.single_state, action_tensors)
        
        self.assertIsInstance(log_prob, torch.Tensor)
        self.assertEqual(log_prob.shape, (1,))
        self.assertFalse(torch.isnan(log_prob).any())
    
    def test_save_and_load_model(self):
        """Test model saving and loading functionality."""
        with tempfile.TemporaryDirectory() as temp_dir:
            model_path = os.path.join(temp_dir, "test_model")
            
            # Save model with metadata
            metadata = {'test': True, 'version': '1.0'}
            self.network.save_model(model_path, metadata)
            
            # Check files were created
            self.assertTrue(os.path.exists(f"{model_path}.pth"))
            self.assertTrue(os.path.exists(f"{model_path}_config.json"))
            
            # Load model
            loaded_network = PolicyNetwork.load_model(model_path)
            
            # Check loaded model has same architecture
            self.assertEqual(loaded_network.state_dim, self.network.state_dim)
            self.assertEqual(loaded_network.hidden_dim, self.network.hidden_dim)
            self.assertEqual(loaded_network.dropout_rate, self.network.dropout_rate)
            
            # Check that loaded model produces same outputs (set to eval mode to disable dropout)
            self.network.eval()
            loaded_network.eval()
            original_output = self.network.forward(self.single_state)
            loaded_output = loaded_network.forward(self.single_state)
            
            for key in original_output.keys():
                torch.testing.assert_close(original_output[key], loaded_output[key])
    
    def test_model_info(self):
        """Test model information retrieval."""
        info = self.network.get_model_info()
        
        # Check required fields
        required_fields = ['state_dim', 'hidden_dim', 'dropout_rate', 
                          'total_parameters', 'trainable_parameters', 
                          'model_size_mb', 'architecture']
        for field in required_fields:
            self.assertIn(field, info)
        
        # Check values make sense
        self.assertEqual(info['state_dim'], self.state_dim)
        self.assertEqual(info['hidden_dim'], 256)
        self.assertEqual(info['dropout_rate'], 0.1)
        self.assertGreater(info['total_parameters'], 0)
        self.assertGreater(info['trainable_parameters'], 0)
        self.assertGreater(info['model_size_mb'], 0)
    
    def test_batch_processing(self):
        """Test that network handles batch processing correctly."""
        batch_sizes = [1, 4, 16, 32]
        
        for batch_size in batch_sizes:
            batch_state = torch.randn(batch_size, self.state_dim)
            
            # Test forward pass
            outputs = self.network.forward(batch_state)
            for key, value in outputs.items():
                self.assertEqual(value.shape[0], batch_size)
            
            # Test action generation
            actions = self.network.get_actions(batch_state, deterministic=True)
            for key, value in actions.items():
                self.assertEqual(value.shape[0], batch_size)
    
    def test_gradient_flow(self):
        """Test that gradients flow through the network correctly."""
        # Enable gradient computation
        self.network.train()
        
        # Forward pass
        outputs = self.network.forward(self.sample_state)
        
        # Create a dummy loss
        loss = sum(output.sum() for output in outputs.values())
        
        # Backward pass
        loss.backward()
        
        # Check that gradients exist and are not zero
        has_gradients = False
        for param in self.network.parameters():
            if param.grad is not None and not torch.allclose(param.grad, torch.zeros_like(param.grad)):
                has_gradients = True
                break
        
        self.assertTrue(has_gradients, "No gradients found in network parameters")


def run_basic_functionality_test():
    """Run a basic functionality test without unittest framework."""
    print("Running basic PolicyNetwork functionality test...")
    
    # Create network
    state_dim = 15
    network = create_default_policy_network(state_dim)
    print(f"✓ Created network with {network.get_model_info()['total_parameters']} parameters")
    
    # Test forward pass
    sample_state = torch.randn(1, state_dim)
    outputs = network.forward(sample_state)
    print(f"✓ Forward pass successful, outputs: {list(outputs.keys())}")
    
    # Test action sampling
    actions, log_probs = network.sample_action(sample_state)
    print(f"✓ Action sampling successful")
    print(f"  Position size: {actions['position_size']:.4f}")
    print(f"  Stop loss: {actions['stop_loss_pct']:.4f}")
    print(f"  Take profit: {actions['take_profit_pct']:.4f}")
    print(f"  Hold time: {actions['max_hold_time']} minutes")
    print(f"  Enter trade: {actions['enter_trade']}")
    
    # Test deterministic actions
    det_actions = network.get_actions(sample_state, deterministic=True)
    print(f"✓ Deterministic actions successful")
    
    # Test save/load (using temp directory)
    import tempfile
    with tempfile.TemporaryDirectory() as temp_dir:
        model_path = os.path.join(temp_dir, "test_model")
        network.save_model(model_path)
        loaded_network = PolicyNetwork.load_model(model_path)
        print(f"✓ Save/load successful")
    
    print("All basic functionality tests passed!")


if __name__ == "__main__":
    # Run basic test first
    run_basic_functionality_test()
    print("\n" + "="*50 + "\n")
    
    # Run full test suite
    unittest.main(verbosity=2)