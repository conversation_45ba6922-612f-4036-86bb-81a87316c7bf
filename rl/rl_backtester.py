# rl_backtester.py
# 扩展现有回测器支持 RL - 继承 HistoricalBacktester 并替换决策逻辑

import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
import json

# Add parent directory to path to import existing backtester
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import existing backtester and related utilities
try:
    from backtest_money_quick import HistoricalBacktester, load_chushou_config, is_good_time_to_trade, to_beijing_time, format_beijing_time
except ImportError:
    print("警告: 无法导入现有回测器，请确保 backtest_money_quick.py 在路径中")
    # 提供基础的 HistoricalBacktester 类定义
    class HistoricalBacktester:
        def __init__(self, *args, **kwargs):
            pass

from .rl_trading_agent import RLTradingAgent
from .trading_environment import TradingEnvironment, MarketState, TradingAction


class RLBacktester(HistoricalBacktester):
    """
    强化学习回测器 - 继承现有的 HistoricalBacktester
    
    替换固定规则的交易决策为 RL 代理决策，支持：
    - 动态仓位大小
    - 动态止盈止损
    - 智能进出场时机
    """
    
    def __init__(self, model_file: str, config_file: str, rl_agent_path: str, 
                 initial_capital: float, risk_per_trade_pct: float, 
                 price_multiplier: float = 1.0, stop_loss_pct: float = None,
                 rl_config: Optional[Dict] = None):
        """
        初始化 RL 回测器
        
        Args:
            model_file: 现有预测模型文件路径
            config_file: 现有模型配置文件路径
            rl_agent_path: 训练好的 RL 代理模型路径
            initial_capital: 初始资金
            risk_per_trade_pct: 单次交易风险比例
            price_multiplier: 价格乘数
            stop_loss_pct: 传统止损百分比（RL模式下会被动态调整）
            rl_config: RL环境配置
        """
        # 初始化父类
        super().__init__(model_file, config_file, initial_capital, risk_per_trade_pct, 
                        price_multiplier, stop_loss_pct)
        
        # RL 相关初始化
        self.rl_agent_path = rl_agent_path
        self.rl_config = rl_config or self._create_default_rl_config()
        
        # 加载训练好的 RL 代理
        try:
            self.rl_agent = RLTradingAgent.load_model(rl_agent_path)
            self.rl_agent.set_training_mode(False)  # 设置为评估模式
            print(f"✅ 已加载 RL 代理: {rl_agent_path}")
        except Exception as e:
            print(f"❌ 加载 RL 代理失败: {e}")
            print("将使用传统回测模式")
            self.rl_agent = None
        
        # 创建 RL 环境用于状态转换
        self.rl_env = TradingEnvironment(self.rl_config)
        
        # RL 决策统计
        self.rl_decisions = []
        self.rl_stats = {
            'total_rl_decisions': 0,
            'rl_enter_decisions': 0,
            'rl_skip_decisions': 0,
            'avg_rl_position_size': 0.0,
            'avg_rl_stop_loss': 0.0,
            'avg_rl_take_profit': 0.0
        }
        
        # 市场状态历史（用于计算价格变化等）
        self.price_history = []
        self.recent_trades = []  # 最近交易历史
        
        print(f"🤖 RL 回测器初始化完成")
        print(f"RL 模式: {'启用' if self.rl_agent else '禁用（使用传统模式）'}")
    
    def _create_default_rl_config(self) -> Dict:
        """创建默认的 RL 配置"""
        return {
            'initial_capital': self.initial_capital,
            'transaction_cost': 0.001,  # 0.1% 手续费
            'max_position_size': 0.1,   # 最大 10% 仓位
            'max_positions': 5,
            'max_drawdown_limit': 0.2,
            'reward_config': {
                'pnl_weight': 1.0,
                'risk_penalty': 0.5,
                'time_efficiency': 0.1,
                'max_drawdown_penalty': 2.0
            }
        }
    
    def _build_market_state(self, guess: int, probability: float, current_price: float, 
                           current_timestamp: pd.Timestamp, current_idx: int) -> MarketState:
        """
        构建 RL 代理需要的市场状态
        
        Args:
            guess: 现有模型的预测 (0 or 1)
            probability: 预测置信度
            current_price: 当前价格
            current_timestamp: 当前时间戳
            current_idx: 当前数据索引
            
        Returns:
            MarketState 对象
        """
        # 计算价格变化率
        price_change_1h = 0.0
        price_change_4h = 0.0
        volatility_recent = 0.02  # 默认波动率
        
        if len(self.price_history) > 0:
            # 计算 1 小时价格变化（假设 5 分钟间隔，12 个点 = 1 小时）
            lookback_1h = min(12, len(self.price_history))
            if lookback_1h > 0:
                price_1h_ago = self.price_history[-lookback_1h]
                price_change_1h = (current_price - price_1h_ago) / price_1h_ago
            
            # 计算 4 小时价格变化（48 个点 = 4 小时）
            lookback_4h = min(48, len(self.price_history))
            if lookback_4h > 0:
                price_4h_ago = self.price_history[-lookback_4h]
                price_change_4h = (current_price - price_4h_ago) / price_4h_ago
            
            # 计算最近波动率（使用最近 20 个价格点）
            if len(self.price_history) >= 20:
                recent_prices = np.array(self.price_history[-20:] + [current_price])
                returns = np.diff(recent_prices) / recent_prices[:-1]
                volatility_recent = np.std(returns)
        
        # 更新价格历史（保留最近 100 个点）
        self.price_history.append(current_price)
        if len(self.price_history) > 100:
            self.price_history.pop(0)
        
        # 计算投资组合状态
        cash_ratio = self.current_capital / self.initial_capital
        position_count = len(self.active_predictions)
        
        # 计算未实现盈亏
        unrealized_pnl = 0.0
        for pred in self.active_predictions.values():
            if pred['status'] == 'active':
                price_change = (current_price - pred['start_price']) / pred['start_price']
                if pred['guess'] == 1:
                    unrealized_pnl += pred['trade_risk_capital'] * price_change
                else:
                    unrealized_pnl += pred['trade_risk_capital'] * (-price_change)
        
        # 计算最近胜率和连续亏损
        recent_win_rate = 0.5
        consecutive_losses = 0
        
        if len(self.recent_trades) > 0:
            # 计算最近 10 笔交易的胜率
            recent_10 = self.recent_trades[-10:]
            wins = sum(1 for trade in recent_10 if trade.get('Score', 0) > 0)
            recent_win_rate = wins / len(recent_10)
            
            # 计算连续亏损
            for trade in reversed(self.recent_trades):
                if trade.get('Score', 0) < 0:
                    consecutive_losses += 1
                else:
                    break
        
        # 时间特征
        beijing_time = to_beijing_time(current_timestamp)
        hour = beijing_time.hour
        day_of_week = beijing_time.weekday()
        
        # 检查是否是好的交易时间（如果有 chushou 配置）
        is_good_trading_time = True  # 默认为 True
        
        return MarketState(
            model_signal=guess,
            signal_confidence=probability,
            signal_probability=probability,
            current_price=current_price,
            price_change_1h=price_change_1h,
            price_change_4h=price_change_4h,
            volatility_recent=volatility_recent,
            cash_ratio=min(cash_ratio, 1.0),
            position_count=position_count,
            unrealized_pnl=unrealized_pnl,
            recent_win_rate=recent_win_rate,
            consecutive_losses=consecutive_losses,
            hour=hour,
            day_of_week=day_of_week,
            is_good_trading_time=is_good_trading_time
        )
    
    def make_rl_trading_decision(self, guess: int, probability: float, current_price: float,
                                current_timestamp: pd.Timestamp, current_idx: int) -> Optional[Dict]:
        """
        使用 RL 代理做交易决策
        
        Returns:
            交易决策字典，如果不进场则返回 None
        """
        if not self.rl_agent:
            return None
        
        # 构建市场状态
        market_state = self._build_market_state(guess, probability, current_price, 
                                              current_timestamp, current_idx)
        
        # 获取状态向量
        state_vector = self.rl_env.get_state_vector(market_state)
        
        # RL 代理决策
        action, log_prob, value_estimate = self.rl_agent.get_action(state_vector, deterministic=True)
        
        # 记录决策
        decision_record = {
            'timestamp': current_timestamp,
            'model_signal': guess,
            'model_confidence': probability,
            'rl_enter_trade': action['enter_trade'],
            'rl_position_size': action['position_size'],
            'rl_stop_loss_pct': action['stop_loss_pct'],
            'rl_take_profit_pct': action['take_profit_pct'],
            'rl_max_hold_time': action['max_hold_time'],
            'rl_value_estimate': value_estimate,
            'market_price': current_price,
            'price_change_1h': market_state.price_change_1h,
            'price_change_4h': market_state.price_change_4h,
            'portfolio_cash_ratio': market_state.cash_ratio,
            'active_positions': market_state.position_count
        }
        
        self.rl_decisions.append(decision_record)
        self.rl_stats['total_rl_decisions'] += 1
        
        if action['enter_trade']:
            self.rl_stats['rl_enter_decisions'] += 1
            
            # 更新平均值统计
            n = self.rl_stats['rl_enter_decisions']
            self.rl_stats['avg_rl_position_size'] = (
                (self.rl_stats['avg_rl_position_size'] * (n-1) + action['position_size']) / n
            )
            self.rl_stats['avg_rl_stop_loss'] = (
                (self.rl_stats['avg_rl_stop_loss'] * (n-1) + action['stop_loss_pct']) / n
            )
            self.rl_stats['avg_rl_take_profit'] = (
                (self.rl_stats['avg_rl_take_profit'] * (n-1) + action['take_profit_pct']) / n
            )
            
            return action
        else:
            self.rl_stats['rl_skip_decisions'] += 1
            return None
    
    def add_prediction(self, guess: int, probability: float, price: float, 
                      timestamp: pd.Timestamp, current_idx: int):
        """
        重写父类的 add_prediction 方法，使用 RL 决策
        """
        # 如果没有 RL 代理，使用传统方法
        if not self.rl_agent:
            return super().add_prediction(guess, probability, price, timestamp, current_idx)
        
        # 使用 RL 代理决策
        rl_decision = self.make_rl_trading_decision(guess, probability, price, timestamp, current_idx)
        
        if rl_decision is None:
            # RL 代理决定不进场
            return
        
        # RL 代理决定进场，使用动态参数
        self.prediction_counter += 1
        prediction_id = f"rl_pred_{self.prediction_counter:06d}"
        
        # 使用 RL 决定的仓位大小
        trade_risk_capital = self.current_capital * rl_decision['position_size']
        
        # 使用 RL 决定的最大等待时间
        max_wait_minutes = rl_decision['max_hold_time']
        max_wait_candles = max_wait_minutes // self.config['timeframe_minutes']
        
        # 使用 RL 决定的止盈止损比例
        up_threshold = rl_decision['take_profit_pct']
        down_threshold = rl_decision['stop_loss_pct']
        
        prediction = {
            'id': prediction_id,
            'guess': guess,
            'probability': probability,
            'start_price': price,
            'start_timestamp': timestamp,
            'start_idx': current_idx,
            'expire_idx': current_idx + max_wait_candles,
            'up_target': price * (1 + up_threshold),
            'down_target': price * (1 - down_threshold),
            'status': 'active',
            'trade_risk_capital': trade_risk_capital,
            'max_loss_pct': 0.0,
            'max_loss_price': price,
            'max_loss_timestamp': timestamp,
            # RL 特有字段
            'rl_position_size': rl_decision['position_size'],
            'rl_stop_loss_pct': rl_decision['stop_loss_pct'],
            'rl_take_profit_pct': rl_decision['take_profit_pct'],
            'rl_max_hold_time': rl_decision['max_hold_time'],
            'rl_decision': True
        }
        
        self.active_predictions[prediction_id] = prediction
        self.total_predictions += 1
        
        direction_str = f"先涨{up_threshold*100:.1f}%" if guess == 1 else f"先跌{down_threshold*100:.1f}%"
        print(f"[{format_beijing_time(timestamp)}] 🤖 RL预测: {direction_str}, "
              f"信心: {probability:.3f}, 价格: {price:.4f}, "
              f"RL仓位: {rl_decision['position_size']*100:.1f}%, "
              f"风险暴露: ${trade_risk_capital:,.2f}")
    
    def complete_prediction(self, pred_id: str, result: int, final_price: float, 
                           end_timestamp: pd.Timestamp, end_idx: int, reason: str, 
                           custom_score: float = None):
        """
        重写完成预测方法，记录 RL 相关信息
        """
        # 调用父类方法
        super().complete_prediction(pred_id, result, final_price, end_timestamp, end_idx, reason, custom_score)
        
        # 如果是 RL 预测，更新最近交易历史
        if pred_id in self.active_predictions or len(self.completed_predictions) > 0:
            # 获取最新完成的预测
            if self.completed_predictions:
                latest_trade = self.completed_predictions[-1]
                self.recent_trades.append(latest_trade)
                
                # 保留最近 50 笔交易
                if len(self.recent_trades) > 50:
                    self.recent_trades.pop(0)
    
    def get_rl_statistics(self) -> Dict:
        """获取 RL 决策统计信息"""
        stats = self.rl_stats.copy()
        
        if stats['total_rl_decisions'] > 0:
            stats['rl_enter_rate'] = stats['rl_enter_decisions'] / stats['total_rl_decisions']
            stats['rl_skip_rate'] = stats['rl_skip_decisions'] / stats['total_rl_decisions']
        else:
            stats['rl_enter_rate'] = 0.0
            stats['rl_skip_rate'] = 0.0
        
        return stats
    
    def save_rl_decisions(self, filepath: str):
        """保存 RL 决策历史"""
        if not self.rl_decisions:
            print("没有 RL 决策记录可保存")
            return
        
        # 转换为 DataFrame 并保存
        df = pd.DataFrame(self.rl_decisions)
        df['timestamp'] = df['timestamp'].apply(format_beijing_time)
        df.to_csv(filepath, index=False, float_format='%.6f')
        print(f"RL 决策历史已保存到: {filepath}")
    
    def print_rl_summary(self):
        """打印 RL 回测摘要"""
        if not self.rl_agent:
            print("未使用 RL 代理")
            return
        
        print("\n=== RL 决策摘要 ===")
        stats = self.get_rl_statistics()
        
        print(f"总决策次数: {stats['total_rl_decisions']}")
        print(f"进场决策: {stats['rl_enter_decisions']} ({stats['rl_enter_rate']*100:.1f}%)")
        print(f"跳过决策: {stats['rl_skip_decisions']} ({stats['rl_skip_rate']*100:.1f}%)")
        
        if stats['rl_enter_decisions'] > 0:
            print(f"平均仓位大小: {stats['avg_rl_position_size']*100:.2f}%")
            print(f"平均止损: {stats['avg_rl_stop_loss']*100:.2f}%")
            print(f"平均止盈: {stats['avg_rl_take_profit']*100:.2f}%")


def create_rl_backtester(model_file: str, config_file: str, rl_agent_path: str,
                        initial_capital: float = 10000, risk_per_trade_pct: float = 1.0,
                        **kwargs) -> RLBacktester:
    """
    创建 RL 回测器的便捷函数
    
    Args:
        model_file: 现有预测模型文件路径
        config_file: 现有模型配置文件路径  
        rl_agent_path: 训练好的 RL 代理模型路径
        initial_capital: 初始资金
        risk_per_trade_pct: 单次交易风险比例
        **kwargs: 其他参数
        
    Returns:
        RLBacktester 实例
    """
    return RLBacktester(
        model_file=model_file,
        config_file=config_file,
        rl_agent_path=rl_agent_path,
        initial_capital=initial_capital,
        risk_per_trade_pct=risk_per_trade_pct,
        **kwargs
    )


if __name__ == "__main__":
    # 测试 RL 回测器
    print("测试 RL 回测器...")
    
    # 这里需要实际的模型文件来测试
    # 示例用法：
    """
    rl_backtester = create_rl_backtester(
        model_file="models/eth_5m_model.joblib",
        config_file="models/eth_5m_config.json", 
        rl_agent_path="trained_rl_agent",
        initial_capital=10000,
        risk_per_trade_pct=1.0
    )
    
    # 然后可以像使用普通回测器一样使用
    # 但内部会使用 RL 代理做决策
    """
    
    print("RL 回测器模块加载完成！")
    print("使用方法:")
    print("1. 训练 RL 代理并保存模型")
    print("2. 创建 RLBacktester 实例")
    print("3. 运行回测，RL 代理会自动替换传统决策逻辑")