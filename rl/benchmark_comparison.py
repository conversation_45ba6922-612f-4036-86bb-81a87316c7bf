"""
Benchmark strategy comparison for RL trading system.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime

@dataclass
class StrategyResult:
    """Container for strategy performance results."""
    name: str
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    avg_trade_duration: float
    volatility: float
    calmar_ratio: float
    profit_factor: float
    
class BenchmarkComparison:
    """
    Compare RL trading strategy with various benchmark strategies.
    """
    
    def __init__(self, output_dir: str = "rl/analysis"):
        """
        Initialize benchmark comparison.
        
        Args:
            output_dir: Directory to save comparison results
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("Set2")
        
    def calculate_strategy_metrics(self, returns: pd.Series, trades: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate comprehensive strategy performance metrics.
        
        Args:
            returns: Series of strategy returns
            trades: DataFrame with trade details
            
        Returns:
            Dictionary of performance metrics
        """
        # Basic return metrics
        total_return = (1 + returns).prod() - 1
        volatility = returns.std() * np.sqrt(252)  # Annualized
        
        # Sharpe ratio (assuming 0% risk-free rate)
        sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
        
        # Maximum drawdown
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # Calmar ratio
        calmar_ratio = total_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # Trade-based metrics
        if not trades.empty:
            winning_trades = trades[trades['pnl'] > 0]
            losing_trades = trades[trades['pnl'] <= 0]
            
            win_rate = len(winning_trades) / len(trades) if len(trades) > 0 else 0
            avg_trade_duration = trades['duration_minutes'].mean() if 'duration_minutes' in trades.columns else 0
            
            # Profit factor
            gross_profit = winning_trades['pnl'].sum() if not winning_trades.empty else 0
            gross_loss = abs(losing_trades['pnl'].sum()) if not losing_trades.empty else 1e-6
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
        else:
            win_rate = 0
            avg_trade_duration = 0
            profit_factor = 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': len(trades),
            'avg_trade_duration': avg_trade_duration,
            'volatility': volatility,
            'calmar_ratio': calmar_ratio,
            'profit_factor': profit_factor
        }
    
    def create_buy_and_hold_benchmark(self, price_data: pd.Series) -> StrategyResult:
        """
        Create buy and hold benchmark strategy.
        
        Args:
            price_data: Price series for the asset
            
        Returns:
            StrategyResult for buy and hold strategy
        """
        returns = price_data.pct_change().dropna()
        
        # Single trade for entire period
        trades = pd.DataFrame({
            'pnl': [(price_data.iloc[-1] - price_data.iloc[0]) / price_data.iloc[0]],
            'duration_minutes': [len(price_data) * 5]  # Assuming 5-minute data
        })
        
        metrics = self.calculate_strategy_metrics(returns, trades)
        
        return StrategyResult(
            name="Buy & Hold",
            **metrics
        )
    
    def create_simple_momentum_benchmark(self, price_data: pd.Series, 
                                       lookback_periods: int = 20) -> StrategyResult:
        """
        Create simple momentum benchmark strategy.
        
        Args:
            price_data: Price series for the asset
            lookback_periods: Number of periods for momentum calculation
            
        Returns:
            StrategyResult for momentum strategy
        """
        # Calculate momentum signals
        momentum = price_data.pct_change(lookback_periods)
        signals = (momentum > 0).astype(int)
        
        # Calculate returns
        returns = price_data.pct_change() * signals.shift(1)
        returns = returns.dropna()
        
        # Simulate trades (simplified)
        signal_changes = signals.diff().fillna(0)
        entry_points = signal_changes == 1
        exit_points = signal_changes == -1
        
        trades = []
        position_entry = None
        
        for i, (entry, exit) in enumerate(zip(entry_points, exit_points)):
            if entry and position_entry is None:
                position_entry = i
            elif exit and position_entry is not None:
                duration = i - position_entry
                pnl = (price_data.iloc[i] - price_data.iloc[position_entry]) / price_data.iloc[position_entry]
                trades.append({
                    'pnl': pnl,
                    'duration_minutes': duration * 5
                })
                position_entry = None
        
        trades_df = pd.DataFrame(trades)
        metrics = self.calculate_strategy_metrics(returns, trades_df)
        
        return StrategyResult(
            name="Simple Momentum",
            **metrics
        )
    
    def create_mean_reversion_benchmark(self, price_data: pd.Series, 
                                      lookback_periods: int = 20,
                                      std_threshold: float = 2.0) -> StrategyResult:
        """
        Create mean reversion benchmark strategy.
        
        Args:
            price_data: Price series for the asset
            lookback_periods: Number of periods for mean calculation
            std_threshold: Standard deviation threshold for signals
            
        Returns:
            StrategyResult for mean reversion strategy
        """
        # Calculate rolling mean and std
        rolling_mean = price_data.rolling(lookback_periods).mean()
        rolling_std = price_data.rolling(lookback_periods).std()
        
        # Calculate z-score
        z_score = (price_data - rolling_mean) / rolling_std
        
        # Generate signals (buy when oversold, sell when overbought)
        buy_signals = z_score < -std_threshold
        sell_signals = z_score > std_threshold
        
        # Calculate returns (simplified mean reversion)
        signals = np.where(buy_signals, 1, np.where(sell_signals, -1, 0))
        signals = pd.Series(signals, index=price_data.index)
        
        returns = price_data.pct_change() * signals.shift(1)
        returns = returns.dropna()
        
        # Simulate trades
        signal_changes = pd.Series(signals).diff().fillna(0)
        trades = []
        
        for i in range(1, len(signals)):
            if abs(signal_changes.iloc[i]) > 0:  # Signal change
                # Simplified trade simulation
                if signals.iloc[i] != 0:
                    pnl = np.random.normal(0, 0.01)  # Simplified for benchmark
                    trades.append({
                        'pnl': pnl,
                        'duration_minutes': np.random.randint(30, 180)
                    })
        
        trades_df = pd.DataFrame(trades)
        metrics = self.calculate_strategy_metrics(returns, trades_df)
        
        return StrategyResult(
            name="Mean Reversion",
            **metrics
        )
    
    def create_random_strategy_benchmark(self, price_data: pd.Series, 
                                       n_trades: int = 100) -> StrategyResult:
        """
        Create random trading benchmark strategy.
        
        Args:
            price_data: Price series for the asset
            n_trades: Number of random trades to simulate
            
        Returns:
            StrategyResult for random strategy
        """
        np.random.seed(42)  # For reproducibility
        
        # Generate random trades
        trades = []
        for _ in range(n_trades):
            # Random entry and exit points
            entry_idx = np.random.randint(0, len(price_data) - 50)
            duration = np.random.randint(10, 100)
            exit_idx = min(entry_idx + duration, len(price_data) - 1)
            
            # Random direction
            direction = np.random.choice([-1, 1])
            
            # Calculate PnL
            price_change = (price_data.iloc[exit_idx] - price_data.iloc[entry_idx]) / price_data.iloc[entry_idx]
            pnl = direction * price_change
            
            trades.append({
                'pnl': pnl,
                'duration_minutes': duration * 5
            })
        
        trades_df = pd.DataFrame(trades)
        
        # Calculate returns series (simplified)
        returns = pd.Series([t['pnl'] / n_trades for t in trades])
        
        metrics = self.calculate_strategy_metrics(returns, trades_df)
        
        return StrategyResult(
            name="Random Trading",
            **metrics
        )
    
    def compare_strategies(self, rl_result: StrategyResult, 
                          benchmark_results: List[StrategyResult],
                          save_path: Optional[str] = None) -> str:
        """
        Create comprehensive strategy comparison visualization.
        
        Args:
            rl_result: RL strategy results
            benchmark_results: List of benchmark strategy results
            save_path: Path to save the comparison plot
            
        Returns:
            Path to saved plot
        """
        all_strategies = [rl_result] + benchmark_results
        
        # Prepare data for plotting
        metrics = ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate', 
                  'volatility', 'calmar_ratio', 'profit_factor']
        
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        fig.suptitle('Strategy Performance Comparison', fontsize=16)
        axes = axes.flatten()
        
        for i, metric in enumerate(metrics):
            if i >= len(axes):
                break
                
            values = [getattr(strategy, metric) for strategy in all_strategies]
            names = [strategy.name for strategy in all_strategies]
            
            # Color RL strategy differently
            colors = ['red' if name == rl_result.name else 'blue' for name in names]
            
            bars = axes[i].bar(names, values, color=colors, alpha=0.7)
            axes[i].set_title(metric.replace('_', ' ').title())
            axes[i].set_ylabel(metric.replace('_', ' ').title())
            axes[i].tick_params(axis='x', rotation=45)
            axes[i].grid(True, alpha=0.3)
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                axes[i].text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:.3f}', ha='center', va='bottom')
        
        # Hide unused subplot
        if len(metrics) < len(axes):
            axes[-1].set_visible(False)
        
        plt.tight_layout()
        
        if save_path is None:
            save_path = self.output_dir / f"strategy_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
    
    def create_performance_ranking(self, rl_result: StrategyResult,
                                 benchmark_results: List[StrategyResult],
                                 save_path: Optional[str] = None) -> str:
        """
        Create performance ranking table and visualization.
        
        Args:
            rl_result: RL strategy results
            benchmark_results: List of benchmark strategy results
            save_path: Path to save the ranking
            
        Returns:
            Path to saved ranking
        """
        all_strategies = [rl_result] + benchmark_results
        
        # Create ranking DataFrame
        ranking_data = []
        for strategy in all_strategies:
            ranking_data.append({
                'Strategy': strategy.name,
                'Total Return': strategy.total_return,
                'Sharpe Ratio': strategy.sharpe_ratio,
                'Max Drawdown': strategy.max_drawdown,
                'Win Rate': strategy.win_rate,
                'Calmar Ratio': strategy.calmar_ratio,
                'Profit Factor': strategy.profit_factor
            })
        
        df = pd.DataFrame(ranking_data)
        
        # Calculate composite score (higher is better)
        df['Composite Score'] = (
            df['Total Return'] * 0.3 +
            df['Sharpe Ratio'] * 0.25 +
            abs(df['Max Drawdown']) * -0.2 +  # Negative because lower is better
            df['Win Rate'] * 0.15 +
            df['Calmar Ratio'] * 0.1
        )
        
        # Sort by composite score
        df_sorted = df.sort_values('Composite Score', ascending=False)
        
        # Create visualization
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        fig.suptitle('Strategy Performance Ranking', fontsize=16)
        
        # Composite score ranking
        colors = ['red' if name == rl_result.name else 'blue' for name in df_sorted['Strategy']]
        bars = ax1.barh(df_sorted['Strategy'], df_sorted['Composite Score'], color=colors, alpha=0.7)
        ax1.set_xlabel('Composite Score')
        ax1.set_title('Overall Performance Ranking')
        ax1.grid(True, alpha=0.3)
        
        # Add score labels
        for bar, score in zip(bars, df_sorted['Composite Score']):
            width = bar.get_width()
            ax1.text(width, bar.get_y() + bar.get_height()/2.,
                    f'{score:.3f}', ha='left', va='center')
        
        # Risk-return scatter plot
        ax2.scatter(df['Max Drawdown'], df['Total Return'], 
                   c=['red' if name == rl_result.name else 'blue' for name in df['Strategy']],
                   s=100, alpha=0.7)
        
        for i, strategy in enumerate(df['Strategy']):
            ax2.annotate(strategy, (df['Max Drawdown'].iloc[i], df['Total Return'].iloc[i]),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        ax2.set_xlabel('Max Drawdown')
        ax2.set_ylabel('Total Return')
        ax2.set_title('Risk-Return Profile')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path is None:
            save_path = self.output_dir / f"performance_ranking_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        # Save ranking table as CSV
        csv_path = save_path.with_suffix('.csv')
        df_sorted.to_csv(csv_path, index=False)
        
        return str(save_path)