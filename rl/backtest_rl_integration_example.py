#!/usr/bin/env python3
# backtest_rl_integration_example.py
# 展示如何将 RL 回测器集成到现有 backtest_money_quick.py 系统中

import sys
import os
import argparse
import pandas as pd
import numpy as np
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import existing backtest components
from backtest_money_quick import (
    load_data_from_sqlite, parse_time_input, load_chushou_config,
    is_good_time_to_trade, finalize_and_report, format_beijing_time,
    run_quick_backtest, run_backtest_original
)

# Import RL components
from rl.rl_backtester import RLBacktester, create_rl_backtester

try:
    from model_utils_815 import calculate_features, get_coin_config
    from analyze_money import analyze_and_plot_results
except ImportError:
    print("警告: 无法导入某些依赖模块")
    def calculate_features(df, timeframe): return df
    def get_coin_config(coin): return {'model_basename': f"{coin.lower()}_5m", 'api_symbol': coin.upper()}
    def analyze_and_plot_results(df, basename): print(f"分析结果: {basename}")


def run_rl_enhanced_backtest(model_file, config_file, df, rl_agent_path=None, **kwargs):
    """
    增强版回测：支持传统模式和 RL 模式
    
    Args:
        model_file: 预测模型文件路径
        config_file: 模型配置文件路径
        df: 历史数据 DataFrame
        rl_agent_path: RL 代理路径（如果提供则使用 RL 模式）
        **kwargs: 其他回测参数
    """
    
    if rl_agent_path and os.path.exists(f"{rl_agent_path}.pth"):
        # RL 模式
        print("=== 🤖 RL 强化学习回测模式 ===")
        return _run_rl_mode_backtest(model_file, config_file, df, rl_agent_path, **kwargs)
    else:
        # 传统模式
        print("=== 📊 传统规则回测模式 ===")
        if rl_agent_path:
            print(f"⚠️ RL 代理文件不存在: {rl_agent_path}.pth，使用传统模式")
        
        # 使用原有的快速回测
        return run_quick_backtest(model_file, config_file, df, **kwargs)


def _run_rl_mode_backtest(model_file, config_file, df, rl_agent_path, **kwargs):
    """RL 模式回测实现"""
    
    # 创建 RL 回测器
    rl_backtester = create_rl_backtester(
        model_file=model_file,
        config_file=config_file,
        rl_agent_path=rl_agent_path,
        initial_capital=kwargs['initial_capital'],
        risk_per_trade_pct=kwargs['risk_per_trade_pct'],
        price_multiplier=kwargs.get('price_multiplier', 1.0),
        stop_loss_pct=kwargs.get('stop_loss_pct'),
        rl_config=kwargs.get('rl_config', {
            'initial_capital': kwargs['initial_capital'],
            'transaction_cost': 0.001,
            'max_position_size': 0.1,
            'max_positions': 5,
            'max_drawdown_limit': 0.2
        })
    )
    
    if not rl_backtester.rl_agent:
        print("❌ RL 代理加载失败，回退到传统模式")
        return run_quick_backtest(model_file, config_file, df, **kwargs)
    
    # 加载出手时间配置
    time_filter = load_chushou_config(kwargs.get('chushou_file', 'chushou.json'))
    
    # 预计算特征
    print("正在预先计算所有时间序列的特征，请稍候...")
    features_df = calculate_features(df, timeframe=rl_backtester.config['timeframe_minutes'])
    print("特征计算完成。")
    
    # 找到有效数据起始点
    valid_features_df = features_df.dropna(subset=rl_backtester.config['feature_list'])
    if valid_features_df.empty:
        print("❌ 计算特征后没有剩下任何有效数据行，无法进行回测。")
        return
    
    first_valid_index_pos = df.index.get_loc(valid_features_df.index[0])
    print(f"\n从索引 {first_valid_index_pos} 开始预测 (时间: {format_beijing_time(valid_features_df.index[0])})")
    print(f"将同时保持最多 {kwargs['max_active_predictions']} 笔活跃的投资。")
    print(f"🤖 使用 RL 代理进行智能交易决策")
    
    # 回测主循环
    skipped_predictions = 0
    rl_decisions_made = 0
    rl_entries = 0
    
    for i in range(first_valid_index_pos, len(df)):
        current_timestamp = df.index[i]
        current_price = df.iloc[i]['close']
        
        # 检查现有预测状态
        rl_backtester.check_predictions(current_price, current_timestamp, i)
        
        # 检查是否可以进行新预测
        active_count = len([p for p in rl_backtester.active_predictions.values() 
                           if p['status'] == 'active'])
        
        if active_count < kwargs['max_active_predictions']:
            # 获取当前特征
            if current_timestamp in features_df.index:
                latest_features_series = features_df.loc[current_timestamp]
                
                # 使用现有模型预测
                guess, probability, pred_price = rl_backtester.make_prediction_from_features(latest_features_series)
                
                if guess is not None:
                    rl_decisions_made += 1
                    
                    # 检查交易时间
                    if is_good_time_to_trade(current_timestamp, time_filter):
                        # 记录进场前的活跃预测数
                        before_count = len(rl_backtester.active_predictions)
                        
                        # RL 代理决策（内部会调用 make_rl_trading_decision）
                        rl_backtester.add_prediction(guess, probability, pred_price, current_timestamp, i)
                        
                        # 检查是否真的进场了
                        after_count = len(rl_backtester.active_predictions)
                        if after_count > before_count:
                            rl_entries += 1
                    else:
                        skipped_predictions += 1
    
    # 结束所有活跃预测
    final_timestamp, final_price, final_idx = df.index[-1], df.iloc[-1]['close'], len(df) - 1
    for pred_id in list(rl_backtester.active_predictions.keys()):
        rl_backtester.complete_prediction(pred_id, -1, final_price, final_timestamp, final_idx, "数据结束-超时")
    
    # 打印 RL 特有统计
    print(f"\n🤖 RL 决策统计:")
    print(f"  模型信号总数: {rl_decisions_made}")
    print(f"  RL 实际进场: {rl_entries}")
    print(f"  RL 进场率: {rl_entries/rl_decisions_made*100:.1f}%" if rl_decisions_made > 0 else "  RL 进场率: 0%")
    
    if time_filter and skipped_predictions > 0:
        print(f"  ⏰ 时间过滤跳过: {skipped_predictions}")
    
    # 打印详细 RL 统计
    rl_backtester.print_rl_summary()
    
    # 保存 RL 决策历史
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    rl_backtester.save_rl_decisions(f"rl_decisions_{timestamp}.csv")
    
    # 使用原有的报告函数
    finalize_and_report(rl_backtester, kwargs['initial_capital'], kwargs.get('stop_loss_pct'))
    
    return rl_backtester


def compare_rl_vs_traditional(model_file, config_file, df, rl_agent_path, **kwargs):
    """
    对比 RL 模式和传统模式的回测结果
    """
    print("\n" + "="*80)
    print("🔄 开始对比 RL 模式 vs 传统模式")
    print("="*80)
    
    # 保存原始参数
    original_kwargs = kwargs.copy()
    
    # 1. 运行传统模式
    print("\n1️⃣ 运行传统模式回测...")
    traditional_results = run_quick_backtest(model_file, config_file, df.copy(), **original_kwargs)
    
    # 获取传统模式结果
    traditional_capital = traditional_results.current_capital if hasattr(traditional_results, 'current_capital') else original_kwargs['initial_capital']
    traditional_predictions = traditional_results.total_predictions if hasattr(traditional_results, 'total_predictions') else 0
    traditional_successful = traditional_results.successful_predictions if hasattr(traditional_results, 'successful_predictions') else 0
    
    print(f"\n📊 传统模式结果:")
    print(f"  最终资金: ${traditional_capital:,.2f}")
    print(f"  总预测数: {traditional_predictions}")
    print(f"  成功预测: {traditional_successful}")
    print(f"  成功率: {traditional_successful/traditional_predictions*100:.1f}%" if traditional_predictions > 0 else "  成功率: 0%")
    
    # 2. 运行 RL 模式
    print(f"\n2️⃣ 运行 RL 模式回测...")
    rl_backtester = _run_rl_mode_backtest(model_file, config_file, df.copy(), rl_agent_path, **original_kwargs)
    
    if rl_backtester and rl_backtester.rl_agent:
        print(f"\n🤖 RL 模式结果:")
        print(f"  最终资金: ${rl_backtester.current_capital:,.2f}")
        print(f"  总预测数: {rl_backtester.total_predictions}")
        print(f"  成功预测: {rl_backtester.successful_predictions}")
        print(f"  成功率: {rl_backtester.successful_predictions/rl_backtester.total_predictions*100:.1f}%" if rl_backtester.total_predictions > 0 else "  成功率: 0%")
        
        # 3. 对比分析
        print(f"\n📈 对比分析:")
        capital_diff = rl_backtester.current_capital - traditional_capital
        capital_diff_pct = capital_diff / original_kwargs['initial_capital'] * 100
        
        print(f"  资金差异: ${capital_diff:+,.2f} ({capital_diff_pct:+.2f}%)")
        
        if capital_diff > 0:
            print(f"  🎉 RL 模式表现更好！")
        elif capital_diff < 0:
            print(f"  📉 传统模式表现更好")
        else:
            print(f"  🤝 两种模式表现相当")
        
        # RL 特有优势分析
        rl_stats = rl_backtester.get_rl_statistics()
        if rl_stats['total_rl_decisions'] > 0:
            print(f"\n🧠 RL 智能决策分析:")
            print(f"  决策选择性: {rl_stats['rl_skip_rate']*100:.1f}% 信号被跳过")
            print(f"  平均仓位: {rl_stats['avg_rl_position_size']*100:.2f}%")
            print(f"  动态止损: {rl_stats['avg_rl_stop_loss']*100:.2f}%")
            print(f"  动态止盈: {rl_stats['avg_rl_take_profit']*100:.2f}%")
    
    print("\n" + "="*80)
    print("✅ 对比分析完成")
    print("="*80)


def main():
    """主函数 - 演示 RL 集成"""
    parser = argparse.ArgumentParser(description="RL 回测集成示例")
    
    # 基础参数
    parser.add_argument("--coin", default="ETH", help="交易对")
    parser.add_argument("--interval", default="5m", help="K线间隔")
    parser.add_argument("--market", default="spot", help="市场类型")
    parser.add_argument("--db", default="coin_data.db", help="数据库路径")
    
    # 时间参数
    parser.add_argument("--start-time", help="开始时间")
    parser.add_argument("--end-time", help="结束时间")
    
    # 回测参数
    parser.add_argument("--initial-capital", type=float, default=10000, help="初始资金")
    parser.add_argument("--risk-per-trade", type=float, default=1.0, help="单次风险比例")
    parser.add_argument("--max-active-predictions", type=int, default=1000, help="最大活跃预测数")
    
    # 模型参数
    parser.add_argument("--model-file", help="预测模型文件")
    parser.add_argument("--config-file", help="模型配置文件")
    parser.add_argument("--rl-agent", help="RL 代理路径")
    
    # 模式选择
    parser.add_argument("--mode", choices=["rl", "traditional", "compare"], default="rl",
                       help="回测模式: rl=RL模式, traditional=传统模式, compare=对比模式")
    
    # 其他选项
    parser.add_argument("--chushou-file", default="chushou.json", help="出手时间配置")
    parser.add_argument("--use-chushou", action='store_true', help="启用时间过滤")
    
    args = parser.parse_args()
    
    # 解析时间
    start_time = parse_time_input(args.start_time) if args.start_time else None
    end_time = parse_time_input(args.end_time) if args.end_time else None
    
    # 获取币种配置
    coin_config = get_coin_config(args.coin)
    
    # 确定模型文件
    model_file = args.model_file or f"models/{coin_config['model_basename']}_model.joblib"
    config_file = args.config_file or f"models/{coin_config['model_basename']}_config.json"
    
    # 检查文件
    if not os.path.exists(model_file):
        print(f"❌ 模型文件不存在: {model_file}")
        return
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return
    
    # 加载数据
    print(f"📊 加载数据: {args.coin} {args.interval}")
    df = load_data_from_sqlite(
        args.db, coin_config['api_symbol'], args.interval, args.market,
        start_time=start_time, end_time=end_time
    )
    
    if df is None or df.empty:
        print("❌ 数据加载失败")
        return
    
    print(f"✅ 加载了 {len(df)} 条数据")
    
    # 准备参数
    backtest_params = {
        'initial_capital': args.initial_capital,
        'risk_per_trade_pct': args.risk_per_trade,
        'price_multiplier': 1.0,
        'stop_loss_pct': None,
        'max_active_predictions': args.max_active_predictions,
        'chushou_file': args.chushou_file if args.use_chushou else None
    }
    
    # 根据模式运行
    if args.mode == "traditional":
        # 传统模式
        run_quick_backtest(model_file, config_file, df, **backtest_params)
        
    elif args.mode == "rl":
        # RL 模式
        if not args.rl_agent:
            print("❌ RL 模式需要指定 --rl-agent 参数")
            return
        
        run_rl_enhanced_backtest(model_file, config_file, df, args.rl_agent, **backtest_params)
        
    elif args.mode == "compare":
        # 对比模式
        if not args.rl_agent:
            print("❌ 对比模式需要指定 --rl-agent 参数")
            return
        
        compare_rl_vs_traditional(model_file, config_file, df, args.rl_agent, **backtest_params)
    
    print(f"\n🎯 {args.mode.upper()} 模式回测完成！")


if __name__ == "__main__":
    main()