"""
Policy Network for RL Trading Agent

This module implements a simple policy network using PyTorch for the RL trading optimization system.
The network takes market state as input and outputs trading actions including position size,
stop loss, take profit, hold time, and entry decision.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import json
import os
from typing import Dict, Tuple, Optional


class PolicyNetwork(nn.Module):
    """
    Simple policy network for trading decisions.
    
    Architecture: Input -> Shared layers -> Multiple output heads
    - Shared feature extraction layers
    - Separate heads for different action types
    - Supports both continuous and discrete actions
    """
    
    def __init__(self, state_dim: int, hidden_dim: int = 256, dropout_rate: float = 0.1):
        """
        Initialize the policy network.
        
        Args:
            state_dim: Dimension of the input state
            hidden_dim: Size of hidden layers
            dropout_rate: Dropout rate for regularization
        """
        super(PolicyNetwork, self).__init__()
        
        self.state_dim = state_dim
        self.hidden_dim = hidden_dim
        self.dropout_rate = dropout_rate
        
        # Shared feature extraction layers
        self.shared = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim // 2, 128),
            nn.ReLU()
        )
        
        # Output heads for different action types
        # Position size: continuous [0, 0.1] (0-10% of capital)
        self.position_size_head = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 1),
            nn.Sigmoid()  # Output [0, 1], will be scaled to [0, 0.1]
        )
        
        # Stop loss percentage: continuous [0.01, 0.05] (1-5%)
        self.stop_loss_head = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 1),
            nn.Sigmoid()  # Output [0, 1], will be scaled to [0.01, 0.05]
        )
        
        # Take profit percentage: continuous [0.01, 0.04] (1-4%)
        self.take_profit_head = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 1),
            nn.Sigmoid()  # Output [0, 1], will be scaled to [0.01, 0.04]
        )
        
        # Max hold time: continuous [60, 240] minutes
        self.hold_time_head = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 1),
            nn.Sigmoid()  # Output [0, 1], will be scaled to [60, 240]
        )
        
        # Enter trade decision: discrete [0, 1] (no/yes)
        self.enter_trade_head = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 2)  # Logits for binary classification
        )
        
        # Initialize network parameters
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize network weights using Xavier initialization."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def forward(self, state: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass through the network.
        
        Args:
            state: Input state tensor of shape (batch_size, state_dim)
            
        Returns:
            Dictionary containing raw outputs for each action type
        """
        # Extract shared features
        shared_features = self.shared(state)
        
        # Get outputs from each head
        outputs = {
            'position_size_raw': self.position_size_head(shared_features),
            'stop_loss_raw': self.stop_loss_head(shared_features),
            'take_profit_raw': self.take_profit_head(shared_features),
            'hold_time_raw': self.hold_time_head(shared_features),
            'enter_trade_logits': self.enter_trade_head(shared_features)
        }
        
        return outputs
    
    def get_actions(self, state: torch.Tensor, deterministic: bool = False) -> Dict[str, torch.Tensor]:
        """
        Get scaled actions from network outputs.
        
        Args:
            state: Input state tensor
            deterministic: If True, use deterministic actions (no sampling)
            
        Returns:
            Dictionary containing scaled actions ready for environment
        """
        with torch.no_grad() if deterministic else torch.enable_grad():
            raw_outputs = self.forward(state)
            
            # Scale continuous actions to appropriate ranges
            actions = {
                'position_size': raw_outputs['position_size_raw'] * 0.1,  # [0, 0.1]
                'stop_loss_pct': raw_outputs['stop_loss_raw'] * 0.04 + 0.01,  # [0.01, 0.05]
                'take_profit_pct': raw_outputs['take_profit_raw'] * 0.03 + 0.01,  # [0.01, 0.04]
                'max_hold_time': raw_outputs['hold_time_raw'] * 180 + 60,  # [60, 240]
            }
            
            # Handle discrete action (enter trade)
            if deterministic:
                # Use argmax for deterministic action
                actions['enter_trade'] = torch.argmax(raw_outputs['enter_trade_logits'], dim=-1)
            else:
                # Sample from categorical distribution
                probs = F.softmax(raw_outputs['enter_trade_logits'], dim=-1)
                actions['enter_trade'] = torch.multinomial(probs, 1).squeeze(-1)
            
            return actions
    
    def sample_action(self, state: torch.Tensor) -> Tuple[Dict[str, float], Dict[str, torch.Tensor]]:
        """
        Sample an action and return both the action and log probabilities.
        
        Args:
            state: Input state tensor of shape (1, state_dim) for single state
            
        Returns:
            Tuple of (action_dict, log_probs_dict) where:
            - action_dict: Dictionary with scalar action values
            - log_probs_dict: Dictionary with log probabilities for each action
        """
        raw_outputs = self.forward(state)
        
        # For continuous actions, we'll use the raw outputs as means of normal distributions
        # with fixed standard deviation for exploration
        std = 0.1  # Fixed exploration noise
        
        # Sample continuous actions with noise
        position_size_noise = torch.normal(0, std, raw_outputs['position_size_raw'].shape)
        stop_loss_noise = torch.normal(0, std, raw_outputs['stop_loss_raw'].shape)
        take_profit_noise = torch.normal(0, std, raw_outputs['take_profit_raw'].shape)
        hold_time_noise = torch.normal(0, std, raw_outputs['hold_time_raw'].shape)
        
        # Add noise and clamp to valid ranges
        position_size_raw = torch.clamp(raw_outputs['position_size_raw'] + position_size_noise, 0, 1)
        stop_loss_raw = torch.clamp(raw_outputs['stop_loss_raw'] + stop_loss_noise, 0, 1)
        take_profit_raw = torch.clamp(raw_outputs['take_profit_raw'] + take_profit_noise, 0, 1)
        hold_time_raw = torch.clamp(raw_outputs['hold_time_raw'] + hold_time_noise, 0, 1)
        
        # Scale to final ranges
        position_size = position_size_raw * 0.1
        stop_loss_pct = stop_loss_raw * 0.04 + 0.01
        take_profit_pct = take_profit_raw * 0.03 + 0.01
        max_hold_time = hold_time_raw * 180 + 60
        
        # Sample discrete action
        enter_trade_probs = F.softmax(raw_outputs['enter_trade_logits'], dim=-1)
        enter_trade = torch.multinomial(enter_trade_probs, 1).squeeze(-1)
        
        # Calculate log probabilities (simplified for continuous actions)
        log_probs = {
            'position_size': -0.5 * ((position_size_raw - raw_outputs['position_size_raw']) / std) ** 2,
            'stop_loss_pct': -0.5 * ((stop_loss_raw - raw_outputs['stop_loss_raw']) / std) ** 2,
            'take_profit_pct': -0.5 * ((take_profit_raw - raw_outputs['take_profit_raw']) / std) ** 2,
            'max_hold_time': -0.5 * ((hold_time_raw - raw_outputs['hold_time_raw']) / std) ** 2,
            'enter_trade': torch.log(enter_trade_probs.gather(1, enter_trade.unsqueeze(1))).squeeze(1)
        }
        
        # Convert to scalar values for action dict
        actions = {
            'position_size': position_size.item(),
            'stop_loss_pct': stop_loss_pct.item(),
            'take_profit_pct': take_profit_pct.item(),
            'max_hold_time': int(max_hold_time.item()),
            'enter_trade': bool(enter_trade.item())
        }
        
        return actions, log_probs
    
    def get_log_prob(self, state: torch.Tensor, actions: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Calculate log probability of given actions under current policy.
        
        Args:
            state: Input state tensor
            actions: Dictionary of actions taken
            
        Returns:
            Total log probability of the actions
        """
        raw_outputs = self.forward(state)
        std = 0.1  # Same as in sample_action
        
        total_log_prob = torch.zeros(state.shape[0], device=state.device)
        
        # Log prob for continuous actions (assuming normal distribution)
        if 'position_size' in actions:
            # Convert back to raw scale
            pos_size_raw = actions['position_size'] / 0.1
            mean = raw_outputs['position_size_raw']
            log_prob = -0.5 * ((pos_size_raw - mean) / std) ** 2 - np.log(std * np.sqrt(2 * np.pi))
            total_log_prob += log_prob.squeeze()
        
        # Log prob for discrete action
        if 'enter_trade' in actions:
            probs = F.softmax(raw_outputs['enter_trade_logits'], dim=-1)
            log_prob = torch.log(probs.gather(1, actions['enter_trade'].unsqueeze(1))).squeeze(1)
            total_log_prob += log_prob
        
        return total_log_prob
    
    def save_model(self, filepath: str, metadata: Optional[Dict] = None):
        """
        Save the model state and configuration.
        
        Args:
            filepath: Path to save the model (without extension)
            metadata: Optional metadata to save with the model
        """
        # Save model state
        model_path = f"{filepath}.pth"
        torch.save({
            'state_dict': self.state_dict(),
            'state_dim': self.state_dim,
            'hidden_dim': self.hidden_dim,
            'dropout_rate': self.dropout_rate,
            'metadata': metadata or {}
        }, model_path)
        
        # Save configuration as JSON
        config_path = f"{filepath}_config.json"
        config = {
            'state_dim': self.state_dim,
            'hidden_dim': self.hidden_dim,
            'dropout_rate': self.dropout_rate,
            'model_path': model_path,
            'metadata': metadata or {}
        }
        
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"Model saved to {model_path}")
        print(f"Config saved to {config_path}")
    
    @classmethod
    def load_model(cls, filepath: str) -> 'PolicyNetwork':
        """
        Load a saved model.
        
        Args:
            filepath: Path to the saved model (without extension)
            
        Returns:
            Loaded PolicyNetwork instance
        """
        model_path = f"{filepath}.pth"
        
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        # Load model data
        checkpoint = torch.load(model_path, map_location='cpu')
        
        # Create model instance
        model = cls(
            state_dim=checkpoint['state_dim'],
            hidden_dim=checkpoint['hidden_dim'],
            dropout_rate=checkpoint['dropout_rate']
        )
        
        # Load state dict
        model.load_state_dict(checkpoint['state_dict'])
        
        print(f"Model loaded from {model_path}")
        if 'metadata' in checkpoint and checkpoint['metadata']:
            print(f"Metadata: {checkpoint['metadata']}")
        
        return model
    
    def get_model_info(self) -> Dict:
        """
        Get information about the model architecture and parameters.
        
        Returns:
            Dictionary containing model information
        """
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'state_dim': self.state_dim,
            'hidden_dim': self.hidden_dim,
            'dropout_rate': self.dropout_rate,
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024),  # Assuming float32
            'architecture': {
                'shared_layers': 3,
                'output_heads': 5,
                'activation': 'ReLU',
                'regularization': 'Dropout'
            }
        }


def create_default_policy_network(state_dim: int) -> PolicyNetwork:
    """
    Create a policy network with default parameters.
    
    Args:
        state_dim: Dimension of the input state
        
    Returns:
        PolicyNetwork instance with default configuration
    """
    return PolicyNetwork(
        state_dim=state_dim,
        hidden_dim=256,
        dropout_rate=0.1
    )


if __name__ == "__main__":
    # Example usage and testing
    print("Testing PolicyNetwork...")
    
    # Create a sample network
    state_dim = 20  # Example state dimension
    network = create_default_policy_network(state_dim)
    
    print(f"Created network with {network.get_model_info()['total_parameters']} parameters")
    
    # Test forward pass
    batch_size = 4
    sample_state = torch.randn(batch_size, state_dim)
    
    # Test forward pass
    outputs = network.forward(sample_state)
    print(f"Forward pass output shapes:")
    for key, value in outputs.items():
        print(f"  {key}: {value.shape}")
    
    # Test action sampling
    single_state = torch.randn(1, state_dim)
    actions, log_probs = network.sample_action(single_state)
    print(f"\nSampled actions: {actions}")
    
    # Test deterministic actions
    det_actions = network.get_actions(single_state, deterministic=True)
    print(f"Deterministic actions:")
    for key, value in det_actions.items():
        print(f"  {key}: {value.item() if hasattr(value, 'item') else value}")
    
    # Test save/load
    test_path = "/tmp/test_policy_network"
    network.save_model(test_path, metadata={'test': True})
    
    loaded_network = PolicyNetwork.load_model(test_path)
    print(f"Successfully loaded network")
    
    # Clean up test files
    import os
    try:
        os.remove(f"{test_path}.pth")
        os.remove(f"{test_path}_config.json")
        print("Test files cleaned up")
    except:
        pass
    
    print("PolicyNetwork testing completed!")