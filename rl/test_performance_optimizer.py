"""
Unit tests for performance optimization utilities.
"""

import unittest
import numpy as np
import torch
import torch.nn as nn
import tempfile
import os
from unittest.mock import patch, MagicMock
import psutil

from .performance_optimizer import (
    MemoryOptimizer, TrainingSpeedOptimizer, DataOptimizer,
    PerformanceProfiler, PerformanceMetrics, setup_performance_optimizations
)


class TestMemoryOptimizer(unittest.TestCase):
    """Test MemoryOptimizer functionality"""
    
    def setUp(self):
        self.optimizer = MemoryOptimizer()
    
    def test_get_memory_usage(self):
        """Test memory usage statistics"""
        stats = self.optimizer.get_memory_usage()
        
        # Check required fields
        self.assertIn('rss_mb', stats)
        self.assertIn('vms_mb', stats)
        self.assertIn('cpu_percent', stats)
        
        # Check values are reasonable
        self.assertGreater(stats['rss_mb'], 0)
        self.assertGreater(stats['vms_mb'], 0)
        self.assertGreaterEqual(stats['cpu_percent'], 0)
    
    def test_optimize_tensor_memory(self):
        """Test tensor memory optimization"""
        # Test float32 tensor without gradients
        tensor_f32 = torch.randn(100, 100, dtype=torch.float32, requires_grad=False)
        optimized = self.optimizer.optimize_tensor_memory(tensor_f32)
        self.assertEqual(optimized.dtype, torch.float16)
        
        # Test tensor with gradients (should not be optimized)
        tensor_grad = torch.randn(100, 100, dtype=torch.float32, requires_grad=True)
        optimized_grad = self.optimizer.optimize_tensor_memory(tensor_grad)
        self.assertEqual(optimized_grad.dtype, torch.float32)
    
    def test_memory_monitor(self):
        """Test memory monitoring context manager"""
        with patch.object(self.optimizer, 'get_memory_usage') as mock_memory:
            mock_memory.side_effect = [
                {'rss_mb': 100.0, 'vms_mb': 200.0, 'cpu_percent': 10.0},
                {'rss_mb': 120.0, 'vms_mb': 220.0, 'cpu_percent': 15.0}
            ]
            
            with self.optimizer.memory_monitor("test_operation"):
                # Simulate some work
                pass
            
            # Should have called get_memory_usage twice
            self.assertEqual(mock_memory.call_count, 2)
    
    def test_clear_memory(self):
        """Test memory clearing functionality"""
        # This should not raise any exceptions
        self.optimizer.clear_memory()


class TestTrainingSpeedOptimizer(unittest.TestCase):
    """Test TrainingSpeedOptimizer functionality"""
    
    def setUp(self):
        self.optimizer = TrainingSpeedOptimizer()
    
    def test_optimize_dataloader_workers(self):
        """Test dataloader worker optimization"""
        # Test with small dataset
        workers = self.optimizer.optimize_dataloader_workers(50)
        self.assertGreaterEqual(workers, 1)
        self.assertLessEqual(workers, 8)
        
        # Test with large dataset
        workers_large = self.optimizer.optimize_dataloader_workers(10000)
        self.assertGreaterEqual(workers_large, 1)
    
    def test_setup_torch_optimizations(self):
        """Test PyTorch optimization setup"""
        # This should not raise any exceptions
        self.optimizer.setup_torch_optimizations()
        
        # Check that thread count is set
        self.assertGreater(torch.get_num_threads(), 0)
    
    def test_batch_size_finder(self):
        """Test batch size optimization"""
        # Create a simple model
        model = nn.Sequential(
            nn.Linear(10, 50),
            nn.ReLU(),
            nn.Linear(50, 1)
        )
        
        # Test batch size finding
        batch_size = self.optimizer.batch_size_finder(
            model, (10,), max_memory_mb=1000
        )
        
        self.assertGreaterEqual(batch_size, 1)
        self.assertLessEqual(batch_size, 512)


class TestDataOptimizer(unittest.TestCase):
    """Test DataOptimizer functionality"""
    
    def setUp(self):
        self.optimizer = DataOptimizer()
    
    def test_optimize_numpy_arrays(self):
        """Test numpy array optimization"""
        # Create test arrays with suboptimal dtypes
        arr_f64 = np.random.randn(100, 10).astype(np.float64)
        arr_i64 = np.arange(100).astype(np.int64)
        
        optimized = self.optimizer.optimize_numpy_arrays([arr_f64, arr_i64])
        
        # Check dtype optimization
        self.assertEqual(optimized[0].dtype, np.float32)
        self.assertEqual(optimized[1].dtype, np.int32)
        
        # Check C-contiguous
        for arr in optimized:
            self.assertTrue(arr.flags['C_CONTIGUOUS'])
    
    def test_create_memory_mapped_dataset(self):
        """Test memory-mapped dataset creation"""
        data = np.random.randn(1000, 10).astype(np.float32)
        
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            try:
                memmap = self.optimizer.create_memory_mapped_dataset(data, tmp.name)
                
                # Check that memmap has same shape and dtype
                self.assertEqual(memmap.shape, data.shape)
                self.assertEqual(memmap.dtype, data.dtype)
                
                # Check that data is preserved
                np.testing.assert_array_equal(memmap, data)
                
            finally:
                if os.path.exists(tmp.name):
                    os.unlink(tmp.name)
    
    def test_batch_generator(self):
        """Test memory-efficient batch generator"""
        data = np.arange(100).reshape(100, 1)
        batch_size = 10
        
        batches = list(self.optimizer.batch_generator(data, batch_size, shuffle=False))
        
        # Check number of batches
        expected_batches = len(data) // batch_size
        self.assertEqual(len(batches), expected_batches)
        
        # Check batch sizes
        for i, batch in enumerate(batches[:-1]):
            self.assertEqual(len(batch), batch_size)
        
        # Test with shuffle
        batches_shuffled = list(self.optimizer.batch_generator(data, batch_size, shuffle=True))
        self.assertEqual(len(batches_shuffled), expected_batches)


class TestPerformanceProfiler(unittest.TestCase):
    """Test PerformanceProfiler functionality"""
    
    def setUp(self):
        self.profiler = PerformanceProfiler()
    
    def test_profile_training_step(self):
        """Test training step profiling"""
        def dummy_step():
            # Simulate some work
            import time
            time.sleep(0.01)
            return "result"
        
        result, metrics = self.profiler.profile_training_step(dummy_step)
        
        # Check result
        self.assertEqual(result, "result")
        
        # Check metrics
        self.assertIsInstance(metrics, PerformanceMetrics)
        self.assertGreater(metrics.training_time_seconds, 0)
        self.assertGreater(metrics.episodes_per_second, 0)
    
    def test_get_performance_summary(self):
        """Test performance summary generation"""
        # Add some dummy metrics
        for i in range(5):
            metrics = PerformanceMetrics(
                memory_usage_mb=100.0 + i,
                cpu_usage_percent=50.0 + i,
                gpu_memory_mb=None,
                training_time_seconds=1.0,
                episodes_per_second=1.0
            )
            self.profiler.metrics_history.append(metrics)
        
        summary = self.profiler.get_performance_summary()
        
        # Check summary fields
        self.assertIn('avg_memory_mb', summary)
        self.assertIn('avg_cpu_percent', summary)
        self.assertIn('avg_training_time', summary)
        self.assertIn('total_episodes', summary)
        
        # Check values
        self.assertEqual(summary['total_episodes'], 5)
        self.assertEqual(summary['avg_memory_mb'], 102.0)  # 100, 101, 102, 103, 104
    
    def test_save_performance_report(self):
        """Test performance report saving"""
        # Add some metrics
        metrics = PerformanceMetrics(
            memory_usage_mb=100.0,
            cpu_usage_percent=50.0,
            gpu_memory_mb=None,
            training_time_seconds=1.0,
            episodes_per_second=1.0
        )
        self.profiler.metrics_history.append(metrics)
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as tmp:
            try:
                self.profiler.save_performance_report(tmp.name)
                
                # Check that file was created and has content
                self.assertTrue(os.path.exists(tmp.name))
                
                with open(tmp.name, 'r') as f:
                    content = f.read()
                    self.assertIn('Performance Report', content)
                    self.assertIn('avg_memory_mb', content)
                    
            finally:
                if os.path.exists(tmp.name):
                    os.unlink(tmp.name)


class TestPerformanceMetrics(unittest.TestCase):
    """Test PerformanceMetrics dataclass"""
    
    def test_performance_metrics_creation(self):
        """Test PerformanceMetrics creation and attributes"""
        metrics = PerformanceMetrics(
            memory_usage_mb=100.0,
            cpu_usage_percent=50.0,
            gpu_memory_mb=200.0,
            training_time_seconds=1.5,
            episodes_per_second=0.67
        )
        
        self.assertEqual(metrics.memory_usage_mb, 100.0)
        self.assertEqual(metrics.cpu_usage_percent, 50.0)
        self.assertEqual(metrics.gpu_memory_mb, 200.0)
        self.assertEqual(metrics.training_time_seconds, 1.5)
        self.assertEqual(metrics.episodes_per_second, 0.67)


class TestUtilityFunctions(unittest.TestCase):
    """Test utility functions"""
    
    def test_setup_performance_optimizations(self):
        """Test performance optimization setup"""
        optimizer, memory_optimizer = setup_performance_optimizations()
        
        self.assertIsInstance(optimizer, TrainingSpeedOptimizer)
        self.assertIsInstance(memory_optimizer, MemoryOptimizer)
    
    @patch('rl.performance_optimizer.MemoryOptimizer')
    def test_monitor_system_resources(self):
        """Test system resource monitoring"""
        from .performance_optimizer import monitor_system_resources
        
        # This should return a dictionary with resource information
        resources = monitor_system_resources()
        self.assertIsInstance(resources, dict)


if __name__ == '__main__':
    unittest.main()