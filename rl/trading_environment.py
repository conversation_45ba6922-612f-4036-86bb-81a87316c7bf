# trading_environment.py
# 强化学习交易环境 - 基于现有模型信号进行交易决策优化

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import json

@dataclass
class MarketState:
    """市场状态数据结构"""
    # 现有模型信号
    model_signal: int  # 0 or 1
    signal_confidence: float  # 0-1
    signal_probability: float  # 原始概率值
    
    # 市场基础信息
    current_price: float
    price_change_1h: float  # 1小时价格变化率
    price_change_4h: float  # 4小时价格变化率
    volatility_recent: float  # 最近波动率
    
    # 投资组合状态
    cash_ratio: float  # 现金比例 0-1
    position_count: int  # 当前持仓数
    unrealized_pnl: float  # 未实现盈亏
    recent_win_rate: float  # 最近10笔交易胜率
    consecutive_losses: int  # 连续亏损次数
    
    # 时间特征
    hour: int  # 0-23
    day_of_week: int  # 0-6
    is_good_trading_time: bool  # 基于chushou.json

@dataclass 
class TradingAction:
    """交易动作数据结构"""
    enter_trade: bool  # 是否进场
    position_size: float  # 仓位大小 0-0.1 (0-10%)
    stop_loss_pct: float  # 止损百分比 0.01-0.05
    take_profit_pct: float  # 止盈百分比 0.01-0.04
    max_hold_time: int  # 最大持仓时间(分钟) 60-240

class TradingEnvironment:
    """
    强化学习交易环境
    
    专注于基于现有模型信号的交易执行优化，不重新预测市场方向
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.initial_capital = config.get('initial_capital', 10000)
        self.current_capital = self.initial_capital
        self.transaction_cost = config.get('transaction_cost', 0.001)  # 0.1%手续费
        
        # 状态跟踪
        self.active_positions = {}
        self.completed_trades = []
        self.trade_history = []  # 最近交易历史，用于计算胜率等
        
        # 奖励函数参数
        self.reward_config = config.get('reward_config', {
            'pnl_weight': 1.0,
            'risk_penalty': 0.5,
            'time_efficiency': 0.1,
            'max_drawdown_penalty': 2.0
        })
        
        # 风险控制
        self.max_position_size = config.get('max_position_size', 0.1)  # 最大10%
        self.max_positions = config.get('max_positions', 5)
        self.max_drawdown_limit = config.get('max_drawdown_limit', 0.2)  # 20%
        
        print(f"交易环境初始化完成")
        print(f"初始资金: ${self.initial_capital:,.2f}")
        print(f"最大单笔仓位: {self.max_position_size*100:.1f}%")
        print(f"最大同时持仓: {self.max_positions}")
    
    def get_state_vector(self, market_state: MarketState) -> np.ndarray:
        """将市场状态转换为RL算法需要的向量格式"""
        state_vector = np.array([
            # 模型信号 (3个特征)
            float(market_state.model_signal),
            market_state.signal_confidence,
            market_state.signal_probability,
            
            # 市场状态 (4个特征)
            market_state.current_price / 10000.0,  # 归一化价格
            np.tanh(market_state.price_change_1h * 100),  # 归一化价格变化
            np.tanh(market_state.price_change_4h * 100),
            np.tanh(market_state.volatility_recent * 100),
            
            # 投资组合状态 (5个特征)
            market_state.cash_ratio,
            min(market_state.position_count / self.max_positions, 1.0),
            np.tanh(market_state.unrealized_pnl / self.initial_capital),
            market_state.recent_win_rate,
            min(market_state.consecutive_losses / 5.0, 1.0),  # 最多5次连亏
            
            # 时间特征 (3个特征)
            market_state.hour / 24.0,
            market_state.day_of_week / 7.0,
            float(market_state.is_good_trading_time)
        ])
        
        return state_vector
    
    def validate_action(self, action: TradingAction, market_state: MarketState) -> TradingAction:
        """验证和修正交易动作，确保符合风险控制要求"""
        # 限制仓位大小
        action.position_size = np.clip(action.position_size, 0.0, self.max_position_size)
        
        # 限制止损止盈范围
        action.stop_loss_pct = np.clip(action.stop_loss_pct, 0.01, 0.05)
        action.take_profit_pct = np.clip(action.take_profit_pct, 0.01, 0.04)
        
        # 限制持仓时间
        action.max_hold_time = int(np.clip(action.max_hold_time, 60, 240))
        
        # 检查是否超过最大持仓数
        if len(self.active_positions) >= self.max_positions:
            action.enter_trade = False
        
        # 检查现金是否足够
        required_capital = self.current_capital * action.position_size
        if required_capital > self.current_capital * market_state.cash_ratio:
            action.enter_trade = False
        
        # 检查是否有有效信号
        if market_state.model_signal is None:
            action.enter_trade = False
            
        return action
    
    def execute_action(self, action: TradingAction, market_state: MarketState, 
                      timestamp: pd.Timestamp, current_idx: int) -> Dict:
        """执行交易动作并返回结果"""
        result = {
            'trade_executed': False,
            'position_id': None,
            'capital_used': 0.0,
            'transaction_cost': 0.0
        }
        
        # 验证动作
        action = self.validate_action(action, market_state)
        
        if not action.enter_trade or action.position_size <= 0.001:
            return result
        
        # 计算交易资金
        trade_capital = self.current_capital * action.position_size
        cost = trade_capital * self.transaction_cost
        
        # 创建新仓位
        position_id = f"pos_{len(self.completed_trades) + len(self.active_positions):06d}"
        
        position = {
            'id': position_id,
            'signal': market_state.model_signal,
            'confidence': market_state.signal_confidence,
            'entry_price': market_state.current_price,
            'entry_time': timestamp,
            'entry_idx': current_idx,
            'capital': trade_capital,
            'stop_loss_pct': action.stop_loss_pct,
            'take_profit_pct': action.take_profit_pct,
            'max_hold_time': action.max_hold_time,
            'expire_idx': current_idx + action.max_hold_time,
            'status': 'active',
            'max_loss': 0.0,
            'transaction_cost': cost
        }
        
        # 计算目标价格
        if market_state.model_signal == 1:  # 看涨
            position['take_profit_price'] = market_state.current_price * (1 + action.take_profit_pct)
            position['stop_loss_price'] = market_state.current_price * (1 - action.stop_loss_pct)
        else:  # 看跌
            position['take_profit_price'] = market_state.current_price * (1 - action.take_profit_pct)
            position['stop_loss_price'] = market_state.current_price * (1 + action.stop_loss_pct)
        
        self.active_positions[position_id] = position
        self.current_capital -= cost  # 扣除手续费
        
        result.update({
            'trade_executed': True,
            'position_id': position_id,
            'capital_used': trade_capital,
            'transaction_cost': cost
        })
        
        return result
    
    def update_positions(self, current_price: float, timestamp: pd.Timestamp, 
                        current_idx: int) -> List[Dict]:
        """更新所有活跃仓位状态，返回已完成的交易"""
        completed_trades = []
        positions_to_remove = []
        
        for pos_id, position in self.active_positions.items():
            if position['status'] != 'active':
                continue
                
            # 计算当前盈亏
            if position['signal'] == 1:  # 看涨仓位
                pnl_pct = (current_price - position['entry_price']) / position['entry_price']
            else:  # 看跌仓位
                pnl_pct = (position['entry_price'] - current_price) / position['entry_price']
            
            current_pnl = position['capital'] * pnl_pct
            
            # 更新最大亏损
            if pnl_pct < 0:
                position['max_loss'] = min(position['max_loss'], pnl_pct)
            
            # 检查退出条件
            exit_reason = None
            
            # 止盈检查
            if position['signal'] == 1 and current_price >= position['take_profit_price']:
                exit_reason = 'take_profit'
            elif position['signal'] == 0 and current_price <= position['take_profit_price']:
                exit_reason = 'take_profit'
            
            # 止损检查
            elif position['signal'] == 1 and current_price <= position['stop_loss_price']:
                exit_reason = 'stop_loss'
            elif position['signal'] == 0 and current_price >= position['stop_loss_price']:
                exit_reason = 'stop_loss'
            
            # 超时检查
            elif current_idx >= position['expire_idx']:
                exit_reason = 'timeout'
            
            # 如果需要退出
            if exit_reason:
                # 计算最终盈亏
                final_pnl = current_pnl - position['transaction_cost']  # 扣除开仓手续费
                exit_cost = position['capital'] * self.transaction_cost  # 平仓手续费
                final_pnl -= exit_cost
                
                # 更新资金
                self.current_capital += position['capital'] + final_pnl
                
                # 记录完成的交易
                completed_trade = {
                    'position_id': pos_id,
                    'signal': position['signal'],
                    'confidence': position['confidence'],
                    'entry_price': position['entry_price'],
                    'exit_price': current_price,
                    'entry_time': position['entry_time'],
                    'exit_time': timestamp,
                    'hold_time_minutes': (current_idx - position['entry_idx']),
                    'capital': position['capital'],
                    'pnl': final_pnl,
                    'pnl_pct': final_pnl / position['capital'],
                    'max_loss_pct': position['max_loss'],
                    'exit_reason': exit_reason,
                    'total_cost': position['transaction_cost'] + exit_cost
                }
                
                completed_trades.append(completed_trade)
                self.completed_trades.append(completed_trade)
                positions_to_remove.append(pos_id)
                
                # 更新交易历史（保留最近20笔）
                self.trade_history.append(completed_trade)
                if len(self.trade_history) > 20:
                    self.trade_history.pop(0)
        
        # 移除已完成的仓位
        for pos_id in positions_to_remove:
            del self.active_positions[pos_id]
        
        return completed_trades
    
    def calculate_reward(self, completed_trades: List[Dict], market_state: MarketState) -> float:
        """计算奖励函数 - 使用多目标奖励系统"""
        if not completed_trades:
            return 0.0
        
        # 导入多目标奖励函数
        from .reward_functions import MultiObjectiveRewardFunction, RewardConfig
        
        # 如果还没有初始化奖励函数，则创建
        if not hasattr(self, 'reward_function'):
            reward_config = RewardConfig(
                pnl_weight=self.reward_config.get('pnl_weight', 1.0),
                risk_penalty_weight=self.reward_config.get('risk_penalty', 0.5),
                time_efficiency_weight=self.reward_config.get('time_efficiency', 0.1),
                drawdown_penalty_weight=self.reward_config.get('max_drawdown_penalty', 2.0)
            )
            self.reward_function = MultiObjectiveRewardFunction(reward_config)
        
        total_reward = 0.0
        
        for trade in completed_trades:
            # 准备市场状态数据
            market_state_dict = {
                'price_change_1h': market_state.price_change_1h,
                'price_change_4h': market_state.price_change_4h,
                'volatility_recent': market_state.volatility_recent
            }
            
            # 获取投资组合指标
            portfolio_metrics = self.get_portfolio_metrics()
            portfolio_metrics['current_drawdown'] = (self.initial_capital - self.current_capital) / self.initial_capital
            
            # 计算多目标奖励
            reward_breakdown = self.reward_function.calculate_reward(
                trade, market_state_dict, portfolio_metrics
            )
            
            total_reward += reward_breakdown['total_reward']
        
        return total_reward
    
    def get_portfolio_metrics(self) -> Dict:
        """获取投资组合指标"""
        if not self.trade_history:
            return {
                'recent_win_rate': 0.5,
                'consecutive_losses': 0,
                'avg_hold_time': 120,
                'total_trades': 0
            }
        
        # 计算最近胜率
        recent_wins = sum(1 for trade in self.trade_history[-10:] if trade['pnl'] > 0)
        recent_win_rate = recent_wins / min(len(self.trade_history), 10)
        
        # 计算连续亏损
        consecutive_losses = 0
        for trade in reversed(self.trade_history):
            if trade['pnl'] < 0:
                consecutive_losses += 1
            else:
                break
        
        # 平均持仓时间
        avg_hold_time = np.mean([trade['hold_time_minutes'] for trade in self.trade_history])
        
        return {
            'recent_win_rate': recent_win_rate,
            'consecutive_losses': consecutive_losses,
            'avg_hold_time': avg_hold_time,
            'total_trades': len(self.completed_trades),
            'current_capital': self.current_capital,
            'total_return': (self.current_capital - self.initial_capital) / self.initial_capital
        }
    
    def reset(self):
        """重置环境状态"""
        self.current_capital = self.initial_capital
        self.active_positions = {}
        self.completed_trades = []
        self.trade_history = []
    
    def get_state_dim(self) -> int:
        """返回状态向量维度"""
        return 15  # 根据get_state_vector中的特征数量
    
    def get_action_dim(self) -> int:
        """返回动作空间维度"""
        return 5  # enter_trade, position_size, stop_loss_pct, take_profit_pct, max_hold_time


if __name__ == "__main__":
    # 简单测试
    config = {
        'initial_capital': 10000,
        'transaction_cost': 0.001,
        'max_position_size': 0.05,
        'max_positions': 3
    }
    
    env = TradingEnvironment(config)
    
    # 创建测试市场状态
    market_state = MarketState(
        model_signal=1,
        signal_confidence=0.75,
        signal_probability=0.82,
        current_price=2500.0,
        price_change_1h=0.02,
        price_change_4h=-0.01,
        volatility_recent=0.03,
        cash_ratio=0.9,
        position_count=0,
        unrealized_pnl=0.0,
        recent_win_rate=0.6,
        consecutive_losses=0,
        hour=14,
        day_of_week=2,
        is_good_trading_time=True
    )
    
    # 测试状态向量转换
    state_vector = env.get_state_vector(market_state)
    print(f"状态向量维度: {len(state_vector)}")
    print(f"状态向量: {state_vector}")
    
    # 测试交易动作
    action = TradingAction(
        enter_trade=True,
        position_size=0.03,
        stop_loss_pct=0.025,
        take_profit_pct=0.02,
        max_hold_time=120
    )
    
    result = env.execute_action(action, market_state, pd.Timestamp.now(), 0)
    print(f"交易执行结果: {result}")
    
    print("交易环境测试完成！")