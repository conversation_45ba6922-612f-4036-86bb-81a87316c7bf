"""
RL Trading Agent - 强化学习交易代理

This module implements the main RL trading agent that integrates the policy network
and trading environment. It handles action selection, experience collection, and
PPO (Proximal Policy Optimization) algorithm implementation.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from collections import deque
import json
import os
from dataclasses import dataclass

from .policy_network import PolicyNetwork
from .trading_environment import TradingEnvironment, MarketState, TradingAction


@dataclass
class Experience:
    """单个经验样本"""
    state: np.ndarray
    action: Dict[str, Any]
    reward: float
    next_state: np.ndarray
    done: bool
    log_prob: float
    value: float


class ExperienceBuffer:
    """经验回放缓冲区"""
    
    def __init__(self, max_size: int = 10000):
        self.max_size = max_size
        self.buffer = deque(maxlen=max_size)
        
    def add(self, experience: Experience):
        """添加经验"""
        self.buffer.append(experience)
    
    def get_batch(self, batch_size: int) -> List[Experience]:
        """获取批量经验"""
        if len(self.buffer) < batch_size:
            return list(self.buffer)
        
        indices = np.random.choice(len(self.buffer), batch_size, replace=False)
        return [self.buffer[i] for i in indices]
    
    def get_all(self) -> List[Experience]:
        """获取所有经验"""
        return list(self.buffer)
    
    def clear(self):
        """清空缓冲区"""
        self.buffer.clear()
    
    def __len__(self):
        return len(self.buffer)


class ValueNetwork(nn.Module):
    """价值网络 - 用于PPO算法中的价值函数估计"""
    
    def __init__(self, state_dim: int, hidden_dim: int = 256):
        super(ValueNetwork, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
        
        # 初始化权重
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        return self.network(state)


class RLTradingAgent:
    """
    强化学习交易代理
    
    整合策略网络和价值网络，实现PPO算法进行交易策略优化
    """
    
    def __init__(self, config: Dict):
        """
        初始化RL交易代理
        
        Args:
            config: 配置字典，包含网络参数、训练参数等
        """
        self.config = config
        
        # 网络参数
        self.state_dim = config.get('state_dim', 15)
        self.hidden_dim = config.get('hidden_dim', 256)
        self.learning_rate = config.get('learning_rate', 3e-4)
        
        # PPO参数
        self.clip_epsilon = config.get('clip_epsilon', 0.2)
        self.value_loss_coef = config.get('value_loss_coef', 0.5)
        self.entropy_coef = config.get('entropy_coef', 0.01)
        self.gamma = config.get('gamma', 0.99)
        self.gae_lambda = config.get('gae_lambda', 0.95)
        
        # 训练参数
        self.batch_size = config.get('batch_size', 64)
        self.update_epochs = config.get('update_epochs', 4)
        self.max_grad_norm = config.get('max_grad_norm', 0.5)
        
        # 探索参数
        self.exploration_noise = config.get('exploration_noise', 0.1)
        self.epsilon_start = config.get('epsilon_start', 1.0)
        self.epsilon_end = config.get('epsilon_end', 0.1)
        self.epsilon_decay = config.get('epsilon_decay', 0.995)
        self.current_epsilon = self.epsilon_start
        
        # 初始化网络
        self.policy_network = PolicyNetwork(self.state_dim, self.hidden_dim)
        self.value_network = ValueNetwork(self.state_dim, self.hidden_dim)
        
        # 优化器
        self.policy_optimizer = optim.Adam(
            self.policy_network.parameters(), 
            lr=self.learning_rate
        )
        self.value_optimizer = optim.Adam(
            self.value_network.parameters(), 
            lr=self.learning_rate
        )
        
        # 经验缓冲区
        self.experience_buffer = ExperienceBuffer(
            max_size=config.get('buffer_size', 10000)
        )
        
        # 训练统计
        self.training_stats = {
            'episodes': 0,
            'total_steps': 0,
            'policy_losses': [],
            'value_losses': [],
            'rewards': [],
            'epsilon_history': []
        }
        
        print(f"RL交易代理初始化完成")
        print(f"状态维度: {self.state_dim}")
        print(f"策略网络参数: {sum(p.numel() for p in self.policy_network.parameters()):,}")
        print(f"价值网络参数: {sum(p.numel() for p in self.value_network.parameters()):,}")
    
    def get_action(self, state: np.ndarray, deterministic: bool = False) -> Tuple[Dict[str, Any], float, float]:
        """
        根据当前状态选择动作
        
        Args:
            state: 当前状态向量
            deterministic: 是否使用确定性策略（用于评估）
            
        Returns:
            Tuple of (action_dict, log_prob, value_estimate)
        """
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        
        with torch.no_grad():
            # 获取价值估计
            value = self.value_network(state_tensor).item()
        
        if deterministic:
            # 确定性动作选择（用于评估）
            with torch.no_grad():
                action_tensors = self.policy_network.get_actions(state_tensor, deterministic=True)
                
                action = {
                    'enter_trade': bool(action_tensors['enter_trade'].item()),
                    'position_size': float(action_tensors['position_size'].item()),
                    'stop_loss_pct': float(action_tensors['stop_loss_pct'].item()),
                    'take_profit_pct': float(action_tensors['take_profit_pct'].item()),
                    'max_hold_time': int(action_tensors['max_hold_time'].item())
                }
                
                return action, 0.0, value
        else:
            # 随机动作选择（用于训练）
            # 使用epsilon-greedy策略进行探索
            if np.random.random() < self.current_epsilon:
                # 随机探索
                action = self._get_random_action()
                log_prob = 0.0  # 随机动作的log_prob设为0
            else:
                # 使用策略网络
                action, log_probs = self.policy_network.sample_action(state_tensor)
                # 计算总log_prob（简化版本）
                log_prob = sum(lp.item() if hasattr(lp, 'item') else float(lp) 
                             for lp in log_probs.values())
            
            return action, log_prob, value
    
    def _get_random_action(self) -> Dict[str, Any]:
        """生成随机动作用于探索"""
        return {
            'enter_trade': np.random.choice([True, False], p=[0.3, 0.7]),  # 30%概率进场
            'position_size': np.random.uniform(0.01, 0.1),
            'stop_loss_pct': np.random.uniform(0.01, 0.05),
            'take_profit_pct': np.random.uniform(0.01, 0.04),
            'max_hold_time': int(np.random.uniform(60, 240))
        }
    
    def store_experience(self, state: np.ndarray, action: Dict[str, Any], 
                        reward: float, next_state: np.ndarray, done: bool,
                        log_prob: float, value: float):
        """存储经验到缓冲区"""
        experience = Experience(
            state=state,
            action=action,
            reward=reward,
            next_state=next_state,
            done=done,
            log_prob=log_prob,
            value=value
        )
        self.experience_buffer.add(experience)
    
    def update_policy(self) -> Dict[str, float]:
        """
        使用PPO算法更新策略
        
        Returns:
            训练损失字典
        """
        if len(self.experience_buffer) < self.batch_size:
            return {'policy_loss': 0.0, 'value_loss': 0.0}
        
        # 获取所有经验
        experiences = self.experience_buffer.get_all()
        
        # 计算GAE优势估计
        advantages, returns = self._compute_gae(experiences)
        
        # 准备训练数据
        states = torch.FloatTensor([exp.state for exp in experiences])
        actions = self._prepare_actions_tensor(experiences)
        old_log_probs = torch.FloatTensor([exp.log_prob for exp in experiences])
        advantages = torch.FloatTensor(advantages)
        returns = torch.FloatTensor(returns)
        
        # 标准化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        total_policy_loss = 0.0
        total_value_loss = 0.0
        
        # 多轮更新
        for epoch in range(self.update_epochs):
            # 随机打乱数据
            indices = torch.randperm(len(experiences))
            
            for start_idx in range(0, len(experiences), self.batch_size):
                end_idx = min(start_idx + self.batch_size, len(experiences))
                batch_indices = indices[start_idx:end_idx]
                
                batch_states = states[batch_indices]
                batch_actions = {k: v[batch_indices] for k, v in actions.items()}
                batch_old_log_probs = old_log_probs[batch_indices]
                batch_advantages = advantages[batch_indices]
                batch_returns = returns[batch_indices]
                
                # 计算当前策略的log_prob
                current_log_probs = self._compute_log_probs(batch_states, batch_actions)
                
                # 计算比率
                ratio = torch.exp(current_log_probs - batch_old_log_probs)
                
                # PPO裁剪损失
                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1 - self.clip_epsilon, 1 + self.clip_epsilon) * batch_advantages
                policy_loss = -torch.min(surr1, surr2).mean()
                
                # 价值函数损失
                current_values = self.value_network(batch_states).squeeze()
                value_loss = nn.MSELoss()(current_values, batch_returns)
                
                # 熵损失（鼓励探索）
                entropy_loss = self._compute_entropy_loss(batch_states)
                
                # 总损失
                total_loss = policy_loss + self.value_loss_coef * value_loss - self.entropy_coef * entropy_loss
                
                # 更新策略网络
                self.policy_optimizer.zero_grad()
                policy_loss.backward(retain_graph=True)
                torch.nn.utils.clip_grad_norm_(self.policy_network.parameters(), self.max_grad_norm)
                self.policy_optimizer.step()
                
                # 更新价值网络
                self.value_optimizer.zero_grad()
                value_loss.backward()
                torch.nn.utils.clip_grad_norm_(self.value_network.parameters(), self.max_grad_norm)
                self.value_optimizer.step()
                
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
        
        # 清空经验缓冲区
        self.experience_buffer.clear()
        
        # 更新epsilon
        self.current_epsilon = max(self.epsilon_end, self.current_epsilon * self.epsilon_decay)
        
        # 记录统计信息
        avg_policy_loss = total_policy_loss / (self.update_epochs * len(experiences) // self.batch_size)
        avg_value_loss = total_value_loss / (self.update_epochs * len(experiences) // self.batch_size)
        
        self.training_stats['policy_losses'].append(avg_policy_loss)
        self.training_stats['value_losses'].append(avg_value_loss)
        self.training_stats['epsilon_history'].append(self.current_epsilon)
        
        return {
            'policy_loss': avg_policy_loss,
            'value_loss': avg_value_loss,
            'epsilon': self.current_epsilon
        }
    
    def _compute_gae(self, experiences: List[Experience]) -> Tuple[List[float], List[float]]:
        """计算GAE（Generalized Advantage Estimation）"""
        advantages = []
        returns = []
        
        # 反向计算GAE
        gae = 0
        for i in reversed(range(len(experiences))):
            exp = experiences[i]
            
            if i == len(experiences) - 1:
                next_value = 0.0 if exp.done else exp.value
            else:
                next_value = experiences[i + 1].value
            
            # TD误差
            td_error = exp.reward + self.gamma * next_value - exp.value
            
            # GAE计算
            gae = td_error + self.gamma * self.gae_lambda * gae
            advantages.insert(0, gae)
            
            # 计算回报
            returns.insert(0, gae + exp.value)
        
        return advantages, returns
    
    def _prepare_actions_tensor(self, experiences: List[Experience]) -> Dict[str, torch.Tensor]:
        """准备动作张量用于训练"""
        actions = {
            'enter_trade': torch.LongTensor([int(exp.action['enter_trade']) for exp in experiences]),
            'position_size': torch.FloatTensor([exp.action['position_size'] for exp in experiences]),
            'stop_loss_pct': torch.FloatTensor([exp.action['stop_loss_pct'] for exp in experiences]),
            'take_profit_pct': torch.FloatTensor([exp.action['take_profit_pct'] for exp in experiences]),
            'max_hold_time': torch.FloatTensor([exp.action['max_hold_time'] for exp in experiences])
        }
        return actions
    
    def _compute_log_probs(self, states: torch.Tensor, actions: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算给定状态和动作的log概率"""
        # 简化版本：使用策略网络的get_log_prob方法
        # 这里需要根据实际的策略网络实现来调整
        raw_outputs = self.policy_network.forward(states)
        
        # 对于连续动作，假设使用正态分布
        std = 0.1
        log_probs = torch.zeros(states.shape[0])
        
        # 计算position_size的log_prob
        pos_size_mean = raw_outputs['position_size_raw']
        pos_size_actual = actions['position_size'] / 0.1  # 反向缩放
        log_prob_pos = -0.5 * ((pos_size_actual - pos_size_mean.squeeze()) / std) ** 2
        log_probs += log_prob_pos
        
        # 计算enter_trade的log_prob
        enter_probs = torch.softmax(raw_outputs['enter_trade_logits'], dim=-1)
        enter_log_probs = torch.log(enter_probs.gather(1, actions['enter_trade'].unsqueeze(1))).squeeze(1)
        log_probs += enter_log_probs
        
        return log_probs
    
    def _compute_entropy_loss(self, states: torch.Tensor) -> torch.Tensor:
        """计算熵损失以鼓励探索"""
        raw_outputs = self.policy_network.forward(states)
        
        # 计算离散动作的熵
        enter_probs = torch.softmax(raw_outputs['enter_trade_logits'], dim=-1)
        enter_entropy = -(enter_probs * torch.log(enter_probs + 1e-8)).sum(dim=-1).mean()
        
        # 连续动作的熵（简化为常数）
        continuous_entropy = torch.tensor(1.0)  # 简化处理
        
        return enter_entropy + continuous_entropy
    
    def save_model(self, filepath: str, metadata: Optional[Dict] = None):
        """保存模型和训练状态"""
        save_dict = {
            'policy_state_dict': self.policy_network.state_dict(),
            'value_state_dict': self.value_network.state_dict(),
            'policy_optimizer_state_dict': self.policy_optimizer.state_dict(),
            'value_optimizer_state_dict': self.value_optimizer.state_dict(),
            'config': self.config,
            'training_stats': self.training_stats,
            'current_epsilon': self.current_epsilon,
            'metadata': metadata or {}
        }
        
        torch.save(save_dict, f"{filepath}.pth")
        
        # 保存配置文件
        config_path = f"{filepath}_config.json"
        with open(config_path, 'w') as f:
            json.dump({
                'config': self.config,
                'training_stats': self.training_stats,
                'metadata': metadata or {}
            }, f, indent=2)
        
        print(f"RL代理模型已保存到: {filepath}.pth")
        print(f"配置已保存到: {config_path}")
    
    @classmethod
    def load_model(cls, filepath: str) -> 'RLTradingAgent':
        """加载保存的模型"""
        checkpoint = torch.load(f"{filepath}.pth", map_location='cpu')
        
        # 创建代理实例
        agent = cls(checkpoint['config'])
        
        # 加载网络状态
        agent.policy_network.load_state_dict(checkpoint['policy_state_dict'])
        agent.value_network.load_state_dict(checkpoint['value_state_dict'])
        agent.policy_optimizer.load_state_dict(checkpoint['policy_optimizer_state_dict'])
        agent.value_optimizer.load_state_dict(checkpoint['value_optimizer_state_dict'])
        
        # 加载训练状态
        agent.training_stats = checkpoint['training_stats']
        agent.current_epsilon = checkpoint['current_epsilon']
        
        print(f"RL代理模型已从 {filepath}.pth 加载")
        if 'metadata' in checkpoint and checkpoint['metadata']:
            print(f"元数据: {checkpoint['metadata']}")
        
        return agent
    
    def get_training_stats(self) -> Dict:
        """获取训练统计信息"""
        return {
            'episodes': self.training_stats['episodes'],
            'total_steps': self.training_stats['total_steps'],
            'avg_policy_loss': np.mean(self.training_stats['policy_losses'][-100:]) if self.training_stats['policy_losses'] else 0.0,
            'avg_value_loss': np.mean(self.training_stats['value_losses'][-100:]) if self.training_stats['value_losses'] else 0.0,
            'avg_reward': np.mean(self.training_stats['rewards'][-100:]) if self.training_stats['rewards'] else 0.0,
            'current_epsilon': self.current_epsilon,
            'buffer_size': len(self.experience_buffer)
        }
    
    def set_training_mode(self, training: bool = True):
        """设置训练/评估模式"""
        self.policy_network.train(training)
        self.value_network.train(training)
    
    def reset_training_stats(self):
        """重置训练统计"""
        self.training_stats = {
            'episodes': 0,
            'total_steps': 0,
            'policy_losses': [],
            'value_losses': [],
            'rewards': [],
            'epsilon_history': []
        }


def create_default_rl_agent(state_dim: int = 15) -> RLTradingAgent:
    """创建默认配置的RL代理"""
    config = {
        'state_dim': state_dim,
        'hidden_dim': 256,
        'learning_rate': 3e-4,
        'clip_epsilon': 0.2,
        'value_loss_coef': 0.5,
        'entropy_coef': 0.01,
        'gamma': 0.99,
        'gae_lambda': 0.95,
        'batch_size': 64,
        'update_epochs': 4,
        'max_grad_norm': 0.5,
        'buffer_size': 10000,
        'exploration_noise': 0.1,
        'epsilon_start': 1.0,
        'epsilon_end': 0.1,
        'epsilon_decay': 0.995
    }
    
    return RLTradingAgent(config)


if __name__ == "__main__":
    # 测试RL代理
    print("测试RL交易代理...")
    
    # 创建代理
    agent = create_default_rl_agent(state_dim=15)
    
    # 测试动作选择
    test_state = np.random.randn(15)
    
    # 测试确定性动作
    det_action, _, value = agent.get_action(test_state, deterministic=True)
    print(f"确定性动作: {det_action}")
    print(f"价值估计: {value:.4f}")
    
    # 测试随机动作
    rand_action, log_prob, value = agent.get_action(test_state, deterministic=False)
    print(f"随机动作: {rand_action}")
    print(f"Log概率: {log_prob:.4f}")
    
    # 测试经验存储
    agent.store_experience(
        state=test_state,
        action=rand_action,
        reward=0.1,
        next_state=np.random.randn(15),
        done=False,
        log_prob=log_prob,
        value=value
    )
    
    print(f"经验缓冲区大小: {len(agent.experience_buffer)}")
    
    # 测试保存/加载
    test_path = "/tmp/test_rl_agent"
    agent.save_model(test_path, metadata={'test': True})
    
    loaded_agent = RLTradingAgent.load_model(test_path)
    print("模型加载测试成功")
    
    # 清理测试文件
    try:
        os.remove(f"{test_path}.pth")
        os.remove(f"{test_path}_config.json")
        print("测试文件已清理")
    except:
        pass
    
    print("RL交易代理测试完成！")