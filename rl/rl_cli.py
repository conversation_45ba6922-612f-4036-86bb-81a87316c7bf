#!/usr/bin/env python3
"""
RL Trading System CLI

Comprehensive command-line interface for the RL trading system.
Provides model management, training, monitoring, and analysis capabilities.
"""

import argparse
import sys
import json
from pathlib import Path
from typing import List, Dict, Any

# Import all CLI modules
from .cli_utils import (
    list_available_models, check_database_status, validate_model_compatibility,
    find_training_logs, cleanup_old_logs, generate_training_report,
    compare_training_runs, export_model_info
)
from .config_cli import main as config_cli_main
from .train_rl_agent import main as train_main


def cmd_list_models(args):
    """列出可用模型"""
    models = list_available_models(args.models_dir)
    
    if not models:
        print(f"❌ 在目录 {args.models_dir} 中未找到模型")
        return
    
    print(f"📦 找到 {len(models)} 个可用模型:")
    print("-" * 80)
    
    for model in models:
        status = "✅" if model['config_file'] else "⚠️ "
        size_str = f"{model['size_mb']:.1f}MB"
        
        print(f"{status} {model['name']:<25} | {model['coin']:<4} {model['interval']:<4} | {size_str:>8} | {model['modified'][:10]}")
        
        if args.verbose and model['config_file']:
            print(f"    特征数: {model.get('features', 'N/A'):<3} | 阈值: {model.get('threshold', 'N/A'):<6} | 准确率: {model.get('accuracy', 'N/A')}")
    
    print("-" * 80)
    print(f"总计: {len(models)} 个模型, {sum(m['size_mb'] for m in models):.1f}MB")


def cmd_check_db(args):
    """检查数据库状态"""
    status = check_database_status(args.db_path)
    
    if not status['exists']:
        print(f"❌ 数据库不存在: {args.db_path}")
        return
    
    if 'error' in status:
        print(f"❌ 数据库错误: {status['error']}")
        return
    
    print(f"📊 数据库状态: {args.db_path}")
    print("-" * 60)
    print(f"文件大小: {status['file_size_mb']:.1f}MB")
    print(f"表数量: {status['total_tables']}")
    print(f"总记录数: {status['total_rows']:,}")
    print()
    
    if args.verbose:
        print("表详情:")
        for table, info in status['tables'].items():
            if 'error' in info:
                print(f"  ❌ {table}: {info['error']}")
            else:
                print(f"  ✅ {table}: {info['rows']:,} 行")
                if info['start_date'] and info['end_date']:
                    print(f"      时间范围: {info['start_date'][:10]} 到 {info['end_date'][:10]}")


def cmd_validate_model(args):
    """验证模型兼容性"""
    if args.auto_detect:
        models = list_available_models(args.models_dir)
        if not models:
            print("❌ 未找到可用模型")
            return
        
        print("🔍 验证所有模型...")
        for model in models:
            if model['config_file']:
                print(f"\n验证: {model['name']}")
                result = validate_model_compatibility(
                    model['model_file'], 
                    model['config_file'], 
                    args.db_path
                )
                _print_validation_result(result)
    else:
        if not args.model_file or not args.config_file:
            print("❌ 请指定 --model-file 和 --config-file，或使用 --auto-detect")
            return
        
        result = validate_model_compatibility(args.model_file, args.config_file, args.db_path)
        _print_validation_result(result)


def _print_validation_result(result: Dict[str, Any]):
    """打印验证结果"""
    if result['compatible']:
        print("✅ 模型兼容性验证通过")
        if 'model_info' in result:
            info = result['model_info']
            print(f"   币种: {info['coin']}, 时间间隔: {info['interval']}")
            print(f"   特征数: {info['features']}, 预期表: {info['expected_table']}")
    else:
        print("❌ 模型兼容性验证失败")
    
    if result['issues']:
        print("   问题:")
        for issue in result['issues']:
            print(f"     - {issue}")
    
    if result['warnings']:
        print("   警告:")
        for warning in result['warnings']:
            print(f"     - {warning}")


def cmd_list_logs(args):
    """列出训练日志"""
    logs = find_training_logs(args.base_dir)
    
    if not logs:
        print(f"❌ 在目录 {args.base_dir} 中未找到训练日志")
        return
    
    print(f"📋 找到 {len(logs)} 个训练日志:")
    print("-" * 100)
    
    for log in logs:
        status = "🔄" if log.get('checkpoints', 0) > 0 else "📁"
        size_str = f"{log['size_mb']:.1f}MB"
        
        print(f"{status} {log['name']:<35} | {size_str:>8} | {log['created'][:16]}")
        
        if args.verbose:
            if 'episodes_completed' in log:
                print(f"    Episodes: {log['episodes_completed']:<6} | 检查点: {log.get('checkpoints', 0):<3}")
                if 'best_return' in log:
                    print(f"    最佳收益: {log['best_return']:+7.2%} | 平均收益: {log.get('avg_return', 0):+7.2%}")
    
    print("-" * 100)
    total_size = sum(log['size_mb'] for log in logs)
    print(f"总计: {len(logs)} 个日志, {total_size:.1f}MB")


def cmd_cleanup_logs(args):
    """清理旧日志"""
    result = cleanup_old_logs(args.base_dir, args.days, args.dry_run)
    
    if result['dry_run']:
        print(f"🔍 预览清理结果 (超过 {args.days} 天的日志):")
        print(f"找到 {result['found']} 个旧日志, 总大小 {result['total_size_mb']:.1f}MB")
        
        if args.verbose and 'logs' in result:
            print("\n将被删除的日志:")
            for log in result['logs']:
                print(f"  - {log['name']} ({log['size_mb']:.1f}MB)")
        
        print(f"\n使用 --no-dry-run 执行实际删除")
    else:
        print(f"🗑️  清理完成:")
        print(f"删除了 {result['removed']}/{result['found']} 个日志")
        print(f"释放空间: {result['total_size_mb']:.1f}MB")


def cmd_training_report(args):
    """生成训练报告"""
    if args.compare:
        # 比较多个训练运行
        comparison = compare_training_runs(args.log_dirs)
        
        print("📊 训练运行比较")
        print("=" * 80)
        
        if not comparison['runs']:
            print("❌ 未找到有效的训练运行")
            return
        
        # 显示基本信息
        for run in comparison['runs']:
            print(f"\n📁 {run['name']}")
            print(f"   Episodes: {run['total_episodes']:,}")
            print(f"   最佳收益: {run['best_return']:+7.2%}")
            print(f"   平均收益: {run['avg_return']:+7.2%}")
            print(f"   胜率: {run['avg_win_rate']:6.1%}")
            print(f"   夏普比率: {run['avg_sharpe_ratio']:7.3f}")
        
        # 显示比较指标
        if comparison['comparison_metrics']:
            print(f"\n🏆 最佳表现:")
            for metric, stats in comparison['comparison_metrics'].items():
                print(f"   {metric}: {stats['best']:.4f} ({stats['best_run']})")
    
    else:
        # 单个训练报告
        if not args.log_dir:
            print("❌ 请指定 --log-dir 或使用 --compare")
            return
        
        report = generate_training_report(args.log_dir)
        
        if 'error' in report:
            print(f"❌ {report['error']}")
            return
        
        print(f"📊 训练报告: {Path(args.log_dir).name}")
        print("=" * 60)
        
        if 'training_summary' in report:
            summary = report['training_summary']
            print(f"总Episodes: {summary['total_episodes']:,}")
            print(f"训练时间: {summary['total_training_time']:.1f}秒")
            print(f"最佳收益: {summary['best_return']:+7.2%} (Episode {summary['best_episode']})")
            print(f"最终收益: {summary['final_return']:+7.2%}")
            print(f"平均胜率: {summary['avg_win_rate']:6.1%}")
            print(f"平均夏普比率: {summary['avg_sharpe_ratio']:7.3f}")
            print(f"平均最大回撤: {summary['avg_max_drawdown']:6.1%}")
            
            if 'learning_progress' in report:
                progress = report['learning_progress']
                print(f"\n学习进度:")
                print(f"前100episodes平均: {progress['first_100_avg']:+7.2%}")
                print(f"后100episodes平均: {progress['last_100_avg']:+7.2%}")
                print(f"改进幅度: {progress['improvement']:+7.2%}")
        
        if 'checkpoints' in report:
            checkpoints = report['checkpoints']
            print(f"\n检查点: {checkpoints['count']} 个")
            if checkpoints['latest']:
                print(f"最新: {Path(checkpoints['latest']).name}")
    
    # 保存报告
    if args.output:
        output_data = comparison if args.compare else report
        with open(args.output, 'w') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        print(f"\n📄 报告已保存到: {args.output}")


def cmd_export_inventory(args):
    """导出模型清单"""
    inventory = export_model_info(args.models_dir, args.output)
    
    print(f"📦 模型清单已导出: {args.output}")
    print(f"总计 {inventory['total_models']} 个模型, {inventory['statistics']['total_size_mb']:.1f}MB")
    
    print(f"\n按币种分布:")
    for coin, count in inventory['statistics']['by_coin'].items():
        print(f"  {coin}: {count} 个")
    
    print(f"\n按时间间隔分布:")
    for interval, count in inventory['statistics']['by_interval'].items():
        print(f"  {interval}: {count} 个")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="RL交易系统命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
子命令说明:
  models      模型管理 (列出、验证模型)
  database    数据库管理 (检查状态)
  logs        日志管理 (列出、清理、报告)
  config      配置管理 (创建、验证、比较配置)
  train       训练管理 (启动训练)
  
使用示例:
  # 列出所有可用模型
  python -m rl.rl_cli models list
  
  # 检查数据库状态
  python -m rl.rl_cli database check
  
  # 验证模型兼容性
  python -m rl.rl_cli models validate --auto-detect
  
  # 列出训练日志
  python -m rl.rl_cli logs list
  
  # 生成训练报告
  python -m rl.rl_cli logs report --log-dir rl_training_logs_ETH_5m_20240101_120000
  
  # 清理旧日志
  python -m rl.rl_cli logs cleanup --days 30
  
  # 配置管理
  python -m rl.rl_cli config list
  
  # 开始训练
  python -m rl.rl_cli train --model-file models/eth_5m_model.joblib --config-file models/eth_5m_config.json
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # Models 子命令
    models_parser = subparsers.add_parser('models', help='模型管理')
    models_subparsers = models_parser.add_subparsers(dest='models_action')
    
    # models list
    models_list_parser = models_subparsers.add_parser('list', help='列出可用模型')
    models_list_parser.add_argument('--models-dir', default='models/', help='模型目录')
    models_list_parser.add_argument('--verbose', action='store_true', help='显示详细信息')
    models_list_parser.set_defaults(func=cmd_list_models)
    
    # models validate
    models_validate_parser = models_subparsers.add_parser('validate', help='验证模型兼容性')
    models_validate_parser.add_argument('--model-file', help='模型文件路径')
    models_validate_parser.add_argument('--config-file', help='配置文件路径')
    models_validate_parser.add_argument('--db-path', default='coin_data.db', help='数据库路径')
    models_validate_parser.add_argument('--models-dir', default='models/', help='模型目录')
    models_validate_parser.add_argument('--auto-detect', action='store_true', help='自动检测所有模型')
    models_validate_parser.set_defaults(func=cmd_validate_model)
    
    # models export
    models_export_parser = models_subparsers.add_parser('export', help='导出模型清单')
    models_export_parser.add_argument('--models-dir', default='models/', help='模型目录')
    models_export_parser.add_argument('--output', default='model_inventory.json', help='输出文件')
    models_export_parser.set_defaults(func=cmd_export_inventory)
    
    # Database 子命令
    db_parser = subparsers.add_parser('database', help='数据库管理')
    db_subparsers = db_parser.add_subparsers(dest='db_action')
    
    # database check
    db_check_parser = db_subparsers.add_parser('check', help='检查数据库状态')
    db_check_parser.add_argument('--db-path', default='coin_data.db', help='数据库路径')
    db_check_parser.add_argument('--verbose', action='store_true', help='显示详细信息')
    db_check_parser.set_defaults(func=cmd_check_db)
    
    # Logs 子命令
    logs_parser = subparsers.add_parser('logs', help='日志管理')
    logs_subparsers = logs_parser.add_subparsers(dest='logs_action')
    
    # logs list
    logs_list_parser = logs_subparsers.add_parser('list', help='列出训练日志')
    logs_list_parser.add_argument('--base-dir', default='.', help='基础目录')
    logs_list_parser.add_argument('--verbose', action='store_true', help='显示详细信息')
    logs_list_parser.set_defaults(func=cmd_list_logs)
    
    # logs cleanup
    logs_cleanup_parser = logs_subparsers.add_parser('cleanup', help='清理旧日志')
    logs_cleanup_parser.add_argument('--base-dir', default='.', help='基础目录')
    logs_cleanup_parser.add_argument('--days', type=int, default=30, help='保留天数')
    logs_cleanup_parser.add_argument('--dry-run', action='store_true', default=True, help='预览模式')
    logs_cleanup_parser.add_argument('--no-dry-run', action='store_false', dest='dry_run', help='执行删除')
    logs_cleanup_parser.add_argument('--verbose', action='store_true', help='显示详细信息')
    logs_cleanup_parser.set_defaults(func=cmd_cleanup_logs)
    
    # logs report
    logs_report_parser = logs_subparsers.add_parser('report', help='生成训练报告')
    logs_report_parser.add_argument('--log-dir', help='训练日志目录')
    logs_report_parser.add_argument('--compare', action='store_true', help='比较多个训练运行')
    logs_report_parser.add_argument('--log-dirs', nargs='+', help='要比较的日志目录列表')
    logs_report_parser.add_argument('--output', help='保存报告到文件')
    logs_report_parser.set_defaults(func=cmd_training_report)
    
    # Config 子命令 - 委托给config_cli
    config_parser = subparsers.add_parser('config', help='配置管理')
    config_parser.set_defaults(func=lambda args: config_cli_main())
    
    # Train 子命令 - 委托给train_rl_agent
    train_parser = subparsers.add_parser('train', help='训练管理')
    train_parser.set_defaults(func=lambda args: train_main())
    
    # 解析参数
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 处理特殊命令
    if args.command in ['config', 'train']:
        # 重新设置sys.argv以便子命令正确解析
        if args.command == 'config':
            sys.argv = ['config_cli'] + sys.argv[2:]  # 移除 'rl_cli config'
            config_cli_main()
        elif args.command == 'train':
            sys.argv = ['train_rl_agent'] + sys.argv[2:]  # 移除 'rl_cli train'
            train_main()
        return
    
    # 检查子命令
    if args.command == 'models' and not hasattr(args, 'models_action'):
        models_parser.print_help()
        return
    elif args.command == 'database' and not hasattr(args, 'db_action'):
        db_parser.print_help()
        return
    elif args.command == 'logs' and not hasattr(args, 'logs_action'):
        logs_parser.print_help()
        return
    
    # 执行命令
    try:
        args.func(args)
    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()