#!/usr/bin/env python3
"""
Comprehensive Test Suite for RL Trading System

This script runs all tests to validate the complete RL trading system
including unit tests, integration tests, and validation tests.

Usage:
    python rl/run_comprehensive_tests.py
    python rl/run_comprehensive_tests.py --quick
    python rl/run_comprehensive_tests.py --component training
"""

import os
import sys
import subprocess
import argparse
import logging
from pathlib import Path
from datetime import datetime
import json

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ComprehensiveTestRunner:
    """Comprehensive test runner for RL trading system"""
    
    def __init__(self, quick_mode=False):
        self.quick_mode = quick_mode
        self.test_results = {}
        self.failed_tests = []
        
        # Test categories
        self.test_categories = {
            'unit_tests': [
                'rl/test_trading_environment.py',
                'rl/test_policy_network.py',
                'rl/test_rl_trading_agent.py',
                'rl/test_reward_functions.py',
                'rl/test_signal_generator.py',
                'rl/test_training_config.py',
                'rl/test_config_validator.py'
            ],
            'integration_tests': [
                'rl/test_rl_backtester.py',
                'rl/test_training_manager.py',
                'test_rl_integration.py'
            ],
            'performance_tests': [
                'rl/test_performance_optimizer.py'
            ],
            'validation_tests': [
                'rl/automated_validation.py'
            ]
        }
        
        if quick_mode:
            # Reduce test scope for quick mode
            self.test_categories['unit_tests'] = self.test_categories['unit_tests'][:3]
            self.test_categories['integration_tests'] = self.test_categories['integration_tests'][:1]
    
    def run_all_tests(self):
        """Run all test categories"""
        
        logger.info("Starting comprehensive test suite...")
        start_time = datetime.now()
        
        # Run each test category
        for category, tests in self.test_categories.items():
            logger.info(f"Running {category}...")
            self.run_test_category(category, tests)
        
        # Generate test report
        end_time = datetime.now()
        duration = end_time - start_time
        
        self.generate_test_report(duration)
        
        # Return overall success
        return len(self.failed_tests) == 0
    
    def run_test_category(self, category: str, test_files: list):
        """Run tests in a specific category"""
        
        category_results = {
            'passed': 0,
            'failed': 0,
            'errors': []
        }
        
        for test_file in test_files:
            try:
                result = self.run_single_test(test_file)
                if result:
                    category_results['passed'] += 1
                else:
                    category_results['failed'] += 1
                    self.failed_tests.append(test_file)
                    
            except Exception as e:
                category_results['failed'] += 1
                category_results['errors'].append(f"{test_file}: {str(e)}")
                self.failed_tests.append(test_file)
                logger.error(f"Error running {test_file}: {e}")
        
        self.test_results[category] = category_results
        
        # Log category summary
        total = category_results['passed'] + category_results['failed']
        logger.info(f"{category}: {category_results['passed']}/{total} tests passed")
    
    def run_single_test(self, test_file: str) -> bool:
        """Run a single test file"""
        
        if not os.path.exists(test_file):
            logger.warning(f"Test file not found: {test_file}")
            return False
        
        try:
            # Determine how to run the test
            if test_file.endswith('.py'):
                if 'pytest' in test_file or test_file.startswith('test_'):
                    # Run with pytest
                    result = subprocess.run(
                        ['python', '-m', 'pytest', test_file, '-v'],
                        capture_output=True,
                        text=True,
                        timeout=300 if not self.quick_mode else 60
                    )
                else:
                    # Run as Python script
                    result = subprocess.run(
                        ['python', test_file],
                        capture_output=True,
                        text=True,
                        timeout=300 if not self.quick_mode else 60
                    )
                
                success = result.returncode == 0
                
                if not success:
                    logger.error(f"Test failed: {test_file}")
                    logger.error(f"STDOUT: {result.stdout}")
                    logger.error(f"STDERR: {result.stderr}")
                else:
                    logger.info(f"Test passed: {test_file}")
                
                return success
                
        except subprocess.TimeoutExpired:
            logger.error(f"Test timed out: {test_file}")
            return False
        except Exception as e:
            logger.error(f"Error running test {test_file}: {e}")
            return False
    
    def run_component_tests(self, component: str):
        """Run tests for a specific component"""
        
        component_mapping = {
            'environment': ['rl/test_trading_environment.py'],
            'policy': ['rl/test_policy_network.py'],
            'agent': ['rl/test_rl_trading_agent.py'],
            'training': ['rl/test_training_manager.py', 'rl/test_training_config.py'],
            'backtesting': ['rl/test_rl_backtester.py'],
            'rewards': ['rl/test_reward_functions.py'],
            'config': ['rl/test_config_validator.py'],
            'integration': ['test_rl_integration.py']
        }
        
        if component not in component_mapping:
            logger.error(f"Unknown component: {component}")
            logger.info(f"Available components: {list(component_mapping.keys())}")
            return False
        
        test_files = component_mapping[component]
        logger.info(f"Running tests for component: {component}")
        
        self.run_test_category(f"{component}_tests", test_files)
        
        return len(self.failed_tests) == 0
    
    def generate_test_report(self, duration):
        """Generate comprehensive test report"""
        
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        with open(report_file, 'w') as f:
            f.write("# RL Trading System Test Report\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**Duration:** {duration}\n")
            f.write(f"**Mode:** {'Quick' if self.quick_mode else 'Full'}\n\n")
            
            # Overall summary
            total_passed = sum(r['passed'] for r in self.test_results.values())
            total_failed = sum(r['failed'] for r in self.test_results.values())
            total_tests = total_passed + total_failed
            
            f.write("## Overall Summary\n\n")
            f.write(f"- **Total Tests:** {total_tests}\n")
            f.write(f"- **Passed:** {total_passed} ({total_passed/total_tests*100:.1f}%)\n")
            f.write(f"- **Failed:** {total_failed} ({total_failed/total_tests*100:.1f}%)\n")
            f.write(f"- **Success Rate:** {total_passed/total_tests*100:.1f}%\n\n")
            
            # Category breakdown
            f.write("## Test Categories\n\n")
            f.write("| Category | Passed | Failed | Success Rate |\n")
            f.write("|----------|--------|--------|-------------|\n")
            
            for category, results in self.test_results.items():
                total = results['passed'] + results['failed']
                success_rate = results['passed'] / total * 100 if total > 0 else 0
                f.write(f"| {category} | {results['passed']} | {results['failed']} | {success_rate:.1f}% |\n")
            
            f.write("\n")
            
            # Failed tests
            if self.failed_tests:
                f.write("## Failed Tests\n\n")
                for test in self.failed_tests:
                    f.write(f"- ❌ {test}\n")
                f.write("\n")
            
            # Recommendations
            f.write("## Recommendations\n\n")
            if total_failed == 0:
                f.write("✅ All tests passed! The system is ready for deployment.\n")
            else:
                f.write("❌ Some tests failed. Please review and fix the following:\n\n")
                for test in self.failed_tests:
                    f.write(f"- Fix issues in {test}\n")
                f.write("\n")
                f.write("Re-run tests after fixes: `python rl/run_comprehensive_tests.py`\n")
        
        logger.info(f"Test report generated: {report_file}")
        
        # Print summary to console
        print("\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {total_passed}")
        print(f"Failed: {total_failed}")
        print(f"Success Rate: {total_passed/total_tests*100:.1f}%")
        
        if self.failed_tests:
            print(f"\nFailed Tests:")
            for test in self.failed_tests:
                print(f"  - {test}")
        
        print("="*60)
    
    def setup_test_environment(self):
        """Setup test environment and dependencies"""
        
        logger.info("Setting up test environment...")
        
        # Create necessary directories
        test_dirs = [
            'test_results',
            'test_models',
            'test_logs'
        ]
        
        for dir_name in test_dirs:
            Path(dir_name).mkdir(exist_ok=True)
        
        # Create minimal test data if needed
        self.create_test_data()
        
        logger.info("Test environment setup complete")
    
    def create_test_data(self):
        """Create minimal test data for testing"""
        
        test_data_file = "test_data.json"
        
        if not os.path.exists(test_data_file):
            # Create minimal test configuration
            test_config = {
                "symbol": "ETH",
                "timeframe": "5m",
                "model_file": "test_models/test_model.joblib",
                "config_file": "test_models/test_config.json",
                "training": {
                    "episodes": 10,  # Minimal for testing
                    "episode_length": 100,
                    "learning_rate": 3e-4,
                    "batch_size": 32
                },
                "environment": {
                    "initial_capital": 10000,
                    "transaction_cost": 0.001,
                    "max_position_size": 0.1
                },
                "data": {
                    "train_start": "2024-01-01",
                    "train_end": "2024-06-30",
                    "val_start": "2024-07-01",
                    "val_end": "2024-09-30",
                    "test_start": "2024-10-01",
                    "test_end": "2024-12-31"
                }
            }
            
            with open(test_data_file, 'w') as f:
                json.dump(test_config, f, indent=2)
            
            logger.info(f"Created test configuration: {test_data_file}")

def main():
    parser = argparse.ArgumentParser(description="Comprehensive Test Runner")
    parser.add_argument("--quick", action="store_true", help="Run quick test suite")
    parser.add_argument("--component", help="Run tests for specific component")
    parser.add_argument("--setup", action="store_true", help="Setup test environment only")
    
    args = parser.parse_args()
    
    # Initialize test runner
    test_runner = ComprehensiveTestRunner(quick_mode=args.quick)
    
    if args.setup:
        # Setup test environment only
        test_runner.setup_test_environment()
        return
    
    # Setup test environment
    test_runner.setup_test_environment()
    
    if args.component:
        # Run component-specific tests
        success = test_runner.run_component_tests(args.component)
    else:
        # Run all tests
        success = test_runner.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()