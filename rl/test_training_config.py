# test_training_config.py
# 训练配置系统测试

import unittest
import tempfile
import json
import os
from pathlib import Path
import shutil

from .training_config import (
    TrainingConfigManager, FullTrainingConfig, DataConfig, 
    ModelConfig, EnvironmentConfig, RewardConfig, RLConfig, 
    TrainingConfig, LoggingConfig, create_config_from_dict,
    validate_hyperparameters
)
from .config_validator import ConfigValidator
from .config_updater import DynamicConfigManager, ConfigScheduler


class TestTrainingConfig(unittest.TestCase):
    """训练配置测试"""
    
    def setUp(self):
        """测试设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = TrainingConfigManager(self.temp_dir)
    
    def tearDown(self):
        """测试清理"""
        shutil.rmtree(self.temp_dir)
    
    def test_default_config_creation(self):
        """测试默认配置创建"""
        config = FullTrainingConfig()
        
        # 检查默认值
        self.assertEqual(config.data.coin, "ETH")
        self.assertEqual(config.data.interval, "5m")
        self.assertEqual(config.environment.initial_capital, 10000.0)
        self.assertEqual(config.rl.learning_rate, 3e-4)
        self.assertEqual(config.training.episodes, 1000)
    
    def test_config_save_and_load(self):
        """测试配置保存和加载"""
        # 创建测试配置
        config = FullTrainingConfig(
            config_name="test_config",
            description="测试配置"
        )
        config.rl.learning_rate = 0.0001
        config.environment.max_position_size = 0.08
        
        # 保存配置
        self.config_manager.save_config(config)
        
        # 加载配置
        loaded_config = self.config_manager.load_config("test_config")
        
        self.assertIsNotNone(loaded_config)
        self.assertEqual(loaded_config.config_name, "test_config")
        self.assertEqual(loaded_config.rl.learning_rate, 0.0001)
        self.assertEqual(loaded_config.environment.max_position_size, 0.08)
    
    def test_custom_config_creation(self):
        """测试自定义配置创建"""
        # 创建自定义配置
        custom_config = self.config_manager.create_custom_config(
            name="custom_test",
            base_config="default",  # 使用默认模板
            modifications={
                'rl.learning_rate': 0.0002,
                'environment.max_position_size': 0.12,
                'training.episodes': 1500
            }
        )
        
        self.assertEqual(custom_config.config_name, "custom_test")
        self.assertEqual(custom_config.rl.learning_rate, 0.0002)
        self.assertEqual(custom_config.environment.max_position_size, 0.12)
        self.assertEqual(custom_config.training.episodes, 1500)
    
    def test_config_validation(self):
        """测试配置验证"""
        validator = ConfigValidator()
        
        # 测试有效配置
        valid_config = FullTrainingConfig()
        issues = validator.validate_config(valid_config)
        self.assertEqual(len(issues['errors']), 0)
        
        # 测试无效配置
        invalid_config = FullTrainingConfig()
        invalid_config.data.train_ratio = 0.3  # 太小
        invalid_config.data.val_ratio = 0.3
        invalid_config.data.test_ratio = 0.3  # 总和不等于1
        invalid_config.rl.learning_rate = 0.1  # 太大
        
        issues = validator.validate_config(invalid_config)
        self.assertGreater(len(issues['errors']), 0)
        self.assertGreater(len(issues['warnings']), 0)
    
    def test_config_comparison(self):
        """测试配置比较"""
        # 创建两个不同的配置
        config1 = FullTrainingConfig(config_name="config1")
        config1.rl.learning_rate = 0.001
        
        config2 = FullTrainingConfig(config_name="config2")
        config2.rl.learning_rate = 0.0005
        config2.environment.max_position_size = 0.15
        
        # 保存配置
        self.config_manager.save_config(config1)
        self.config_manager.save_config(config2)
        
        # 比较配置
        comparison = self.config_manager.compare_configs(["config1", "config2"])
        
        self.assertIn("config1", comparison)
        self.assertIn("config2", comparison)
        self.assertNotEqual(
            comparison["config1"]["rl"]["learning_rate"],
            comparison["config2"]["rl"]["learning_rate"]
        )
    
    def test_config_templates(self):
        """测试配置模板"""
        # 检查模板是否正确创建
        configs = self.config_manager.list_configs()
        
        # 应该有模板配置
        self.assertIn('templates', configs)
        self.assertGreater(len(configs['templates']), 0)
        
        # 测试加载模板
        for template_name in configs['templates']:
            config = self.config_manager.load_config(template_name)
            self.assertIsNotNone(config)
            
            # 验证模板配置
            validator = ConfigValidator()
            issues = validator.validate_config(config)
            self.assertEqual(len(issues['errors']), 0, 
                           f"模板 {template_name} 验证失败: {issues['errors']}")
    
    def test_dict_to_config_conversion(self):
        """测试字典到配置对象的转换"""
        config_dict = {
            'data': {
                'coin': 'BTC',
                'interval': '15m'
            },
            'rl': {
                'learning_rate': 0.0001,
                'batch_size': 128
            },
            'config_name': 'test_dict_config'
        }
        
        config = create_config_from_dict(config_dict)
        
        self.assertEqual(config.data.coin, 'BTC')
        self.assertEqual(config.data.interval, '15m')
        self.assertEqual(config.rl.learning_rate, 0.0001)
        self.assertEqual(config.rl.batch_size, 128)
        self.assertEqual(config.config_name, 'test_dict_config')
    
    def test_hyperparameter_validation(self):
        """测试超参数验证"""
        # 有效配置
        valid_config = FullTrainingConfig()
        self.assertTrue(validate_hyperparameters(valid_config))
        
        # 无效配置
        invalid_config = FullTrainingConfig()
        invalid_config.data.train_ratio = 1.5  # 无效值
        self.assertFalse(validate_hyperparameters(invalid_config))


class TestDynamicConfigManager(unittest.TestCase):
    """动态配置管理器测试"""
    
    def setUp(self):
        """测试设置"""
        self.initial_config = FullTrainingConfig(config_name="test_dynamic")
        self.dynamic_manager = DynamicConfigManager(self.initial_config)
    
    def test_config_update(self):
        """测试配置更新"""
        # 创建新配置
        new_config = FullTrainingConfig(config_name="updated_config")
        new_config.rl.learning_rate = 0.0001
        
        # 更新配置
        success = self.dynamic_manager.update_config(new_config)
        self.assertTrue(success)
        
        # 检查更新结果
        current_config = self.dynamic_manager.get_current_config()
        self.assertEqual(current_config.config_name, "updated_config")
        self.assertEqual(current_config.rl.learning_rate, 0.0001)
    
    def test_partial_update(self):
        """测试部分更新"""
        # 部分更新
        success = self.dynamic_manager.update_partial({
            'rl.learning_rate': 0.0002,
            'training.episodes': 1500
        })
        self.assertTrue(success)
        
        # 检查更新结果
        current_config = self.dynamic_manager.get_current_config()
        self.assertEqual(current_config.rl.learning_rate, 0.0002)
        self.assertEqual(current_config.training.episodes, 1500)
    
    def test_config_rollback(self):
        """测试配置回滚"""
        original_lr = self.initial_config.rl.learning_rate
        
        # 更新配置
        self.dynamic_manager.update_partial({'rl.learning_rate': 0.0001})
        
        # 回滚配置
        success = self.dynamic_manager.rollback_config(steps=1)
        self.assertTrue(success)
        
        # 检查回滚结果
        current_config = self.dynamic_manager.get_current_config()
        self.assertEqual(current_config.rl.learning_rate, original_lr)
    
    def test_config_diff(self):
        """测试配置差异"""
        # 更新配置
        self.dynamic_manager.update_partial({
            'rl.learning_rate': 0.0001,
            'training.episodes': 1500
        })
        
        # 获取差异
        diff = self.dynamic_manager.get_config_diff()
        
        self.assertIn('rl.learning_rate', diff)
        self.assertIn('training.episodes', diff)
        self.assertEqual(diff['rl.learning_rate']['action'], 'changed')
        self.assertEqual(diff['rl.learning_rate']['new_value'], 0.0001)
    
    def test_update_callback(self):
        """测试更新回调"""
        callback_called = False
        callback_old_config = None
        callback_new_config = None
        
        def test_callback(old_config, new_config):
            nonlocal callback_called, callback_old_config, callback_new_config
            callback_called = True
            callback_old_config = old_config
            callback_new_config = new_config
        
        # 注册回调
        self.dynamic_manager.register_update_callback(test_callback)
        
        # 更新配置
        self.dynamic_manager.update_partial({'rl.learning_rate': 0.0001})
        
        # 检查回调是否被调用
        self.assertTrue(callback_called)
        self.assertIsNotNone(callback_old_config)
        self.assertIsNotNone(callback_new_config)
        self.assertNotEqual(
            callback_old_config.rl.learning_rate,
            callback_new_config.rl.learning_rate
        )


class TestConfigScheduler(unittest.TestCase):
    """配置调度器测试"""
    
    def setUp(self):
        """测试设置"""
        self.initial_config = FullTrainingConfig()
        self.dynamic_manager = DynamicConfigManager(self.initial_config)
        self.scheduler = ConfigScheduler(self.dynamic_manager)
    
    def test_schedule_update(self):
        """测试调度更新"""
        # 调度更新
        self.scheduler.schedule_update(
            delay_seconds=0.1,
            updates={'rl.learning_rate': 0.0001},
            description="测试调度更新"
        )
        
        # 检查调度列表
        self.assertEqual(len(self.scheduler.scheduled_updates), 1)
        
        # 检查调度内容
        scheduled = self.scheduler.scheduled_updates[0]
        self.assertEqual(scheduled['updates']['rl.learning_rate'], 0.0001)
        self.assertEqual(scheduled['description'], "测试调度更新")
    
    def test_learning_rate_decay_schedule(self):
        """测试学习率衰减调度"""
        initial_lr = 0.001
        decay_factor = 0.5
        
        # 调度学习率衰减
        self.scheduler.schedule_learning_rate_decay(
            initial_lr=initial_lr,
            decay_factor=decay_factor,
            decay_interval_seconds=0.1,
            min_lr=1e-5
        )
        
        # 应该有多个调度更新
        self.assertGreater(len(self.scheduler.scheduled_updates), 1)
        
        # 检查第一个调度的学习率
        first_update = self.scheduler.scheduled_updates[0]
        expected_lr = initial_lr * decay_factor
        self.assertEqual(first_update['updates']['rl.learning_rate'], expected_lr)


def run_all_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestTrainingConfig,
        TestDynamicConfigManager,
        TestConfigScheduler
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    print("运行训练配置系统测试...")
    success = run_all_tests()
    
    if success:
        print("\n✅ 所有测试通过！")
    else:
        print("\n❌ 部分测试失败！")
        exit(1)