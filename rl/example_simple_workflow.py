#!/usr/bin/env python3
"""
Simple RL Trading Workflow Example

This script demonstrates a simplified workflow that works with existing components
and gracefully handles missing RL modules.

Usage:
    python rl/example_simple_workflow.py --symbol ETH --timeframe 5m
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

try:
    import numpy as np
    import pandas as pd
except ImportError as e:
    print(f"Error: Required dependencies missing: {e}")
    print("Please install with: pip install numpy pandas")
    sys.exit(1)

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleRLWorkflow:
    """Simplified RL trading workflow that works with existing components"""
    
    def __init__(self, symbol: str, timeframe: str):
        self.symbol = symbol.upper()
        self.timeframe = timeframe
        
        # Create output directory
        self.output_dir = Path(f"rl_results/{symbol}_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Initialized simple workflow for {symbol} {timeframe}")
        logger.info(f"Output directory: {self.output_dir}")
        
    def create_config(self):
        """Create a basic configuration"""
        
        config = {
            "symbol": self.symbol,
            "timeframe": self.timeframe,
            "model_file": f"models/{self.symbol.lower()}_{self.timeframe}_model.joblib",
            "config_file": f"models/{self.symbol.lower()}_{self.timeframe}_config.json",
            
            "training": {
                "episodes": 100,  # Small number for demo
                "episode_length": 500,
                "learning_rate": 3e-4,
                "batch_size": 32
            },
            
            "environment": {
                "initial_capital": 10000,
                "transaction_cost": 0.001,
                "slippage": 0.0005,
                "max_position_size": 0.1,
                "min_position_size": 0.01,
                "max_positions": 3
            },
            
            "reward": {
                "profit_weight": 1.0,
                "risk_weight": 0.5,
                "efficiency_weight": 0.1,
                "drawdown_penalty": 2.0,
                "transaction_cost_penalty": 0.1
            },
            
            "data": {
                "train_start": "2024-01-01",
                "train_end": "2024-06-30",
                "val_start": "2024-07-01",
                "val_end": "2024-09-30",
                "test_start": "2024-10-01",
                "test_end": "2024-12-31"
            }
        }
        
        # Save config
        config_file = self.output_dir / "config.json"
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
            
        logger.info(f"Configuration saved to {config_file}")
        return config
    
    def check_dependencies(self):
        """Check which components are available"""
        
        available_components = {}
        
        # Check core dependencies
        try:
            import numpy as np
            import pandas as pd
            available_components['numpy'] = True
            available_components['pandas'] = True
        except ImportError as e:
            logger.warning(f"Missing core dependencies: {e}")
            available_components['numpy'] = False
            available_components['pandas'] = False
        
        # Check existing models
        model_file = f"models/{self.symbol.lower()}_{self.timeframe}_model.joblib"
        config_file = f"models/{self.symbol.lower()}_{self.timeframe}_config.json"
        
        available_components['existing_model'] = os.path.exists(model_file)
        available_components['existing_config'] = os.path.exists(config_file)
        
        if available_components['existing_model']:
            logger.info(f"Found existing model: {model_file}")
        else:
            logger.info(f"No existing model found: {model_file}")
            
        # Check RL components
        rl_components = [
            'rl.trading_environment',
            'rl.policy_network', 
            'rl.rl_trading_agent',
            'rl.training_manager',
            'rl.rl_backtester'
        ]
        
        for component in rl_components:
            try:
                __import__(component)
                available_components[component] = True
                logger.info(f"✅ {component} available")
            except ImportError:
                available_components[component] = False
                logger.info(f"❌ {component} not available")
        
        return available_components
    
    def simulate_training(self, config):
        """Simulate training process"""
        
        logger.info("Starting simulated training...")
        
        training_results = {
            "episodes_completed": config["training"]["episodes"],
            "final_reward": 125.5,
            "training_time": 45.2,
            "best_episode": 78,
            "convergence_episode": 65,
            "status": "completed"
        }
        
        # Simulate episode progress
        episode_rewards = []
        for episode in range(config["training"]["episodes"]):
            # Simulate improving performance
            base_reward = 50 + episode * 0.8 + np.random.normal(0, 10)
            episode_rewards.append(base_reward)
            
            if episode % 20 == 0:
                logger.info(f"Episode {episode}/{config['training']['episodes']}, Reward: {base_reward:.2f}")
        
        training_results["episode_rewards"] = episode_rewards
        training_results["final_reward"] = episode_rewards[-1]
        
        # Save training results
        results_file = self.output_dir / "training_results.json"
        with open(results_file, 'w') as f:
            json.dump(training_results, f, indent=2)
        
        logger.info(f"Training simulation completed. Results saved to {results_file}")
        return training_results
    
    def simulate_backtesting(self, config):
        """Simulate backtesting process"""
        
        logger.info("Starting simulated backtesting...")
        
        # Generate simulated performance metrics
        np.random.seed(42)  # For reproducible results
        
        backtest_results = {
            "total_return": np.random.uniform(0.08, 0.25),  # 8-25% return
            "sharpe_ratio": np.random.uniform(1.2, 2.5),
            "max_drawdown": np.random.uniform(0.05, 0.15),
            "win_rate": np.random.uniform(0.45, 0.65),
            "profit_factor": np.random.uniform(1.3, 2.1),
            "total_trades": np.random.randint(80, 200),
            "avg_trade_duration": np.random.uniform(2.5, 8.0),  # hours
            "volatility": np.random.uniform(0.15, 0.35)
        }
        
        # Generate trade log
        trades = []
        for i in range(backtest_results["total_trades"]):
            trade = {
                "trade_id": i + 1,
                "entry_time": f"2024-10-{1 + i//10:02d} {np.random.randint(0,24):02d}:{np.random.randint(0,60):02d}",
                "exit_time": f"2024-10-{1 + i//10:02d} {np.random.randint(0,24):02d}:{np.random.randint(0,60):02d}",
                "side": np.random.choice(["long", "short"]),
                "entry_price": np.random.uniform(2000, 4000),
                "exit_price": np.random.uniform(2000, 4000),
                "quantity": np.random.uniform(0.1, 1.0),
                "pnl": np.random.uniform(-50, 100),
                "pnl_pct": np.random.uniform(-0.05, 0.08)
            }
            trades.append(trade)
        
        backtest_results["trades"] = trades
        
        # Save backtest results
        results_file = self.output_dir / "backtest_results.json"
        with open(results_file, 'w') as f:
            json.dump(backtest_results, f, indent=2)
        
        # Save trade log as CSV
        if trades:
            import pandas as pd
            trades_df = pd.DataFrame(trades)
            trades_file = self.output_dir / "trade_log.csv"
            trades_df.to_csv(trades_file, index=False)
            logger.info(f"Trade log saved to {trades_file}")
        
        logger.info(f"Backtesting simulation completed. Results saved to {results_file}")
        return backtest_results
    
    def analyze_performance(self, training_results, backtest_results):
        """Analyze performance and generate insights"""
        
        logger.info("Analyzing performance...")
        
        analysis = {
            "performance_grade": "B+",
            "risk_assessment": "Moderate",
            "key_strengths": [],
            "areas_for_improvement": [],
            "recommendations": []
        }
        
        # Analyze key metrics
        sharpe_ratio = backtest_results["sharpe_ratio"]
        total_return = backtest_results["total_return"]
        max_drawdown = backtest_results["max_drawdown"]
        win_rate = backtest_results["win_rate"]
        
        # Performance grading
        if sharpe_ratio > 2.0 and total_return > 0.20:
            analysis["performance_grade"] = "A"
        elif sharpe_ratio > 1.5 and total_return > 0.15:
            analysis["performance_grade"] = "A-"
        elif sharpe_ratio > 1.2 and total_return > 0.10:
            analysis["performance_grade"] = "B+"
        elif sharpe_ratio > 0.8 and total_return > 0.05:
            analysis["performance_grade"] = "B"
        else:
            analysis["performance_grade"] = "C"
        
        # Risk assessment
        if max_drawdown < 0.08:
            analysis["risk_assessment"] = "Low"
        elif max_drawdown < 0.15:
            analysis["risk_assessment"] = "Moderate"
        else:
            analysis["risk_assessment"] = "High"
        
        # Identify strengths
        if sharpe_ratio > 1.5:
            analysis["key_strengths"].append("Excellent risk-adjusted returns")
        if win_rate > 0.55:
            analysis["key_strengths"].append("High win rate")
        if max_drawdown < 0.10:
            analysis["key_strengths"].append("Low maximum drawdown")
        if total_return > 0.15:
            analysis["key_strengths"].append("Strong absolute returns")
        
        # Identify improvement areas
        if sharpe_ratio < 1.0:
            analysis["areas_for_improvement"].append("Risk-adjusted returns")
        if win_rate < 0.45:
            analysis["areas_for_improvement"].append("Trade success rate")
        if max_drawdown > 0.15:
            analysis["areas_for_improvement"].append("Drawdown control")
        
        # Generate recommendations
        if total_return < 0.10:
            analysis["recommendations"].append("Consider adjusting reward function to improve returns")
        if sharpe_ratio < 1.0:
            analysis["recommendations"].append("Optimize risk management parameters")
        if max_drawdown > 0.15:
            analysis["recommendations"].append("Implement stricter position sizing rules")
        if win_rate < 0.45:
            analysis["recommendations"].append("Review entry/exit signal quality")
        
        # Save analysis
        analysis_file = self.output_dir / "performance_analysis.json"
        with open(analysis_file, 'w') as f:
            json.dump(analysis, f, indent=2)
        
        logger.info(f"Performance analysis completed. Results saved to {analysis_file}")
        return analysis
    
    def generate_report(self, config, training_results, backtest_results, analysis):
        """Generate comprehensive workflow report"""
        
        report_file = self.output_dir / "WORKFLOW_REPORT.md"
        
        with open(report_file, 'w') as f:
            f.write("# RL Trading Workflow Report\n\n")
            f.write(f"**Symbol:** {self.symbol}\n")
            f.write(f"**Timeframe:** {self.timeframe}\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Executive Summary
            f.write("## Executive Summary\n\n")
            f.write(f"- **Performance Grade:** {analysis['performance_grade']}\n")
            f.write(f"- **Risk Assessment:** {analysis['risk_assessment']}\n")
            f.write(f"- **Total Return:** {backtest_results['total_return']:.2%}\n")
            f.write(f"- **Sharpe Ratio:** {backtest_results['sharpe_ratio']:.2f}\n")
            f.write(f"- **Max Drawdown:** {backtest_results['max_drawdown']:.2%}\n\n")
            
            # Training Results
            f.write("## Training Results\n\n")
            f.write(f"- **Episodes Completed:** {training_results['episodes_completed']}\n")
            f.write(f"- **Final Reward:** {training_results['final_reward']:.2f}\n")
            f.write(f"- **Training Time:** {training_results['training_time']:.1f} seconds\n")
            f.write(f"- **Best Episode:** {training_results['best_episode']}\n\n")
            
            # Backtest Results
            f.write("## Backtest Results\n\n")
            f.write(f"- **Total Return:** {backtest_results['total_return']:.2%}\n")
            f.write(f"- **Sharpe Ratio:** {backtest_results['sharpe_ratio']:.2f}\n")
            f.write(f"- **Maximum Drawdown:** {backtest_results['max_drawdown']:.2%}\n")
            f.write(f"- **Win Rate:** {backtest_results['win_rate']:.2%}\n")
            f.write(f"- **Profit Factor:** {backtest_results['profit_factor']:.2f}\n")
            f.write(f"- **Total Trades:** {backtest_results['total_trades']}\n")
            f.write(f"- **Average Trade Duration:** {backtest_results['avg_trade_duration']:.1f} hours\n\n")
            
            # Key Strengths
            if analysis['key_strengths']:
                f.write("## Key Strengths\n\n")
                for strength in analysis['key_strengths']:
                    f.write(f"- ✅ {strength}\n")
                f.write("\n")
            
            # Areas for Improvement
            if analysis['areas_for_improvement']:
                f.write("## Areas for Improvement\n\n")
                for area in analysis['areas_for_improvement']:
                    f.write(f"- ⚠️ {area}\n")
                f.write("\n")
            
            # Recommendations
            if analysis['recommendations']:
                f.write("## Recommendations\n\n")
                for rec in analysis['recommendations']:
                    f.write(f"- 💡 {rec}\n")
                f.write("\n")
            
            # Next Steps
            f.write("## Next Steps\n\n")
            f.write("1. **Review Results:** Analyze the detailed metrics and trade log\n")
            f.write("2. **Parameter Tuning:** Adjust hyperparameters based on recommendations\n")
            f.write("3. **Extended Training:** Run longer training with more episodes\n")
            f.write("4. **Real Data Testing:** Test with actual market data\n")
            f.write("5. **Risk Management:** Implement additional risk controls\n\n")
            
            # Files Generated
            f.write("## Generated Files\n\n")
            f.write("- `config.json` - Configuration used for this run\n")
            f.write("- `training_results.json` - Training metrics and progress\n")
            f.write("- `backtest_results.json` - Backtesting performance data\n")
            f.write("- `trade_log.csv` - Detailed trade records\n")
            f.write("- `performance_analysis.json` - Performance analysis results\n")
            f.write("- `WORKFLOW_REPORT.md` - This comprehensive report\n")
        
        logger.info(f"Workflow report generated: {report_file}")
        return report_file
    
    def run_workflow(self):
        """Run the complete simplified workflow"""
        
        try:
            print("\n" + "="*60)
            print("SIMPLIFIED RL TRADING WORKFLOW")
            print("="*60)
            print(f"Symbol: {self.symbol}")
            print(f"Timeframe: {self.timeframe}")
            print(f"Output Directory: {self.output_dir}")
            print("="*60)
            
            # Step 1: Create configuration
            print("\n📋 Step 1: Creating Configuration")
            config = self.create_config()
            print("✅ Configuration created")
            
            # Step 2: Check dependencies
            print("\n🔍 Step 2: Checking Dependencies")
            dependencies = self.check_dependencies()
            available_count = sum(1 for v in dependencies.values() if v)
            total_count = len(dependencies)
            print(f"✅ Dependencies checked ({available_count}/{total_count} available)")
            
            # Step 3: Simulate training
            print("\n🎯 Step 3: Simulating Training")
            training_results = self.simulate_training(config)
            print("✅ Training simulation completed")
            
            # Step 4: Simulate backtesting
            print("\n📊 Step 4: Simulating Backtesting")
            backtest_results = self.simulate_backtesting(config)
            print("✅ Backtesting simulation completed")
            
            # Step 5: Analyze performance
            print("\n📈 Step 5: Analyzing Performance")
            analysis = self.analyze_performance(training_results, backtest_results)
            print("✅ Performance analysis completed")
            
            # Step 6: Generate report
            print("\n📝 Step 6: Generating Report")
            report_file = self.generate_report(config, training_results, backtest_results, analysis)
            print("✅ Report generated")
            
            print("\n" + "="*60)
            print("WORKFLOW COMPLETED SUCCESSFULLY!")
            print("="*60)
            print(f"Performance Grade: {analysis['performance_grade']}")
            print(f"Total Return: {backtest_results['total_return']:.2%}")
            print(f"Sharpe Ratio: {backtest_results['sharpe_ratio']:.2f}")
            print(f"Max Drawdown: {backtest_results['max_drawdown']:.2%}")
            print(f"\nDetailed Report: {report_file}")
            print(f"All Results: {self.output_dir}")
            print("="*60)
            
            return True
            
        except Exception as e:
            logger.error(f"Workflow failed: {e}")
            print(f"\n❌ Workflow failed: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description="Simple RL Trading Workflow")
    parser.add_argument("--symbol", default="ETH", help="Trading symbol (e.g., ETH, BTC)")
    parser.add_argument("--timeframe", default="5m", help="Timeframe (e.g., 5m, 15m, 1h)")
    
    args = parser.parse_args()
    
    # Import numpy here to avoid issues if not available
    try:
        import numpy as np
    except ImportError:
        print("Error: numpy is required. Please install with: pip install numpy pandas")
        sys.exit(1)
    
    # Initialize and run workflow
    workflow = SimpleRLWorkflow(
        symbol=args.symbol,
        timeframe=args.timeframe
    )
    
    success = workflow.run_workflow()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()