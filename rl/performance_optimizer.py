"""
Performance optimization utilities for RL training.
Focuses on memory efficiency and training speed improvements.
"""

import torch
import numpy as np
import gc
import psutil
import time
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
from contextlib import contextmanager

@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    memory_usage_mb: float
    cpu_usage_percent: float
    gpu_memory_mb: Optional[float]
    training_time_seconds: float
    episodes_per_second: float
    
class MemoryOptimizer:
    """Memory usage optimization utilities"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        stats = {
            'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
            'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
            'cpu_percent': process.cpu_percent(),
        }
        
        # GPU memory if available
        if torch.cuda.is_available():
            stats['gpu_allocated_mb'] = torch.cuda.memory_allocated() / 1024 / 1024
            stats['gpu_reserved_mb'] = torch.cuda.memory_reserved() / 1024 / 1024
            
        return stats
    
    def clear_memory(self):
        """Force garbage collection and clear GPU cache"""
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            
    def optimize_tensor_memory(self, tensor: torch.Tensor) -> torch.Tensor:
        """Optimize tensor memory usage"""
        # Use half precision for inference if possible
        if tensor.dtype == torch.float32 and not tensor.requires_grad:
            return tensor.half()
        return tensor
    
    @contextmanager
    def memory_monitor(self, operation_name: str):
        """Context manager to monitor memory usage during operations"""
        start_memory = self.get_memory_usage()
        start_time = time.time()
        
        try:
            yield
        finally:
            end_memory = self.get_memory_usage()
            end_time = time.time()
            
            memory_diff = end_memory['rss_mb'] - start_memory['rss_mb']
            time_diff = end_time - start_time
            
            self.logger.info(f"{operation_name}: Memory change: {memory_diff:.2f}MB, Time: {time_diff:.2f}s")

class TrainingSpeedOptimizer:
    """Training speed optimization utilities"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def optimize_dataloader_workers(self, dataset_size: int) -> int:
        """Determine optimal number of dataloader workers"""
        cpu_count = psutil.cpu_count()
        
        # Rule of thumb: 2-4 workers per CPU core, but not more than dataset batches
        optimal_workers = min(cpu_count * 2, dataset_size // 10, 8)
        return max(1, optimal_workers)
    
    def setup_torch_optimizations(self):
        """Setup PyTorch optimizations for training"""
        # Enable cuDNN benchmark for consistent input sizes
        if torch.cuda.is_available():
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            
        # Set number of threads for CPU operations
        cpu_count = psutil.cpu_count()
        torch.set_num_threads(cpu_count)
        
        self.logger.info(f"PyTorch optimizations enabled: CPU threads={cpu_count}")
    
    def batch_size_finder(self, model: torch.nn.Module, input_shape: Tuple[int, ...], 
                         max_memory_mb: float = 8000) -> int:
        """Find optimal batch size that fits in memory"""
        device = next(model.parameters()).device
        
        # Start with small batch size and increase
        batch_size = 1
        max_batch_size = 1
        
        while batch_size <= 512:  # Reasonable upper limit
            try:
                # Create dummy input
                dummy_input = torch.randn(batch_size, *input_shape, device=device)
                
                # Forward pass
                with torch.no_grad():
                    _ = model(dummy_input)
                
                # Check memory usage
                if torch.cuda.is_available():
                    memory_used = torch.cuda.memory_allocated() / 1024 / 1024
                    if memory_used > max_memory_mb:
                        break
                
                max_batch_size = batch_size
                batch_size *= 2
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    break
                raise e
            finally:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
        
        self.logger.info(f"Optimal batch size found: {max_batch_size}")
        return max_batch_size

class DataOptimizer:
    """Data loading and preprocessing optimization"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def optimize_numpy_arrays(self, arrays: List[np.ndarray]) -> List[np.ndarray]:
        """Optimize numpy arrays for memory and speed"""
        optimized = []
        
        for arr in arrays:
            # Use appropriate dtype
            if arr.dtype == np.float64:
                arr = arr.astype(np.float32)
            elif arr.dtype == np.int64 and arr.max() < 2**31:
                arr = arr.astype(np.int32)
                
            # Ensure C-contiguous for faster access
            if not arr.flags['C_CONTIGUOUS']:
                arr = np.ascontiguousarray(arr)
                
            optimized.append(arr)
            
        return optimized
    
    def create_memory_mapped_dataset(self, data: np.ndarray, filepath: str) -> np.memmap:
        """Create memory-mapped dataset for large data"""
        memmap = np.memmap(filepath, dtype=data.dtype, mode='w+', shape=data.shape)
        memmap[:] = data[:]
        del data  # Free original data
        return memmap
    
    def batch_generator(self, data: np.ndarray, batch_size: int, shuffle: bool = True):
        """Memory-efficient batch generator"""
        n_samples = len(data)
        indices = np.arange(n_samples)
        
        if shuffle:
            np.random.shuffle(indices)
            
        for start_idx in range(0, n_samples, batch_size):
            end_idx = min(start_idx + batch_size, n_samples)
            batch_indices = indices[start_idx:end_idx]
            yield data[batch_indices]

class PerformanceProfiler:
    """Performance profiling and monitoring"""
    
    def __init__(self):
        self.metrics_history = []
        self.logger = logging.getLogger(__name__)
        
    def profile_training_step(self, step_func, *args, **kwargs) -> Tuple[any, PerformanceMetrics]:
        """Profile a single training step"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        start_cpu = psutil.cpu_percent()
        
        # GPU memory if available
        start_gpu = None
        if torch.cuda.is_available():
            start_gpu = torch.cuda.memory_allocated() / 1024 / 1024
            
        # Execute the step
        result = step_func(*args, **kwargs)
        
        # Measure end state
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        end_cpu = psutil.cpu_percent()
        
        end_gpu = None
        if torch.cuda.is_available():
            end_gpu = torch.cuda.memory_allocated() / 1024 / 1024
            
        # Create metrics
        metrics = PerformanceMetrics(
            memory_usage_mb=end_memory - start_memory,
            cpu_usage_percent=(start_cpu + end_cpu) / 2,
            gpu_memory_mb=end_gpu - start_gpu if end_gpu is not None else None,
            training_time_seconds=end_time - start_time,
            episodes_per_second=1.0 / (end_time - start_time) if end_time > start_time else 0
        )
        
        self.metrics_history.append(metrics)
        return result, metrics
    
    def get_performance_summary(self) -> Dict[str, float]:
        """Get summary of performance metrics"""
        if not self.metrics_history:
            return {}
            
        return {
            'avg_memory_mb': np.mean([m.memory_usage_mb for m in self.metrics_history]),
            'avg_cpu_percent': np.mean([m.cpu_usage_percent for m in self.metrics_history]),
            'avg_training_time': np.mean([m.training_time_seconds for m in self.metrics_history]),
            'avg_episodes_per_sec': np.mean([m.episodes_per_second for m in self.metrics_history]),
            'total_episodes': len(self.metrics_history)
        }
    
    def save_performance_report(self, filepath: str):
        """Save detailed performance report"""
        summary = self.get_performance_summary()
        
        with open(filepath, 'w') as f:
            f.write("Performance Report\n")
            f.write("==================\n\n")
            
            for key, value in summary.items():
                f.write(f"{key}: {value:.4f}\n")
                
            f.write(f"\nTotal training steps: {len(self.metrics_history)}\n")
            
            if self.metrics_history:
                f.write(f"Memory usage range: {min(m.memory_usage_mb for m in self.metrics_history):.2f} - "
                       f"{max(m.memory_usage_mb for m in self.metrics_history):.2f} MB\n")

# Utility functions for easy integration
def setup_performance_optimizations():
    """Setup all performance optimizations"""
    optimizer = TrainingSpeedOptimizer()
    optimizer.setup_torch_optimizations()
    
    memory_optimizer = MemoryOptimizer()
    memory_optimizer.clear_memory()
    
    return optimizer, memory_optimizer

def monitor_system_resources():
    """Get current system resource usage"""
    memory_optimizer = MemoryOptimizer()
    return memory_optimizer.get_memory_usage()