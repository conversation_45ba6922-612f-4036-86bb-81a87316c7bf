#!/usr/bin/env python3
"""
Complete End-to-End RL Trading Workflow Example

This script demonstrates the complete workflow from training an RL agent
to running backtests and evaluating performance.

Usage:
    python rl/example_complete_workflow.py --symbol ETH --timeframe 5m
    python rl/example_complete_workflow.py --symbol BTC --timeframe 15m --episodes 2000
"""

import os
import sys
import argparse
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Import dependencies with error handling
try:
    import pandas as pd
    import numpy as np
except ImportError as e:
    print(f"Error: Missing required dependencies: {e}")
    print("Please install with: pip install numpy pandas")
    sys.exit(1)

# Configure logging first
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('rl_workflow.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import RL modules with graceful fallback
rl_modules_available = True
try:
    from rl.training_manager import TrainingManager
    from rl.rl_backtester import RLBacktester
    from rl.performance_evaluator import PerformanceEvaluator
    from rl.benchmark_comparison import BenchmarkComparison
    from rl.config_validator import ConfigValidator
except ImportError as e:
    logger.warning(f"Some RL modules not available: {e}")
    rl_modules_available = False

# Model manager is not implemented yet
ModelManager = None

class CompleteRLWorkflow:
    """Complete RL trading workflow manager"""
    
    def __init__(self, symbol: str, timeframe: str, config_path: str = None):
        self.symbol = symbol.upper()
        self.timeframe = timeframe
        self.config_path = config_path or f"rl/configs/{symbol.lower()}_{timeframe}_config.json"
        
        # Create output directories
        self.output_dir = Path(f"rl_results/{symbol}_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Initialized workflow for {symbol} {timeframe}")
        logger.info(f"Output directory: {self.output_dir}")
        
    def load_or_create_config(self):
        """Load existing config or create default one"""
        if os.path.exists(self.config_path):
            logger.info(f"Loading config from {self.config_path}")
            with open(self.config_path, 'r') as f:
                config = json.load(f)
        else:
            logger.info("Creating default config")
            config = self.create_default_config()
            
        # Validate config if validator is available
        if rl_modules_available:
            try:
                validator = ConfigValidator()
                if not validator.validate_config(config):
                    logger.warning("Configuration validation failed, but continuing...")
            except Exception as e:
                logger.warning(f"Config validation error: {e}, continuing anyway...")
            
        return config
        
    def create_default_config(self):
        """Create default configuration for the symbol/timeframe"""
        return {
            "symbol": self.symbol,
            "timeframe": self.timeframe,
            "model_file": f"models/{self.symbol.lower()}_{self.timeframe}_model.joblib",
            "config_file": f"models/{self.symbol.lower()}_{self.timeframe}_config.json",
            
            # Training parameters
            "training": {
                "episodes": 1000,
                "episode_length": 1000,
                "learning_rate": 3e-4,
                "batch_size": 64,
                "update_frequency": 100,
                "gamma": 0.99,
                "clip_epsilon": 0.2,
                "entropy_coef": 0.01,
                "value_loss_coef": 0.5
            },
            
            # Environment parameters
            "environment": {
                "initial_capital": 10000,
                "transaction_cost": 0.001,
                "slippage": 0.0005,
                "max_position_size": 0.1,
                "min_position_size": 0.001,
                "max_positions": 5
            },
            
            # Reward function parameters
            "reward": {
                "profit_weight": 1.0,
                "risk_weight": 0.5,
                "efficiency_weight": 0.1,
                "drawdown_penalty": 2.0,
                "transaction_cost_penalty": 0.1
            },
            
            # Data parameters
            "data": {
                "train_start": "2023-01-01",
                "train_end": "2024-06-30",
                "val_start": "2024-07-01", 
                "val_end": "2024-09-30",
                "test_start": "2024-10-01",
                "test_end": "2024-12-31"
            }
        }
        
    def step1_prepare_data(self, config):
        """Step 1: Prepare and validate data"""
        logger.info("Step 1: Preparing data...")
        
        # Check if model files exist
        model_file = config["model_file"]
        config_file = config["config_file"]
        
        if not os.path.exists(model_file):
            raise FileNotFoundError(f"Model file not found: {model_file}")
        if not os.path.exists(config_file):
            raise FileNotFoundError(f"Config file not found: {config_file}")
            
        logger.info(f"Using model: {model_file}")
        logger.info(f"Using config: {config_file}")
        
        # Validate data availability
        try:
            from data_loader import DataProvider
            data_provider = DataProvider()
            
            # Test data loading for the specified period
            test_data = data_provider.get_data(
                symbol=self.symbol,
                timeframe=self.timeframe,
                start_date=config["data"]["train_start"],
                end_date=config["data"]["test_end"]
            )
            logger.info(f"Data loaded successfully: {len(test_data)} records")
            
        except ImportError:
            logger.warning("DataProvider not available, skipping data validation")
        except Exception as e:
            logger.warning(f"Data loading test failed: {e}, but continuing...")
            
        return True
        
    def step2_train_agent(self, config):
        """Step 2: Train the RL agent"""
        logger.info("Step 2: Training RL agent...")
        
        # Initialize training manager
        trainer = TrainingManager(config)
        
        # Train the agent
        training_results = trainer.train(
            episodes=config["training"]["episodes"],
            save_dir=str(self.output_dir / "models"),
            log_dir=str(self.output_dir / "logs")
        )
        
        # Save training results
        results_file = self.output_dir / "training_results.json"
        with open(results_file, 'w') as f:
            json.dump(training_results, f, indent=2)
            
        logger.info(f"Training completed. Results saved to {results_file}")
        return training_results
        
    def step3_validate_model(self, config, training_results):
        """Step 3: Validate the trained model"""
        logger.info("Step 3: Validating model...")
        
        # Load the best model
        model_path = self.output_dir / "models" / "best_model.pth"
        if not model_path.exists():
            raise FileNotFoundError(f"Trained model not found: {model_path}")
            
        # Run validation backtest
        backtester = RLBacktester(
            model_path=str(model_path),
            config=config
        )
        
        validation_results = backtester.run_backtest(
            start_date=config["data"]["val_start"],
            end_date=config["data"]["val_end"],
            mode="validation"
        )
        
        # Save validation results
        val_file = self.output_dir / "validation_results.json"
        with open(val_file, 'w') as f:
            json.dump(validation_results, f, indent=2)
            
        logger.info(f"Validation completed. Results saved to {val_file}")
        return validation_results
        
    def step4_run_backtest(self, config):
        """Step 4: Run comprehensive backtest"""
        logger.info("Step 4: Running comprehensive backtest...")
        
        model_path = self.output_dir / "models" / "best_model.pth"
        
        # Initialize backtester
        backtester = RLBacktester(
            model_path=str(model_path),
            config=config
        )
        
        # Run test backtest
        test_results = backtester.run_backtest(
            start_date=config["data"]["test_start"],
            end_date=config["data"]["test_end"],
            mode="test"
        )
        
        # Save detailed results
        test_file = self.output_dir / "test_results.json"
        with open(test_file, 'w') as f:
            json.dump(test_results, f, indent=2)
            
        # Save trade log
        if 'trades' in test_results:
            trades_df = pd.DataFrame(test_results['trades'])
            trades_file = self.output_dir / "trade_log.csv"
            trades_df.to_csv(trades_file, index=False)
            logger.info(f"Trade log saved to {trades_file}")
            
        logger.info(f"Backtest completed. Results saved to {test_file}")
        return test_results
        
    def step5_benchmark_comparison(self, config, test_results):
        """Step 5: Compare with benchmark strategies"""
        logger.info("Step 5: Running benchmark comparison...")
        
        # Initialize benchmark comparison
        benchmark = BenchmarkComparison(config)
        
        # Run comparisons
        comparison_results = benchmark.compare_strategies(
            rl_results=test_results,
            start_date=config["data"]["test_start"],
            end_date=config["data"]["test_end"]
        )
        
        # Save comparison results
        comp_file = self.output_dir / "benchmark_comparison.json"
        with open(comp_file, 'w') as f:
            json.dump(comparison_results, f, indent=2)
            
        # Generate comparison charts
        chart_file = self.output_dir / "performance_comparison.png"
        benchmark.plot_comparison(comparison_results, save_path=str(chart_file))
        
        logger.info(f"Benchmark comparison completed. Results saved to {comp_file}")
        return comparison_results
        
    def step6_performance_analysis(self, config, test_results, comparison_results):
        """Step 6: Detailed performance analysis"""
        logger.info("Step 6: Generating performance analysis...")
        
        # Initialize performance evaluator
        evaluator = PerformanceEvaluator(config)
        
        # Generate comprehensive analysis
        analysis_results = evaluator.analyze_performance(
            test_results,
            comparison_results,
            output_dir=str(self.output_dir)
        )
        
        # Save analysis report
        report_file = self.output_dir / "performance_report.json"
        with open(report_file, 'w') as f:
            json.dump(analysis_results, f, indent=2)
            
        # Generate summary report
        self.generate_summary_report(analysis_results)
        
        logger.info(f"Performance analysis completed. Report saved to {report_file}")
        return analysis_results
        
    def generate_summary_report(self, analysis_results):
        """Generate human-readable summary report"""
        report_file = self.output_dir / "SUMMARY_REPORT.md"
        
        with open(report_file, 'w') as f:
            f.write(f"# RL Trading Model Performance Report\n\n")
            f.write(f"**Symbol:** {self.symbol}\n")
            f.write(f"**Timeframe:** {self.timeframe}\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Key metrics
            f.write("## Key Performance Metrics\n\n")
            metrics = analysis_results.get('key_metrics', {})
            for key, value in metrics.items():
                f.write(f"- **{key}:** {value}\n")
            f.write("\n")
            
            # Comparison with benchmarks
            f.write("## Benchmark Comparison\n\n")
            comparison = analysis_results.get('benchmark_comparison', {})
            for strategy, results in comparison.items():
                f.write(f"### vs {strategy}\n")
                for metric, value in results.items():
                    f.write(f"- {metric}: {value}\n")
                f.write("\n")
            
            # Recommendations
            f.write("## Recommendations\n\n")
            recommendations = analysis_results.get('recommendations', [])
            for rec in recommendations:
                f.write(f"- {rec}\n")
            f.write("\n")
            
            # Files generated
            f.write("## Generated Files\n\n")
            f.write("- `training_results.json` - Training metrics and logs\n")
            f.write("- `validation_results.json` - Validation performance\n")
            f.write("- `test_results.json` - Test period performance\n")
            f.write("- `trade_log.csv` - Detailed trade records\n")
            f.write("- `benchmark_comparison.json` - Comparison with other strategies\n")
            f.write("- `performance_comparison.png` - Performance visualization\n")
            f.write("- `models/best_model.pth` - Trained RL model\n")
            f.write("- `logs/` - Training logs and metrics\n")
            
        logger.info(f"Summary report generated: {report_file}")
        
    def run_complete_workflow(self):
        """Run the complete workflow"""
        try:
            logger.info("Starting complete RL trading workflow...")
            
            # Check if RL modules are available and compatible
            if not rl_modules_available:
                logger.error("Required RL modules are not available. Please ensure all RL components are properly installed.")
                print("\n❌ Error: Required RL modules are not available.")
                print("This appears to be a development environment where some RL components are not yet implemented.")
                print("\nTry using the simple workflow instead:")
                print(f"python rl/example_simple_workflow.py --symbol {self.symbol} --timeframe {self.timeframe}")
                return False
            
            # Additional compatibility check
            try:
                # Test if we can create a TrainingManager with a simple config
                test_config = {"logging": {"log_dir": "test"}}
                TrainingManager(test_config)
            except Exception as e:
                logger.error(f"RL modules are not compatible with current configuration format: {e}")
                print("\n❌ Error: RL modules are not compatible with the current system.")
                print("This appears to be a development environment where RL components are still being integrated.")
                print("\nTry using the simple workflow instead:")
                print(f"python rl/example_simple_workflow.py --symbol {self.symbol} --timeframe {self.timeframe}")
                return False
            
            # Load configuration
            config = self.load_or_create_config()
            
            # Save config to output directory
            config_file = self.output_dir / "config.json"
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            # Run all steps
            self.step1_prepare_data(config)
            training_results = self.step2_train_agent(config)
            validation_results = self.step3_validate_model(config, training_results)
            test_results = self.step4_run_backtest(config)
            comparison_results = self.step5_benchmark_comparison(config, test_results)
            analysis_results = self.step6_performance_analysis(config, test_results, comparison_results)
            
            logger.info("Complete workflow finished successfully!")
            logger.info(f"All results saved to: {self.output_dir}")
            
            # Print summary
            print("\n" + "="*60)
            print("RL TRADING WORKFLOW COMPLETED")
            print("="*60)
            print(f"Symbol: {self.symbol}")
            print(f"Timeframe: {self.timeframe}")
            print(f"Output Directory: {self.output_dir}")
            print(f"Summary Report: {self.output_dir}/SUMMARY_REPORT.md")
            print("="*60)
            
            return True
            
        except Exception as e:
            logger.error(f"Workflow failed: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description="Complete RL Trading Workflow")
    parser.add_argument("--symbol", required=True, help="Trading symbol (e.g., ETH, BTC)")
    parser.add_argument("--timeframe", required=True, help="Timeframe (e.g., 5m, 15m, 1h)")
    parser.add_argument("--config", help="Custom config file path")
    parser.add_argument("--episodes", type=int, help="Number of training episodes")
    
    args = parser.parse_args()
    
    # Initialize workflow
    workflow = CompleteRLWorkflow(
        symbol=args.symbol,
        timeframe=args.timeframe,
        config_path=args.config
    )
    
    # Override episodes if specified
    if args.episodes:
        config = workflow.load_or_create_config()
        config["training"]["episodes"] = args.episodes
        workflow.config_path = str(workflow.output_dir / "config.json")
        with open(workflow.config_path, 'w') as f:
            json.dump(config, f, indent=2)
    
    # Run workflow
    workflow.run_complete_workflow()

if __name__ == "__main__":
    main()