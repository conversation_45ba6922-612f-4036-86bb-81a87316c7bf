#!/usr/bin/env python3
# test_config_minimal.py
# 最小化配置系统测试

def test_imports():
    """测试导入"""
    print("测试模块导入...")
    
    try:
        from .training_config import (
            FullTrainingConfig, DataConfig, ModelConfig, 
            EnvironmentConfig, RewardConfig, RLConfig, 
            TrainingConfig, LoggingConfig
        )
        print("✅ 配置类导入成功")
        
        from .config_validator import ConfigValidator
        print("✅ 验证器导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_config_creation():
    """测试配置创建"""
    print("测试配置创建...")
    
    try:
        from .training_config import FullTrainingConfig
        
        # 创建默认配置
        config = FullTrainingConfig()
        
        # 检查基本属性
        assert hasattr(config, 'data')
        assert hasattr(config, 'model')
        assert hasattr(config, 'environment')
        assert hasattr(config, 'reward')
        assert hasattr(config, 'rl')
        assert hasattr(config, 'training')
        assert hasattr(config, 'logging')
        
        # 检查默认值
        assert config.data.coin == "ETH"
        assert config.data.interval == "5m"
        assert config.environment.initial_capital == 10000.0
        assert config.rl.learning_rate == 3e-4
        assert config.training.episodes == 1000
        
        print("✅ 配置创建和默认值检查成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置创建失败: {e}")
        return False


def test_config_modification():
    """测试配置修改"""
    print("测试配置修改...")
    
    try:
        from .training_config import FullTrainingConfig
        
        config = FullTrainingConfig()
        
        # 修改配置
        config.config_name = "test_config"
        config.rl.learning_rate = 0.0001
        config.environment.max_position_size = 0.08
        config.training.episodes = 1500
        
        # 验证修改
        assert config.config_name == "test_config"
        assert config.rl.learning_rate == 0.0001
        assert config.environment.max_position_size == 0.08
        assert config.training.episodes == 1500
        
        print("✅ 配置修改成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置修改失败: {e}")
        return False


def test_basic_validation():
    """测试基本验证"""
    print("测试基本验证...")
    
    try:
        from .training_config import FullTrainingConfig
        from .config_validator import ConfigValidator
        
        validator = ConfigValidator()
        
        # 测试有效配置
        valid_config = FullTrainingConfig()
        issues = validator.validate_config(valid_config)
        
        print(f"验证结果: {len(issues['errors'])} 错误, {len(issues['warnings'])} 警告")
        
        # 应该没有严重错误
        assert isinstance(issues, dict)
        assert 'errors' in issues
        assert 'warnings' in issues
        assert 'suggestions' in issues
        
        print("✅ 基本验证功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 基本验证失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始最小化配置系统测试...")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_config_creation,
        test_config_modification,
        test_basic_validation
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print("✅ 通过\n")
            else:
                failed += 1
                print("❌ 失败\n")
        except Exception as e:
            print(f"❌ 测试异常: {e}\n")
            failed += 1
    
    print(f"测试结果: {passed}/{len(tests)} 通过")
    
    if failed == 0:
        print("🎉 所有基本功能测试通过！")
        return True
    else:
        print("⚠️  部分测试失败")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)