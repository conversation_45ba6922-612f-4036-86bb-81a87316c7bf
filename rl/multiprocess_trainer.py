"""
Multiprocess training support for RL trading optimization.
Implements distributed training across multiple processes for improved performance.
"""

import multiprocessing as mp
import numpy as np
import pandas as pd
import torch
import torch.multiprocessing as torch_mp
import logging
import time
import queue
import signal
import os
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from concurrent.futures import ProcessPoolExecutor, as_completed
from pathlib import Path
import pickle
import tempfile

from .trading_environment import TradingEnvironment
from .rl_trading_agent import RLTradingAgent
from .performance_optimizer import MemoryOptimizer, monitor_system_resources


@dataclass
class WorkerConfig:
    """Configuration for worker processes"""
    worker_id: int
    episodes_per_worker: int
    data_path: str
    signals_path: str
    config_dict: Dict[str, Any]
    shared_model_path: str
    results_queue_name: str


@dataclass
class WorkerResult:
    """Result from a worker process"""
    worker_id: int
    episodes_completed: int
    total_reward: float
    avg_return: float
    training_time: float
    memory_usage: Dict[str, float]
    model_updates: Optional[bytes] = None


class SharedModelManager:
    """Manages shared model state across processes"""
    
    def __init__(self, model_path: str):
        self.model_path = Path(model_path)
        self.lock = mp.Lock()
        self.update_counter = mp.Value('i', 0)
        
    def save_model(self, model_state: Dict[str, Any]):
        """Save model state to shared location"""
        with self.lock:
            temp_path = self.model_path.with_suffix('.tmp')
            torch.save(model_state, temp_path)
            temp_path.replace(self.model_path)
            
            with self.update_counter.get_lock():
                self.update_counter.value += 1
    
    def load_model(self) -> Dict[str, Any]:
        """Load model state from shared location"""
        with self.lock:
            if self.model_path.exists():
                return torch.load(self.model_path)
            return None
    
    def get_update_count(self) -> int:
        """Get current update counter value"""
        with self.update_counter.get_lock():
            return self.update_counter.value


class MultiprocessTrainer:
    """Multiprocess trainer for RL trading optimization"""
    
    def __init__(self, config: Dict[str, Any], num_workers: int = None):
        self.config = config
        self.num_workers = num_workers or max(1, mp.cpu_count() - 1)
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Performance monitoring
        self.memory_optimizer = MemoryOptimizer()
        
        # Shared resources
        self.shared_model_manager = None
        self.temp_dir = None
        
        # Training state
        self.is_training = False
        self.workers = []
        
        self.logger.info(f"Initialized multiprocess trainer with {self.num_workers} workers")
    
    def setup_shared_resources(self):
        """Setup shared resources for multiprocess training"""
        # Create temporary directory for shared files
        self.temp_dir = tempfile.mkdtemp(prefix='rl_training_')
        
        # Setup shared model manager
        shared_model_path = os.path.join(self.temp_dir, 'shared_model.pth')
        self.shared_model_manager = SharedModelManager(shared_model_path)
        
        self.logger.info(f"Shared resources created in {self.temp_dir}")
    
    def cleanup_shared_resources(self):
        """Cleanup shared resources"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
            self.logger.info("Shared resources cleaned up")
    
    def prepare_worker_data(self, train_data: pd.DataFrame, 
                           train_signals: pd.DataFrame) -> List[str]:
        """Prepare data files for worker processes"""
        data_files = []
        
        # Split data among workers
        data_chunks = np.array_split(train_data, self.num_workers)
        signal_chunks = np.array_split(train_signals, self.num_workers) if train_signals is not None else [None] * self.num_workers
        
        for i, (data_chunk, signal_chunk) in enumerate(zip(data_chunks, signal_chunks)):
            # Save data chunk
            data_path = os.path.join(self.temp_dir, f'worker_{i}_data.pkl')
            with open(data_path, 'wb') as f:
                pickle.dump(data_chunk, f)
            
            # Save signal chunk
            signals_path = os.path.join(self.temp_dir, f'worker_{i}_signals.pkl')
            if signal_chunk is not None:
                with open(signals_path, 'wb') as f:
                    pickle.dump(signal_chunk, f)
            else:
                signals_path = None
            
            data_files.append((data_path, signals_path))
        
        return data_files
    
    def create_worker_configs(self, episodes: int, 
                            data_files: List[Tuple[str, str]]) -> List[WorkerConfig]:
        """Create configuration for each worker"""
        episodes_per_worker = episodes // self.num_workers
        remaining_episodes = episodes % self.num_workers
        
        worker_configs = []
        
        for i, (data_path, signals_path) in enumerate(data_files):
            # Distribute remaining episodes among first workers
            worker_episodes = episodes_per_worker + (1 if i < remaining_episodes else 0)
            
            config = WorkerConfig(
                worker_id=i,
                episodes_per_worker=worker_episodes,
                data_path=data_path,
                signals_path=signals_path,
                config_dict=self.config,
                shared_model_path=self.shared_model_manager.model_path,
                results_queue_name=f'results_queue_{i}'
            )
            
            worker_configs.append(config)
        
        return worker_configs
    
    def train_distributed(self, train_data: pd.DataFrame, 
                         train_signals: pd.DataFrame,
                         episodes: int,
                         sync_frequency: int = 50) -> Dict[str, Any]:
        """
        Run distributed training across multiple processes
        
        Args:
            train_data: Training data
            train_signals: Training signals
            episodes: Total episodes to train
            sync_frequency: How often to synchronize models between workers
            
        Returns:
            Training results dictionary
        """
        self.logger.info(f"Starting distributed training: {episodes} episodes, {self.num_workers} workers")
        
        try:
            # Setup shared resources
            self.setup_shared_resources()
            
            # Prepare worker data
            data_files = self.prepare_worker_data(train_data, train_signals)
            
            # Create worker configurations
            worker_configs = self.create_worker_configs(episodes, data_files)
            
            # Start training
            start_time = time.time()
            self.is_training = True
            
            # Use ProcessPoolExecutor for better resource management
            with ProcessPoolExecutor(max_workers=self.num_workers) as executor:
                # Submit worker tasks
                futures = []
                for config in worker_configs:
                    if config.episodes_per_worker > 0:
                        future = executor.submit(
                            self._worker_training_loop,
                            config, sync_frequency
                        )
                        futures.append(future)
                
                # Collect results
                worker_results = []
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        worker_results.append(result)
                        self.logger.info(
                            f"Worker {result.worker_id} completed: "
                            f"{result.episodes_completed} episodes, "
                            f"avg reward: {result.total_reward:.4f}"
                        )
                    except Exception as e:
                        self.logger.error(f"Worker failed: {e}")
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Aggregate results
            results = self._aggregate_worker_results(worker_results, total_time)
            
            self.logger.info(f"Distributed training completed in {total_time:.2f}s")
            
            return results
            
        finally:
            self.is_training = False
            self.cleanup_shared_resources()
    
    @staticmethod
    def _worker_training_loop(config: WorkerConfig, sync_frequency: int) -> WorkerResult:
        """
        Training loop for individual worker process
        
        Args:
            config: Worker configuration
            sync_frequency: Model synchronization frequency
            
        Returns:
            Worker training results
        """
        # Setup worker logging
        logger = logging.getLogger(f'worker_{config.worker_id}')
        logger.info(f"Worker {config.worker_id} starting training")
        
        # Initialize memory optimizer
        memory_optimizer = MemoryOptimizer()
        
        try:
            # Load worker data
            with open(config.data_path, 'rb') as f:
                worker_data = pickle.load(f)
            
            worker_signals = None
            if config.signals_path:
                with open(config.signals_path, 'rb') as f:
                    worker_signals = pickle.load(f)
            
            # Create worker environment and agent
            env = TradingEnvironment(config.config_dict)
            agent = RLTradingAgent(config.config_dict)
            
            # Load shared model if available
            shared_manager = SharedModelManager(config.shared_model_path)
            shared_state = shared_manager.load_model()
            if shared_state:
                agent.load_state_dict(shared_state)
            
            # Training loop
            total_reward = 0.0
            total_return = 0.0
            episodes_completed = 0
            last_sync_update = 0
            
            start_time = time.time()
            
            for episode in range(config.episodes_per_worker):
                # Run episode
                episode_result = MultiprocessTrainer._run_worker_episode(
                    env, agent, worker_data, worker_signals
                )
                
                total_reward += episode_result['reward']
                total_return += episode_result['return']
                episodes_completed += 1
                
                # Periodic model synchronization
                if episode % sync_frequency == 0:
                    current_update = shared_manager.get_update_count()
                    
                    # Load updated model if available
                    if current_update > last_sync_update:
                        shared_state = shared_manager.load_model()
                        if shared_state:
                            agent.load_state_dict(shared_state)
                            last_sync_update = current_update
                    
                    # Save our model updates
                    shared_manager.save_model(agent.get_state_dict())
                
                # Memory cleanup
                if episode % 20 == 0:
                    memory_optimizer.clear_memory()
            
            end_time = time.time()
            training_time = end_time - start_time
            
            # Get final memory usage
            memory_usage = memory_optimizer.get_memory_usage()
            
            logger.info(f"Worker {config.worker_id} completed training")
            
            return WorkerResult(
                worker_id=config.worker_id,
                episodes_completed=episodes_completed,
                total_reward=total_reward / max(episodes_completed, 1),
                avg_return=total_return / max(episodes_completed, 1),
                training_time=training_time,
                memory_usage=memory_usage,
                model_updates=pickle.dumps(agent.get_state_dict())
            )
            
        except Exception as e:
            logger.error(f"Worker {config.worker_id} failed: {e}")
            raise
    
    @staticmethod
    def _run_worker_episode(env: TradingEnvironment, 
                           agent: RLTradingAgent,
                           data: pd.DataFrame,
                           signals: pd.DataFrame) -> Dict[str, float]:
        """
        Run a single episode in worker process
        
        Args:
            env: Trading environment
            agent: RL agent
            data: Price data
            signals: Signal data
            
        Returns:
            Episode results
        """
        # Reset environment
        env.reset()
        
        # Sample episode data
        episode_length = min(1000, len(data))  # Limit episode length
        if len(data) > episode_length:
            start_idx = np.random.randint(0, len(data) - episode_length)
            episode_data = data.iloc[start_idx:start_idx + episode_length]
            if signals is not None:
                episode_signals = signals.iloc[start_idx:start_idx + episode_length]
            else:
                episode_signals = None
        else:
            episode_data = data
            episode_signals = signals
        
        total_reward = 0.0
        initial_capital = env.current_capital
        
        # Run episode steps
        for i in range(len(episode_data)):
            # Create market state (simplified)
            current_price = episode_data.iloc[i]['close']
            
            # Get signal if available
            signal = None
            confidence = 0.5
            if episode_signals is not None and i < len(episode_signals):
                signal_row = episode_signals.iloc[i]
                if 'signal' in signal_row and signal_row['signal'] != -1:
                    signal = signal_row['signal']
                    confidence = signal_row.get('confidence', 0.5)
            
            # Skip if no signal
            if signal is None:
                continue
            
            # Create simplified market state
            market_state = {
                'model_signal': signal,
                'signal_confidence': confidence,
                'current_price': current_price,
                'portfolio_metrics': env.get_portfolio_metrics()
            }
            
            # Get action from agent
            state_vector = env.get_state_vector(market_state)
            action_dict, _, _ = agent.get_action(state_vector, deterministic=False)
            
            # Execute action (simplified)
            if action_dict['enter_trade'] and action_dict['position_size'] > 0.001:
                # Simple position entry
                position_size = min(action_dict['position_size'], 0.1)
                capital_used = env.current_capital * position_size
                
                if capital_used > 100:  # Minimum position size
                    # Simulate trade execution
                    entry_price = current_price
                    
                    # Simple reward calculation
                    if i < len(episode_data) - 10:  # Look ahead for reward
                        future_price = episode_data.iloc[i + 10]['close']
                        if signal == 1:  # Long position
                            pnl_pct = (future_price - entry_price) / entry_price
                        else:  # Short position
                            pnl_pct = (entry_price - future_price) / entry_price
                        
                        reward = pnl_pct * position_size
                        total_reward += reward
        
        # Calculate final return
        final_capital = env.current_capital
        total_return = (final_capital - initial_capital) / initial_capital
        
        return {
            'reward': total_reward,
            'return': total_return
        }
    
    def _aggregate_worker_results(self, worker_results: List[WorkerResult], 
                                 total_time: float) -> Dict[str, Any]:
        """
        Aggregate results from all workers
        
        Args:
            worker_results: List of worker results
            total_time: Total training time
            
        Returns:
            Aggregated results dictionary
        """
        if not worker_results:
            return {'error': 'No worker results'}
        
        total_episodes = sum(r.episodes_completed for r in worker_results)
        avg_reward = np.mean([r.total_reward for r in worker_results])
        avg_return = np.mean([r.avg_return for r in worker_results])
        
        # Memory statistics
        memory_stats = {}
        for key in worker_results[0].memory_usage.keys():
            values = [r.memory_usage[key] for r in worker_results if key in r.memory_usage]
            if values:
                memory_stats[f'avg_{key}'] = np.mean(values)
                memory_stats[f'max_{key}'] = np.max(values)
        
        results = {
            'summary': {
                'total_episodes': total_episodes,
                'avg_reward': avg_reward,
                'avg_return': avg_return,
                'total_time': total_time,
                'episodes_per_second': total_episodes / total_time,
                'workers_used': len(worker_results)
            },
            'worker_results': [
                {
                    'worker_id': r.worker_id,
                    'episodes': r.episodes_completed,
                    'reward': r.total_reward,
                    'return': r.avg_return,
                    'time': r.training_time
                }
                for r in worker_results
            ],
            'memory_stats': memory_stats,
            'performance': {
                'parallel_efficiency': (total_episodes / total_time) / (sum(r.episodes_completed / r.training_time for r in worker_results) / len(worker_results)),
                'memory_efficiency': memory_stats.get('avg_rss_mb', 0) * len(worker_results)
            }
        }
        
        return results
    
    def train_with_dynamic_scaling(self, train_data: pd.DataFrame,
                                  train_signals: pd.DataFrame,
                                  episodes: int,
                                  target_episodes_per_second: float = 1.0) -> Dict[str, Any]:
        """
        Train with dynamic worker scaling based on performance
        
        Args:
            train_data: Training data
            train_signals: Training signals
            episodes: Total episodes
            target_episodes_per_second: Target performance
            
        Returns:
            Training results
        """
        self.logger.info("Starting dynamic scaling training")
        
        # Start with half the available workers
        current_workers = max(1, self.num_workers // 2)
        episodes_per_batch = 100
        completed_episodes = 0
        
        all_results = []
        
        while completed_episodes < episodes:
            remaining_episodes = episodes - completed_episodes
            batch_episodes = min(episodes_per_batch, remaining_episodes)
            
            # Temporarily set worker count
            original_workers = self.num_workers
            self.num_workers = current_workers
            
            try:
                # Run training batch
                batch_results = self.train_distributed(
                    train_data, train_signals, batch_episodes
                )
                
                all_results.append(batch_results)
                completed_episodes += batch_results['summary']['total_episodes']
                
                # Analyze performance
                eps_per_sec = batch_results['summary']['episodes_per_second']
                memory_usage = batch_results['memory_stats'].get('max_rss_mb', 0)
                
                self.logger.info(
                    f"Batch completed: {current_workers} workers, "
                    f"{eps_per_sec:.2f} eps/sec, {memory_usage:.1f}MB memory"
                )
                
                # Adjust worker count based on performance
                if eps_per_sec < target_episodes_per_second * 0.8:
                    # Performance too low, reduce workers
                    current_workers = max(1, current_workers - 1)
                    self.logger.info(f"Reducing workers to {current_workers}")
                elif eps_per_sec > target_episodes_per_second * 1.2 and memory_usage < 8000:
                    # Performance good and memory available, increase workers
                    current_workers = min(original_workers, current_workers + 1)
                    self.logger.info(f"Increasing workers to {current_workers}")
                
            finally:
                # Restore original worker count
                self.num_workers = original_workers
        
        # Aggregate all results
        total_time = sum(r['summary']['total_time'] for r in all_results)
        total_episodes = sum(r['summary']['total_episodes'] for r in all_results)
        avg_reward = np.mean([r['summary']['avg_reward'] for r in all_results])
        
        return {
            'summary': {
                'total_episodes': total_episodes,
                'avg_reward': avg_reward,
                'total_time': total_time,
                'episodes_per_second': total_episodes / total_time,
                'batches_run': len(all_results)
            },
            'batch_results': all_results,
            'final_workers': current_workers
        }