# training_config.py
# 强化学习训练配置管理系统

import json
import os
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict, field
from pathlib import Path
import copy

@dataclass
class DataConfig:
    """数据配置"""
    db_path: str = "coin_data.db"
    coin: str = "ETH"
    interval: str = "5m"
    market: str = "spot"
    train_ratio: float = 0.7
    val_ratio: float = 0.15
    test_ratio: float = 0.15

@dataclass
class ModelConfig:
    """模型配置"""
    model_file: Optional[str] = None
    config_file: Optional[str] = None
    state_dim: int = 20
    hidden_dim: int = 256
    num_layers: int = 3
    activation: str = "relu"
    dropout_rate: float = 0.1

@dataclass
class EnvironmentConfig:
    """环境配置"""
    initial_capital: float = 10000.0
    transaction_cost: float = 0.001
    max_position_size: float = 0.1
    max_positions: int = 5
    slippage: float = 0.0001
    min_trade_amount: float = 10.0

@dataclass
class RewardConfig:
    """奖励函数配置"""
    pnl_weight: float = 1.0
    risk_penalty_weight: float = 0.5
    time_efficiency_weight: float = 0.1
    drawdown_penalty_weight: float = 2.0
    sharpe_bonus_weight: float = 0.3
    max_drawdown_threshold: float = 0.05
    volatility_penalty_weight: float = 0.2
    win_rate_bonus_weight: float = 0.4
    consecutive_win_bonus: float = 0.1
    trade_frequency_penalty: float = 0.05
    market_regime_adjustments: Dict[str, float] = field(default_factory=lambda: {
        "trending_up": 1.2,
        "trending_down": 0.8,
        "sideways": 0.9,
        "high_volatility": 0.7,
        "low_volatility": 1.1
    })
    adaptive_learning_rate: float = 0.01
    performance_window: int = 50

@dataclass
class RLConfig:
    """强化学习算法配置"""
    learning_rate: float = 3e-4
    batch_size: int = 64
    gamma: float = 0.99
    clip_epsilon: float = 0.2
    value_loss_coef: float = 0.5
    entropy_coef: float = 0.01
    gae_lambda: float = 0.95
    update_epochs: int = 4
    max_grad_norm: float = 0.5
    buffer_size: int = 10000
    exploration_noise: float = 0.1
    epsilon_start: float = 1.0
    epsilon_end: float = 0.1
    epsilon_decay: float = 0.995

@dataclass
class TrainingConfig:
    """训练配置"""
    episodes: int = 1000
    episode_length: int = 1000
    update_frequency: int = 100
    eval_frequency: int = 50
    save_frequency: int = 100
    early_stopping_patience: int = 200
    early_stopping_threshold: float = 0.001
    max_training_time_hours: float = 24.0
    resume_from_checkpoint: bool = False
    checkpoint_path: Optional[str] = None

@dataclass
class LoggingConfig:
    """日志配置"""
    log_dir: str = "rl_training_logs"
    log_level: str = "INFO"
    save_plots: bool = True
    plot_frequency: int = 100
    tensorboard_logging: bool = False
    wandb_logging: bool = False
    wandb_project: Optional[str] = None

@dataclass
class FullTrainingConfig:
    """完整的训练配置"""
    data: DataConfig = field(default_factory=DataConfig)
    model: ModelConfig = field(default_factory=ModelConfig)
    environment: EnvironmentConfig = field(default_factory=EnvironmentConfig)
    reward: RewardConfig = field(default_factory=RewardConfig)
    rl: RLConfig = field(default_factory=RLConfig)
    training: TrainingConfig = field(default_factory=TrainingConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    # 元数据
    config_name: str = "default"
    description: str = "默认训练配置"
    version: str = "1.0"
    created_by: str = "system"


class TrainingConfigManager:
    """训练配置管理器"""
    
    def __init__(self, config_dir: str = "rl/configs"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.templates_dir = self.config_dir / "templates"
        self.templates_dir.mkdir(exist_ok=True)
        self.custom_dir = self.config_dir / "custom"
        self.custom_dir.mkdir(exist_ok=True)
        
        # 创建默认配置模板
        self._create_default_templates()
    
    def _create_default_templates(self):
        """创建默认配置模板"""
        templates = self._get_default_templates()
        
        for name, config in templates.items():
            template_path = self.templates_dir / f"{name}.json"
            if not template_path.exists():
                self.save_config(config, str(template_path))
    
    def _get_default_templates(self) -> Dict[str, FullTrainingConfig]:
        """获取默认配置模板"""
        templates = {}
        
        # 1. 保守型配置 - 适合初学者和稳定收益
        conservative = FullTrainingConfig(
            config_name="conservative",
            description="保守型配置，注重风险控制和稳定收益",
            environment=EnvironmentConfig(
                initial_capital=10000.0,
                transaction_cost=0.001,
                max_position_size=0.05,  # 最大5%仓位
                max_positions=3
            ),
            reward=RewardConfig(
                pnl_weight=0.8,
                risk_penalty_weight=1.5,  # 更高的风险惩罚
                drawdown_penalty_weight=3.0,
                max_drawdown_threshold=0.03,  # 3%最大回撤
                win_rate_bonus_weight=0.6
            ),
            rl=RLConfig(
                learning_rate=1e-4,  # 较低的学习率
                exploration_noise=0.05,  # 较低的探索
                epsilon_decay=0.99
            ),
            training=TrainingConfig(
                episodes=2000,  # 更多训练轮次
                early_stopping_patience=300
            )
        )
        templates["conservative"] = conservative
        
        # 2. 激进型配置 - 追求高收益
        aggressive = FullTrainingConfig(
            config_name="aggressive",
            description="激进型配置，追求高收益，容忍更高风险",
            environment=EnvironmentConfig(
                initial_capital=10000.0,
                transaction_cost=0.001,
                max_position_size=0.15,  # 最大15%仓位
                max_positions=8
            ),
            reward=RewardConfig(
                pnl_weight=1.5,  # 更重视收益
                risk_penalty_weight=0.3,  # 较低的风险惩罚
                drawdown_penalty_weight=1.0,
                max_drawdown_threshold=0.08,  # 8%最大回撤
                time_efficiency_weight=0.2,
                consecutive_win_bonus=0.15
            ),
            rl=RLConfig(
                learning_rate=5e-4,  # 较高的学习率
                exploration_noise=0.2,  # 更多探索
                epsilon_start=1.5,
                epsilon_decay=0.99
            ),
            training=TrainingConfig(
                episodes=1500,
                update_frequency=50  # 更频繁的更新
            )
        )
        templates["aggressive"] = aggressive
        
        # 3. 日内交易配置
        day_trading = FullTrainingConfig(
            config_name="day_trading",
            description="日内交易配置，适合短期快速交易",
            data=DataConfig(
                interval="5m"  # 5分钟数据
            ),
            environment=EnvironmentConfig(
                initial_capital=10000.0,
                transaction_cost=0.0008,  # 稍低的手续费
                max_position_size=0.08,
                max_positions=5
            ),
            reward=RewardConfig(
                pnl_weight=1.2,
                time_efficiency_weight=0.3,  # 重视时间效率
                trade_frequency_penalty=0.02,  # 较低的频率惩罚
                win_rate_bonus_weight=0.5,
                max_drawdown_threshold=0.04
            ),
            rl=RLConfig(
                learning_rate=3e-4,
                batch_size=128  # 更大的批次
            ),
            training=TrainingConfig(
                episodes=1200,
                episode_length=800,  # 较短的episode
                update_frequency=80
            )
        )
        templates["day_trading"] = day_trading
        
        # 4. 波段交易配置
        swing_trading = FullTrainingConfig(
            config_name="swing_trading",
            description="波段交易配置，适合中期持仓",
            data=DataConfig(
                interval="15m"  # 15分钟数据
            ),
            environment=EnvironmentConfig(
                initial_capital=10000.0,
                transaction_cost=0.001,
                max_position_size=0.12,
                max_positions=4
            ),
            reward=RewardConfig(
                pnl_weight=1.0,
                time_efficiency_weight=0.05,  # 不太重视时间效率
                trade_frequency_penalty=0.08,  # 较高的频率惩罚
                consecutive_win_bonus=0.12,
                max_drawdown_threshold=0.06
            ),
            rl=RLConfig(
                learning_rate=2e-4,
                gamma=0.995,  # 更高的折扣因子
                batch_size=64
            ),
            training=TrainingConfig(
                episodes=1800,
                episode_length=1500  # 较长的episode
            )
        )
        templates["swing_trading"] = swing_trading
        
        # 5. 高频交易配置
        scalping = FullTrainingConfig(
            config_name="scalping",
            description="高频交易配置，适合剥头皮策略",
            data=DataConfig(
                interval="1m"  # 1分钟数据
            ),
            environment=EnvironmentConfig(
                initial_capital=10000.0,
                transaction_cost=0.0005,  # 更低的手续费
                max_position_size=0.06,
                max_positions=10,
                slippage=0.0002
            ),
            reward=RewardConfig(
                pnl_weight=0.8,
                risk_penalty_weight=1.2,
                time_efficiency_weight=0.5,  # 非常重视时间效率
                trade_frequency_penalty=0.01,  # 很低的频率惩罚
                win_rate_bonus_weight=0.7,
                max_drawdown_threshold=0.02  # 很低的回撤容忍
            ),
            rl=RLConfig(
                learning_rate=4e-4,
                batch_size=256,  # 大批次
                exploration_noise=0.15
            ),
            training=TrainingConfig(
                episodes=800,
                episode_length=500,  # 短episode
                update_frequency=30
            )
        )
        templates["scalping"] = scalping
        
        return templates
    
    def save_config(self, config: FullTrainingConfig, file_path: Optional[str] = None) -> str:
        """保存配置到文件"""
        if file_path is None:
            file_path = self.custom_dir / f"{config.config_name}.json"
        
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 转换为字典并保存
        config_dict = asdict(config)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
        
        print(f"配置已保存: {file_path}")
        return str(file_path)
    
    def load_config(self, config_name: str) -> Optional[FullTrainingConfig]:
        """加载配置"""
        # 首先在自定义目录查找
        custom_path = self.custom_dir / f"{config_name}.json"
        if custom_path.exists():
            return self._load_config_from_file(custom_path)
        
        # 然后在模板目录查找
        template_path = self.templates_dir / f"{config_name}.json"
        if template_path.exists():
            return self._load_config_from_file(template_path)
        
        # 最后在根配置目录查找
        root_path = self.config_dir / f"{config_name}.json"
        if root_path.exists():
            return self._load_config_from_file(root_path)
        
        print(f"配置文件未找到: {config_name}")
        return None
    
    def _load_config_from_file(self, file_path: Path) -> Optional[FullTrainingConfig]:
        """从文件加载配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            
            # 递归创建配置对象
            return self._dict_to_config(config_dict)
        
        except Exception as e:
            print(f"加载配置失败 {file_path}: {e}")
            return None
    
    def _dict_to_config(self, config_dict: Dict) -> FullTrainingConfig:
        """将字典转换为配置对象"""
        # 创建子配置对象
        data_config = DataConfig(**config_dict.get('data', {}))
        model_config = ModelConfig(**config_dict.get('model', {}))
        env_config = EnvironmentConfig(**config_dict.get('environment', {}))
        reward_config = RewardConfig(**config_dict.get('reward', {}))
        rl_config = RLConfig(**config_dict.get('rl', {}))
        training_config = TrainingConfig(**config_dict.get('training', {}))
        logging_config = LoggingConfig(**config_dict.get('logging', {}))
        
        # 创建完整配置
        full_config = FullTrainingConfig(
            data=data_config,
            model=model_config,
            environment=env_config,
            reward=reward_config,
            rl=rl_config,
            training=training_config,
            logging=logging_config,
            config_name=config_dict.get('config_name', 'unknown'),
            description=config_dict.get('description', ''),
            version=config_dict.get('version', '1.0'),
            created_by=config_dict.get('created_by', 'unknown')
        )
        
        return full_config
    
    def list_configs(self) -> Dict[str, List[str]]:
        """列出所有可用配置"""
        configs = {
            'templates': [],
            'custom': [],
            'root': []
        }
        
        # 模板配置
        for file_path in self.templates_dir.glob("*.json"):
            configs['templates'].append(file_path.stem)
        
        # 自定义配置
        for file_path in self.custom_dir.glob("*.json"):
            configs['custom'].append(file_path.stem)
        
        # 根目录配置
        for file_path in self.config_dir.glob("*.json"):
            if file_path.parent == self.config_dir:
                configs['root'].append(file_path.stem)
        
        return configs
    
    def create_custom_config(self, name: str, base_config: str = 'conservative',
                           modifications: Dict[str, Any] = None) -> FullTrainingConfig:
        """创建自定义配置"""
        # 加载基础配置
        base = self.load_config(base_config)
        if base is None:
            base = FullTrainingConfig()  # 使用默认配置
        
        # 深拷贝基础配置
        custom_config = copy.deepcopy(base)
        custom_config.config_name = name
        custom_config.description = f"基于 {base_config} 的自定义配置"
        
        # 应用修改
        if modifications:
            self._apply_modifications(custom_config, modifications)
        
        # 保存自定义配置
        self.save_config(custom_config)
        
        return custom_config
    
    def _apply_modifications(self, config: FullTrainingConfig, modifications: Dict[str, Any]):
        """应用配置修改"""
        for key, value in modifications.items():
            if '.' in key:
                # 处理嵌套键，如 'rl.learning_rate'
                parts = key.split('.')
                obj = config
                for part in parts[:-1]:
                    obj = getattr(obj, part)
                setattr(obj, parts[-1], value)
            else:
                # 直接设置属性
                if hasattr(config, key):
                    setattr(config, key, value)
    
    def validate_config(self, config: FullTrainingConfig) -> Dict[str, List[str]]:
        """验证配置的合理性"""
        issues = {
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        # 验证数据配置
        if config.data.train_ratio + config.data.val_ratio + config.data.test_ratio != 1.0:
            issues['errors'].append("数据分割比例之和必须等于1.0")
        
        if config.data.train_ratio < 0.5:
            issues['warnings'].append("训练数据比例过低，可能影响模型性能")
        
        # 验证环境配置
        if config.environment.max_position_size > 0.2:
            issues['warnings'].append("最大仓位过大，风险较高")
        
        if config.environment.transaction_cost > 0.01:
            issues['errors'].append("交易成本过高，不现实")
        
        # 验证RL配置
        if config.rl.learning_rate > 0.01:
            issues['warnings'].append("学习率过高，可能导致训练不稳定")
        
        if config.rl.batch_size < 16:
            issues['warnings'].append("批次大小过小，可能影响训练效果")
        
        # 验证训练配置
        if config.training.episodes < 100:
            issues['warnings'].append("训练轮次过少，可能训练不充分")
        
        if config.training.episode_length < 100:
            issues['warnings'].append("Episode长度过短，可能无法学习长期策略")
        
        # 验证奖励配置
        total_weight = (config.reward.pnl_weight + config.reward.risk_penalty_weight + 
                       config.reward.time_efficiency_weight)
        if total_weight > 5.0:
            issues['warnings'].append("奖励权重总和过大，可能导致奖励不稳定")
        
        # 提供建议
        if config.data.interval == "1m" and config.training.episode_length > 1000:
            issues['suggestions'].append("1分钟数据建议使用较短的episode长度")
        
        if config.environment.max_positions > 10:
            issues['suggestions'].append("考虑减少最大持仓数量以降低复杂度")
        
        return issues
    
    def compare_configs(self, config_names: List[str]) -> Dict[str, Dict]:
        """比较多个配置"""
        comparison = {}
        
        for name in config_names:
            config = self.load_config(name)
            if config:
                comparison[name] = asdict(config)
        
        return comparison
    
    def get_config_summary(self, config_name: str) -> Optional[Dict[str, Any]]:
        """获取配置摘要"""
        config = self.load_config(config_name)
        if not config:
            return None
        
        summary = {
            'name': config.config_name,
            'description': config.description,
            'version': config.version,
            'key_parameters': {
                'coin': config.data.coin,
                'interval': config.data.interval,
                'episodes': config.training.episodes,
                'learning_rate': config.rl.learning_rate,
                'max_position_size': config.environment.max_position_size,
                'initial_capital': config.environment.initial_capital
            },
            'risk_profile': self._assess_risk_profile(config),
            'trading_style': self._assess_trading_style(config)
        }
        
        return summary
    
    def _assess_risk_profile(self, config: FullTrainingConfig) -> str:
        """评估风险档案"""
        risk_score = 0
        
        # 仓位大小影响
        if config.environment.max_position_size > 0.1:
            risk_score += 2
        elif config.environment.max_position_size > 0.05:
            risk_score += 1
        
        # 回撤容忍度影响
        if config.reward.max_drawdown_threshold > 0.06:
            risk_score += 2
        elif config.reward.max_drawdown_threshold > 0.03:
            risk_score += 1
        
        # 风险惩罚权重影响
        if config.reward.risk_penalty_weight < 0.5:
            risk_score += 1
        
        if risk_score >= 4:
            return "高风险"
        elif risk_score >= 2:
            return "中等风险"
        else:
            return "低风险"
    
    def _assess_trading_style(self, config: FullTrainingConfig) -> str:
        """评估交易风格"""
        if config.data.interval == "1m":
            return "高频交易"
        elif config.data.interval == "5m" and config.reward.time_efficiency_weight > 0.2:
            return "日内交易"
        elif config.data.interval in ["15m", "30m"]:
            return "波段交易"
        elif config.data.interval in ["1h", "4h"]:
            return "趋势跟踪"
        else:
            return "混合策略"


def create_config_from_dict(config_dict: Dict) -> FullTrainingConfig:
    """从字典创建配置对象"""
    manager = TrainingConfigManager()
    return manager._dict_to_config(config_dict)


def validate_hyperparameters(config: FullTrainingConfig) -> bool:
    """验证超参数的合理性"""
    manager = TrainingConfigManager()
    issues = manager.validate_config(config)
    
    # 如果有错误，返回False
    if issues['errors']:
        print("配置验证失败:")
        for error in issues['errors']:
            print(f"  错误: {error}")
        return False
    
    # 打印警告和建议
    if issues['warnings']:
        print("配置警告:")
        for warning in issues['warnings']:
            print(f"  警告: {warning}")
    
    if issues['suggestions']:
        print("配置建议:")
        for suggestion in issues['suggestions']:
            print(f"  建议: {suggestion}")
    
    return True


if __name__ == "__main__":
    # 测试配置管理系统
    print("测试训练配置管理系统")
    
    # 创建配置管理器
    manager = TrainingConfigManager()
    
    # 列出所有配置
    configs = manager.list_configs()
    print(f"可用配置: {configs}")
    
    # 加载模板配置
    conservative_config = manager.load_config("conservative")
    if conservative_config:
        print(f"保守型配置: {conservative_config.config_name}")
        print(f"描述: {conservative_config.description}")
        
        # 验证配置
        if validate_hyperparameters(conservative_config):
            print("配置验证通过")
        
        # 获取配置摘要
        summary = manager.get_config_summary("conservative")
        print(f"配置摘要: {summary}")
    
    # 创建自定义配置
    custom_config = manager.create_custom_config(
        name="my_custom",
        base_config="conservative",
        modifications={
            'rl.learning_rate': 1e-4,
            'environment.max_position_size': 0.08,
            'training.episodes': 1500
        }
    )
    
    print(f"创建自定义配置: {custom_config.config_name}")
    
    print("配置管理系统测试完成！")