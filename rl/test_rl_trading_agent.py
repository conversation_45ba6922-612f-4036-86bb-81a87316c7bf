"""
Test script for RLTradingAgent

This script tests the core functionality of the RL trading agent including:
- Action selection (deterministic and stochastic)
- Experience collection and storage
- PPO policy updates
- Model saving and loading
- Integration with trading environment
"""

import sys
import os
import numpy as np
import pandas as pd
import torch

# Add the parent directory to the path to import rl modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from rl.rl_trading_agent import RLTradingAgent, create_default_rl_agent, Experience
from rl.trading_environment import TradingEnvironment, MarketState, TradingAction
from rl.policy_network import PolicyNetwork


def test_agent_initialization():
    """测试代理初始化"""
    print("=== 测试代理初始化 ===")
    
    agent = create_default_rl_agent(state_dim=15)
    
    assert agent.state_dim == 15
    assert agent.policy_network is not None
    assert agent.value_network is not None
    assert len(agent.experience_buffer) == 0
    
    print("✓ 代理初始化测试通过")
    return agent


def test_action_selection(agent):
    """测试动作选择"""
    print("\n=== 测试动作选择 ===")
    
    # 创建测试状态
    test_state = np.random.randn(15)
    
    # 测试确定性动作选择
    det_action, det_log_prob, det_value = agent.get_action(test_state, deterministic=True)
    
    print(f"确定性动作: {det_action}")
    print(f"价值估计: {det_value:.4f}")
    
    # 验证动作格式
    assert isinstance(det_action, dict)
    assert 'enter_trade' in det_action
    assert 'position_size' in det_action
    assert 'stop_loss_pct' in det_action
    assert 'take_profit_pct' in det_action
    assert 'max_hold_time' in det_action
    
    # 验证动作范围
    assert isinstance(det_action['enter_trade'], (bool, np.bool_))
    assert 0.0 <= det_action['position_size'] <= 0.1
    assert 0.01 <= det_action['stop_loss_pct'] <= 0.05
    assert 0.01 <= det_action['take_profit_pct'] <= 0.04
    assert 60 <= det_action['max_hold_time'] <= 240
    
    # 测试随机动作选择
    rand_action, rand_log_prob, rand_value = agent.get_action(test_state, deterministic=False)
    
    print(f"随机动作: {rand_action}")
    print(f"Log概率: {rand_log_prob:.4f}")
    
    # 验证随机动作也符合格式要求
    assert isinstance(rand_action, dict)
    assert all(key in rand_action for key in det_action.keys())
    
    print("✓ 动作选择测试通过")
    return test_state, det_action, rand_action


def test_experience_storage(agent, test_state, action):
    """测试经验存储"""
    print("\n=== 测试经验存储 ===")
    
    # 存储多个经验
    for i in range(10):
        next_state = np.random.randn(15)
        reward = np.random.uniform(-0.1, 0.1)
        done = i == 9  # 最后一个episode结束
        
        agent.store_experience(
            state=test_state,
            action=action,
            reward=reward,
            next_state=next_state,
            done=done,
            log_prob=0.1,
            value=0.05
        )
        
        test_state = next_state  # 更新状态
    
    assert len(agent.experience_buffer) == 10
    
    # 验证经验格式
    experiences = agent.experience_buffer.get_all()
    exp = experiences[0]
    
    assert isinstance(exp, Experience)
    assert exp.state.shape == (15,)
    assert isinstance(exp.action, dict)
    assert isinstance(exp.reward, float)
    assert exp.next_state.shape == (15,)
    assert isinstance(exp.done, bool)
    
    print(f"✓ 经验存储测试通过，缓冲区大小: {len(agent.experience_buffer)}")


def test_policy_update(agent):
    """测试策略更新"""
    print("\n=== 测试策略更新 ===")
    
    # 确保有足够的经验进行更新
    if len(agent.experience_buffer) < agent.batch_size:
        # 添加更多经验
        for i in range(agent.batch_size):
            state = np.random.randn(15)
            action = {
                'enter_trade': np.random.choice([True, False]),
                'position_size': np.random.uniform(0.01, 0.1),
                'stop_loss_pct': np.random.uniform(0.01, 0.05),
                'take_profit_pct': np.random.uniform(0.01, 0.04),
                'max_hold_time': int(np.random.uniform(60, 240))
            }
            
            agent.store_experience(
                state=state,
                action=action,
                reward=np.random.uniform(-0.1, 0.1),
                next_state=np.random.randn(15),
                done=False,
                log_prob=np.random.uniform(-1, 0),
                value=np.random.uniform(-0.1, 0.1)
            )
    
    # 记录更新前的参数
    old_policy_params = [p.clone() for p in agent.policy_network.parameters()]
    old_value_params = [p.clone() for p in agent.value_network.parameters()]
    
    # 执行策略更新
    losses = agent.update_policy()
    
    print(f"策略损失: {losses['policy_loss']:.6f}")
    print(f"价值损失: {losses['value_loss']:.6f}")
    print(f"当前epsilon: {losses['epsilon']:.4f}")
    
    # 验证参数确实更新了
    policy_updated = False
    for old_p, new_p in zip(old_policy_params, agent.policy_network.parameters()):
        if not torch.equal(old_p, new_p):
            policy_updated = True
            break
    
    value_updated = False
    for old_p, new_p in zip(old_value_params, agent.value_network.parameters()):
        if not torch.equal(old_p, new_p):
            value_updated = True
            break
    
    assert policy_updated, "策略网络参数未更新"
    assert value_updated, "价值网络参数未更新"
    assert len(agent.experience_buffer) == 0, "经验缓冲区未清空"
    
    print("✓ 策略更新测试通过")


def test_model_save_load(agent):
    """测试模型保存和加载"""
    print("\n=== 测试模型保存和加载 ===")
    
    # 保存模型
    test_path = "/tmp/test_rl_agent_full"
    metadata = {
        'test_version': '1.0',
        'training_episodes': 100,
        'performance': 0.85
    }
    
    agent.save_model(test_path, metadata=metadata)
    
    # 验证文件存在
    assert os.path.exists(f"{test_path}.pth")
    assert os.path.exists(f"{test_path}_config.json")
    
    # 加载模型
    loaded_agent = RLTradingAgent.load_model(test_path)
    
    # 验证配置一致
    assert loaded_agent.state_dim == agent.state_dim
    assert loaded_agent.hidden_dim == agent.hidden_dim
    assert loaded_agent.learning_rate == agent.learning_rate
    
    # 验证网络参数一致
    for p1, p2 in zip(agent.policy_network.parameters(), loaded_agent.policy_network.parameters()):
        assert torch.allclose(p1, p2, atol=1e-6)
    
    for p1, p2 in zip(agent.value_network.parameters(), loaded_agent.value_network.parameters()):
        assert torch.allclose(p1, p2, atol=1e-6)
    
    # 测试加载后的动作选择
    test_state = np.random.randn(15)
    
    # 设置为评估模式确保一致性
    agent.set_training_mode(False)
    loaded_agent.set_training_mode(False)
    
    original_action, _, _ = agent.get_action(test_state, deterministic=True)
    loaded_action, _, _ = loaded_agent.get_action(test_state, deterministic=True)
    
    # 动作应该一致（在合理的数值误差范围内）
    assert abs(original_action['position_size'] - loaded_action['position_size']) < 1e-3
    assert abs(original_action['stop_loss_pct'] - loaded_action['stop_loss_pct']) < 1e-3
    assert abs(original_action['take_profit_pct'] - loaded_action['take_profit_pct']) < 1e-3
    assert original_action['enter_trade'] == loaded_action['enter_trade']
    
    # 清理测试文件
    try:
        os.remove(f"{test_path}.pth")
        os.remove(f"{test_path}_config.json")
    except:
        pass
    
    print("✓ 模型保存和加载测试通过")


def test_integration_with_environment():
    """测试与交易环境的集成"""
    print("\n=== 测试与交易环境集成 ===")
    
    # 创建环境和代理
    env_config = {
        'initial_capital': 10000,
        'transaction_cost': 0.001,
        'max_position_size': 0.05,
        'max_positions': 3
    }
    
    env = TradingEnvironment(env_config)
    agent = create_default_rl_agent(state_dim=env.get_state_dim())
    
    # 创建测试市场状态
    market_state = MarketState(
        model_signal=1,
        signal_confidence=0.75,
        signal_probability=0.82,
        current_price=2500.0,
        price_change_1h=0.02,
        price_change_4h=-0.01,
        volatility_recent=0.03,
        cash_ratio=0.9,
        position_count=0,
        unrealized_pnl=0.0,
        recent_win_rate=0.6,
        consecutive_losses=0,
        hour=14,
        day_of_week=2,
        is_good_trading_time=True
    )
    
    # 转换为状态向量
    state_vector = env.get_state_vector(market_state)
    
    # 代理选择动作
    action_dict, log_prob, value = agent.get_action(state_vector, deterministic=False)
    
    # 转换为交易动作
    trading_action = TradingAction(
        enter_trade=action_dict['enter_trade'],
        position_size=action_dict['position_size'],
        stop_loss_pct=action_dict['stop_loss_pct'],
        take_profit_pct=action_dict['take_profit_pct'],
        max_hold_time=action_dict['max_hold_time']
    )
    
    # 执行交易
    result = env.execute_action(trading_action, market_state, pd.Timestamp.now(), 0)
    
    print(f"交易执行结果: {result}")
    print(f"代理动作: {action_dict}")
    print(f"环境状态维度: {len(state_vector)}")
    
    # 验证集成正常工作
    assert len(state_vector) == agent.state_dim
    assert isinstance(result, dict)
    
    print("✓ 环境集成测试通过")


def test_training_stats(agent):
    """测试训练统计功能"""
    print("\n=== 测试训练统计 ===")
    
    # 获取初始统计
    stats = agent.get_training_stats()
    print(f"初始统计: {stats}")
    
    # 模拟一些训练数据
    agent.training_stats['episodes'] = 50
    agent.training_stats['total_steps'] = 1000
    agent.training_stats['rewards'].extend([0.1, 0.2, -0.1, 0.3, 0.05])
    
    updated_stats = agent.get_training_stats()
    print(f"更新后统计: {updated_stats}")
    
    assert updated_stats['episodes'] == 50
    assert updated_stats['total_steps'] == 1000
    assert updated_stats['avg_reward'] > 0
    
    # 测试重置统计
    agent.reset_training_stats()
    reset_stats = agent.get_training_stats()
    
    assert reset_stats['episodes'] == 0
    assert reset_stats['total_steps'] == 0
    assert reset_stats['avg_reward'] == 0.0
    
    print("✓ 训练统计测试通过")


def run_all_tests():
    """运行所有测试"""
    print("开始RL交易代理完整测试...\n")
    
    try:
        # 1. 初始化测试
        agent = test_agent_initialization()
        
        # 2. 动作选择测试
        test_state, det_action, rand_action = test_action_selection(agent)
        
        # 3. 经验存储测试
        test_experience_storage(agent, test_state, rand_action)
        
        # 4. 策略更新测试
        test_policy_update(agent)
        
        # 5. 模型保存加载测试
        test_model_save_load(agent)
        
        # 6. 环境集成测试
        test_integration_with_environment()
        
        # 7. 训练统计测试
        test_training_stats(agent)
        
        print("\n" + "="*50)
        print("🎉 所有测试通过！RL交易代理实现正确")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n✅ RL交易代理测试完成，所有功能正常工作")
        print("可以开始使用RLTradingAgent进行强化学习训练")
    else:
        print("\n❌ 测试失败，请检查实现")
        sys.exit(1)