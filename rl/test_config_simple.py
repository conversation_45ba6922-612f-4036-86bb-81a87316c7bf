#!/usr/bin/env python3
# test_config_simple.py
# 简单的配置系统测试

import sys
import tempfile
import shutil
from pathlib import Path

def test_basic_config():
    """测试基本配置功能"""
    print("测试基本配置功能...")
    
    try:
        from .training_config import FullTrainingConfig, TrainingConfigManager
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 创建配置管理器
            manager = TrainingConfigManager(temp_dir)
            
            # 测试默认配置
            config = FullTrainingConfig()
            assert config.data.coin == "ETH"
            assert config.rl.learning_rate == 3e-4
            print("✅ 默认配置创建成功")
            
            # 测试保存和加载
            config.config_name = "test_config"
            manager.save_config(config)
            
            loaded_config = manager.load_config("test_config")
            assert loaded_config is not None
            assert loaded_config.config_name == "test_config"
            print("✅ 配置保存和加载成功")
            
            # 测试自定义配置
            custom_config = manager.create_custom_config(
                name="custom_test",
                base_config="default",
                modifications={
                    'rl.learning_rate': 0.0001,
                    'environment.max_position_size': 0.08
                }
            )
            assert custom_config.rl.learning_rate == 0.0001
            assert custom_config.environment.max_position_size == 0.08
            print("✅ 自定义配置创建成功")
            
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir)
            
        return True
        
    except Exception as e:
        print(f"❌ 基本配置测试失败: {e}")
        return False


def test_config_validation():
    """测试配置验证"""
    print("测试配置验证...")
    
    try:
        from .training_config import FullTrainingConfig
        from .config_validator import ConfigValidator
        
        validator = ConfigValidator()
        
        # 测试有效配置
        valid_config = FullTrainingConfig()
        issues = validator.validate_config(valid_config)
        assert len(issues['errors']) == 0
        print("✅ 有效配置验证通过")
        
        # 测试无效配置
        invalid_config = FullTrainingConfig()
        invalid_config.data.train_ratio = 1.5  # 无效值
        issues = validator.validate_config(invalid_config)
        assert len(issues['errors']) > 0
        print("✅ 无效配置正确识别")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证测试失败: {e}")
        return False


def test_dynamic_config():
    """测试动态配置管理"""
    print("测试动态配置管理...")
    
    try:
        from .training_config import FullTrainingConfig
        
        # 只测试基本的配置对象操作，不使用DynamicConfigManager
        config = FullTrainingConfig()
        original_lr = config.rl.learning_rate
        
        # 测试配置修改
        config.rl.learning_rate = 0.0001
        config.training.episodes = 1500
        
        assert config.rl.learning_rate == 0.0001
        assert config.training.episodes == 1500
        print("✅ 配置修改成功")
        
        # 测试配置恢复
        config.rl.learning_rate = original_lr
        assert config.rl.learning_rate == original_lr
        print("✅ 配置恢复成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 动态配置测试失败: {e}")
        return False


def test_config_templates():
    """测试配置模板"""
    print("测试配置模板...")
    
    try:
        import tempfile
        import shutil
        from .training_config import TrainingConfigManager
        from .config_validator import ConfigValidator
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            manager = TrainingConfigManager(temp_dir)
            validator = ConfigValidator()
            
            # 获取所有模板
            configs = manager.list_configs()
            templates = configs.get('templates', [])
            
            assert len(templates) > 0
            print(f"✅ 找到 {len(templates)} 个模板配置")
            
            # 验证每个模板
            for template_name in templates[:3]:  # 只测试前3个，避免太慢
                config = manager.load_config(template_name)
                assert config is not None
                
                issues = validator.validate_config(config)
                assert len(issues['errors']) == 0
                print(f"✅ 模板 {template_name} 验证通过")
            
        finally:
            shutil.rmtree(temp_dir)
            
        return True
        
    except Exception as e:
        print(f"❌ 配置模板测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始配置系统简单测试...")
    print("=" * 50)
    
    tests = [
        test_basic_config,
        test_config_validation,
        test_dynamic_config,
        test_config_templates
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            failed += 1
        
        print("-" * 30)
    
    print(f"\n测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！配置系统工作正常")
        return True
    else:
        print("⚠️  部分测试失败，请检查问题")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)