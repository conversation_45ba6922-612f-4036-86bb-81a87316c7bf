"""
Performance evaluation and visualization tools for RL trading system.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional, Tuple, Any
import json
from datetime import datetime
import os
from pathlib import Path

class PerformanceEvaluator:
    """
    Comprehensive performance evaluation and visualization for RL trading agents.
    """
    
    def __init__(self, output_dir: str = "rl/analysis"):
        """
        Initialize performance evaluator.
        
        Args:
            output_dir: Directory to save analysis results
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # Storage for metrics
        self.training_metrics = []
        self.episode_rewards = []
        self.performance_history = []
        
    def log_training_step(self, episode: int, metrics: Dict[str, float]):
        """
        Log metrics from a training step.
        
        Args:
            episode: Episode number
            metrics: Dictionary of metrics (reward, loss, etc.)
        """
        metrics['episode'] = episode
        metrics['timestamp'] = datetime.now().isoformat()
        self.training_metrics.append(metrics)
        
    def log_episode_reward(self, episode: int, reward: float, additional_metrics: Optional[Dict] = None):
        """
        Log episode reward and additional metrics.
        
        Args:
            episode: Episode number
            reward: Total episode reward
            additional_metrics: Additional metrics to log
        """
        entry = {
            'episode': episode,
            'reward': reward,
            'timestamp': datetime.now().isoformat()
        }
        if additional_metrics:
            entry.update(additional_metrics)
        self.episode_rewards.append(entry)
        
    def log_performance_evaluation(self, episode: int, performance_metrics: Dict[str, float]):
        """
        Log performance evaluation results.
        
        Args:
            episode: Episode number
            performance_metrics: Performance metrics from evaluation
        """
        performance_metrics['episode'] = episode
        performance_metrics['timestamp'] = datetime.now().isoformat()
        self.performance_history.append(performance_metrics)
        
    def plot_learning_curves(self, save_path: Optional[str] = None) -> str:
        """
        Plot learning curves showing training progress.
        
        Args:
            save_path: Path to save the plot
            
        Returns:
            Path to saved plot
        """
        if not self.episode_rewards:
            raise ValueError("No episode rewards logged yet")
            
        df = pd.DataFrame(self.episode_rewards)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('RL Training Learning Curves', fontsize=16)
        
        # Episode rewards
        axes[0, 0].plot(df['episode'], df['reward'], alpha=0.7, label='Episode Reward')
        if len(df) > 10:
            # Add moving average
            window = min(50, len(df) // 10)
            moving_avg = df['reward'].rolling(window=window).mean()
            axes[0, 0].plot(df['episode'], moving_avg, 'r-', linewidth=2, label=f'MA({window})')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Reward')
        axes[0, 0].set_title('Episode Rewards')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Cumulative reward
        cumulative_reward = df['reward'].cumsum()
        axes[0, 1].plot(df['episode'], cumulative_reward, 'g-', linewidth=2)
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Cumulative Reward')
        axes[0, 1].set_title('Cumulative Rewards')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Reward distribution
        axes[1, 0].hist(df['reward'], bins=30, alpha=0.7, edgecolor='black')
        axes[1, 0].axvline(df['reward'].mean(), color='red', linestyle='--', 
                          label=f'Mean: {df["reward"].mean():.3f}')
        axes[1, 0].set_xlabel('Reward')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].set_title('Reward Distribution')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # Recent performance trend
        if len(df) > 100:
            recent_df = df.tail(100)
            axes[1, 1].plot(recent_df['episode'], recent_df['reward'], 'b-', alpha=0.7)
            recent_ma = recent_df['reward'].rolling(window=10).mean()
            axes[1, 1].plot(recent_df['episode'], recent_ma, 'r-', linewidth=2)
            axes[1, 1].set_xlabel('Episode')
            axes[1, 1].set_ylabel('Reward')
            axes[1, 1].set_title('Recent Performance (Last 100 Episodes)')
        else:
            axes[1, 1].text(0.5, 0.5, 'Need >100 episodes\nfor recent trend', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path is None:
            save_path = self.output_dir / f"learning_curves_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
        
    def plot_performance_metrics(self, save_path: Optional[str] = None) -> str:
        """
        Plot detailed performance metrics over time.
        
        Args:
            save_path: Path to save the plot
            
        Returns:
            Path to saved plot
        """
        if not self.performance_history:
            raise ValueError("No performance history logged yet")
            
        df = pd.DataFrame(self.performance_history)
        
        # Determine available metrics
        metric_columns = [col for col in df.columns if col not in ['episode', 'timestamp']]
        
        n_metrics = len(metric_columns)
        n_cols = 2
        n_rows = (n_metrics + 1) // 2
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 4 * n_rows))
        fig.suptitle('Performance Metrics Over Time', fontsize=16)
        
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        
        for i, metric in enumerate(metric_columns):
            row = i // n_cols
            col = i % n_cols
            
            axes[row, col].plot(df['episode'], df[metric], 'o-', linewidth=2, markersize=4)
            axes[row, col].set_xlabel('Episode')
            axes[row, col].set_ylabel(metric.replace('_', ' ').title())
            axes[row, col].set_title(f'{metric.replace("_", " ").title()} Over Time')
            axes[row, col].grid(True, alpha=0.3)
            
            # Add trend line if enough data points
            if len(df) > 5:
                z = np.polyfit(df['episode'], df[metric], 1)
                p = np.poly1d(z)
                axes[row, col].plot(df['episode'], p(df['episode']), 'r--', alpha=0.7, 
                                   label=f'Trend: {z[0]:.4f}x + {z[1]:.4f}')
                axes[row, col].legend()
        
        # Hide empty subplots
        for i in range(n_metrics, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            axes[row, col].set_visible(False)
        
        plt.tight_layout()
        
        if save_path is None:
            save_path = self.output_dir / f"performance_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)        
 
    def compare_with_baseline(self, baseline_results: Dict[str, float], 
                            rl_results: Dict[str, float], 
                            save_path: Optional[str] = None) -> str:
        """
        Compare RL performance with baseline strategy.
        
        Args:
            baseline_results: Baseline strategy performance metrics
            rl_results: RL strategy performance metrics
            save_path: Path to save the comparison plot
            
        Returns:
            Path to saved plot
        """
        # Common metrics to compare
        common_metrics = ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate', 
                         'avg_trade_duration', 'total_trades']
        
        # Filter to available metrics
        available_metrics = [m for m in common_metrics 
                           if m in baseline_results and m in rl_results]
        
        if not available_metrics:
            raise ValueError("No common metrics found between baseline and RL results")
        
        # Prepare data for plotting
        baseline_values = [baseline_results[m] for m in available_metrics]
        rl_values = [rl_results[m] for m in available_metrics]
        
        # Create comparison plot
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('RL vs Baseline Strategy Comparison', fontsize=16)
        
        # Bar comparison
        x = np.arange(len(available_metrics))
        width = 0.35
        
        ax1.bar(x - width/2, baseline_values, width, label='Baseline', alpha=0.8)
        ax1.bar(x + width/2, rl_values, width, label='RL Strategy', alpha=0.8)
        
        ax1.set_xlabel('Metrics')
        ax1.set_ylabel('Values')
        ax1.set_title('Performance Comparison')
        ax1.set_xticks(x)
        ax1.set_xticklabels([m.replace('_', ' ').title() for m in available_metrics], 
                           rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Improvement percentage
        improvements = [(rl_values[i] - baseline_values[i]) / abs(baseline_values[i]) * 100 
                       for i in range(len(available_metrics))]
        
        colors = ['green' if imp > 0 else 'red' for imp in improvements]
        ax2.bar(range(len(available_metrics)), improvements, color=colors, alpha=0.7)
        ax2.set_xlabel('Metrics')
        ax2.set_ylabel('Improvement (%)')
        ax2.set_title('RL Improvement Over Baseline')
        ax2.set_xticks(range(len(available_metrics)))
        ax2.set_xticklabels([m.replace('_', ' ').title() for m in available_metrics], 
                           rotation=45, ha='right')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.grid(True, alpha=0.3)
        
        # Add improvement values as text
        for i, imp in enumerate(improvements):
            ax2.text(i, imp + (max(improvements) - min(improvements)) * 0.02, 
                    f'{imp:.1f}%', ha='center', va='bottom' if imp > 0 else 'top')
        
        plt.tight_layout()
        
        if save_path is None:
            save_path = self.output_dir / f"baseline_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)
        
    def generate_training_report(self, model_config: Dict[str, Any], 
                               final_performance: Dict[str, float],
                               save_path: Optional[str] = None) -> str:
        """
        Generate comprehensive training report.
        
        Args:
            model_config: Model configuration used for training
            final_performance: Final performance metrics
            save_path: Path to save the report
            
        Returns:
            Path to saved report
        """
        if save_path is None:
            save_path = self.output_dir / f"training_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        
        # Calculate training statistics
        if self.episode_rewards:
            rewards_df = pd.DataFrame(self.episode_rewards)
            training_stats = {
                'total_episodes': len(rewards_df),
                'avg_reward': rewards_df['reward'].mean(),
                'std_reward': rewards_df['reward'].std(),
                'min_reward': rewards_df['reward'].min(),
                'max_reward': rewards_df['reward'].max(),
                'final_100_avg': rewards_df['reward'].tail(100).mean() if len(rewards_df) >= 100 else None
            }
        else:
            training_stats = {}
        
        # Generate HTML report
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>RL Trading Agent Training Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .metric {{ display: inline-block; margin: 10px; padding: 10px; 
                          background-color: #e8f4f8; border-radius: 5px; }}
                .config {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; 
                          font-family: monospace; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>RL Trading Agent Training Report</h1>
                <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <h2>Training Summary</h2>
        """
        
        # Add training statistics
        if training_stats:
            html_content += f"""
                <div class="metric">
                    <strong>Total Episodes:</strong> {training_stats.get('total_episodes', 'N/A')}
                </div>
                <div class="metric">
                    <strong>Average Reward:</strong> {training_stats.get('avg_reward', 0):.4f}
                </div>
                <div class="metric">
                    <strong>Reward Std:</strong> {training_stats.get('std_reward', 0):.4f}
                </div>
                <div class="metric">
                    <strong>Best Reward:</strong> {training_stats.get('max_reward', 0):.4f}
                </div>
                <div class="metric">
                    <strong>Worst Reward:</strong> {training_stats.get('min_reward', 0):.4f}
                </div>
            """
            if training_stats.get('final_100_avg'):
                html_content += f"""
                <div class="metric">
                    <strong>Final 100 Avg:</strong> {training_stats['final_100_avg']:.4f}
                </div>
                """
        
        html_content += """
            </div>
            
            <div class="section">
                <h2>Final Performance Metrics</h2>
                <table>
                    <tr><th>Metric</th><th>Value</th></tr>
        """
        
        # Add performance metrics
        for metric, value in final_performance.items():
            if isinstance(value, float):
                formatted_value = f"{value:.4f}"
            else:
                formatted_value = str(value)
            html_content += f"<tr><td>{metric.replace('_', ' ').title()}</td><td>{formatted_value}</td></tr>"
        
        html_content += """
                </table>
            </div>
            
            <div class="section">
                <h2>Model Configuration</h2>
                <div class="config">
        """
        
        # Add model configuration
        html_content += f"<pre>{json.dumps(model_config, indent=2)}</pre>"
        
        html_content += """
                </div>
            </div>
            
            <div class="section">
                <h2>Training Progress Analysis</h2>
        """
        
        # Add training analysis
        if self.episode_rewards and len(self.episode_rewards) > 10:
            rewards_df = pd.DataFrame(self.episode_rewards)
            
            # Calculate learning phases
            early_phase = rewards_df.head(len(rewards_df) // 3)['reward'].mean()
            middle_phase = rewards_df.iloc[len(rewards_df) // 3:2 * len(rewards_df) // 3]['reward'].mean()
            late_phase = rewards_df.tail(len(rewards_df) // 3)['reward'].mean()
            
            html_content += f"""
                <p><strong>Learning Progress:</strong></p>
                <ul>
                    <li>Early Phase (first 1/3): {early_phase:.4f} average reward</li>
                    <li>Middle Phase (middle 1/3): {middle_phase:.4f} average reward</li>
                    <li>Late Phase (last 1/3): {late_phase:.4f} average reward</li>
                    <li>Overall Improvement: {((late_phase - early_phase) / abs(early_phase) * 100):.2f}%</li>
                </ul>
            """
        
        html_content += """
            </div>
        </body>
        </html>
        """
        
        # Save report
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return str(save_path)
        
    def save_metrics_to_csv(self, save_path: Optional[str] = None) -> str:
        """
        Save all collected metrics to CSV files.
        
        Args:
            save_path: Base path for saving CSV files
            
        Returns:
            Directory path containing saved CSV files
        """
        if save_path is None:
            save_dir = self.output_dir / f"metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        else:
            save_dir = Path(save_path)
        
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # Save episode rewards
        if self.episode_rewards:
            rewards_df = pd.DataFrame(self.episode_rewards)
            rewards_df.to_csv(save_dir / "episode_rewards.csv", index=False)
        
        # Save training metrics
        if self.training_metrics:
            training_df = pd.DataFrame(self.training_metrics)
            training_df.to_csv(save_dir / "training_metrics.csv", index=False)
        
        # Save performance history
        if self.performance_history:
            performance_df = pd.DataFrame(self.performance_history)
            performance_df.to_csv(save_dir / "performance_history.csv", index=False)
        
        return str(save_dir)
        
    def create_model_diagnostics(self, model_state: Dict[str, Any], 
                               save_path: Optional[str] = None) -> str:
        """
        Create model diagnostics visualization.
        
        Args:
            model_state: Model state dictionary containing weights and gradients
            save_path: Path to save the diagnostics plot
            
        Returns:
            Path to saved plot
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Model Diagnostics', fontsize=16)
        
        # Extract weight statistics
        weight_stats = []
        gradient_stats = []
        
        for name, param in model_state.items():
            if 'weight' in name and hasattr(param, 'data'):
                weights = param.data.cpu().numpy().flatten()
                weight_stats.extend(weights)
                
                if hasattr(param, 'grad') and param.grad is not None:
                    gradients = param.grad.cpu().numpy().flatten()
                    gradient_stats.extend(gradients)
        
        # Weight distribution
        if weight_stats:
            axes[0, 0].hist(weight_stats, bins=50, alpha=0.7, edgecolor='black')
            axes[0, 0].set_xlabel('Weight Values')
            axes[0, 0].set_ylabel('Frequency')
            axes[0, 0].set_title('Weight Distribution')
            axes[0, 0].axvline(np.mean(weight_stats), color='red', linestyle='--', 
                              label=f'Mean: {np.mean(weight_stats):.4f}')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
        
        # Gradient distribution
        if gradient_stats:
            axes[0, 1].hist(gradient_stats, bins=50, alpha=0.7, edgecolor='black')
            axes[0, 1].set_xlabel('Gradient Values')
            axes[0, 1].set_ylabel('Frequency')
            axes[0, 1].set_title('Gradient Distribution')
            axes[0, 1].axvline(np.mean(gradient_stats), color='red', linestyle='--', 
                              label=f'Mean: {np.mean(gradient_stats):.6f}')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
        
        # Weight statistics by layer
        layer_names = []
        layer_means = []
        layer_stds = []
        
        for name, param in model_state.items():
            if 'weight' in name and hasattr(param, 'data'):
                weights = param.data.cpu().numpy()
                layer_names.append(name.replace('.weight', ''))
                layer_means.append(np.mean(weights))
                layer_stds.append(np.std(weights))
        
        if layer_names:
            x_pos = np.arange(len(layer_names))
            axes[1, 0].bar(x_pos, layer_means, yerr=layer_stds, capsize=5, alpha=0.7)
            axes[1, 0].set_xlabel('Layers')
            axes[1, 0].set_ylabel('Weight Mean ± Std')
            axes[1, 0].set_title('Weight Statistics by Layer')
            axes[1, 0].set_xticks(x_pos)
            axes[1, 0].set_xticklabels(layer_names, rotation=45, ha='right')
            axes[1, 0].grid(True, alpha=0.3)
        
        # Training loss over time (if available in training metrics)
        if self.training_metrics:
            training_df = pd.DataFrame(self.training_metrics)
            if 'loss' in training_df.columns:
                axes[1, 1].plot(training_df['episode'], training_df['loss'], 'b-', linewidth=2)
                axes[1, 1].set_xlabel('Episode')
                axes[1, 1].set_ylabel('Loss')
                axes[1, 1].set_title('Training Loss Over Time')
                axes[1, 1].grid(True, alpha=0.3)
            else:
                axes[1, 1].text(0.5, 0.5, 'No loss data\navailable', 
                               ha='center', va='center', transform=axes[1, 1].transAxes)
        else:
            axes[1, 1].text(0.5, 0.5, 'No training\nmetrics available', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
        
        plt.tight_layout()
        
        if save_path is None:
            save_path = self.output_dir / f"model_diagnostics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(save_path)