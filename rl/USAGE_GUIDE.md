# RL Trading System Usage Guide

This guide provides comprehensive instructions for using the Reinforcement Learning (RL) trading optimization system.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Configuration Guide](#configuration-guide)
3. [Training Workflows](#training-workflows)
4. [Backtesting and Evaluation](#backtesting-and-evaluation)
5. [Best Practices](#best-practices)
6. [Troubleshooting](#troubleshooting)
7. [Advanced Usage](#advanced-usage)

## Quick Start

### Prerequisites

1. **Existing Models**: Ensure you have trained prediction models (`.joblib` files) and their configurations
2. **Data**: Historical price data in the expected format
3. **Dependencies**: Install required Python packages (see `requirements.txt`)

### Basic Usage

```bash
# Run complete workflow for ETH 5-minute strategy
python rl/example_complete_workflow.py --symbol ETH --timeframe 5m

# Run with custom episodes
python rl/example_complete_workflow.py --symbol BTC --timeframe 15m --episodes 2000

# Use custom configuration
python rl/example_complete_workflow.py --symbol ETH --timeframe 5m --config rl/configs/eth_5m_conservative.json
```

### Expected Output

The workflow creates a timestamped directory with:
- `SUMMARY_REPORT.md` - Human-readable performance summary
- `training_results.json` - Training metrics and logs
- `test_results.json` - Backtest performance data
- `trade_log.csv` - Detailed trade records
- `models/best_model.pth` - Trained RL model
- Performance charts and analysis files

## Configuration Guide

### Configuration File Structure

```json
{
  "symbol": "ETH",
  "timeframe": "5m",
  "model_file": "models/eth_5m_model.joblib",
  "config_file": "models/eth_5m_config.json",
  
  "training": {
    "episodes": 1000,
    "learning_rate": 3e-4,
    "batch_size": 64
  },
  
  "environment": {
    "initial_capital": 10000,
    "max_position_size": 0.1,
    "transaction_cost": 0.001
  },
  
  "reward": {
    "profit_weight": 1.0,
    "risk_weight": 0.5,
    "drawdown_penalty": 2.0
  }
}
```

### Pre-configured Scenarios

#### Conservative Strategy (`eth_5m_conservative.json`)
- **Use Case**: Risk-averse trading with capital preservation focus
- **Features**: Lower position sizes, higher risk penalties, stricter stop losses
- **Best For**: Stable returns, lower volatility tolerance

#### Aggressive Strategy (`eth_5m_aggressive.json`)
- **Use Case**: High-risk, high-reward trading
- **Features**: Larger positions, higher profit weights, wider stop losses
- **Best For**: Maximum returns, higher volatility tolerance

#### Balanced Strategy (`btc_15m_balanced.json`)
- **Use Case**: Moderate risk/reward balance
- **Features**: Medium position sizes, balanced reward weights
- **Best For**: General purpose trading

#### Multi-Coin Diversified (`multi_coin_diversified.json`)
- **Use Case**: Portfolio-level optimization across multiple assets
- **Features**: Cross-asset correlation management, portfolio rebalancing
- **Best For**: Diversified crypto portfolio management

### Customizing Configurations

#### Training Parameters
```json
"training": {
  "episodes": 1500,           // Number of training episodes
  "episode_length": 1000,     // Steps per episode
  "learning_rate": 3e-4,      // Learning rate for optimizer
  "batch_size": 64,           // Batch size for updates
  "update_frequency": 100,    // Steps between policy updates
  "gamma": 0.99,              // Discount factor
  "clip_epsilon": 0.2,        // PPO clipping parameter
  "entropy_coef": 0.01,       // Entropy regularization
  "value_loss_coef": 0.5      // Value function loss weight
}
```

#### Environment Parameters
```json
"environment": {
  "initial_capital": 10000,        // Starting capital
  "transaction_cost": 0.001,       // Trading fees (0.1%)
  "slippage": 0.0005,             // Market impact (0.05%)
  "max_position_size": 0.1,       // Max 10% per position
  "min_position_size": 0.001,     // Min 0.1% per position
  "max_positions": 5,             // Max concurrent positions
  "max_drawdown_limit": 0.2,      // 20% drawdown limit
  "stop_loss_range": [0.01, 0.05], // 1-5% stop loss range
  "take_profit_range": [0.015, 0.04] // 1.5-4% take profit range
}
```

#### Reward Function Parameters
```json
"reward": {
  "profit_weight": 1.0,           // Weight for profit component
  "risk_weight": 0.5,             // Weight for risk penalty
  "efficiency_weight": 0.1,       // Weight for time efficiency
  "drawdown_penalty": 2.0,        // Penalty for drawdowns
  "transaction_cost_penalty": 0.1, // Penalty for excessive trading
  "consistency_bonus": 0.1,       // Bonus for consistent performance
  "win_rate_bonus": 0.15          // Bonus for high win rate
}
```

## Training Workflows

### Single Asset Training

```bash
# Basic training
python rl/train_rl_agent.py --symbol ETH --timeframe 5m --episodes 1000

# With custom config
python rl/train_rl_agent.py --config rl/configs/eth_5m_conservative.json

# Resume training
python rl/train_rl_agent.py --symbol ETH --timeframe 5m --resume models/eth_5m_rl_checkpoint.pth
```

### Multi-Asset Training

```bash
# Train portfolio strategy
python rl/train_rl_agent.py --config rl/configs/multi_coin_diversified.json

# Train individual models then ensemble
python rl/train_ensemble.py --symbols ETH,BTC,DOT --timeframe 15m
```

### Training Monitoring

Monitor training progress:
```bash
# View training logs
tail -f rl_training_logs/training.log

# Launch TensorBoard (if configured)
tensorboard --logdir rl_training_logs/tensorboard

# Check training status
python rl/check_training_status.py --model_dir models/
```

## Backtesting and Evaluation

### Running Backtests

```bash
# Basic backtest
python rl/run_rl_backtest.py --model models/eth_5m_rl_model.pth --start 2024-01-01 --end 2024-12-31

# Compare with benchmarks
python rl/benchmark_comparison.py --rl_model models/eth_5m_rl_model.pth --symbol ETH --timeframe 5m

# Detailed performance analysis
python rl/performance_evaluator.py --results backtest_results.json --output analysis_report/
```

### Evaluation Metrics

The system tracks comprehensive metrics:

#### Return Metrics
- **Total Return**: Cumulative percentage return
- **Annualized Return**: Yearly return rate
- **Excess Return**: Return above benchmark

#### Risk Metrics
- **Sharpe Ratio**: Risk-adjusted return
- **Sortino Ratio**: Downside risk-adjusted return
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Value at Risk (VaR)**: Potential loss at confidence level

#### Trading Metrics
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Gross profit / Gross loss
- **Average Trade**: Mean profit/loss per trade
- **Trade Frequency**: Number of trades per period

#### Efficiency Metrics
- **Calmar Ratio**: Return / Maximum Drawdown
- **Recovery Factor**: Return / Maximum Drawdown
- **Ulcer Index**: Measure of downside volatility

### Benchmark Comparisons

Available benchmark strategies:
- **Buy and Hold**: Simple buy and hold strategy
- **Simple Momentum**: Basic momentum strategy
- **Mean Reversion**: RSI-based mean reversion
- **Random Trading**: Random entry/exit for baseline

## Best Practices

### Configuration Best Practices

1. **Start Conservative**: Begin with conservative parameters and gradually increase risk
2. **Validate Data**: Ensure sufficient historical data for training periods
3. **Parameter Scaling**: Scale parameters appropriately for different timeframes
4. **Risk Limits**: Always set maximum drawdown and position size limits

### Training Best Practices

1. **Data Quality**: Use clean, validated price data
2. **Train/Val/Test Split**: Use proper time-based splits (70%/15%/15%)
3. **Hyperparameter Tuning**: Use validation set for hyperparameter optimization
4. **Early Stopping**: Monitor validation performance to prevent overfitting
5. **Multiple Seeds**: Train with different random seeds for robustness

### Deployment Best Practices

1. **Paper Trading**: Test with paper trading before live deployment
2. **Gradual Scaling**: Start with small position sizes
3. **Performance Monitoring**: Continuously monitor live performance
4. **Model Updates**: Retrain periodically with new data
5. **Risk Management**: Implement circuit breakers and position limits

### Risk Management

1. **Position Sizing**: Never risk more than you can afford to lose
2. **Diversification**: Don't put all capital in one strategy/asset
3. **Stop Losses**: Always use stop losses to limit downside
4. **Correlation Monitoring**: Monitor correlation between positions
5. **Drawdown Limits**: Set maximum acceptable drawdown levels

## Troubleshooting

### Common Issues

#### Training Issues

**Problem**: Training loss not decreasing
```bash
# Check learning rate
python rl/diagnose_training.py --model_dir models/ --check learning_rate

# Reduce learning rate
# Edit config: "learning_rate": 1e-4
```

**Problem**: Agent not learning to trade
```bash
# Check reward function
python rl/diagnose_training.py --model_dir models/ --check rewards

# Adjust reward weights in config
```

**Problem**: Overfitting to training data
```bash
# Check validation performance
python rl/diagnose_training.py --model_dir models/ --check overfitting

# Increase regularization or reduce model complexity
```

#### Data Issues

**Problem**: Insufficient data
```bash
# Check data availability
python rl/check_data.py --symbol ETH --timeframe 5m --start 2023-01-01

# Extend data collection period or use different timeframe
```

**Problem**: Data quality issues
```bash
# Validate data quality
python rl/validate_data.py --symbol ETH --timeframe 5m

# Clean data or exclude problematic periods
```

#### Performance Issues

**Problem**: Poor backtest performance
```bash
# Analyze performance breakdown
python rl/analyze_performance.py --results test_results.json

# Check for data leakage or overfitting
python rl/check_data_leakage.py --model models/eth_5m_rl_model.pth
```

### Debugging Tools

```bash
# Training diagnostics
python rl/training_diagnostics.py --model_dir models/

# Performance analysis
python rl/performance_diagnostics.py --results backtest_results.json

# Model inspection
python rl/model_inspector.py --model models/eth_5m_rl_model.pth
```

## Advanced Usage

### Custom Reward Functions

Create custom reward functions:

```python
# rl/custom_rewards.py
def custom_reward_function(trade_result, portfolio_state, market_state):
    """Custom reward function example"""
    
    # Base profit/loss
    pnl_reward = trade_result['pnl'] / portfolio_state['capital']
    
    # Custom risk adjustment
    volatility_penalty = -market_state['volatility'] * 0.5
    
    # Custom efficiency bonus
    time_bonus = 1.0 / max(trade_result['hold_time'], 1)
    
    return pnl_reward + volatility_penalty + time_bonus * 0.1
```

### Custom Features

Add custom market features:

```python
# rl/custom_features.py
def extract_custom_features(market_data):
    """Extract custom market features"""
    
    features = {}
    
    # Technical indicators
    features['rsi'] = calculate_rsi(market_data['close'])
    features['macd'] = calculate_macd(market_data['close'])
    
    # Market microstructure
    features['bid_ask_spread'] = market_data['ask'] - market_data['bid']
    features['order_flow'] = calculate_order_flow(market_data)
    
    return features
```

### Integration with External Systems

```python
# Integration example
from rl.rl_trading_agent import RLTradingAgent

# Load trained model
agent = RLTradingAgent.load('models/eth_5m_rl_model.pth')

# Get trading decision
market_state = get_current_market_state()
action = agent.get_action(market_state)

# Execute trade through broker API
if action['enter_trade']:
    execute_trade(
        symbol='ETH',
        size=action['position_size'],
        stop_loss=action['stop_loss_pct'],
        take_profit=action['take_profit_pct']
    )
```

### Performance Optimization

```bash
# Enable GPU training
export CUDA_VISIBLE_DEVICES=0
python rl/train_rl_agent.py --symbol ETH --timeframe 5m --device cuda

# Parallel training
python rl/train_parallel.py --symbols ETH,BTC,DOT --workers 4

# Memory optimization
python rl/train_rl_agent.py --symbol ETH --timeframe 5m --memory_efficient
```

## Support and Resources

### Documentation
- [API Reference](rl/API_REFERENCE.md)
- [Architecture Guide](rl/ARCHITECTURE.md)
- [Development Guide](rl/DEVELOPMENT.md)

### Examples
- [Basic Training Example](rl/examples/basic_training.py)
- [Custom Strategy Example](rl/examples/custom_strategy.py)
- [Portfolio Management Example](rl/examples/portfolio_management.py)

### Community
- GitHub Issues: Report bugs and request features
- Discussions: Ask questions and share strategies
- Wiki: Community-contributed guides and tips

---

For additional help, please refer to the specific component documentation or open an issue on GitHub.