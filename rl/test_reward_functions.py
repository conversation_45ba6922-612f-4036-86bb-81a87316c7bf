# test_reward_functions.py
# 奖励函数测试系统

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple
import json
import os
from datetime import datetime, timedelta

# Optional imports for plotting
try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False

from .reward_functions import (
    MultiObjectiveRewardFunction, 
    RewardConfig, 
    RewardFunctionTester,
    create_default_configs,
    MarketRegime
)
from .reward_config import (
    RewardConfigManager,
    create_template_config,
    REWARD_CONFIG_TEMPLATES
)

class RewardFunctionAnalyzer:
    """奖励函数分析器 - 测试不同奖励函数对学习效果的影响"""
    
    def __init__(self, output_dir: str = "rl/analysis"):
        self.output_dir = output_dir
        self.ensure_output_dir()
        self.test_results = {}
    
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def generate_test_scenarios(self, num_scenarios: int = 100) -> List[Dict]:
        """生成测试场景数据"""
        scenarios = []
        
        for i in range(num_scenarios):
            # 随机生成交易结果
            pnl_pct = np.random.normal(0.005, 0.03)  # 平均0.5%收益，3%标准差
            pnl = pnl_pct * 1000  # 假设1000资金
            
            trade_result = {
                'pnl': pnl,
                'pnl_pct': pnl_pct,
                'max_loss_pct': min(pnl_pct, np.random.uniform(-0.05, 0)),
                'hold_time_minutes': np.random.randint(30, 240),
                'exit_reason': np.random.choice(['take_profit', 'stop_loss', 'timeout'])
            }
            
            # 随机生成市场状态
            market_state = {
                'price_change_1h': np.random.normal(0, 0.02),
                'price_change_4h': np.random.normal(0, 0.03),
                'volatility_recent': np.random.uniform(0.01, 0.08)
            }
            
            # 随机生成投资组合指标
            portfolio_metrics = {
                'recent_win_rate': np.random.uniform(0.3, 0.8),
                'consecutive_wins': np.random.randint(0, 8),
                'consecutive_losses': np.random.randint(0, 5),
                'total_trades': np.random.randint(10, 200),
                'current_drawdown': np.random.uniform(0, 0.15),
                'recent_volatility': np.random.uniform(0.02, 0.06),
                'sharpe_ratio': np.random.normal(0.8, 0.5)
            }
            
            scenarios.append({
                'trade_result': trade_result,
                'market_state': market_state,
                'portfolio_metrics': portfolio_metrics
            })
        
        return scenarios
    
    def test_reward_function_sensitivity(self, base_config: RewardConfig, 
                                       parameter_ranges: Dict[str, List[float]],
                                       test_scenarios: List[Dict]) -> Dict:
        """测试奖励函数参数敏感性"""
        sensitivity_results = {}
        
        for param_name, param_values in parameter_ranges.items():
            print(f"测试参数敏感性: {param_name}")
            param_results = []
            
            for param_value in param_values:
                # 创建修改后的配置
                config_dict = base_config.__dict__.copy()
                config_dict[param_name] = param_value
                test_config = RewardConfig(**config_dict)
                
                # 测试奖励函数
                reward_func = MultiObjectiveRewardFunction(test_config)
                rewards = []
                
                for scenario in test_scenarios:
                    reward_breakdown = reward_func.calculate_reward(
                        scenario['trade_result'],
                        scenario['market_state'],
                        scenario['portfolio_metrics']
                    )
                    rewards.append(reward_breakdown['total_reward'])
                
                param_results.append({
                    'param_value': param_value,
                    'mean_reward': np.mean(rewards),
                    'std_reward': np.std(rewards),
                    'reward_sharpe': np.mean(rewards) / (np.std(rewards) + 1e-8)
                })
            
            sensitivity_results[param_name] = param_results
        
        return sensitivity_results
    
    def compare_market_regime_performance(self, configs: Dict[str, RewardConfig],
                                        test_scenarios: List[Dict]) -> Dict:
        """比较不同市场状态下的奖励函数表现"""
        regime_results = {}
        
        # 按市场状态分组测试场景
        regime_scenarios = {
            MarketRegime.TRENDING_UP.value: [],
            MarketRegime.TRENDING_DOWN.value: [],
            MarketRegime.SIDEWAYS.value: [],
            MarketRegime.HIGH_VOLATILITY.value: [],
            MarketRegime.LOW_VOLATILITY.value: []
        }
        
        for scenario in test_scenarios:
            market_state = scenario['market_state']
            
            # 简单的市场状态分类
            if market_state['volatility_recent'] > 0.05:
                regime = MarketRegime.HIGH_VOLATILITY.value
            elif market_state['volatility_recent'] < 0.02:
                regime = MarketRegime.LOW_VOLATILITY.value
            elif market_state['price_change_1h'] > 0.01:
                regime = MarketRegime.TRENDING_UP.value
            elif market_state['price_change_1h'] < -0.01:
                regime = MarketRegime.TRENDING_DOWN.value
            else:
                regime = MarketRegime.SIDEWAYS.value
            
            regime_scenarios[regime].append(scenario)
        
        # 测试每个配置在不同市场状态下的表现
        for config_name, config in configs.items():
            regime_results[config_name] = {}
            reward_func = MultiObjectiveRewardFunction(config)
            
            for regime, scenarios in regime_scenarios.items():
                if not scenarios:
                    continue
                
                rewards = []
                for scenario in scenarios:
                    reward_breakdown = reward_func.calculate_reward(
                        scenario['trade_result'],
                        scenario['market_state'],
                        scenario['portfolio_metrics']
                    )
                    rewards.append(reward_breakdown['total_reward'])
                
                regime_results[config_name][regime] = {
                    'mean_reward': np.mean(rewards),
                    'std_reward': np.std(rewards),
                    'num_scenarios': len(scenarios),
                    'reward_sharpe': np.mean(rewards) / (np.std(rewards) + 1e-8)
                }
        
        return regime_results
    
    def analyze_reward_components(self, config: RewardConfig, 
                                test_scenarios: List[Dict]) -> Dict:
        """分析奖励函数各组件的贡献"""
        reward_func = MultiObjectiveRewardFunction(config)
        component_analysis = {
            'pnl_rewards': [],
            'risk_penalties': [],
            'efficiency_rewards': [],
            'sharpe_bonuses': [],
            'win_rate_bonuses': [],
            'consecutive_bonuses': [],
            'frequency_penalties': [],
            'total_rewards': []
        }
        
        for scenario in test_scenarios:
            reward_breakdown = reward_func.calculate_reward(
                scenario['trade_result'],
                scenario['market_state'],
                scenario['portfolio_metrics']
            )
            
            component_analysis['pnl_rewards'].append(reward_breakdown['pnl_reward'])
            component_analysis['risk_penalties'].append(reward_breakdown['risk_penalty'])
            component_analysis['efficiency_rewards'].append(reward_breakdown['efficiency_reward'])
            component_analysis['sharpe_bonuses'].append(reward_breakdown['sharpe_bonus'])
            component_analysis['win_rate_bonuses'].append(reward_breakdown['win_rate_bonus'])
            component_analysis['consecutive_bonuses'].append(reward_breakdown['consecutive_bonus'])
            component_analysis['frequency_penalties'].append(reward_breakdown['frequency_penalty'])
            component_analysis['total_rewards'].append(reward_breakdown['total_reward'])
        
        # 计算统计信息
        component_stats = {}
        for component, values in component_analysis.items():
            component_stats[component] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values),
                'contribution_pct': abs(np.mean(values)) / (abs(np.mean(component_analysis['total_rewards'])) + 1e-8) * 100
            }
        
        return component_stats
    
    def run_comprehensive_test(self, save_results: bool = True) -> Dict:
        """运行综合测试"""
        print("开始综合奖励函数测试...")
        
        # 生成测试场景
        test_scenarios = self.generate_test_scenarios(200)
        print(f"生成了 {len(test_scenarios)} 个测试场景")
        
        # 获取所有配置
        default_configs = create_default_configs()
        template_configs = {}
        for template_name in REWARD_CONFIG_TEMPLATES.keys():
            template_configs[template_name] = create_template_config(template_name)
        
        all_configs = {**default_configs, **template_configs}
        
        results = {
            'test_timestamp': datetime.now().isoformat(),
            'num_scenarios': len(test_scenarios),
            'configs_tested': list(all_configs.keys())
        }
        
        # 1. 基础性能比较
        print("1. 基础性能比较...")
        tester = RewardFunctionTester()
        basic_comparison = tester.compare_reward_functions(all_configs, test_scenarios)
        results['basic_comparison'] = basic_comparison
        
        # 2. 市场状态表现比较
        print("2. 市场状态表现比较...")
        regime_performance = self.compare_market_regime_performance(all_configs, test_scenarios)
        results['regime_performance'] = regime_performance
        
        # 3. 参数敏感性分析（使用平衡配置作为基准）
        print("3. 参数敏感性分析...")
        base_config = default_configs['balanced']
        parameter_ranges = {
            'pnl_weight': [0.5, 0.8, 1.0, 1.2, 1.5],
            'risk_penalty_weight': [0.2, 0.5, 0.7, 1.0, 1.5],
            'time_efficiency_weight': [0.05, 0.1, 0.15, 0.2, 0.3],
            'sharpe_bonus_weight': [0.1, 0.2, 0.3, 0.4, 0.5]
        }
        sensitivity_analysis = self.test_reward_function_sensitivity(
            base_config, parameter_ranges, test_scenarios[:50]  # 使用较少场景以节省时间
        )
        results['sensitivity_analysis'] = sensitivity_analysis
        
        # 4. 奖励组件分析
        print("4. 奖励组件分析...")
        component_analysis = {}
        for config_name, config in all_configs.items():
            component_analysis[config_name] = self.analyze_reward_components(
                config, test_scenarios[:50]
            )
        results['component_analysis'] = component_analysis
        
        # 保存结果
        if save_results:
            self.save_test_results(results)
        
        self.test_results = results
        print("综合测试完成！")
        
        return results
    
    def save_test_results(self, results: Dict):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"reward_function_test_results_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        # 转换numpy类型为Python原生类型
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {key: convert_numpy(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            else:
                return obj
        
        results_serializable = convert_numpy(results)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results_serializable, f, indent=2, ensure_ascii=False)
        
        print(f"测试结果已保存: {filepath}")
    
    def generate_report(self, results: Dict = None) -> str:
        """生成测试报告"""
        if results is None:
            results = self.test_results
        
        if not results:
            return "没有可用的测试结果"
        
        report = []
        report.append("# 奖励函数测试报告")
        report.append(f"测试时间: {results.get('test_timestamp', 'Unknown')}")
        report.append(f"测试场景数: {results.get('num_scenarios', 0)}")
        report.append("")
        
        # 基础性能比较
        if 'basic_comparison' in results:
            report.append("## 基础性能比较")
            basic_comp = results['basic_comparison']
            
            # 按夏普比率排序
            sorted_configs = sorted(
                basic_comp.items(), 
                key=lambda x: x[1]['sharpe'], 
                reverse=True
            )
            
            report.append("| 配置名称 | 平均奖励 | 标准差 | 夏普比率 |")
            report.append("|---------|---------|--------|----------|")
            
            for config_name, metrics in sorted_configs:
                report.append(
                    f"| {config_name} | {metrics['mean']:.4f} | "
                    f"{metrics['std']:.4f} | {metrics['sharpe']:.4f} |"
                )
            report.append("")
        
        # 市场状态表现
        if 'regime_performance' in results:
            report.append("## 不同市场状态下的表现")
            regime_perf = results['regime_performance']
            
            for config_name, regimes in regime_perf.items():
                report.append(f"### {config_name}")
                report.append("| 市场状态 | 平均奖励 | 夏普比率 | 场景数 |")
                report.append("|---------|---------|----------|--------|")
                
                for regime, metrics in regimes.items():
                    report.append(
                        f"| {regime} | {metrics['mean_reward']:.4f} | "
                        f"{metrics['reward_sharpe']:.4f} | {metrics['num_scenarios']} |"
                    )
                report.append("")
        
        # 参数敏感性分析
        if 'sensitivity_analysis' in results:
            report.append("## 参数敏感性分析")
            sensitivity = results['sensitivity_analysis']
            
            for param_name, param_results in sensitivity.items():
                report.append(f"### {param_name}")
                
                # 找到最佳参数值
                best_result = max(param_results, key=lambda x: x['reward_sharpe'])
                report.append(f"最佳值: {best_result['param_value']} (夏普比率: {best_result['reward_sharpe']:.4f})")
                report.append("")
        
        # 组件分析
        if 'component_analysis' in results:
            report.append("## 奖励组件贡献分析")
            component_analysis = results['component_analysis']
            
            # 选择一个代表性配置进行展示
            if 'balanced' in component_analysis:
                components = component_analysis['balanced']
                report.append("### 平衡配置的组件贡献")
                report.append("| 组件 | 平均值 | 贡献占比(%) |")
                report.append("|------|--------|-------------|")
                
                for component, stats in components.items():
                    if component != 'total_rewards':
                        report.append(
                            f"| {component} | {stats['mean']:.4f} | "
                            f"{stats['contribution_pct']:.2f}% |"
                        )
                report.append("")
        
        return "\n".join(report)
    
    def plot_results(self, results: Dict = None):
        """绘制测试结果图表"""
        if not HAS_MATPLOTLIB:
            print("matplotlib未安装，跳过绘图")
            return
            
        if results is None:
            results = self.test_results
        
        if not results:
            print("没有可用的测试结果")
            return
        
        # 设置图表样式
        if HAS_SEABORN:
            plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 基础性能比较
        if 'basic_comparison' in results:
            ax = axes[0, 0]
            basic_comp = results['basic_comparison']
            
            configs = list(basic_comp.keys())
            sharpe_ratios = [basic_comp[config]['sharpe'] for config in configs]
            
            bars = ax.bar(configs, sharpe_ratios)
            ax.set_title('奖励函数夏普比率比较')
            ax.set_ylabel('夏普比率')
            ax.tick_params(axis='x', rotation=45)
            
            # 添加数值标签
            for bar, value in zip(bars, sharpe_ratios):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{value:.3f}', ha='center', va='bottom')
        
        # 2. 参数敏感性分析
        if 'sensitivity_analysis' in results:
            ax = axes[0, 1]
            sensitivity = results['sensitivity_analysis']
            
            # 选择一个参数进行展示
            if 'pnl_weight' in sensitivity:
                param_results = sensitivity['pnl_weight']
                param_values = [r['param_value'] for r in param_results]
                sharpe_values = [r['reward_sharpe'] for r in param_results]
                
                ax.plot(param_values, sharpe_values, 'o-')
                ax.set_title('PnL权重敏感性分析')
                ax.set_xlabel('PnL权重')
                ax.set_ylabel('夏普比率')
                ax.grid(True)
        
        # 3. 市场状态表现热力图
        if 'regime_performance' in results:
            ax = axes[1, 0]
            regime_perf = results['regime_performance']
            
            # 准备热力图数据
            configs = list(regime_perf.keys())
            regimes = list(MarketRegime)
            regime_names = [r.value for r in regimes]
            
            heatmap_data = []
            for config in configs:
                row = []
                for regime_name in regime_names:
                    if regime_name in regime_perf[config]:
                        row.append(regime_perf[config][regime_name]['mean_reward'])
                    else:
                        row.append(0)
                heatmap_data.append(row)
            
            if HAS_SEABORN:
                sns.heatmap(heatmap_data, 
                           xticklabels=regime_names,
                           yticklabels=configs,
                           annot=True, 
                           fmt='.3f',
                           ax=ax)
            else:
                # Fallback to matplotlib imshow
                im = ax.imshow(heatmap_data, cmap='viridis', aspect='auto')
                ax.set_xticks(range(len(regime_names)))
                ax.set_xticklabels(regime_names)
                ax.set_yticks(range(len(configs)))
                ax.set_yticklabels(configs)
                plt.colorbar(im, ax=ax)
            ax.set_title('不同市场状态下的平均奖励')
        
        # 4. 组件贡献分析
        if 'component_analysis' in results and 'balanced' in results['component_analysis']:
            ax = axes[1, 1]
            components = results['component_analysis']['balanced']
            
            component_names = []
            contributions = []
            
            for component, stats in components.items():
                if component != 'total_rewards':
                    component_names.append(component.replace('_', ' ').title())
                    contributions.append(abs(stats['contribution_pct']))
            
            ax.pie(contributions, labels=component_names, autopct='%1.1f%%')
            ax.set_title('奖励组件贡献占比 (平衡配置)')
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        plot_filename = f"reward_function_analysis_{timestamp}.png"
        plot_filepath = os.path.join(self.output_dir, plot_filename)
        plt.savefig(plot_filepath, dpi=300, bbox_inches='tight')
        
        print(f"分析图表已保存: {plot_filepath}")
        plt.show()

def run_reward_function_tests():
    """运行奖励函数测试的主函数"""
    print("开始奖励函数优化测试...")
    
    # 创建分析器
    analyzer = RewardFunctionAnalyzer()
    
    # 运行综合测试
    results = analyzer.run_comprehensive_test()
    
    # 生成报告
    report = analyzer.generate_report(results)
    
    # 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"reward_function_report_{timestamp}.md"
    report_filepath = os.path.join(analyzer.output_dir, report_filename)
    
    with open(report_filepath, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"测试报告已保存: {report_filepath}")
    
    # 绘制结果图表
    try:
        analyzer.plot_results(results)
    except Exception as e:
        print(f"绘图失败: {e}")
    
    # 打印简要结果
    print("\n=== 测试结果摘要 ===")
    if 'basic_comparison' in results:
        basic_comp = results['basic_comparison']
        best_config = max(basic_comp.items(), key=lambda x: x[1]['sharpe'])
        print(f"最佳配置: {best_config[0]} (夏普比率: {best_config[1]['sharpe']:.4f})")
    
    return results

if __name__ == "__main__":
    # 运行测试
    results = run_reward_function_tests()
    print("奖励函数测试完成！")