#!/usr/bin/env python3
"""
测试 SuperTrend 计算功能
验证 SuperTrend 指标计算是否与 TradingView 的 Pine Script 一致
"""

import pandas as pd
import numpy as np
from backtest_money_quick import calculate_supertrend, load_data_from_sqlite
from get_coin_history import get_table_name

def test_supertrend_with_sample_data():
    """使用示例数据测试 SuperTrend 计算"""
    print("=== SuperTrend 计算测试 ===")
    
    # 创建示例数据
    np.random.seed(42)
    dates = pd.date_range('2024-01-01', periods=100, freq='5T')
    
    # 生成模拟价格数据
    base_price = 100
    price_changes = np.random.normal(0, 0.02, 100)  # 2% 标准差
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    # 创建 OHLC 数据
    df = pd.DataFrame(index=dates)
    df['close'] = prices
    df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0])
    
    # 生成 high 和 low（基于 close 价格的小幅波动）
    volatility = 0.01  # 1% 波动
    df['high'] = df['close'] * (1 + np.random.uniform(0, volatility, len(df)))
    df['low'] = df['close'] * (1 - np.random.uniform(0, volatility, len(df)))
    
    # 确保 high >= close >= low
    df['high'] = np.maximum(df['high'], df['close'])
    df['low'] = np.minimum(df['low'], df['close'])
    
    print(f"生成了 {len(df)} 条示例数据")
    print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    # 计算 SuperTrend
    supertrend_result = calculate_supertrend(df, atr_period=10, multiplier=3.0)
    
    # 合并结果
    result_df = pd.concat([df, supertrend_result], axis=1)
    
    print("\n=== SuperTrend 计算结果 ===")
    print(f"SuperTrend 值范围: {result_df['supertrend'].min():.2f} - {result_df['supertrend'].max():.2f}")
    
    # 统计趋势变化
    trend_changes = (result_df['trend'] != result_df['trend'].shift(1)).sum()
    bullish_periods = (result_df['trend'] == 1).sum()
    bearish_periods = (result_df['trend'] == -1).sum()
    
    print(f"趋势变化次数: {trend_changes}")
    print(f"看涨周期: {bullish_periods} ({bullish_periods/len(result_df)*100:.1f}%)")
    print(f"看跌周期: {bearish_periods} ({bearish_periods/len(result_df)*100:.1f}%)")
    
    # 显示最后几行数据
    print("\n=== 最后 10 行数据 ===")
    display_cols = ['close', 'supertrend', 'trend']
    print(result_df[display_cols].tail(10).round(4))
    
    return result_df

def test_supertrend_with_real_data(db_path="coin_data.db", coin="ETH", interval="15m", market="spot"):
    """使用真实数据测试 SuperTrend 计算"""
    print(f"\n=== 使用真实数据测试: {coin} {interval} ===")
    
    try:
        # 加载真实数据（最近 200 条记录）
        table_name = get_table_name(coin + "USDT", interval, market)
        
        import sqlite3
        conn = sqlite3.connect(db_path)
        query = f"""
        SELECT timestamp, open, high, low, close, volume 
        FROM {table_name} 
        ORDER BY timestamp DESC 
        LIMIT 200
        """
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if df.empty:
            print(f"❌ 未找到数据表: {table_name}")
            return None
        
        # 反转数据顺序（从旧到新）
        df = df.iloc[::-1].reset_index(drop=True)
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
        df.set_index('timestamp', inplace=True)
        df = df.astype(float)
        
        print(f"加载了 {len(df)} 条真实数据")
        print(f"时间范围: {df.index[0]} 到 {df.index[-1]}")
        print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        
        # 计算 SuperTrend
        supertrend_result = calculate_supertrend(df, atr_period=10, multiplier=3.0)
        
        # 合并结果
        result_df = pd.concat([df, supertrend_result], axis=1)
        
        # 统计分析
        trend_changes = (result_df['trend'] != result_df['trend'].shift(1)).sum()
        bullish_periods = (result_df['trend'] == 1).sum()
        bearish_periods = (result_df['trend'] == -1).sum()
        
        print(f"\n趋势变化次数: {trend_changes}")
        print(f"看涨周期: {bullish_periods} ({bullish_periods/len(result_df)*100:.1f}%)")
        print(f"看跌周期: {bearish_periods} ({bearish_periods/len(result_df)*100:.1f}%)")
        
        # 显示最后几行数据
        print("\n=== 最后 10 行数据 ===")
        display_cols = ['close', 'supertrend', 'trend']
        print(result_df[display_cols].tail(10).round(4))
        
        # 保存结果到 CSV 用于验证
        output_file = f"supertrend_test_{coin}_{interval}.csv"
        result_df.to_csv(output_file)
        print(f"\n结果已保存到: {output_file}")
        
        return result_df
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def validate_supertrend_logic(df):
    """验证 SuperTrend 逻辑的正确性"""
    print("\n=== SuperTrend 逻辑验证 ===")
    
    # 检查趋势变化点
    trend_changes = df[df['trend'] != df['trend'].shift(1)].copy()
    
    if len(trend_changes) > 0:
        print(f"发现 {len(trend_changes)} 个趋势变化点:")
        
        for i, (timestamp, row) in enumerate(trend_changes.iterrows()):
            if i < 5:  # 只显示前5个
                prev_trend = df.loc[:timestamp]['trend'].iloc[-2] if len(df.loc[:timestamp]) > 1 else None
                print(f"  {timestamp}: {prev_trend} → {row['trend']}, 价格: {row['close']:.4f}, ST: {row['supertrend']:.4f}")
    
    # 验证 SuperTrend 与价格的关系
    bullish_above_st = ((df['trend'] == 1) & (df['close'] > df['supertrend'])).sum()
    bearish_below_st = ((df['trend'] == -1) & (df['close'] < df['supertrend'])).sum()
    
    total_bullish = (df['trend'] == 1).sum()
    total_bearish = (df['trend'] == -1).sum()
    
    print(f"\n价格与 SuperTrend 关系验证:")
    if total_bullish > 0:
        print(f"  看涨时价格在 SuperTrend 之上: {bullish_above_st}/{total_bullish} ({bullish_above_st/total_bullish*100:.1f}%)")
    if total_bearish > 0:
        print(f"  看跌时价格在 SuperTrend 之下: {bearish_below_st}/{total_bearish} ({bearish_below_st/total_bearish*100:.1f}%)")

if __name__ == "__main__":
    print("SuperTrend 计算功能测试")
    print("=" * 50)
    
    # 测试 1: 使用示例数据
    sample_result = test_supertrend_with_sample_data()
    if sample_result is not None:
        validate_supertrend_logic(sample_result)
    
    # 测试 2: 使用真实数据
    real_result = test_supertrend_with_real_data()
    if real_result is not None:
        validate_supertrend_logic(real_result)
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n使用方法:")
    print("python backtest_money_quick.py --coin ETH --interval 5m --use-supertrend --supertrend-interval 15m --quick")