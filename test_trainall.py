#!/usr/bin/env python3
"""
测试 trainall.py 的配置和标签生成
"""

import json
import pandas as pd
import numpy as np
from trainall import (
    load_train_config, 
    get_coin_config_from_json, 
    get_multi_coin_config,
    create_percentage_target
)

def test_config_loading():
    """测试配置加载"""
    print("=== 测试配置加载 ===")
    
    config = load_train_config('train.json')
    if config is None:
        print("❌ 配置文件加载失败")
        return False
    
    print("✅ 配置文件加载成功")
    
    # 测试币种配置
    eth_config = get_coin_config_from_json(config, 'eth')
    if eth_config:
        print(f"✅ ETH配置: {eth_config}")
    else:
        print("❌ ETH配置获取失败")
        return False
    
    # 测试多币种配置
    multi_config = get_multi_coin_config(config, 'crypto_major')
    if multi_config:
        print(f"✅ 多币种配置: {multi_config}")
    else:
        print("❌ 多币种配置获取失败")
    
    return True

def test_label_generation():
    """测试标签生成"""
    print("\n=== 测试标签生成 ===")
    
    # 创建模拟数据
    dates = pd.date_range('2024-01-01', periods=1000, freq='5min')
    np.random.seed(42)
    
    # 生成有趋势的价格数据
    base_price = 100
    price_changes = np.random.normal(0, 0.002, 1000)  # 0.2% 标准差
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    df = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.001))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.001))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000, 10000, 1000)
    }, index=dates)
    
    print(f"模拟数据: {len(df)} 条记录")
    print(f"价格范围: {df['close'].min():.4f} - {df['close'].max():.4f}")
    print(f"价格变化范围: {((df['close'].pct_change().dropna()).min()*100):.2f}% - {((df['close'].pct_change().dropna()).max()*100):.2f}%")
    
    # 测试不同的阈值设置
    test_configs = [
        {'up_threshold': 0.01, 'down_threshold': 0.01, 'max_lookforward_minutes': 60, 'timeframe': 5},
        {'up_threshold': 0.005, 'down_threshold': 0.005, 'max_lookforward_minutes': 30, 'timeframe': 5},
        {'up_threshold': 0.002, 'down_threshold': 0.002, 'max_lookforward_minutes': 15, 'timeframe': 5},
    ]
    
    for i, config in enumerate(test_configs):
        print(f"\n--- 测试配置 {i+1} ---")
        labels = create_percentage_target(
            df, 
            config['up_threshold'], 
            config['down_threshold'], 
            config['max_lookforward_minutes'], 
            config['timeframe']
        )
        
        if len(labels) > 0:
            print(f"✅ 成功生成 {len(labels)} 个标签")
        else:
            print(f"❌ 未生成标签")

def main():
    """主测试函数"""
    print("🧪 trainall.py 功能测试")
    print("=" * 50)
    
    # 测试配置加载
    if not test_config_loading():
        print("❌ 配置测试失败，退出")
        return
    
    # 测试标签生成
    test_label_generation()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成")

if __name__ == '__main__':
    main()