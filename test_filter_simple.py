#!/usr/bin/env python3
# test_filter_simple.py
# 简单测试过滤功能

import pandas as pd
from backtest_after_filter import BacktestFilter

def test_basic_filtering():
    """测试基本过滤功能"""
    
    # 检查CSV文件是否存在
    csv_file = "backtest_money_log_quick.csv"
    try:
        # 创建过滤器
        filter_engine = BacktestFilter(csv_file)
        
        print(f"✅ 成功加载数据: {len(filter_engine.original_df)} 条记录")
        
        # 测试置信度过滤
        print("\n=== 测试置信度过滤 ===")
        confidence_filtered = filter_engine.apply_confidence_filter(min_confidence=0.7)
        print(f"置信度过滤结果: {len(confidence_filtered)} 条记录")
        
        # 测试时间过滤
        print("\n=== 测试时间过滤 ===")
        time_filtered = filter_engine.apply_time_filter(
            allowed_hours=[10, 11, 14, 15, 16, 20, 21]
        )
        print(f"时间过滤结果: {len(time_filtered)} 条记录")
        
        # 测试连续亏损过滤
        print("\n=== 测试连续亏损过滤 ===")
        consecutive_filtered = filter_engine.apply_consecutive_filter(max_consecutive_losses=2)
        print(f"连续亏损过滤结果: {len(consecutive_filtered)} 条记录")
        
        # 测试性能指标计算
        print("\n=== 测试性能指标计算 ===")
        initial_capital = 1000
        
        # 重新计算资金序列
        confidence_filtered = filter_engine.recalculate_capital_and_metrics(
            confidence_filtered, initial_capital
        )
        
        # 计算性能指标
        metrics = filter_engine.calculate_performance_metrics(
            confidence_filtered, initial_capital
        )
        
        print(f"总收益率: {metrics['total_return_pct']:.2f}%")
        print(f"胜率: {metrics['win_rate']:.2f}%")
        print(f"最大回撤: {metrics['max_drawdown_pct']:.2f}%")
        print(f"夏普比率: {metrics['sharpe_ratio']:.3f}")
        
        print("\n✅ 所有测试通过！")
        
    except FileNotFoundError:
        print(f"❌ 文件不存在: {csv_file}")
        print("请先运行 backtest_money_quick.py 生成回测结果")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_basic_filtering()