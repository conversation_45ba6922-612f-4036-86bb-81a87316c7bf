#!/usr/bin/env python3
# 测试挂单失败时资金不变的逻辑

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import pytz

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_failed_order_capital():
    """测试挂单失败时资金不变"""
    print("=== 测试挂单失败时资金变化 ===")
    
    # 模拟回测器的资金管理逻辑
    class MockBacktester:
        def __init__(self, initial_capital):
            self.current_capital = initial_capital
            self.config = {
                'up_threshold': 0.02,
                'down_threshold': 0.02
            }
        
        def calculate_score(self, prediction, start_price, end_price, result):
            """简化的得分计算"""
            if result == 1:  # 成功
                return 1.0
            elif result == 0:  # 失败
                return -1.0
            elif result == -1:  # 超时
                price_change_pct = (end_price - start_price) / start_price
                threshold = self.config.get('up_threshold', 0.01) if prediction == 1 else self.config.get('down_threshold', 0.01)
                score = (price_change_pct - 0.001) / threshold if prediction == 1 else (-price_change_pct + 0.001) / threshold
                return score
            else:
                return 0.0
        
        def complete_prediction_old(self, pred, result, final_price):
            """旧版本：所有情况都计算盈亏"""
            start_price = pred.get('start_price', pred.get('order_price', 0))
            score = self.calculate_score(pred['guess'], start_price, final_price, result)
            profit_loss = pred['trade_risk_capital'] * score
            self.current_capital += profit_loss
            return profit_loss, score
        
        def complete_prediction_new(self, pred, result, final_price):
            """新版本：挂单失败时不计算盈亏"""
            start_price = pred.get('start_price', pred.get('order_price', 0))
            
            # 挂单失败时不计算盈亏，因为没有实际交易发生
            if result == -4:  # 挂单失败
                score = 0.0
                profit_loss = 0.0
            else:
                score = self.calculate_score(pred['guess'], start_price, final_price, result)
                profit_loss = pred['trade_risk_capital'] * score
            
            self.current_capital += profit_loss
            return profit_loss, score
    
    # 测试数据
    initial_capital = 1000.0
    risk_capital = 20.0  # 2% 风险
    
    test_cases = [
        {
            'name': '挂单失败',
            'result': -4,
            'pred': {
                'guess': 1,
                'order_price': 2000.0,
                'trade_risk_capital': risk_capital
            },
            'final_price': 2010.0,
            'expected_capital_change': 0.0
        },
        {
            'name': '成功交易',
            'result': 1,
            'pred': {
                'guess': 1,
                'start_price': 2000.0,
                'trade_risk_capital': risk_capital
            },
            'final_price': 2040.0,
            'expected_capital_change': 20.0  # 1.0 * 20.0
        },
        {
            'name': '失败交易',
            'result': 0,
            'pred': {
                'guess': 1,
                'start_price': 2000.0,
                'trade_risk_capital': risk_capital
            },
            'final_price': 1960.0,
            'expected_capital_change': -20.0  # -1.0 * 20.0
        }
    ]
    
    print(f"初始资金: ${initial_capital}")
    print(f"单次风险资金: ${risk_capital}")
    print()
    
    for case in test_cases:
        print(f"测试案例: {case['name']}")
        
        # 测试旧版本
        backtester_old = MockBacktester(initial_capital)
        capital_before_old = backtester_old.current_capital
        profit_loss_old, score_old = backtester_old.complete_prediction_old(case['pred'], case['result'], case['final_price'])
        capital_change_old = backtester_old.current_capital - capital_before_old
        
        # 测试新版本
        backtester_new = MockBacktester(initial_capital)
        capital_before_new = backtester_new.current_capital
        profit_loss_new, score_new = backtester_new.complete_prediction_new(case['pred'], case['result'], case['final_price'])
        capital_change_new = backtester_new.current_capital - capital_before_new
        
        print(f"  旧版本 - 盈亏: ${profit_loss_old:+.2f}, 资金变化: ${capital_change_old:+.2f}, 得分: {score_old:+.2f}")
        print(f"  新版本 - 盈亏: ${profit_loss_new:+.2f}, 资金变化: ${capital_change_new:+.2f}, 得分: {score_new:+.2f}")
        print(f"  预期资金变化: ${case['expected_capital_change']:+.2f}")
        
        # 验证新版本是否正确
        if abs(capital_change_new - case['expected_capital_change']) < 0.01:
            print(f"  结果: ✅ 新版本正确")
        else:
            print(f"  结果: ❌ 新版本错误")
        
        # 特别检查挂单失败的情况
        if case['result'] == -4:
            if capital_change_old != 0:
                print(f"  ⚠️  旧版本问题: 挂单失败时资金不应该变化")
            if capital_change_new == 0:
                print(f"  ✅ 新版本修复: 挂单失败时资金正确保持不变")
        
        print()

if __name__ == "__main__":
    test_failed_order_capital()
    print("🎉 测试完成！")
