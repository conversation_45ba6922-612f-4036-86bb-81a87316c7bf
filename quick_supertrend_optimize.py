#!/usr/bin/env python3
"""
快速 SuperTrend 参数优化脚本
用于快速测试不同的 SuperTrend 参数组合
"""

import pandas as pd
import numpy as np
import json
import argparse
import os
from datetime import datetime
import itertools
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp

# 导入回测相关模块
from backtest_money_quick import (
    HistoricalBacktester, load_data_from_sqlite, load_supertrend_data,
    parse_time_input, load_chushou_config, get_supertrend_signal, is_good_time_to_trade
)

try:
    from model_utils_815 import get_coin_config, calculate_features
except ImportError:
    def get_coin_config(coin): 
        return {'model_basename': f"{coin.lower()}_model", 'api_symbol': coin.upper() + 'USDT'}
    def calculate_features(df, timeframe): 
        return df

def run_backtest_with_params(params):
    """运行单个参数组合的回测"""
    (coin, interval, market, db_path, model_file, config_file, 
     start_time, end_time, initial_capital, risk_per_trade, max_active_predictions,
     supertrend_interval, atr_period, multiplier, chushou_file) = params
    
    try:
        # 获取币种配置
        coin_config = get_coin_config(coin)
        api_symbol = coin_config['api_symbol']
        
        # 加载主要数据
        main_df = load_data_from_sqlite(db_path, api_symbol, interval, market, 1.0, start_time, end_time)
        if main_df is None or main_df.empty:
            return {'error': '无法加载主要数据'}
        
        # 加载 SuperTrend 数据
        supertrend_df = load_supertrend_data(
            db_path, api_symbol, supertrend_interval, market,
            start_time, end_time, atr_period, multiplier
        )
        if supertrend_df is None:
            return {'error': 'SuperTrend数据加载失败'}
        
        # 加载模型配置
        with open(config_file, 'r') as f:
            model_config = json.load(f)
        
        # 计算特征
        features_df = calculate_features(main_df, model_config['timeframe_minutes'])
        
        # 创建回测器
        backtester = HistoricalBacktester(
            model_file, config_file, initial_capital, risk_per_trade, 1.0, None,
            supertrend_df, True  # 启用 SuperTrend 过滤
        )
        
        # 加载时间过滤
        time_filter = load_chushou_config(chushou_file) if chushou_file else None
        
        # 找到有效的特征数据起始点
        valid_features_df = features_df.dropna(subset=model_config['feature_list'])
        if valid_features_df.empty:
            return {'error': '无有效特征数据'}
        
        first_valid_index_pos = main_df.index.get_loc(valid_features_df.index[0])
        
        # 运行回测
        for i in range(first_valid_index_pos, len(main_df)):
            current_timestamp = main_df.index[i]
            current_price = main_df.iloc[i]['close']
            
            # 检查现有预测
            backtester.check_predictions(current_price, current_timestamp, i)
            
            # 检查是否可以新增预测
            active_count = len([p for p in backtester.active_predictions.values() if p['status'] == 'active'])
            if active_count < max_active_predictions:
                if current_timestamp in features_df.index:
                    latest_features_series = features_df.loc[current_timestamp]
                    guess, probability, pred_price = backtester.make_prediction_from_features(latest_features_series)
                    
                    if guess is not None:
                        if is_good_time_to_trade(current_timestamp, time_filter):
                            supertrend_signal = get_supertrend_signal(current_timestamp, backtester.supertrend_df)
                            backtester.add_prediction(guess, probability, pred_price, current_timestamp, i, supertrend_signal)
        
        # 结束所有活跃预测
        final_timestamp, final_price, final_idx = main_df.index[-1], main_df.iloc[-1]['close'], len(main_df) - 1
        for pred_id in list(backtester.active_predictions.keys()):
            backtester.complete_prediction(pred_id, -1, final_price, final_timestamp, final_idx, "数据结束-超时")
        
        # 计算结果指标
        final_capital = backtester.current_capital
        total_return = (final_capital - initial_capital) / initial_capital
        win_rate = backtester.successful_predictions / max(backtester.total_predictions, 1)
        
        # 计算最大回撤
        if backtester.completed_predictions:
            capital_series = [initial_capital]
            for pred in backtester.completed_predictions:
                capital_series.append(pred['CapitalAfter'])
            
            capital_df = pd.Series(capital_series)
            rolling_max = capital_df.expanding().max()
            drawdown = (capital_df - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
        else:
            max_drawdown = 0.0
        
        # 计算更详细的性能指标
        sharpe_ratio = 0.0
        sortino_ratio = 0.0
        calmar_ratio = 0.0
        
        if backtester.completed_predictions:
            returns = [pred['Score'] for pred in backtester.completed_predictions]
            if len(returns) > 1:
                returns_array = np.array(returns)
                # 夏普比率
                sharpe_ratio = np.mean(returns_array) / (np.std(returns_array) + 1e-8)
                
                # Sortino比率
                downside_returns = returns_array[returns_array < 0]
                if len(downside_returns) > 0:
                    downside_std = np.std(downside_returns)
                    if downside_std > 0:
                        sortino_ratio = np.mean(returns_array) / downside_std
                    else:
                        sortino_ratio = float('inf') if np.mean(returns_array) > 0 else 0.0
                else:
                    sortino_ratio = float('inf') if np.mean(returns_array) > 0 else 0.0
                
                # Calmar比率
                if abs(max_drawdown) > 0:
                    calmar_ratio = total_return / abs(max_drawdown)
                else:
                    calmar_ratio = float('inf') if total_return > 0 else 0.0
        
        return {
            'supertrend_interval': supertrend_interval,
            'atr_period': atr_period,
            'multiplier': multiplier,
            'total_return': total_return,
            'final_capital': final_capital,
            'total_predictions': backtester.total_predictions,
            'successful_predictions': backtester.successful_predictions,
            'win_rate': win_rate,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'filtered_predictions': backtester.supertrend_filtered_predictions
        }
        
    except Exception as e:
        return {
            'supertrend_interval': supertrend_interval,
            'atr_period': atr_period,
            'multiplier': multiplier,
            'error': str(e)
        }

def quick_optimize_supertrend(coin="ETH", interval="5m", market="spot", db_path="coin_data.db",
                             model_file=None, config_file=None, start_time=None, end_time=None,
                             initial_capital=1000, risk_per_trade=2.0, max_active_predictions=5,
                             chushou_file=None, max_workers=None):
    """快速优化 SuperTrend 参数"""
    
    print(f"=== SuperTrend 快速参数优化 ===")
    print(f"币种: {coin}, 间隔: {interval}")
    
    # 获取币种配置
    coin_config = get_coin_config(coin)
    if model_file is None:
        model_file = f"models/{coin_config['model_basename']}_model.joblib"
    if config_file is None:
        config_file = f"models/{coin_config['model_basename']}_config.json"
    
    # 验证文件存在
    if not os.path.exists(model_file):
        raise FileNotFoundError(f"模型文件不存在: {model_file}")
    if not os.path.exists(config_file):
        raise FileNotFoundError(f"配置文件不存在: {config_file}")
    
    # 定义参数搜索空间
    supertrend_intervals = ['1h','4h']
    atr_periods = [7, 10, 14, 20]
    multipliers = [2.0, 2.5, 3.0, 3.5, 4.0]
    
    # 生成所有参数组合
    param_combinations = list(itertools.product(supertrend_intervals, atr_periods, multipliers))
    
    print(f"总参数组合数: {len(param_combinations)}")
    
    # 准备参数
    base_params = (coin, interval, market, db_path, model_file, config_file,
                   start_time, end_time, initial_capital, risk_per_trade, 
                   max_active_predictions, chushou_file)
    
    all_params = []
    for st_interval, atr_period, multiplier in param_combinations:
        params = base_params[:-1] + (st_interval, atr_period, multiplier, base_params[-1])
        all_params.append(params)
    
    # 运行并行回测
    results = []
    max_workers = max_workers or min(mp.cpu_count(), len(all_params))
    
    print(f"使用 {max_workers} 个进程并行计算...")
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_params = {executor.submit(run_backtest_with_params, params): params for params in all_params}
        
        # 收集结果
        completed = 0
        for future in as_completed(future_to_params):
            result = future.result()
            results.append(result)
            completed += 1
            
            if completed % 10 == 0 or completed == len(all_params):
                print(f"进度: {completed}/{len(all_params)} ({completed/len(all_params)*100:.1f}%)")
    
    # 过滤有效结果
    valid_results = [r for r in results if 'error' not in r and r['total_predictions'] > 0]
    
    if not valid_results:
        print("❌ 没有有效的结果")
        return None
    
    # 按不同指标排序
    print(f"\n=== 优化结果 (共 {len(valid_results)} 个有效结果) ===")
    
    # 1. 按总收益率排序
    by_return = sorted(valid_results, key=lambda x: x['total_return'], reverse=True)
    print(f"\n📈 按总收益率排序 (前5名):")
    for i, result in enumerate(by_return[:5], 1):
        print(f"  {i}. 间隔:{result['supertrend_interval']}, ATR:{result['atr_period']}, "
              f"倍数:{result['multiplier']:.1f} -> 收益:{result['total_return']:.2%}, "
              f"胜率:{result['win_rate']:.1%}, 预测数:{result['total_predictions']}")
    
    # 2. 按胜率排序
    by_winrate = sorted(valid_results, key=lambda x: x['win_rate'], reverse=True)
    print(f"\n🎯 按胜率排序 (前5名):")
    for i, result in enumerate(by_winrate[:5], 1):
        print(f"  {i}. 间隔:{result['supertrend_interval']}, ATR:{result['atr_period']}, "
              f"倍数:{result['multiplier']:.1f} -> 胜率:{result['win_rate']:.1%}, "
              f"收益:{result['total_return']:.2%}, 预测数:{result['total_predictions']}")
    
    # 3. 按最大回撤排序（越小越好）
    by_drawdown = sorted(valid_results, key=lambda x: x['max_drawdown'])
    print(f"\n📉 按最大回撤排序 (前5名):")
    for i, result in enumerate(by_drawdown[:5], 1):
        print(f"  {i}. 间隔:{result['supertrend_interval']}, ATR:{result['atr_period']}, "
              f"倍数:{result['multiplier']:.1f} -> 回撤:{result['max_drawdown']:.2%}, "
              f"收益:{result['total_return']:.2%}, 胜率:{result['win_rate']:.1%}")
    
    # 4. 按Sortino比率排序
    by_sortino = sorted(valid_results, key=lambda x: x.get('sortino_ratio', 0), reverse=True)
    print(f"\n📊 按Sortino比率排序 (前5名):")
    for i, result in enumerate(by_sortino[:5], 1):
        sortino_str = f"{result.get('sortino_ratio', 0):.3f}" if result.get('sortino_ratio', 0) != float('inf') else "∞"
        print(f"  {i}. 间隔:{result['supertrend_interval']}, ATR:{result['atr_period']}, "
              f"倍数:{result['multiplier']:.1f} -> Sortino:{sortino_str}, "
              f"收益:{result['total_return']:.2%}, 胜率:{result['win_rate']:.1%}")
    
    # 5. 按Calmar比率排序
    by_calmar = sorted(valid_results, key=lambda x: x.get('calmar_ratio', 0), reverse=True)
    print(f"\n📈 按Calmar比率排序 (前5名):")
    for i, result in enumerate(by_calmar[:5], 1):
        calmar_str = f"{result.get('calmar_ratio', 0):.3f}" if result.get('calmar_ratio', 0) != float('inf') else "∞"
        print(f"  {i}. 间隔:{result['supertrend_interval']}, ATR:{result['atr_period']}, "
              f"倍数:{result['multiplier']:.1f} -> Calmar:{calmar_str}, "
              f"收益:{result['total_return']:.2%}, 回撤:{result['max_drawdown']:.2%}")
    
    # 6. 综合评分
    for result in valid_results:
        # 处理无穷大值
        sortino_ratio = min(result.get('sortino_ratio', 0), 10.0) if result.get('sortino_ratio', 0) != float('inf') else 10.0
        calmar_ratio = min(result.get('calmar_ratio', 0), 10.0) if result.get('calmar_ratio', 0) != float('inf') else 10.0
        
        # 综合评分公式（可调整权重）
        score = (
            result['total_return'] * 0.3 +
            result['win_rate'] * 0.2 +
            (-result['max_drawdown']) * 0.2 +
            result.get('sharpe_ratio', 0) * 0.1 +
            sortino_ratio * 0.1 +
            calmar_ratio * 0.1
        )
        result['composite_score'] = score
    
    by_composite = sorted(valid_results, key=lambda x: x['composite_score'], reverse=True)
    print(f"\n🏆 综合评分排序 (前5名):")
    for i, result in enumerate(by_composite[:5], 1):
        print(f"  {i}. 间隔:{result['supertrend_interval']}, ATR:{result['atr_period']}, "
              f"倍数:{result['multiplier']:.1f} -> 评分:{result['composite_score']:.3f}, "
              f"收益:{result['total_return']:.2%}, 胜率:{result['win_rate']:.1%}")
    
    # 保存结果
    results_df = pd.DataFrame(valid_results)
    output_file = f"supertrend_quick_optimization_{coin}_{interval}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    results_df.to_csv(output_file, index=False)
    print(f"\n📊 详细结果已保存到: {output_file}")
    
    # 返回最佳结果
    best_result = by_composite[0]
    
    print(f"\n💡 推荐参数:")
    print(f"python backtest_money_quick.py \\")
    print(f"    --coin {coin} --interval {interval} \\")
    print(f"    --use-supertrend \\")
    print(f"    --supertrend-interval {best_result['supertrend_interval']} \\")
    print(f"    --supertrend-atr-period {best_result['atr_period']} \\")
    print(f"    --supertrend-multiplier {best_result['multiplier']:.1f} \\")
    print(f"    --quick")
    
    return best_result

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SuperTrend 快速参数优化")
    
    parser.add_argument("--coin", default="ETH", help="交易对，例如 BTC")
    parser.add_argument("--interval", default="5m", help="K线间隔，例如 5m")
    parser.add_argument("--market", default="spot", choices=["spot", "futures"], help="市场类型")
    parser.add_argument("--db", default="coin_data.db", help="SQLite数据库路径")
    parser.add_argument("--start-time", help="开始时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--end-time", help="结束时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--initial-capital", type=float, default=1000, help="初始资金")
    parser.add_argument("--risk-per-trade", type=float, default=2.0, help="单次交易风险比例(%)")
    parser.add_argument("--max-active-predictions", type=int, default=5, help="最大同时活跃预测数")
    parser.add_argument("--model-file", help="模型文件路径 (.joblib)")
    parser.add_argument("--config-file", help="配置文件路径 (.json)")
    parser.add_argument("--chushou-file", help="出手时间配置文件路径")
    parser.add_argument("--max-workers", type=int, help="最大并行进程数")
    
    args = parser.parse_args()
    
    try:
        # 解析时间
        start_time = parse_time_input(args.start_time) if args.start_time else None
        end_time = parse_time_input(args.end_time) if args.end_time else None
        
        # 运行优化
        best_result = quick_optimize_supertrend(
            coin=args.coin,
            interval=args.interval,
            market=args.market,
            db_path=args.db,
            model_file=args.model_file,
            config_file=args.config_file,
            start_time=start_time,
            end_time=end_time,
            initial_capital=args.initial_capital,
            risk_per_trade=args.risk_per_trade,
            max_active_predictions=args.max_active_predictions,
            chushou_file=args.chushou_file,
            max_workers=args.max_workers
        )
        
        if best_result:
            print(f"\n✅ 优化完成！最佳综合评分: {best_result['composite_score']:.3f}")
        
    except Exception as e:
        print(f"❌ 优化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()