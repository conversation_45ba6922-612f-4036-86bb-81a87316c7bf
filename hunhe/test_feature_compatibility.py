#!/usr/bin/env python3
"""
特征兼容性测试

验证集成系统生成的特征与现有模型期望的特征完全一致
测试模型加载器能够正确使用现有模型进行预测
确保特征名称、数量和计算方法与现有系统完全兼容
"""

import sys
import os
import pandas as pd
import numpy as np
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from hunhe.model_loader import ModelLoader
from hunhe.feature_generator import FeatureGenerator
from hunhe.ensemble_config import EnsembleConfig, FeatureSelectionConfig
from hunhe.utils import setup_logging

# 导入现有的特征计算函数进行对比
from oneway.standalone_utils import calculate_features as calculate_standalone_features
from model_utils_815 import calculate_features as calculate_money_features, get_feature_list

logger = setup_logging()

def create_test_data(n_rows=1000):
    """创建测试数据"""
    logger.info(f"创建测试数据，行数: {n_rows}")
    
    # 创建时间索引
    start_time = pd.Timestamp('2024-01-01')
    time_index = pd.date_range(start=start_time, periods=n_rows, freq='5T')
    
    # 生成模拟的OHLCV数据
    np.random.seed(42)  # 确保可重复性
    
    # 生成价格数据（随机游走）
    base_price = 2500.0
    price_changes = np.random.normal(0, 0.001, n_rows)  # 0.1%的标准差
    prices = base_price * np.exp(np.cumsum(price_changes))
    
    # 生成OHLCV数据
    data = []
    for i, price in enumerate(prices):
        # 生成高低价
        high_offset = np.random.uniform(0, 0.005)  # 最高0.5%
        low_offset = np.random.uniform(0, 0.005)   # 最低0.5%
        
        high = price * (1 + high_offset)
        low = price * (1 - low_offset)
        
        # 开盘价和收盘价在高低价之间
        open_price = np.random.uniform(low, high)
        close_price = np.random.uniform(low, high)
        
        # 成交量
        volume = np.random.uniform(100, 1000)
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data, index=time_index)
    logger.info(f"测试数据创建完成，价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    return df

def test_feature_calculation_consistency():
    """测试特征计算的一致性"""
    logger.info("=" * 60)
    logger.info("测试特征计算一致性")
    logger.info("=" * 60)
    
    # 创建测试数据
    test_data = create_test_data(500)  # 使用较少数据以加快测试
    timeframe = 5
    
    # 1. 使用原始函数计算特征
    logger.info("1. 使用原始money management特征计算函数")
    original_features = calculate_money_features(test_data.copy(), timeframe)
    original_feature_list = get_feature_list(original_features, timeframe)
    
    logger.info(f"原始特征数量: {len(original_feature_list)}")
    logger.info(f"原始特征列表: {original_feature_list[:10]}...")  # 显示前10个
    
    # 2. 创建模拟的集成配置（不加载实际模型）
    config = EnsembleConfig(
        name="test_ensemble",
        timeframe_minutes=timeframe,
        models={},  # 空模型配置用于测试
        feature_selection=FeatureSelectionConfig(
            use_traditional_features=True,
            traditional_feature_count=50,
            use_model_probabilities=True,
            use_model_predictions=True
        )
    )
    
    # 3. 创建模拟的模型加载器（不加载实际模型）
    model_loader = ModelLoader()
    
    # 4. 使用FeatureGenerator计算特征
    logger.info("2. 使用FeatureGenerator计算特征")
    feature_generator = FeatureGenerator(model_loader, config)
    
    try:
        generated_features = feature_generator.calculate_base_features(test_data.copy(), timeframe)
        logger.info(f"生成的特征数量: {len(generated_features.columns)}")
        
        # 5. 比较特征
        logger.info("3. 比较特征一致性")
        
        # 检查共同特征
        original_cols = set(original_features.columns)
        generated_cols = set(generated_features.columns)
        
        common_features = original_cols.intersection(generated_cols)
        missing_in_generated = original_cols - generated_cols
        extra_in_generated = generated_cols - original_cols
        
        logger.info(f"共同特征数量: {len(common_features)}")
        logger.info(f"生成器中缺失的特征: {len(missing_in_generated)}")
        logger.info(f"生成器中额外的特征: {len(extra_in_generated)}")
        
        if missing_in_generated:
            logger.warning(f"缺失特征: {list(missing_in_generated)[:10]}")
        
        if extra_in_generated:
            logger.info(f"额外特征: {list(extra_in_generated)[:10]}")
        
        # 6. 检查数值一致性（对于共同特征）
        logger.info("4. 检查数值一致性")
        numerical_differences = []
        
        for feature in list(common_features)[:10]:  # 检查前10个共同特征
            if feature in original_feature_list:  # 只检查重要特征
                orig_values = original_features[feature].dropna()
                gen_values = generated_features[feature].dropna()
                
                if len(orig_values) > 0 and len(gen_values) > 0:
                    # 对齐索引
                    common_idx = orig_values.index.intersection(gen_values.index)
                    if len(common_idx) > 0:
                        orig_aligned = orig_values.loc[common_idx]
                        gen_aligned = gen_values.loc[common_idx]
                        
                        # 计算差异
                        diff = np.abs(orig_aligned - gen_aligned).mean()
                        max_diff = np.abs(orig_aligned - gen_aligned).max()
                        
                        numerical_differences.append({
                            'feature': feature,
                            'mean_diff': diff,
                            'max_diff': max_diff,
                            'samples': len(common_idx)
                        })
        
        # 显示数值差异结果
        if numerical_differences:
            logger.info("数值差异分析:")
            for diff_info in numerical_differences:
                logger.info(f"  {diff_info['feature']}: 平均差异={diff_info['mean_diff']:.6f}, "
                          f"最大差异={diff_info['max_diff']:.6f}, 样本数={diff_info['samples']}")
        
        # 7. 总结
        logger.info("5. 特征计算一致性测试总结")
        success = len(missing_in_generated) == 0 and len(numerical_differences) > 0
        
        if success:
            logger.info("✅ 特征计算一致性测试通过")
        else:
            logger.warning("⚠️  特征计算存在差异，需要进一步检查")
        
        return success, {
            'common_features': len(common_features),
            'missing_features': len(missing_in_generated),
            'extra_features': len(extra_in_generated),
            'numerical_differences': numerical_differences
        }
        
    except Exception as e:
        logger.error(f"特征计算测试失败: {str(e)}")
        return False, {'error': str(e)}

def test_model_loading_and_prediction():
    """测试模型加载和预测功能"""
    logger.info("=" * 60)
    logger.info("测试模型加载和预测功能")
    logger.info("=" * 60)
    
    # 检查模型文件是否存在
    model_files = {
        'standalone': {
            'model': 'models/standalone_eth_5m_model.joblib',
            'config': 'models/standalone_eth_5m_config.json'
        },
        'money': {
            'model': 'models/eth_5m_model.joblib',
            'config': 'models/eth_5m_config.json'
        }
    }
    
    available_models = {}
    for model_type, paths in model_files.items():
        model_path = Path(paths['model'])
        config_path = Path(paths['config'])
        
        if model_path.exists() and config_path.exists():
            available_models[model_type] = paths
            logger.info(f"✅ {model_type} 模型文件存在")
        else:
            logger.warning(f"⚠️  {model_type} 模型文件不存在: {paths}")
    
    if not available_models:
        logger.warning("没有可用的模型文件，跳过模型预测测试")
        return True, {'message': 'No models available for testing'}
    
    # 创建测试数据
    test_data = create_test_data(100)  # 较少数据用于预测测试
    
    try:
        # 1. 测试模型加载
        logger.info("1. 测试模型加载")
        model_loader = ModelLoader()
        
        loaded_models = []
        for model_type, paths in available_models.items():
            try:
                if model_type == 'standalone':
                    model_loader.load_standalone_model(paths['model'], paths['config'])
                    loaded_models.append('standalone')
                    logger.info(f"✅ {model_type} 模型加载成功")
                elif model_type == 'money':
                    model_loader.load_money_model(paths['model'], paths['config'])
                    loaded_models.append('money')
                    logger.info(f"✅ {model_type} 模型加载成功")
            except Exception as e:
                logger.error(f"❌ {model_type} 模型加载失败: {str(e)}")
        
        if not loaded_models:
            logger.error("没有成功加载任何模型")
            return False, {'error': 'No models loaded successfully'}
        
        # 2. 准备特征数据
        logger.info("2. 准备特征数据")
        
        # 使用完整的特征计算（包含两种模型需要的所有特征）
        features_df = calculate_money_features(test_data.copy(), 5)
        
        # 添加standalone模型需要的额外特征
        try:
            standalone_features = calculate_standalone_features(test_data.copy(), 5)
            
            # 合并standalone特有的特征
            for col in standalone_features.columns:
                if col not in features_df.columns and col not in ['open', 'high', 'low', 'close', 'volume', 'timestamp', 'Timestamp']:
                    features_df[col] = standalone_features[col]
                    
        except Exception as e:
            logger.warning(f"添加standalone特征时出错: {str(e)}")
        
        logger.info(f"特征数据准备完成，特征数: {len(features_df.columns)}, 数据行数: {len(features_df)}")
        
        # 3. 测试模型预测
        logger.info("3. 测试模型预测")
        prediction_results = {}
        
        for model_type in loaded_models:
            try:
                if model_type == 'standalone':
                    predictions = model_loader.predict_standalone(features_df)
                    prediction_results['standalone'] = predictions
                    logger.info(f"✅ Standalone 模型预测成功，输出形状: {predictions.shape}")
                    logger.info(f"   预测列: {list(predictions.columns)}")
                    
                elif model_type == 'money':
                    predictions = model_loader.predict_money(features_df)
                    prediction_results['money'] = predictions
                    logger.info(f"✅ Money 模型预测成功，输出形状: {predictions.shape}")
                    logger.info(f"   预测列: {list(predictions.columns)}")
                    
            except Exception as e:
                logger.error(f"❌ {model_type} 模型预测失败: {str(e)}")
        
        # 4. 验证预测结果格式
        logger.info("4. 验证预测结果格式")
        format_check = True
        
        if 'standalone' in prediction_results:
            standalone_pred = prediction_results['standalone']
            if isinstance(standalone_pred, pd.DataFrame) and len(standalone_pred.columns) >= 3:
                logger.info("✅ Standalone 预测格式正确（至少3列）")
            else:
                logger.error("❌ Standalone 预测格式不正确")
                format_check = False
        
        if 'money' in prediction_results:
            money_pred = prediction_results['money']
            if isinstance(money_pred, pd.DataFrame) and len(money_pred.columns) >= 2:
                logger.info("✅ Money 预测格式正确（至少2列）")
            else:
                logger.error("❌ Money 预测格式不正确")
                format_check = False
        
        # 5. 测试特征生成器的模型特征生成
        logger.info("5. 测试FeatureGenerator的模型特征生成")
        
        config = EnsembleConfig(
            name="test_ensemble",
            timeframe_minutes=5,
            models={},
            feature_selection=FeatureSelectionConfig(
                use_traditional_features=True,
                traditional_feature_count=50,
                use_model_probabilities=True,
                use_model_predictions=True
            )
        )
        feature_generator = FeatureGenerator(model_loader, config)
        
        try:
            model_features = feature_generator.generate_model_features(features_df)
            logger.info(f"✅ 模型特征生成成功，特征数: {len(model_features.columns)}")
            logger.info(f"   模型特征列: {list(model_features.columns)}")
            
            # 检查期望的特征列
            expected_features = []
            if 'standalone' in loaded_models:
                expected_features.extend(['standalone_prob_lowest', 'standalone_prob_neutral', 
                                        'standalone_prob_highest', 'standalone_prediction'])
            if 'money' in loaded_models:
                expected_features.extend(['money_prob_down', 'money_prob_up', 'money_prediction'])
            
            missing_features = set(expected_features) - set(model_features.columns)
            if missing_features:
                logger.warning(f"缺失的期望特征: {missing_features}")
            else:
                logger.info("✅ 所有期望的模型特征都已生成")
            
        except Exception as e:
            logger.error(f"❌ 模型特征生成失败: {str(e)}")
            format_check = False
        
        # 6. 总结
        logger.info("6. 模型加载和预测测试总结")
        success = len(loaded_models) > 0 and format_check
        
        if success:
            logger.info("✅ 模型加载和预测测试通过")
        else:
            logger.warning("⚠️  模型加载和预测测试存在问题")
        
        return success, {
            'loaded_models': loaded_models,
            'prediction_results': {k: v.shape for k, v in prediction_results.items()},
            'format_check': format_check
        }
        
    except Exception as e:
        logger.error(f"模型测试失败: {str(e)}")
        return False, {'error': str(e)}

def test_end_to_end_feature_generation():
    """端到端特征生成测试"""
    logger.info("=" * 60)
    logger.info("端到端特征生成测试")
    logger.info("=" * 60)
    
    try:
        # 创建测试数据
        test_data = create_test_data(200)
        
        # 创建配置
        from hunhe.ensemble_config import ModelConfig, FeatureSelectionConfig
        
        config = EnsembleConfig(
            name="test_ensemble",
            timeframe_minutes=5,
            models={
                "standalone": ModelConfig(
                    model_path="models/standalone_eth_5m_model.joblib",
                    config_path="models/standalone_eth_5m_config.json",
                    model_type="standalone"
                ),
                "money": ModelConfig(
                    model_path="models/eth_5m_model.joblib",
                    config_path="models/eth_5m_config.json",
                    model_type="money"
                )
            },
            feature_selection=FeatureSelectionConfig(
                use_traditional_features=True,
                traditional_feature_count=50,
                use_model_probabilities=True,
                use_model_predictions=True
            )
        )
        
        # 创建模型加载器并尝试加载模型
        model_loader = ModelLoader()
        
        # 尝试加载可用的模型
        loaded_any = False
        for model_type, model_config in config.models.items():
            model_path = Path(model_config.model_path)
            config_path = Path(model_config.config_path)
            
            if model_path.exists() and config_path.exists():
                try:
                    if model_type == 'standalone':
                        model_loader.load_standalone_model(str(model_path), str(config_path))
                    elif model_type == 'money':
                        model_loader.load_money_model(str(model_path), str(config_path))
                    loaded_any = True
                    logger.info(f"✅ 加载 {model_type} 模型成功")
                except Exception as e:
                    logger.warning(f"⚠️  加载 {model_type} 模型失败: {str(e)}")
        
        # 创建特征生成器
        feature_generator = FeatureGenerator(model_loader, config)
        
        # 执行完整的特征生成流程
        logger.info("执行完整特征生成流程")
        all_features = feature_generator.generate_all_features(test_data, 5)
        
        logger.info(f"✅ 端到端特征生成成功")
        logger.info(f"   最终特征数: {len(all_features.columns)}")
        logger.info(f"   数据行数: {len(all_features)}")
        
        # 分析特征类型
        feature_cols = list(all_features.columns)
        base_features = [col for col in feature_cols if not col.startswith(('standalone_', 'money_'))]
        model_features = [col for col in feature_cols if col.startswith(('standalone_', 'money_'))]
        
        logger.info(f"   基础特征数: {len(base_features)}")
        logger.info(f"   模型特征数: {len(model_features)}")
        
        if model_features:
            logger.info(f"   模型特征: {model_features}")
        
        return True, {
            'total_features': len(all_features.columns),
            'base_features': len(base_features),
            'model_features': len(model_features),
            'data_rows': len(all_features)
        }
        
    except Exception as e:
        logger.error(f"端到端测试失败: {str(e)}")
        return False, {'error': str(e)}

def main():
    """主测试函数"""
    logger.info("开始特征兼容性测试")
    logger.info("=" * 80)
    
    test_results = {}
    
    # 1. 测试特征计算一致性
    try:
        success, result = test_feature_calculation_consistency()
        test_results['feature_consistency'] = {'success': success, 'result': result}
    except Exception as e:
        logger.error(f"特征一致性测试异常: {str(e)}")
        test_results['feature_consistency'] = {'success': False, 'error': str(e)}
    
    # 2. 测试模型加载和预测
    try:
        success, result = test_model_loading_and_prediction()
        test_results['model_prediction'] = {'success': success, 'result': result}
    except Exception as e:
        logger.error(f"模型预测测试异常: {str(e)}")
        test_results['model_prediction'] = {'success': False, 'error': str(e)}
    
    # 3. 端到端测试
    try:
        success, result = test_end_to_end_feature_generation()
        test_results['end_to_end'] = {'success': success, 'result': result}
    except Exception as e:
        logger.error(f"端到端测试异常: {str(e)}")
        test_results['end_to_end'] = {'success': False, 'error': str(e)}
    
    # 总结测试结果
    logger.info("=" * 80)
    logger.info("测试结果总结")
    logger.info("=" * 80)
    
    all_passed = True
    for test_name, test_result in test_results.items():
        status = "✅ 通过" if test_result['success'] else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if not test_result['success']:
            all_passed = False
            if 'error' in test_result:
                logger.error(f"  错误: {test_result['error']}")
    
    if all_passed:
        logger.info("🎉 所有测试通过！特征兼容性验证成功")
    else:
        logger.warning("⚠️  部分测试失败，需要进一步检查")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)