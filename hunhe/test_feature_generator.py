#!/usr/bin/env python3
"""
特征生成器测试脚本

测试 FeatureGenerator 类的基本功能
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hunhe.feature_generator import FeatureGenerator, FeatureGenerationError
from hunhe.model_loader import ModelLoader
from hunhe.ensemble_config import EnsembleConfig

def create_sample_data(n_rows=1000):
    """创建示例K线数据"""
    np.random.seed(42)
    
    # 生成时间索引
    dates = pd.date_range('2024-01-01', periods=n_rows, freq='15min')
    
    # 生成价格数据
    base_price = 100
    price_changes = np.random.normal(0, 0.01, n_rows)
    prices = base_price * np.exp(np.cumsum(price_changes))
    
    # 生成OHLCV数据
    data = {
        'open': prices,
        'high': prices * (1 + np.abs(np.random.normal(0, 0.005, n_rows))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.005, n_rows))),
        'close': prices * (1 + np.random.normal(0, 0.002, n_rows)),
        'volume': np.random.lognormal(10, 1, n_rows)
    }
    
    df = pd.DataFrame(data, index=dates)
    return df

def create_test_config():
    """创建测试配置"""
    from hunhe.ensemble_config import FeatureSelectionConfig, TrainingConfig
    
    feature_config = FeatureSelectionConfig(
        use_traditional_features=True,
        traditional_feature_count=100,
        use_model_probabilities=False,
        use_model_predictions=False
    )
    
    training_config = TrainingConfig(
        test_size=0.2,
        validation_size=0.1,
        random_state=42,
        cv_folds=5,
        early_stopping_rounds=100
    )
    
    config = EnsembleConfig(
        name="test_ensemble",
        timeframe_minutes=15,
        up_threshold=0.02,
        down_threshold=-0.02,
        max_lookforward_minutes=60,
        models={},
        feature_selection=feature_config,
        training=training_config
    )
    
    return config

def test_base_features():
    """测试基础特征计算"""
    print("测试基础特征计算...")
    
    # 创建配置和模型加载器
    config = create_test_config()
    model_loader = ModelLoader()
    
    # 创建特征生成器
    feature_gen = FeatureGenerator(model_loader, config)
    
    # 创建示例数据
    df = create_sample_data(500)
    
    try:
        # 计算基础特征
        base_features = feature_gen.calculate_base_features(df, timeframe_minutes=15)
        
        print(f"✓ 基础特征计算成功")
        print(f"  - 输入数据: {len(df)} 行 {len(df.columns)} 列")
        print(f"  - 输出特征: {len(base_features)} 行 {len(base_features.columns)} 列")
        print(f"  - 新增特征: {len(base_features.columns) - len(df.columns)} 个")
        
        # 验证基础特征
        is_valid, issues = feature_gen.validate_base_features(base_features)
        if is_valid:
            print("✓ 基础特征验证通过")
        else:
            print(f"⚠ 基础特征验证发现问题: {issues}")
        
        return base_features
        
    except Exception as e:
        print(f"✗ 基础特征计算失败: {str(e)}")
        return None

def test_feature_selection():
    """测试特征选择"""
    print("\n测试特征选择...")
    
    # 创建配置和模型加载器
    config = create_test_config()
    model_loader = ModelLoader()
    
    # 创建特征生成器
    feature_gen = FeatureGenerator(model_loader, config)
    
    # 创建示例数据
    df = create_sample_data(500)
    
    try:
        # 计算基础特征
        base_features = feature_gen.calculate_base_features(df, timeframe_minutes=15)
        
        # 选择特征 (简化版本中暂不实现)
        selected_features = base_features
        
        print(f"✓ 特征选择成功")
        print(f"  - 原始特征: {len(base_features.columns)} 列")
        print(f"  - 选择后特征: {len(selected_features.columns)} 列")
        
        return selected_features
        
    except Exception as e:
        print(f"✗ 特征选择失败: {str(e)}")
        return None

def test_model_features():
    """测试模型特征生成（无模型情况）"""
    print("\n测试模型特征生成...")
    
    # 创建配置和模型加载器
    config = create_test_config()
    model_loader = ModelLoader()
    
    # 创建特征生成器
    feature_gen = FeatureGenerator(model_loader, config)
    
    # 创建示例数据
    df = create_sample_data(500)
    
    try:
        # 计算基础特征
        base_features = feature_gen.calculate_base_features(df, timeframe_minutes=15)
        
        # 尝试生成模型特征（应该返回空DataFrame，因为没有加载模型）
        model_features = feature_gen.generate_model_features(base_features)
        
        print(f"✓ 模型特征生成完成（无模型）")
        print(f"  - 模型特征: {len(model_features)} 行 {len(model_features.columns)} 列")
        
        return model_features
        
    except FeatureGenerationError as e:
        if "没有加载任何模型" in str(e):
            print("✓ 正确检测到没有加载模型")
            return pd.DataFrame(index=df.index)
        else:
            print(f"✗ 模型特征生成失败: {str(e)}")
            return None
    except Exception as e:
        print(f"✗ 模型特征生成出错: {str(e)}")
        return None

def test_feature_combination():
    """测试特征合并"""
    print("\n测试特征合并...")
    
    # 创建配置和模型加载器
    config = create_test_config()
    model_loader = ModelLoader()
    
    # 创建特征生成器
    feature_gen = FeatureGenerator(model_loader, config)
    
    # 创建示例数据
    df = create_sample_data(500)
    
    try:
        # 计算基础特征
        base_features = feature_gen.calculate_base_features(df, timeframe_minutes=15)
        
        # 创建空的模型特征（模拟没有模型的情况）
        model_features = pd.DataFrame(index=base_features.index)
        
        # 合并特征
        combined_features = feature_gen.combine_features(base_features, model_features)
        
        print(f"✓ 特征合并成功")
        print(f"  - 基础特征: {len(base_features.columns)} 列")
        print(f"  - 模型特征: {len(model_features.columns)} 列")
        print(f"  - 合并后特征: {len(combined_features.columns)} 列")
        
        return combined_features
        
    except Exception as e:
        print(f"✗ 特征合并失败: {str(e)}")
        return None

def test_feature_preprocessing():
    """测试特征预处理"""
    print("\n测试特征预处理...")
    
    # 创建配置和模型加载器
    config = create_test_config()
    model_loader = ModelLoader()
    
    # 创建特征生成器
    feature_gen = FeatureGenerator(model_loader, config)
    
    # 创建示例数据
    df = create_sample_data(500)
    
    try:
        # 计算基础特征
        base_features = feature_gen.calculate_base_features(df, timeframe_minutes=15)
        
        # 预处理特征 (简化版本中暂不实现)
        processed_features = base_features
        
        print(f"✓ 特征预处理成功")
        print(f"  - 输入特征: {len(base_features)} 行 {len(base_features.columns)} 列")
        print(f"  - 输出特征: {len(processed_features)} 行 {len(processed_features.columns)} 列")
        
        # 检查是否有缺失值和无穷值
        null_count = processed_features.isnull().sum().sum()
        inf_count = np.isinf(processed_features.select_dtypes(include=[np.number])).sum().sum()
        
        print(f"  - 缺失值: {null_count}")
        print(f"  - 无穷值: {inf_count}")
        
        return processed_features
        
    except Exception as e:
        print(f"✗ 特征预处理失败: {str(e)}")
        return None

def test_complete_pipeline():
    """测试完整特征生成流程"""
    print("\n测试完整特征生成流程...")
    
    # 创建配置和模型加载器
    config = create_test_config()
    model_loader = ModelLoader()
    
    # 创建特征生成器
    feature_gen = FeatureGenerator(model_loader, config)
    
    # 创建示例数据
    df = create_sample_data(500)
    
    try:
        # 运行完整流程
        final_features = feature_gen.generate_all_features(df, timeframe_minutes=15)
        
        print(f"✓ 完整特征生成流程成功")
        print(f"  - 输入数据: {len(df)} 行 {len(df.columns)} 列")
        print(f"  - 最终特征: {len(final_features)} 行 {len(final_features.columns)} 列")
        
        # 获取特征生成器信息
        info = feature_gen.get_feature_info()
        print(f"  - 缓存特征数: {info['cached_features']}")
        print(f"  - 已加载模型: {info['loaded_models']}")
        
        return final_features
        
    except Exception as e:
        print(f"✗ 完整特征生成流程失败: {str(e)}")
        return None

def main():
    """主测试函数"""
    print("开始测试特征生成器...")
    print("=" * 50)
    
    # 运行各项测试
    base_features = test_base_features()
    if base_features is not None:
        selected_features = test_feature_selection()
        model_features = test_model_features()
        combined_features = test_feature_combination()
        processed_features = test_feature_preprocessing()
        final_features = test_complete_pipeline()
    
    print("\n" + "=" * 50)
    print("特征生成器测试完成!")

if __name__ == "__main__":
    main()