"""
混合模型训练系统 (Ensemble Model Training System)

这个包实现了一个集成模型训练系统，用于合并多个现有模型的预测来创建更强的混合模型。
"""

__version__ = "1.0.0"
__author__ = "Trading System"

from .ensemble_trainer import EnsembleTrainer
from .model_loader import ModelLoader
from .feature_generator import FeatureGenerator
from .ensemble_config import EnsembleConfig

__all__ = [
    "EnsembleTrainer",
    "ModelLoader", 
    "FeatureGenerator",
    "EnsembleConfig"
]