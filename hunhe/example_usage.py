#!/usr/bin/env python3
"""
集成模型训练使用示例

这个脚本展示了如何使用集成模型训练系统的不同功能，包括：
1. 基本训练示例
2. 高级配置示例
3. 与回测系统集成示例
4. 性能分析示例
"""

import subprocess
import sys
import os
import json
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_command(cmd, description, check_output=False):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"示例: {description}")
    print(f"命令: {cmd}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 成功执行")
            if result.stdout and not check_output:
                print("输出:")
                print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
            return result.stdout if check_output else True
        else:
            print("❌ 执行失败")
            if result.stderr:
                print("错误:")
                print(result.stderr[:500] + "..." if len(result.stderr) > 500 else result.stderr)
            return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def create_example_config():
    """创建示例配置文件"""
    config = {
        "ensemble_config": {
            "name": "example_ensemble",
            "timeframe_minutes": 5,
            "up_threshold": 0.02,
            "down_threshold": -0.02,
            "max_lookforward_minutes": 240,
            "models": {
                "standalone": {
                    "model_path": "models/standalone_eth_5m_model.joblib",
                    "config_path": "models/standalone_eth_5m_config.json",
                    "model_type": "standalone"
                },
                "money": {
                    "model_path": "models/eth_5m_model.joblib",
                    "config_path": "models/eth_5m_config.json",
                    "model_type": "money"
                }
            },
            "feature_selection": {
                "use_traditional_features": True,
                "traditional_feature_count": 20,
                "use_model_probabilities": True,
                "use_model_predictions": True
            },
            "training": {
                "mode": "full",
                "optimize_hyperparameters": True,
                "n_trials": 50,
                "cv_folds": 5,
                "test_size": 0.2,
                "validation_size": 0.2,
                "random_state": 42,
                "early_stopping_rounds": 100
            },
            "data_source": {
                "database_path": "coin_data.db",
                "symbol": "ETHUSDT",
                "timeframe_minutes": 5,
                "table_name": "ethusdt_5min_spot"
            },
            "time_range": {
                "train_days": 30
            },
            "output": {
                "output_dir": "models",
                "save_features": True,
                "save_analysis": True
            }
        }
    }
    
    config_path = "hunhe/example_config.json"
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ 创建示例配置文件: {config_path}")
    return config_path

def demonstrate_basic_usage():
    """演示基本使用方法"""
    print("\n" + "="*80)
    print("1. 基本使用示例")
    print("="*80)
    
    # 确保在正确的目录
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    # 示例1: 显示帮助信息
    run_command(
        "python hunhe/main.py --help",
        "显示命令行帮助信息"
    )
    
    # 示例2: 创建并使用配置文件
    config_path = create_example_config()
    
    # 示例3: 检查模型文件是否存在
    standalone_model = "models/standalone_eth_5m_model.joblib"
    money_model = "models/eth_5m_model.joblib"
    
    if os.path.exists(standalone_model) and os.path.exists(money_model):
        print(f"\n✅ 找到必要的模型文件:")
        print(f"  - Standalone模型: {standalone_model}")
        print(f"  - Money模型: {money_model}")
        
        # 使用配置文件训练
        run_command(
            f"python hunhe/main.py --config {config_path} --quiet",
            "使用配置文件进行训练（静默模式）"
        )
        
        # 使用命令行参数训练
        run_command(
            f"python hunhe/main.py --name cli_ensemble --timeframe 5 "
            f"--standalone-model {standalone_model} "
            f"--money-model {money_model} "
            f"--mode train --train-days 7 --quiet",
            "使用命令行参数进行训练"
        )
    else:
        print(f"\n⚠️  跳过训练示例（缺少必要的模型文件）")
        print(f"需要的文件:")
        print(f"  - {standalone_model}")
        print(f"  - {money_model}")

def demonstrate_advanced_features():
    """演示高级功能"""
    print("\n" + "="*80)
    print("2. 高级功能示例")
    print("="*80)
    
    # 示例1: 不同的训练模式
    print(f"\n{'='*60}")
    print("可用的训练模式:")
    print("="*60)
    modes = [
        ("train", "仅训练模式 - 快速训练，不进行验证"),
        ("validate", "训练+验证模式 - 包含交叉验证"),
        ("test", "训练+验证+测试模式 - 完整评估"),
        ("full", "完整流程模式 - 包含性能对比分析")
    ]
    
    for mode, description in modes:
        print(f"  --mode {mode:<8} : {description}")
    
    # 示例2: 特征选择选项
    print(f"\n{'='*60}")
    print("特征选择选项:")
    print("="*60)
    feature_options = [
        ("--feature-count 20", "使用20个传统技术指标"),
        ("--no-traditional-features", "不使用传统技术指标"),
        ("--no-model-probabilities", "不使用模型概率特征"),
        ("--no-model-predictions", "不使用模型预测特征")
    ]
    
    for option, description in feature_options:
        print(f"  {option:<30} : {description}")
    
    # 示例3: 超参数优化选项
    print(f"\n{'='*60}")
    print("超参数优化选项:")
    print("="*60)
    optimization_options = [
        ("--optimize", "启用超参数优化"),
        ("--n-trials 100", "优化试验次数"),
        ("--cv-folds 5", "交叉验证折数")
    ]
    
    for option, description in optimization_options:
        print(f"  {option:<20} : {description}")
    
    # 示例4: 时间范围选择
    print(f"\n{'='*60}")
    print("时间范围选择选项:")
    print("="*60)
    time_options = [
        ("--start-date 2024-01-01", "指定开始日期"),
        ("--end-date 2024-12-31", "指定结束日期"),
        ("--train-days 30", "使用最近30天的数据")
    ]
    
    for option, description in time_options:
        print(f"  {option:<25} : {description}")

def demonstrate_backtest_integration():
    """演示与回测系统的集成"""
    print("\n" + "="*80)
    print("3. 回测系统集成示例")
    print("="*80)
    
    # 创建模拟的集成模型配置，展示回测兼容性
    backtest_config = {
        "model_type": "ensemble",
        "ensemble_config": {
            "name": "backtest_ensemble",
            "timeframe_minutes": 5,
            "up_threshold": 0.02,
            "down_threshold": -0.02,
            "max_lookforward_minutes": 240
        },
        "component_models": {
            "standalone": {
                "model_path": "models/standalone_eth_5m_model.joblib",
                "config_path": "models/standalone_eth_5m_config.json",
                "model_type": "standalone"
            },
            "money": {
                "model_path": "models/eth_5m_model.joblib",
                "config_path": "models/eth_5m_config.json",
                "model_type": "money"
            }
        },
        "feature_list": [f"feature_{i}" for i in range(25)],
        "timeframe_minutes": 5,
        "up_threshold": 0.02,
        "down_threshold": -0.02,
        "training_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "model_version": "1.0"
    }
    
    # 保存示例配置
    backtest_config_path = "hunhe/backtest_example_config.json"
    with open(backtest_config_path, 'w') as f:
        json.dump(backtest_config, f, indent=2)
    
    print(f"✅ 创建回测兼容配置: {backtest_config_path}")
    
    # 展示回测兼容性要点
    print(f"\n{'='*60}")
    print("回测系统兼容性要点:")
    print("="*60)
    compatibility_points = [
        "模型文件格式: 使用标准joblib格式，与现有回测系统兼容",
        "配置文件结构: 包含model_type、feature_list等必需字段",
        "预测接口: 支持predict()和predict_proba()方法",
        "特征命名: 保持与组件模型一致的特征命名规范",
        "时间框架: 支持与现有模型相同的时间框架设置"
    ]
    
    for i, point in enumerate(compatibility_points, 1):
        print(f"  {i}. {point}")
    
    # 展示如何在回测中使用集成模型
    print(f"\n{'='*60}")
    print("在回测中使用集成模型的步骤:")
    print("="*60)
    backtest_steps = [
        "1. 训练集成模型: python hunhe/main.py --config config.json",
        "2. 验证模型文件: 检查生成的.joblib和_config.json文件",
        "3. 修改回测脚本: 将模型路径指向集成模型文件",
        "4. 运行回测: 使用现有回测脚本进行测试",
        "5. 分析结果: 对比集成模型与单个模型的表现"
    ]
    
    for step in backtest_steps:
        print(f"  {step}")

def demonstrate_performance_analysis():
    """演示性能分析功能"""
    print("\n" + "="*80)
    print("4. 性能分析示例")
    print("="*80)
    
    # 运行单元测试
    print(f"\n{'='*60}")
    print("运行单元测试:")
    print("="*60)
    
    test_files = [
        ("hunhe/test_model_loader.py", "模型加载器测试"),
        ("hunhe/test_feature_generator.py", "特征生成器测试"),
        ("hunhe/test_ensemble_trainer.py", "集成训练器测试")
    ]
    
    for test_file, description in test_files:
        if os.path.exists(test_file):
            run_command(f"python {test_file}", f"运行{description}")
        else:
            print(f"⚠️  测试文件不存在: {test_file}")
    
    # 运行集成测试
    print(f"\n{'='*60}")
    print("运行集成测试:")
    print("="*60)
    
    if os.path.exists("hunhe/test_integration.py"):
        run_command("python hunhe/test_integration.py", "运行完整集成测试")
    else:
        print("⚠️  集成测试文件不存在: hunhe/test_integration.py")
    
    # 展示性能监控选项
    print(f"\n{'='*60}")
    print("性能监控选项:")
    print("="*60)
    performance_options = [
        ("--save-features", "保存特征重要性分析"),
        ("--save-analysis", "保存详细性能分析报告"),
        ("--log-level DEBUG", "启用详细日志记录"),
        ("--log-file training.log", "将日志保存到文件")
    ]
    
    for option, description in performance_options:
        print(f"  {option:<25} : {description}")

def demonstrate_troubleshooting():
    """演示故障排除"""
    print("\n" + "="*80)
    print("5. 故障排除指南")
    print("="*80)
    
    # 常见问题和解决方案
    print(f"\n{'='*60}")
    print("常见问题和解决方案:")
    print("="*60)
    
    troubleshooting_guide = [
        {
            "问题": "模型文件不存在",
            "症状": "ModelLoadError: 模型文件不存在",
            "解决方案": [
                "检查模型文件路径是否正确",
                "确保已经训练并保存了组件模型",
                "使用绝对路径或相对于工作目录的路径"
            ]
        },
        {
            "问题": "数据库连接失败",
            "症状": "sqlite3.OperationalError: no such table",
            "解决方案": [
                "检查数据库文件路径是否正确",
                "确认表名是否存在（如ethusdt_5min_spot）",
                "验证数据库文件是否损坏"
            ]
        },
        {
            "问题": "特征生成失败",
            "症状": "FeatureGenerationError: 特征计算失败",
            "解决方案": [
                "检查输入数据是否包含必需的OHLCV列",
                "确保数据量足够（至少200条记录）",
                "检查数据中是否有异常值或缺失值"
            ]
        },
        {
            "问题": "训练过程内存不足",
            "症状": "MemoryError或进程被杀死",
            "解决方案": [
                "减少特征数量（--feature-count参数）",
                "使用较小的数据集进行训练",
                "增加系统内存或使用更强的机器"
            ]
        }
    ]
    
    for i, issue in enumerate(troubleshooting_guide, 1):
        print(f"\n{i}. {issue['问题']}")
        print(f"   症状: {issue['症状']}")
        print(f"   解决方案:")
        for j, solution in enumerate(issue['解决方案'], 1):
            print(f"     {j}) {solution}")

def create_comprehensive_example():
    """创建综合示例"""
    print("\n" + "="*80)
    print("6. 综合示例")
    print("="*80)
    
    # 创建完整的示例脚本
    example_script = '''#!/usr/bin/env python3
"""
集成模型训练综合示例脚本

这个脚本展示了完整的集成模型训练流程
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from hunhe.ensemble_config import create_default_config
from hunhe.ensemble_trainer import EnsembleTrainer
from hunhe.utils import setup_logging
import pandas as pd
import sqlite3

def main():
    """主函数"""
    # 设置日志
    logger = setup_logging('INFO')
    logger.info("开始集成模型训练综合示例")
    
    # 检查必要文件
    required_files = [
        "models/standalone_eth_5m_model.joblib",
        "models/eth_5m_model.joblib",
        "coin_data.db"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        logger.error(f"缺少必要文件: {missing_files}")
        return 1
    
    try:
        # 创建配置
        config = create_default_config(
            name="comprehensive_example",
            timeframe_minutes=5,
            standalone_model_path="models/standalone_eth_5m_model.joblib",
            money_model_path="models/eth_5m_model.joblib"
        )
        
        # 设置数据源
        config.data_source = {
            'database_path': 'coin_data.db',
            'symbol': 'ETHUSDT',
            'timeframe_minutes': 5,
            'table_name': 'ethusdt_5min_spot'
        }
        
        # 设置训练参数
        config.training.mode = 'full'
        config.training.optimize_hyperparameters = True
        config.training.n_trials = 20  # 减少试验次数以加快示例
        
        # 设置输出
        config.output.save_features = True
        config.output.save_analysis = True
        
        logger.info(f"配置创建完成: {config.name}")
        
        # 创建训练器
        trainer = EnsembleTrainer(config)
        
        # 加载数据
        logger.info("加载训练数据...")
        conn = sqlite3.connect(config.data_source['database_path'])
        query = f"""
            SELECT * FROM {config.data_source['table_name']} 
            ORDER BY timestamp DESC 
            LIMIT 5000
        """
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        df = df.sort_index()  # 按时间正序排列
        
        logger.info(f"加载了 {len(df)} 条数据记录")
        
        # 执行训练
        logger.info("开始训练...")
        model, model_path = trainer.run_training_pipeline(df)
        
        logger.info(f"训练完成！模型保存到: {model_path}")
        
        # 显示结果
        print("\\n" + "="*60)
        print("训练结果:")
        print("="*60)
        print(f"模型文件: {model_path}")
        print(f"配置文件: {model_path.replace('.joblib', '_config.json')}")
        
        return 0
        
    except Exception as e:
        logger.error(f"训练失败: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
    
    # 保存示例脚本
    example_path = "hunhe/comprehensive_example.py"
    with open(example_path, 'w') as f:
        f.write(example_script)
    
    print(f"✅ 创建综合示例脚本: {example_path}")
    print(f"\n运行方式: python {example_path}")

def main():
    """主函数"""
    print("集成模型训练系统完整使用示例")
    print("="*80)
    
    try:
        # 运行各个示例
        demonstrate_basic_usage()
        demonstrate_advanced_features()
        demonstrate_backtest_integration()
        demonstrate_performance_analysis()
        demonstrate_troubleshooting()
        create_comprehensive_example()
        
        print("\n" + "="*80)
        print("✅ 所有示例演示完成！")
        print("="*80)
        
        # 提供快速开始指南
        print("\n快速开始指南:")
        print("1. 确保有必要的组件模型文件")
        print("2. 运行: python hunhe/main.py --help 查看所有选项")
        print("3. 使用: python hunhe/comprehensive_example.py 运行完整示例")
        print("4. 运行: python hunhe/test_integration.py 进行集成测试")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())