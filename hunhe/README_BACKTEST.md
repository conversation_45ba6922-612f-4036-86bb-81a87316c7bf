# 集成模型回测脚本使用指南

## 概述

`backtest_ensemble.py` 是专门为集成模型设计的回测脚本，基于 `backtest_money_quick.py` 改编。它能够：

- 加载集成模型和组件模型
- 生成完整的特征集合（传统技术指标 + 模型预测特征）
- 进行历史数据回测
- 生成详细的回测报告

## 主要特性

### ✅ 已实现的功能

1. **集成模型支持**
   - 自动加载组件模型（standalone + money management）
   - 生成模型预测特征作为集成特征
   - 使用集成模型进行预测

2. **完整的回测功能**
   - 资金管理和风险控制
   - 止损功能
   - 时间过滤（出手时间配置）
   - 详细的交易记录

3. **性能分析**
   - 成功率统计
   - 平均得分和盈亏
   - 详细的CSV日志输出

## 使用方法

### 基本用法

```bash
python -m hunhe.backtest_ensemble \
  --ensemble-model models/eth_5m_ensemble_ensemble_model.joblib \
  --ensemble-config models/eth_5m_ensemble_ensemble_config.json \
  --symbol ETHUSDT \
  --interval 5m \
  --initial-capital 1000 \
  --risk-per-trade 2.0
```

### 带时间范围的回测

```bash
python -m hunhe.backtest_ensemble \
  --ensemble-model models/eth_5m_ensemble_ensemble_model.joblib \
  --ensemble-config models/eth_5m_ensemble_ensemble_config.json \
  --symbol ETHUSDT \
  --start-time "2025-08-01" \
  --end-time "2025-09-01" \
  --initial-capital 1000 \
  --risk-per-trade 1.5
```

### 带止损的回测

```bash
python -m hunhe.backtest_ensemble \
  --ensemble-model models/eth_5m_ensemble_ensemble_model.joblib \
  --ensemble-config models/eth_5m_ensemble_ensemble_config.json \
  --symbol ETHUSDT \
  --stop-loss 3.0 \
  --initial-capital 1000 \
  --risk-per-trade 2.0
```

## 参数说明

### 必需参数

- `--ensemble-model`: 集成模型文件路径 (.joblib)
- `--ensemble-config`: 集成模型配置文件路径 (.json)

### 数据参数

- `--symbol`: 交易对符号 (默认: ETHUSDT)
- `--interval`: K线间隔 (默认: 5m)
- `--market`: 市场类型 (spot/futures, 默认: spot)
- `--db`: SQLite数据库路径 (默认: coin_data.db)
- `--start-time`: 回测开始时间 (北京时间, YYYY-MM-DD HH:MM)
- `--end-time`: 回测结束时间 (北京时间, YYYY-MM-DD HH:MM)

### 交易参数

- `--initial-capital`: 初始资金 (默认: 1000)
- `--risk-per-trade`: 单次交易风险比例 (%, 默认: 1.0)
- `--stop-loss`: 止损百分比 (%, 可选)
- `--max-active-predictions`: 最大同时活跃预测数 (默认: 1000)

### 时间过滤参数

- `--use-chushou`: 启用出手时间过滤
- `--chushou-file`: 出手时间配置文件路径 (默认: chushou.json)

## 输出结果

### 控制台输出

回测过程中会实时显示：
- 模型加载状态
- 特征计算进度
- 每笔预测的详细信息
- 最终统计结果

### CSV日志文件

回测完成后会生成 `backtest_ensemble_log.csv` 文件，包含：

| 字段 | 说明 |
|------|------|
| PredictionID | 预测ID |
| StartTimestamp | 开始时间 |
| EndTimestamp | 结束时间 |
| StartPrice | 开始价格 |
| EndPrice | 结束价格 |
| PriceChangePct | 价格变化百分比 |
| Confidence | 预测信心度 |
| Prediction | 预测方向 (0=跌, 1=涨) |
| Result | 结果 (1=成功, 0=失败, -1=超时, -2=止损) |
| Score | 得分 |
| ProfitLoss | 盈亏金额 |
| CapitalAfter | 交易后资金 |
| Status | 状态描述 |

## 示例结果

```
=== 集成模型回测结果摘要 ===
总预测数: 200, 成功: 30, 失败: 16, 超时: 154

初始资金: $1,000.00
最终资金: $1,010.84
总收益率: +1.08%

性能指标:
  成功率: 15.0%
  平均得分: +0.021
  平均盈亏: $+0.05
```

## 快速开始

1. **确保模型文件存在**
   ```bash
   ls models/eth_5m_ensemble_ensemble_model.joblib
   ls models/eth_5m_ensemble_ensemble_config.json
   ```

2. **运行示例**
   ```bash
   python hunhe/example_backtest.py
   ```

3. **查看结果**
   ```bash
   cat backtest_ensemble_log.csv
   ```

## 注意事项

### 模型兼容性

- 确保集成模型配置文件包含正确的组件模型路径
- 组件模型文件必须存在且可访问
- 数据库中必须有对应的历史数据

### 性能考虑

- 特征计算可能需要一些时间，特别是对于大量历史数据
- 建议先用较小的时间范围测试
- 可以通过 `--max-active-predictions` 控制内存使用

### 数据要求

- 需要足够的历史数据来计算技术指标
- 建议至少有1000根K线的历史数据
- 确保数据质量良好，无大量缺失值

## 故障排除

### 常见错误

1. **模型文件不存在**
   ```
   ❌ 集成模型文件不存在: models/xxx.joblib
   ```
   解决：检查文件路径，确保先运行训练脚本生成模型

2. **组件模型加载失败**
   ```
   ❌ 加载 standalone 模型失败
   ```
   解决：检查组件模型文件是否存在，路径是否正确

3. **数据库连接失败**
   ```
   ❌ 数据库中无符合条件的数据
   ```
   解决：检查数据库路径，确保有对应的历史数据

### 调试建议

1. 先用小范围时间测试
2. 检查日志输出中的错误信息
3. 确认所有依赖文件都存在
4. 验证数据库中有足够的历史数据

## 与原版回测脚本的区别

| 特性 | backtest_money_quick.py | backtest_ensemble.py |
|------|------------------------|---------------------|
| 模型类型 | 单一模型 | 集成模型 |
| 特征生成 | 单一特征计算 | 传统特征 + 模型预测特征 |
| 组件模型 | 不需要 | 自动加载组件模型 |
| 配置格式 | 简单配置 | 嵌套配置结构 |
| 预测方式 | 直接预测 | 集成预测 |

## 扩展功能

未来可以考虑添加：
- 更多的性能指标分析
- 图表可视化
- 参数优化功能
- 多币种并行回测
- 实时回测模式