#!/usr/bin/env python3
"""
集成模型回测使用示例

展示如何使用 backtest_ensemble.py 进行回测
"""

import subprocess
import sys
from pathlib import Path

def run_ensemble_backtest_example():
    """运行集成模型回测示例"""
    
    # 检查必要的文件是否存在
    model_file = "models/eth_5m_ensemble_ensemble_model.joblib"
    config_file = "models/eth_5m_ensemble_ensemble_config.json"
    
    if not Path(model_file).exists():
        print(f"❌ 集成模型文件不存在: {model_file}")
        print("请先运行训练脚本生成集成模型")
        return False
    
    if not Path(config_file).exists():
        print(f"❌ 集成配置文件不存在: {config_file}")
        print("请先运行训练脚本生成集成配置")
        return False
    
    print("🚀 开始集成模型回测示例")
    print(f"使用模型: {model_file}")
    print(f"使用配置: {config_file}")
    
    # 构建回测命令
    cmd = [
        sys.executable, "-m", "hunhe.backtest_ensemble",
        "--ensemble-model", model_file,
        "--ensemble-config", config_file,
        "--symbol", "ETHUSDT",
        "--interval", "5m",
        "--market", "spot",
        "--db", "coin_data.db",
        "--initial-capital", "1000",
        "--risk-per-trade", "2.0",  # 2% 风险
        "--max-active-predictions", "10",
        # "--start-time", "2025-08-01",  # 可选：指定开始时间
        # "--end-time", "2025-09-01",    # 可选：指定结束时间
        # "--stop-loss", "3.0",          # 可选：3% 止损
        # "--use-chushou",               # 可选：启用时间过滤
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 运行回测
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n✅ 回测完成！")
        print("📊 回测结果已保存到 backtest_ensemble_log.csv")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 回测失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 运行回测时发生错误: {e}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "="*60)
    print("集成模型回测脚本使用示例")
    print("="*60)
    
    print("\n1. 基本回测:")
    print("python -m hunhe.backtest_ensemble \\")
    print("  --ensemble-model models/eth_5m_ensemble_ensemble_model.joblib \\")
    print("  --ensemble-config models/eth_5m_ensemble_ensemble_config.json \\")
    print("  --symbol ETHUSDT \\")
    print("  --interval 5m \\")
    print("  --initial-capital 1000 \\")
    print("  --risk-per-trade 2.0")
    
    print("\n2. 带时间范围的回测:")
    print("python -m hunhe.backtest_ensemble \\")
    print("  --ensemble-model models/eth_5m_ensemble_ensemble_model.joblib \\")
    print("  --ensemble-config models/eth_5m_ensemble_ensemble_config.json \\")
    print("  --symbol ETHUSDT \\")
    print("  --start-time \"2025-08-01\" \\")
    print("  --end-time \"2025-09-01\" \\")
    print("  --initial-capital 1000 \\")
    print("  --risk-per-trade 1.5")
    
    print("\n3. 带止损的回测:")
    print("python -m hunhe.backtest_ensemble \\")
    print("  --ensemble-model models/eth_5m_ensemble_ensemble_model.joblib \\")
    print("  --ensemble-config models/eth_5m_ensemble_ensemble_config.json \\")
    print("  --symbol ETHUSDT \\")
    print("  --stop-loss 3.0 \\")
    print("  --initial-capital 1000 \\")
    print("  --risk-per-trade 2.0")
    
    print("\n4. 带时间过滤的回测:")
    print("python -m hunhe.backtest_ensemble \\")
    print("  --ensemble-model models/eth_5m_ensemble_ensemble_model.joblib \\")
    print("  --ensemble-config models/eth_5m_ensemble_ensemble_config.json \\")
    print("  --symbol ETHUSDT \\")
    print("  --use-chushou \\")
    print("  --chushou-file chushou.json \\")
    print("  --initial-capital 1000 \\")
    print("  --risk-per-trade 2.0")
    
    print("\n参数说明:")
    print("  --ensemble-model: 集成模型文件路径")
    print("  --ensemble-config: 集成模型配置文件路径")
    print("  --symbol: 交易对符号 (如 ETHUSDT)")
    print("  --interval: K线间隔 (如 5m, 15m)")
    print("  --market: 市场类型 (spot 或 futures)")
    print("  --db: SQLite数据库路径")
    print("  --start-time: 回测开始时间 (北京时间)")
    print("  --end-time: 回测结束时间 (北京时间)")
    print("  --initial-capital: 初始资金")
    print("  --risk-per-trade: 单次交易风险比例 (%)")
    print("  --stop-loss: 止损百分比 (%)")
    print("  --max-active-predictions: 最大同时活跃预测数")
    print("  --use-chushou: 启用时间过滤")
    print("  --chushou-file: 出手时间配置文件")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        show_usage_examples()
        return
    
    print("集成模型回测示例")
    print("="*40)
    
    # 显示使用示例
    show_usage_examples()
    
    # 询问是否运行示例
    try:
        response = input("\n是否运行回测示例? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            success = run_ensemble_backtest_example()
            if success:
                print("\n🎉 示例运行成功！")
            else:
                print("\n❌ 示例运行失败")
        else:
            print("跳过示例运行")
    except KeyboardInterrupt:
        print("\n用户取消")

if __name__ == "__main__":
    main()