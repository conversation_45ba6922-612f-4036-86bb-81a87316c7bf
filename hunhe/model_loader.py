"""
模型加载器模块

负责加载和管理现有的 standalone 和 money management 模型，
提供统一的预测接口。
"""

import os
import json
import joblib
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Tuple, List
from .utils import setup_logging, validate_model_files, clean_feature_names
from .ensemble_config import ModelConfig

logger = setup_logging()

class ModelLoadError(Exception):
    """模型加载错误"""
    pass

class ModelPredictionError(Exception):
    """模型预测错误"""
    pass

class ModelLoader:
    """
    模型加载器类
    
    负责加载 standalone 和 money management 模型，
    提供统一的预测接口和批量预测功能。
    """
    
    def __init__(self):
        """初始化模型加载器"""
        self.standalone_model = None
        self.standalone_config = None
        self.money_model = None
        self.money_config = None
        self.loaded_models = {}
        
        logger.info("ModelLoader 初始化完成")
    
    def load_standalone_model(self, model_path: str, config_path: str) -> bool:
        """
        加载 standalone 模型
        
        Args:
            model_path: 模型文件路径
            config_path: 配置文件路径
            
        Returns:
            bool: 是否加载成功
            
        Raises:
            ModelLoadError: 模型加载失败
        """
        try:
            # 验证文件存在
            if not validate_model_files(model_path, config_path):
                raise ModelLoadError(f"模型文件不存在: {model_path} 或 {config_path}")
            
            # 加载配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 验证是否为 standalone 模型
            if 'standalone' not in config.get('model_type', '').lower():
                logger.warning(f"模型类型可能不是 standalone: {config.get('model_type')}")
            
            # 验证类别标签
            class_labels = config.get('class_labels', {})
            if len(class_labels) != 3:
                raise ModelLoadError(f"Standalone 模型应该有3个类别，但发现 {len(class_labels)} 个")
            
            # 加载模型
            model = joblib.load(model_path)
            
            # 验证特征列表
            feature_list = config.get('feature_list', [])
            if not feature_list:
                raise ModelLoadError("配置文件中缺少 feature_list")
            
            self.standalone_model = model
            self.standalone_config = config
            self.loaded_models['standalone'] = {
                'model': model,
                'config': config,
                'model_path': model_path,
                'config_path': config_path
            }
            
            logger.info(f"成功加载 standalone 模型: {model_path}")
            logger.info(f"模型特征数量: {len(feature_list)}")
            logger.info(f"模型类别: {list(class_labels.values())}")
            
            return True
            
        except Exception as e:
            error_msg = f"加载 standalone 模型失败: {str(e)}"
            logger.error(error_msg)
            raise ModelLoadError(error_msg) from e
    
    def load_money_model(self, model_path: str, config_path: str) -> bool:
        """
        加载 money management 模型
        
        Args:
            model_path: 模型文件路径
            config_path: 配置文件路径
            
        Returns:
            bool: 是否加载成功
            
        Raises:
            ModelLoadError: 模型加载失败
        """
        try:
            # 验证文件存在
            if not validate_model_files(model_path, config_path):
                raise ModelLoadError(f"模型文件不存在: {model_path} 或 {config_path}")
            
            # 加载配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 验证模型类型（money management 模型通常没有 'standalone' 标识）
            model_type = config.get('model_type', '')
            if 'standalone' in model_type.lower():
                logger.warning(f"模型类型可能不是 money management: {model_type}")
            
            # 验证阈值参数（money management 模型特有）
            if 'up_threshold' not in config or 'down_threshold' not in config:
                logger.warning("配置文件中缺少 up_threshold 或 down_threshold，可能不是 money management 模型")
            
            # 加载模型
            model = joblib.load(model_path)
            
            # 验证特征列表
            feature_list = config.get('feature_list', [])
            if not feature_list:
                raise ModelLoadError("配置文件中缺少 feature_list")
            
            self.money_model = model
            self.money_config = config
            self.loaded_models['money'] = {
                'model': model,
                'config': config,
                'model_path': model_path,
                'config_path': config_path
            }
            
            logger.info(f"成功加载 money management 模型: {model_path}")
            logger.info(f"模型特征数量: {len(feature_list)}")
            logger.info(f"上涨阈值: {config.get('up_threshold', 'N/A')}")
            logger.info(f"下跌阈值: {config.get('down_threshold', 'N/A')}")
            
            return True
            
        except Exception as e:
            error_msg = f"加载 money management 模型失败: {str(e)}"
            logger.error(error_msg)
            raise ModelLoadError(error_msg) from e
    
    def load_models_from_config(self, models_config: Dict[str, ModelConfig]) -> bool:
        """
        从配置对象加载多个模型
        
        Args:
            models_config: 模型配置字典
            
        Returns:
            bool: 是否全部加载成功
        """
        success_count = 0
        total_count = len(models_config)
        
        for model_name, model_config in models_config.items():
            try:
                if model_config.model_type == 'standalone':
                    self.load_standalone_model(model_config.model_path, model_config.config_path)
                elif model_config.model_type == 'money':
                    self.load_money_model(model_config.model_path, model_config.config_path)
                else:
                    logger.error(f"不支持的模型类型: {model_config.model_type}")
                    continue
                
                success_count += 1
                logger.info(f"成功加载模型: {model_name} ({model_config.model_type})")
                
            except Exception as e:
                logger.error(f"加载模型 {model_name} 失败: {str(e)}")
        
        logger.info(f"模型加载完成: {success_count}/{total_count} 成功")
        return success_count == total_count
    
    def validate_loaded_models(self) -> bool:
        """
        验证已加载的模型
        
        Returns:
            bool: 所有模型是否有效
        """
        if not self.loaded_models:
            logger.error("没有加载任何模型")
            return False
        
        valid_count = 0
        for model_name, model_info in self.loaded_models.items():
            try:
                model = model_info['model']
                config = model_info['config']
                
                # 检查模型对象
                if model is None:
                    logger.error(f"模型 {model_name} 为空")
                    continue
                
                # 检查特征列表
                feature_list = config.get('feature_list', [])
                if not feature_list:
                    logger.error(f"模型 {model_name} 缺少特征列表")
                    continue
                
                # 检查模型是否有预测方法
                if not hasattr(model, 'predict_proba'):
                    logger.error(f"模型 {model_name} 缺少 predict_proba 方法")
                    continue
                
                valid_count += 1
                logger.debug(f"模型 {model_name} 验证通过")
                
            except Exception as e:
                logger.error(f"验证模型 {model_name} 时出错: {str(e)}")
        
        is_valid = valid_count == len(self.loaded_models)
        logger.info(f"模型验证结果: {valid_count}/{len(self.loaded_models)} 有效")
        return is_valid
    
    def get_model_info(self, model_type: str) -> Optional[Dict[str, Any]]:
        """
        获取模型信息
        
        Args:
            model_type: 模型类型 ('standalone' 或 'money')
            
        Returns:
            模型信息字典，如果模型未加载则返回 None
        """
        return self.loaded_models.get(model_type)
    
    def get_feature_list(self, model_type: str) -> List[str]:
        """
        获取指定模型的特征列表
        
        Args:
            model_type: 模型类型 ('standalone' 或 'money')
            
        Returns:
            特征列表，如果模型未加载则返回空列表
        """
        model_info = self.get_model_info(model_type)
        if model_info:
            return model_info['config'].get('feature_list', [])
        return []
    
    def is_model_loaded(self, model_type: str) -> bool:
        """
        检查指定类型的模型是否已加载
        
        Args:
            model_type: 模型类型 ('standalone' 或 'money')
            
        Returns:
            bool: 模型是否已加载
        """
        return model_type in self.loaded_models
    
    def get_loaded_model_types(self) -> List[str]:
        """
        获取已加载的模型类型列表
        
        Returns:
            已加载的模型类型列表
        """
        return list(self.loaded_models.keys())
    
    def clear_models(self) -> None:
        """清除所有已加载的模型"""
        self.standalone_model = None
        self.standalone_config = None
        self.money_model = None
        self.money_config = None
        self.loaded_models.clear()
        logger.info("已清除所有加载的模型")
    
    def _prepare_features(self, features_df: pd.DataFrame, model_type: str) -> pd.DataFrame:
        """
        准备模型预测所需的特征
        
        Args:
            features_df: 输入特征DataFrame
            model_type: 模型类型 ('standalone' 或 'money')
            
        Returns:
            准备好的特征DataFrame
            
        Raises:
            ModelPredictionError: 特征准备失败
        """
        try:
            model_info = self.get_model_info(model_type)
            if not model_info:
                raise ModelPredictionError(f"模型 {model_type} 未加载")
            
            required_features = model_info['config'].get('feature_list', [])
            if not required_features:
                raise ModelPredictionError(f"模型 {model_type} 缺少特征列表")
            
            # 清理特征名称
            features_df = clean_feature_names(features_df)
            
            # 检查缺失的特征
            missing_features = set(required_features) - set(features_df.columns)
            if missing_features:
                raise ModelPredictionError(
                    f"模型 {model_type} 缺少必需特征: {missing_features}"
                )
            
            # 选择并排序特征
            prepared_features = features_df[required_features].copy()
            
            # 检查缺失值
            null_counts = prepared_features.isnull().sum()
            if null_counts.sum() > 0:
                logger.warning(f"模型 {model_type} 特征中存在缺失值: {null_counts[null_counts > 0].to_dict()}")
                # 用前向填充处理缺失值
                prepared_features = prepared_features.fillna(method='ffill').fillna(0)
            
            logger.debug(f"为模型 {model_type} 准备了 {len(prepared_features)} 行 {len(required_features)} 个特征")
            return prepared_features
            
        except Exception as e:
            error_msg = f"准备模型 {model_type} 特征失败: {str(e)}"
            logger.error(error_msg)
            raise ModelPredictionError(error_msg) from e
    
    def predict_standalone(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """
        使用 standalone 模型进行预测
        
        Args:
            features_df: 输入特征DataFrame
            
        Returns:
            包含预测结果的DataFrame，列包括:
            - standalone_prob_lowest: 预测最低点的概率
            - standalone_prob_neutral: 预测中性点的概率  
            - standalone_prob_highest: 预测最高点的概率
            - standalone_prediction: 预测类别 (0/1/2)
            
        Raises:
            ModelPredictionError: 预测失败
        """
        try:
            if not self.is_model_loaded('standalone'):
                raise ModelPredictionError("Standalone 模型未加载")
            
            # 准备特征
            prepared_features = self._prepare_features(features_df, 'standalone')
            
            # 进行预测
            model = self.loaded_models['standalone']['model']
            probabilities = model.predict_proba(prepared_features)
            predictions = model.predict(prepared_features)
            
            # 验证概率形状
            if probabilities.shape[1] != 3:
                raise ModelPredictionError(
                    f"Standalone 模型应该输出3个类别的概率，但得到 {probabilities.shape[1]} 个"
                )
            
            # 创建结果DataFrame
            result_df = pd.DataFrame({
                'standalone_prob_lowest': probabilities[:, 0],
                'standalone_prob_neutral': probabilities[:, 1],
                'standalone_prob_highest': probabilities[:, 2],
                'standalone_prediction': predictions
            }, index=features_df.index)
            
            logger.debug(f"Standalone 模型预测完成: {len(result_df)} 行")
            return result_df
            
        except Exception as e:
            error_msg = f"Standalone 模型预测失败: {str(e)}"
            logger.error(error_msg)
            raise ModelPredictionError(error_msg) from e
    
    def predict_money(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """
        使用 money management 模型进行预测
        
        Args:
            features_df: 输入特征DataFrame
            
        Returns:
            包含预测结果的DataFrame，列包括:
            - money_prob_down: 预测下跌的概率
            - money_prob_up: 预测上涨的概率
            - money_prediction: 预测类别 (0/1)
            
        Raises:
            ModelPredictionError: 预测失败
        """
        try:
            if not self.is_model_loaded('money'):
                raise ModelPredictionError("Money management 模型未加载")
            
            # 准备特征
            prepared_features = self._prepare_features(features_df, 'money')
            
            # 进行预测
            model = self.loaded_models['money']['model']
            probabilities = model.predict_proba(prepared_features)
            predictions = model.predict(prepared_features)
            
            # 验证概率形状
            if probabilities.shape[1] != 2:
                raise ModelPredictionError(
                    f"Money management 模型应该输出2个类别的概率，但得到 {probabilities.shape[1]} 个"
                )
            
            # 创建结果DataFrame
            result_df = pd.DataFrame({
                'money_prob_down': probabilities[:, 0],
                'money_prob_up': probabilities[:, 1],
                'money_prediction': predictions
            }, index=features_df.index)
            
            logger.debug(f"Money management 模型预测完成: {len(result_df)} 行")
            return result_df
            
        except Exception as e:
            error_msg = f"Money management 模型预测失败: {str(e)}"
            logger.error(error_msg)
            raise ModelPredictionError(error_msg) from e
    
    def predict_batch(self, features_df: pd.DataFrame, 
                     batch_size: int = 1000) -> Dict[str, pd.DataFrame]:
        """
        批量预测，提高大数据集的预测性能
        
        Args:
            features_df: 输入特征DataFrame
            batch_size: 批次大小
            
        Returns:
            包含所有模型预测结果的字典
            
        Raises:
            ModelPredictionError: 批量预测失败
        """
        try:
            if features_df.empty:
                logger.warning("输入特征DataFrame为空")
                return {}
            
            results = {}
            total_rows = len(features_df)
            
            logger.info(f"开始批量预测: {total_rows} 行数据，批次大小: {batch_size}")
            
            # 为每个已加载的模型进行批量预测
            for model_type in self.get_loaded_model_types():
                logger.info(f"开始 {model_type} 模型批量预测")
                
                batch_results = []
                
                # 分批处理
                for i in range(0, total_rows, batch_size):
                    end_idx = min(i + batch_size, total_rows)
                    batch_features = features_df.iloc[i:end_idx]
                    
                    logger.debug(f"处理批次 {i//batch_size + 1}: 行 {i}-{end_idx-1}")
                    
                    # 根据模型类型调用相应的预测方法
                    if model_type == 'standalone':
                        batch_result = self.predict_standalone(batch_features)
                    elif model_type == 'money':
                        batch_result = self.predict_money(batch_features)
                    else:
                        logger.warning(f"跳过未知模型类型: {model_type}")
                        continue
                    
                    batch_results.append(batch_result)
                
                # 合并批次结果
                if batch_results:
                    results[model_type] = pd.concat(batch_results, axis=0)
                    logger.info(f"{model_type} 模型批量预测完成: {len(results[model_type])} 行")
            
            logger.info(f"批量预测完成，共处理 {len(results)} 个模型")
            return results
            
        except Exception as e:
            error_msg = f"批量预测失败: {str(e)}"
            logger.error(error_msg)
            raise ModelPredictionError(error_msg) from e
    
    def predict_all(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """
        使用所有已加载的模型进行预测并合并结果
        
        Args:
            features_df: 输入特征DataFrame
            
        Returns:
            包含所有模型预测结果的合并DataFrame
            
        Raises:
            ModelPredictionError: 预测失败
        """
        try:
            if features_df.empty:
                logger.warning("输入特征DataFrame为空")
                return pd.DataFrame()
            
            if not self.loaded_models:
                raise ModelPredictionError("没有加载任何模型")
            
            results = []
            
            # 对每个已加载的模型进行预测
            for model_type in self.get_loaded_model_types():
                try:
                    if model_type == 'standalone':
                        result = self.predict_standalone(features_df)
                    elif model_type == 'money':
                        result = self.predict_money(features_df)
                    else:
                        logger.warning(f"跳过未知模型类型: {model_type}")
                        continue
                    
                    results.append(result)
                    logger.debug(f"模型 {model_type} 预测完成")
                    
                except Exception as e:
                    logger.error(f"模型 {model_type} 预测失败: {str(e)}")
                    # 继续处理其他模型
                    continue
            
            # 合并所有预测结果
            if results:
                combined_result = pd.concat(results, axis=1)
                logger.info(f"所有模型预测完成，合并结果: {len(combined_result)} 行 {len(combined_result.columns)} 列")
                return combined_result
            else:
                raise ModelPredictionError("所有模型预测都失败了")
                
        except Exception as e:
            error_msg = f"合并预测失败: {str(e)}"
            logger.error(error_msg)
            raise ModelPredictionError(error_msg) from e
    
    def get_prediction_summary(self, predictions_df: pd.DataFrame) -> Dict[str, Any]:
        """
        获取预测结果的统计摘要
        
        Args:
            predictions_df: 预测结果DataFrame
            
        Returns:
            预测统计摘要字典
        """
        summary = {}
        
        try:
            # Standalone 模型统计
            if 'standalone_prediction' in predictions_df.columns:
                standalone_counts = predictions_df['standalone_prediction'].value_counts().to_dict()
                summary['standalone'] = {
                    'total_predictions': len(predictions_df),
                    'class_distribution': standalone_counts,
                    'class_percentages': {
                        k: v / len(predictions_df) * 100 
                        for k, v in standalone_counts.items()
                    }
                }
                
                # 概率统计
                prob_cols = ['standalone_prob_lowest', 'standalone_prob_neutral', 'standalone_prob_highest']
                for col in prob_cols:
                    if col in predictions_df.columns:
                        summary['standalone'][f'{col}_mean'] = predictions_df[col].mean()
                        summary['standalone'][f'{col}_std'] = predictions_df[col].std()
            
            # Money 模型统计
            if 'money_prediction' in predictions_df.columns:
                money_counts = predictions_df['money_prediction'].value_counts().to_dict()
                summary['money'] = {
                    'total_predictions': len(predictions_df),
                    'class_distribution': money_counts,
                    'class_percentages': {
                        k: v / len(predictions_df) * 100 
                        for k, v in money_counts.items()
                    }
                }
                
                # 概率统计
                prob_cols = ['money_prob_down', 'money_prob_up']
                for col in prob_cols:
                    if col in predictions_df.columns:
                        summary['money'][f'{col}_mean'] = predictions_df[col].mean()
                        summary['money'][f'{col}_std'] = predictions_df[col].std()
            
            logger.debug("预测摘要统计完成")
            return summary
            
        except Exception as e:
            logger.error(f"生成预测摘要失败: {str(e)}")
            return {}