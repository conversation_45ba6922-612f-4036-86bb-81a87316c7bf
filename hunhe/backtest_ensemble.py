#!/usr/bin/env python3
"""
集成模型回测脚本

基于 backtest_money_quick.py 改编，专门用于集成模型的回测
支持加载多个组件模型并使用集成特征进行预测
"""

import pandas as pd
import numpy as np
import joblib
import json
import argparse
import os
import sqlite3
import sys
from pathlib import Path
from datetime import datetime, timedelta
import pytz
from typing import Dict, List

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入集成模型相关模块
from hunhe.model_loader import ModelLoader
from hunhe.feature_generator import FeatureGenerator
from hunhe.ensemble_config import EnsembleConfig

# 导入现有的工具函数
try:
    from get_coin_history import get_table_name
    from model_utils_815 import get_coin_config
    from analyze_money import analyze_and_plot_results
except ImportError:
    print("警告: 无法导入部分工具函数，将使用简化版本")
    def get_table_name(coin, interval, market):
        return f"{coin.upper()}_{interval.replace('m', 'min')}_{market}"
    def get_coin_config(coin):
        return {'model_basename': f"{coin}_model", 'api_symbol': f"{coin}USDT"}
    def analyze_and_plot_results(df, basename):
        print(f"分析结果并绘图: {basename}")

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def to_beijing_time(timestamp):
    """转换为北京时间"""
    if isinstance(timestamp, pd.Timestamp):
        if timestamp.tz is None:
            timestamp = timestamp.tz_localize('UTC')
        return timestamp.tz_convert(BEIJING_TZ)
    elif isinstance(timestamp, datetime):
        if timestamp.tzinfo is None:
            timestamp = pytz.UTC.localize(timestamp)
        return timestamp.astimezone(BEIJING_TZ)
    return timestamp

def format_beijing_time(timestamp):
    """格式化北京时间"""
    beijing_time = to_beijing_time(timestamp)
    return beijing_time.strftime('%Y-%m-%d %H:%M:%S UTC+8')

def parse_time_input(time_str):
    """解析时间输入"""
    if not time_str:
        return None
    try:
        formats = ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M', '%Y-%m-%d', '%m-%d %H:%M', '%m-%d']
        dt = None
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                break
            except ValueError:
                continue
        if dt is None:
            raise ValueError(f"无法解析时间格式: {time_str}")
        if dt.year == 1900:
            dt = dt.replace(year=datetime.now().year)
        beijing_dt = BEIJING_TZ.localize(dt)
        utc_dt = beijing_dt.astimezone(pytz.UTC)
        return pd.Timestamp(utc_dt).tz_localize(None)
    except Exception as e:
        print(f"时间解析错误: {e}")
        return None

def load_chushou_config(chushou_file='chushou.json'):
    """加载出手时间配置"""
    if chushou_file is None or not os.path.exists(chushou_file):
        if chushou_file is not None:
            print(f"⚠️ 出手时间配置文件 '{chushou_file}' 不存在，将在所有时间开单")
        return None
    
    try:
        with open(chushou_file, 'r', encoding='utf-8') as f:
            chushou_config = json.load(f)
        
        # 转换为更易查询的格式
        time_filter = {}
        for day_config in chushou_config:
            day_name = day_config['StartDayName']
            hours = day_config['StartHour']
            time_filter[day_name] = set(hours)
        
        print(f"✅ 已加载出手时间配置，将仅在指定时间段开单")
        for day, hours in time_filter.items():
            print(f"  {day}: {sorted(list(hours))} 时")
        
        return time_filter
    except Exception as e:
        print(f"❌ 加载出手时间配置失败: {e}")
        return None

def is_good_time_to_trade(timestamp, time_filter):
    """检查当前时间是否适合开单"""
    if time_filter is None:
        return True
    
    beijing_time = to_beijing_time(timestamp)
    day_name = beijing_time.strftime('%A')
    hour = beijing_time.hour
    
    return day_name in time_filter and hour in time_filter[day_name]

def load_data_from_sqlite(db_path, symbol, interval, market, start_time=None, end_time=None):
    """从SQLite读取数据"""
    table_name = get_table_name(symbol, interval, market)
    conn = sqlite3.connect(db_path)
    
    query = f"SELECT timestamp, open, high, low, close, volume FROM {table_name}"
    conditions = []
    params = []
    
    if start_time:
        conditions.append("timestamp >= ?")
        params.append(int(start_time.timestamp()))
    if end_time:
        conditions.append("timestamp <= ?")
        params.append(int(end_time.timestamp()))
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    query += " ORDER BY timestamp ASC"
    
    print(f"执行查询: {query}")
    df = pd.read_sql_query(query, conn, params=params)
    conn.close()
    
    if df.empty:
        print("❌ 数据库中无符合条件的数据")
        return None
    
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
    df.set_index('timestamp', inplace=True)
    df = df.astype(float)
    
    print(f"✅ 成功加载 {len(df)} 条数据")
    print(f"数据时间范围: {format_beijing_time(df.index[0])} 到 {format_beijing_time(df.index[-1])}")
    
    return df

class EnsembleBacktester:
    """集成模型回测器"""
    
    def __init__(self, ensemble_model_file: str, ensemble_config_file: str, 
                 initial_capital: float, risk_per_trade_pct: float, 
                 stop_loss_pct: float = None):
        """
        初始化集成模型回测器
        
        Args:
            ensemble_model_file: 集成模型文件路径
            ensemble_config_file: 集成模型配置文件路径
            initial_capital: 初始资金
            risk_per_trade_pct: 单次交易风险比例
            stop_loss_pct: 止损百分比
        """
        self.ensemble_model_file = ensemble_model_file
        self.ensemble_config_file = ensemble_config_file
        self.stop_loss_pct = stop_loss_pct
        
        # 加载集成模型和配置
        self.ensemble_model = joblib.load(ensemble_model_file)
        with open(ensemble_config_file, 'r', encoding='utf-8') as f:
            self.ensemble_config = json.load(f)
        
        # 创建集成配置对象
        # 检查配置文件格式，如果有嵌套的ensemble_config字段，使用它
        if 'ensemble_config' in self.ensemble_config:
            config_data = self.ensemble_config['ensemble_config']
        else:
            config_data = self.ensemble_config
        
        self.config = EnsembleConfig.from_dict(config_data)
        
        # 初始化模型加载器和特征生成器
        self.model_loader = ModelLoader()
        self._load_component_models()
        self.feature_generator = FeatureGenerator(self.model_loader, self.config)
        
        # 资金管理
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.risk_per_trade_pct = risk_per_trade_pct / 100.0
        
        # 预测管理
        self.active_predictions: Dict[str, dict] = {}
        self.completed_predictions: List[dict] = []
        self.prediction_counter = 0
        
        # 统计信息
        self.total_predictions = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        self.timeout_predictions = 0
        self.stop_loss_predictions = 0
        
        # 回撤跟踪
        self.peak_capital = initial_capital
        self.max_drawdown_pct = 0.0
        
        print(f"集成模型回测器初始化完成")
        print(f"集成模型: {ensemble_model_file}")
        print(f"组件模型: {list(self.config.models.keys())}")
        print(f"时间框架: {self.config.timeframe_minutes}分钟")
        print(f"上涨阈值: {self.config.up_threshold*100:.1f}%")
        print(f"下跌阈值: {self.config.down_threshold*100:.1f}%")
        print(f"最大前瞻时间: {self.config.max_lookforward_minutes}分钟")
        if stop_loss_pct is not None:
            print(f"止损触发条件: {stop_loss_pct:.1f}%")
        print(f"初始资金: ${initial_capital:,.2f}")
        print(f"单次风险比例: {risk_per_trade_pct:.2f}%")
    
    def _load_component_models(self):
        """加载组件模型"""
        print("加载组件模型...")
        
        loaded_count = 0
        for model_name, model_config in self.config.models.items():
            try:
                print(f"尝试加载 {model_name} 模型: {model_config.model_path}")
                
                if model_config.model_type == 'standalone':
                    self.model_loader.load_standalone_model(
                        model_config.model_path, 
                        model_config.config_path
                    )
                    loaded_count += 1
                    print(f"✅ 成功加载 standalone 模型")
                    
                elif model_config.model_type == 'money':
                    self.model_loader.load_money_model(
                        model_config.model_path, 
                        model_config.config_path
                    )
                    loaded_count += 1
                    print(f"✅ 成功加载 money management 模型")
                    
                else:
                    print(f"⚠️ 不支持的模型类型: {model_config.model_type}")
                    
            except Exception as e:
                print(f"❌ 加载 {model_name} 模型失败: {str(e)}")
        
        if loaded_count == 0:
            raise ValueError("没有成功加载任何组件模型")
        else:
            print(f"成功加载 {loaded_count} 个组件模型")
    
    def make_prediction_from_features(self, features_df: pd.DataFrame, current_timestamp: pd.Timestamp) -> tuple:
        """使用集成模型进行预测"""
        try:
            # 获取当前时间点的特征
            if current_timestamp not in features_df.index:
                return None, 0.0, 0.0
            
            current_features = features_df.loc[current_timestamp]
            
            # 检查是否有缺失值
            if current_features.isnull().any():
                return None, 0.0, 0.0
            
            # 获取集成模型需要的特征列
            feature_columns = self.ensemble_config.get('feature_list', [])
            if not feature_columns:
                # 如果配置中没有特征列表，使用所有非价格列
                exclude_cols = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
                feature_columns = [col for col in features_df.columns if col not in exclude_cols]
            
            # 检查特征是否存在
            missing_features = [f for f in feature_columns if f not in current_features.index]
            if missing_features:
                print(f"⚠️ 缺失特征: {missing_features[:5]}...")  # 只显示前5个
                return None, 0.0, 0.0
            
            # 准备特征数据 - 使用DataFrame保持列名
            feature_data = current_features[feature_columns].to_frame().T
            
            # 进行预测
            probabilities = self.ensemble_model.predict_proba(feature_data)[0]
            prediction = self.ensemble_model.predict(feature_data)[0]
            
            # 获取当前价格
            current_price = current_features['close']
            
            # 根据概率确定预测结果
            if len(probabilities) == 2:
                # 二分类模型
                prob_up = probabilities[1]
                guess = 1 if prediction == 1 else 0
                confidence = prob_up if guess == 1 else (1 - prob_up)
                if confidence < 0.65:
                    return None, 0.0, 0.0
            else:
                # 多分类或其他情况
                confidence = probabilities.max()
                guess = prediction
            
            return guess, confidence, current_price
            
        except Exception as e:
            print(f"集成模型预测时发生错误: {e}")
            return None, 0.0, 0.0
    
    def add_prediction(self, guess: int, probability: float, price: float, 
                      timestamp: pd.Timestamp, current_idx: int):
        """添加新预测"""
        self.prediction_counter += 1
        prediction_id = f"ensemble_pred_{self.prediction_counter:06d}"
        
        trade_risk_capital = self.current_capital * self.risk_per_trade_pct
        max_wait_candles = self.config.max_lookforward_minutes // self.config.timeframe_minutes
        
        prediction = {
            'id': prediction_id,
            'guess': guess,
            'probability': probability,
            'start_price': price,
            'start_timestamp': timestamp,
            'start_idx': current_idx,
            'expire_idx': current_idx + max_wait_candles,
            'up_target': price * (1 + self.config.up_threshold),
            'down_target': price * (1 - self.config.down_threshold),
            'status': 'active',
            'trade_risk_capital': trade_risk_capital,
            'max_loss_pct': 0.0,
            'max_loss_price': price,
            'max_loss_timestamp': timestamp
        }
        
        self.active_predictions[prediction_id] = prediction
        self.total_predictions += 1
        
        direction_str = f"先涨{self.config.up_threshold*100:.1f}%" if guess == 1 else f"先跌{self.config.down_threshold*100:.1f}%"
        print(f"[{format_beijing_time(timestamp)}] 集成预测: {direction_str}, "
              f"信心: {probability:.3f}, 价格: {price:.4f}, 风险暴露: ${trade_risk_capital:,.2f}")
    
    def check_predictions(self, current_price: float, current_timestamp: pd.Timestamp, current_idx: int):
        """检查现有预测的状态"""
        completed_ids = []
        
        for pred_id, pred in self.active_predictions.items():
            if pred['status'] != 'active':
                continue
            
            # 计算当前价格变化
            current_price_change_pct = (current_price - pred['start_price']) / pred['start_price'] * 100
            
            # 更新最大亏损
            if (pred['guess'] == 1 and current_price_change_pct < 0) or \
               (pred['guess'] == 0 and current_price_change_pct > 0):
                loss_pct = abs(current_price_change_pct)
                if loss_pct > pred['max_loss_pct']:
                    pred.update({
                        'max_loss_pct': loss_pct,
                        'max_loss_price': current_price,
                        'max_loss_timestamp': current_timestamp
                    })
            
            # 检查止损
            if self.stop_loss_pct is not None:
                should_stop_loss = (pred['guess'] == 1 and current_price_change_pct < -self.stop_loss_pct) or \
                                 (pred['guess'] == 0 and current_price_change_pct > self.stop_loss_pct)
                if should_stop_loss:
                    reason = f"止损(触发点:{-self.stop_loss_pct:.1f}%)"
                    self.complete_prediction(pred_id, -2, current_price, current_timestamp, current_idx, reason)
                    completed_ids.append(pred_id)
                    continue
            
            # 检查目标达成
            if current_price >= pred['up_target']:
                result = 1 if pred['guess'] == 1 else 0
                reason = "达到上涨目标" if pred['guess'] == 1 else "达到上涨目标(预测错误)"
                self.complete_prediction(pred_id, result, current_price, current_timestamp, current_idx, reason)
                completed_ids.append(pred_id)
            elif current_price <= pred['down_target']:
                result = 1 if pred['guess'] == 0 else 0
                reason = "达到下跌目标" if pred['guess'] == 0 else "达到下跌目标(预测错误)"
                self.complete_prediction(pred_id, result, current_price, current_timestamp, current_idx, reason)
                completed_ids.append(pred_id)
            elif current_idx >= pred['expire_idx']:
                self.complete_prediction(pred_id, -1, current_price, current_timestamp, current_idx, "超时")
                completed_ids.append(pred_id)
        
        # 移除已完成的预测
        for pred_id in completed_ids:
            if pred_id in self.active_predictions:
                del self.active_predictions[pred_id]
    
    def calculate_score(self, prediction: int, start_price: float, end_price: float, result: int):
        """计算得分"""
        price_change_pct = (end_price - start_price) / start_price
        threshold = self.config.up_threshold if prediction == 1 else self.config.down_threshold
        
        # 根据最终价格变化方向计算得分
        score = price_change_pct / threshold if prediction == 1 else -price_change_pct / threshold
        return score
    
    def complete_prediction(self, pred_id: str, result: int, final_price: float, 
                          end_timestamp: pd.Timestamp, end_idx: int, reason: str):
        """完成预测"""
        if pred_id not in self.active_predictions:
            return
        
        pred = self.active_predictions[pred_id]
        pred['status'] = 'completed'
        
        # 计算得分和盈亏
        score = self.calculate_score(pred['guess'], pred['start_price'], final_price, result)
        profit_loss = pred['trade_risk_capital'] * score
        self.current_capital += profit_loss
        
        # 更新回撤跟踪
        if self.current_capital > self.peak_capital:
            self.peak_capital = self.current_capital
        else:
            current_drawdown_pct = (self.peak_capital - self.current_capital) / self.peak_capital * 100
            if current_drawdown_pct > self.max_drawdown_pct:
                self.max_drawdown_pct = current_drawdown_pct
        
        # 更新统计
        if result == 1:
            self.successful_predictions += 1
            status_str = "成功✅"
        elif result == 0:
            self.failed_predictions += 1
            status_str = "失败❌"
        elif result == -2:
            self.stop_loss_predictions += 1
            status_str = "止损🛑"
        else:
            self.timeout_predictions += 1
            status_str = "超时⏰"
        
        # 记录完成的预测
        start_beijing = to_beijing_time(pred['start_timestamp'])
        end_beijing = to_beijing_time(end_timestamp)
        
        completed_pred = {
            'PredictionID': pred['id'],
            'StartTimestamp': format_beijing_time(pred['start_timestamp']),
            'EndTimestamp': format_beijing_time(end_timestamp),
            'StartPrice': pred['start_price'],
            'EndPrice': final_price,
            'PriceChangePct': ((final_price - pred['start_price']) / pred['start_price']) * 100,
            'MaxLossPct': pred['max_loss_pct'],
            'MaxLossPrice': pred['max_loss_price'],
            'MaxLossTimestamp': format_beijing_time(pred['max_loss_timestamp']),
            'Confidence': pred['probability'],
            'Prediction': pred['guess'],
            'Result': result,
            'Score': score,
            'ProfitLoss': profit_loss,
            'CapitalAfter': self.current_capital,
            'Status': status_str,
            'Reason': reason,
            'DurationMinutes': (end_idx - pred['start_idx']) * self.config.timeframe_minutes,
            'UpTarget': pred['up_target'],
            'DownTarget': pred['down_target'],
            'StartHour': start_beijing.hour,
            'StartDayOfWeek': start_beijing.weekday(),
            'StartDayName': start_beijing.strftime('%A'),
            'EndHour': end_beijing.hour,
            'EndDayOfWeek': end_beijing.weekday(),
            'EndDayName': end_beijing.strftime('%A')
        }
        
        self.completed_predictions.append(completed_pred)
        
        direction_str = f"先涨..." if pred['guess'] == 1 else f"先跌..."
        max_loss_info = f", 最大亏损: {pred['max_loss_pct']:.2f}%" if pred['max_loss_pct'] > 0 else ""
        print(f"[{format_beijing_time(end_timestamp)}] 集成预测完成: {direction_str} -> {status_str}, "
              f"得分: {score:+.2f}, 盈亏: ${profit_loss:+.2f}, "
              f"当前资金: ${self.current_capital:,.2f}{max_loss_info}")

def run_ensemble_backtest(ensemble_model_file, ensemble_config_file, df, **kwargs):
    """运行集成模型回测"""
    print("=== 开始集成模型回测 ===")
    
    backtester = EnsembleBacktester(
        ensemble_model_file, 
        ensemble_config_file,
        kwargs['initial_capital'],
        kwargs['risk_per_trade_pct'],
        kwargs['stop_loss_pct']
    )
    
    # 加载出手时间配置
    time_filter = load_chushou_config(kwargs.get('chushou_file'))
    
    # 预计算所有特征
    print("正在预先计算所有时间序列的特征，请稍候...")
    features_df = backtester.feature_generator.generate_all_features(df, backtester.config.timeframe_minutes)
    print("特征计算完成。")
    
    # 找到第一个可以开始预测的有效点
    valid_features_df = features_df.dropna()
    if valid_features_df.empty:
        print("❌ 计算特征后没有剩下任何有效数据行，无法进行回测。")
        return
    
    first_valid_index_pos = df.index.get_loc(valid_features_df.index[0])
    print(f"\n从索引 {first_valid_index_pos} 开始预测 (时间: {format_beijing_time(valid_features_df.index[0])})")
    print(f"将同时保持最多 {kwargs['max_active_predictions']} 笔活跃的投资。")
    
    # 遍历数据进行回测
    skipped_predictions = 0
    for i in range(first_valid_index_pos, len(df)):
        current_timestamp = df.index[i]
        current_price = df.iloc[i]['close']
        
        # 检查现有预测的状态
        backtester.check_predictions(current_price, current_timestamp, i)
        
        # 检查是否可以进行新预测
        active_count = len([p for p in backtester.active_predictions.values() if p['status'] == 'active'])
        if active_count < kwargs['max_active_predictions']:
            # 使用集成模型进行预测
            guess, probability, pred_price = backtester.make_prediction_from_features(features_df, current_timestamp)
            
            if guess is not None:
                # 检查是否在允许的交易时间内
                if is_good_time_to_trade(current_timestamp, time_filter):
                    backtester.add_prediction(guess, probability, pred_price, current_timestamp, i)
                else:
                    skipped_predictions += 1
    
    # 结束所有仍在活跃的预测
    final_timestamp, final_price, final_idx = df.index[-1], df.iloc[-1]['close'], len(df) - 1
    for pred_id in list(backtester.active_predictions.keys()):
        backtester.complete_prediction(pred_id, -1, final_price, final_timestamp, final_idx, "数据结束-超时")
    
    if time_filter and skipped_predictions > 0:
        print(f"\n⏰ 因时间过滤跳过了 {skipped_predictions} 个潜在预测信号")
    
    # 报告结果
    finalize_and_report(backtester, kwargs['initial_capital'], kwargs['stop_loss_pct'])

def finalize_and_report(backtester: EnsembleBacktester, initial_capital: float, stop_loss_pct: float | None):
    """生成最终报告"""
    print("\n=== 集成模型回测结果摘要 ===")
    print(f"总预测数: {backtester.total_predictions}, 成功: {backtester.successful_predictions}, "
          f"失败: {backtester.failed_predictions}, 超时: {backtester.timeout_predictions}", end="")
    if stop_loss_pct is not None:
        print(f", 止损: {backtester.stop_loss_predictions}")
    else:
        print()
    
    final_capital = backtester.current_capital
    total_return_pct = (final_capital - initial_capital) / initial_capital * 100
    
    print(f"\n初始资金: ${initial_capital:,.2f}")
    print(f"最终资金: ${final_capital:,.2f}")
    print(f"总收益率: {total_return_pct:+.2f}%")
    
    if backtester.completed_predictions:
        results_df = pd.DataFrame(backtester.completed_predictions)
        # 确保时间戳列是datetime类型以便正确排序
        results_df['_sort_time'] = pd.to_datetime(results_df['StartTimestamp'].str.replace(' UTC\\+8', '', regex=True))
        results_df = results_df.sort_values('_sort_time').drop('_sort_time', axis=1)
        
        output_filename = "backtest_ensemble_log.csv"
        results_df.to_csv(output_filename, index=False, float_format='%.4f')
        print(f"\n详细回测日志已保存到: {output_filename}")
        
        # 调用分析和绘图函数
        try:
            analyze_and_plot_results(results_df, "backtest_ensemble")
        except Exception as e:
            print(f"⚠️ 绘图分析过程中出现错误: {e}")
            print("回测数据已保存，但图表生成失败")
        
        # 详细的性能分析
        if len(results_df) > 0:
            success_rate = len(results_df[results_df['Result'] == 1]) / len(results_df) * 100
            avg_score = results_df['Score'].mean()
            avg_profit = results_df['ProfitLoss'].mean()
            
            # 计算最大回撤
            results_df['CumulativeReturn'] = (results_df['CapitalAfter'] / initial_capital - 1) * 100
            peak = results_df['CumulativeReturn'].expanding().max()
            drawdown = results_df['CumulativeReturn'] - peak
            max_drawdown = drawdown.min()
            
            # 计算胜率和盈亏比
            winning_trades = results_df[results_df['ProfitLoss'] > 0]
            losing_trades = results_df[results_df['ProfitLoss'] < 0]
            
            win_rate = len(winning_trades) / len(results_df) * 100 if len(results_df) > 0 else 0
            avg_win = winning_trades['ProfitLoss'].mean() if len(winning_trades) > 0 else 0
            avg_loss = losing_trades['ProfitLoss'].mean() if len(losing_trades) > 0 else 0
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            
            print(f"\n详细性能指标:")
            print(f"  预测成功率: {success_rate:.1f}%")
            print(f"  交易胜率: {win_rate:.1f}%")
            print(f"  平均得分: {avg_score:+.3f}")
            print(f"  平均盈亏: ${avg_profit:+.2f}")
            print(f"  平均盈利: ${avg_win:+.2f}")
            print(f"  平均亏损: ${avg_loss:+.2f}")
            print(f"  盈亏比: {profit_factor:.2f}")
            print(f"  最大回撤(计算): {max_drawdown:.2f}%")
            print(f"  最大回撤(实时): {backtester.max_drawdown_pct:.2f}%")
            
            # 时间分析
            if 'StartHour' in results_df.columns:
                print(f"\n时间分析:")
                hourly_performance = results_df.groupby('StartHour')['ProfitLoss'].agg(['count', 'mean', 'sum'])
                best_hour = hourly_performance['mean'].idxmax()
                worst_hour = hourly_performance['mean'].idxmin()
                print(f"  最佳交易时间: {best_hour}时 (平均盈亏: ${hourly_performance.loc[best_hour, 'mean']:+.2f})")
                print(f"  最差交易时间: {worst_hour}时 (平均盈亏: ${hourly_performance.loc[worst_hour, 'mean']:+.2f})")
                
                daily_performance = results_df.groupby('StartDayName')['ProfitLoss'].agg(['count', 'mean', 'sum'])
                best_day = daily_performance['mean'].idxmax()
                worst_day = daily_performance['mean'].idxmin()
                print(f"  最佳交易日: {best_day} (平均盈亏: ${daily_performance.loc[best_day, 'mean']:+.2f})")
                print(f"  最差交易日: {worst_day} (平均盈亏: ${daily_performance.loc[worst_day, 'mean']:+.2f})")
    else:
        print("\n没有生成任何预测结果。")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="集成模型回测脚本",
        epilog="""
示例用法:
  python hunhe/backtest_ensemble.py --ensemble-model models/ensemble_model.joblib --ensemble-config models/ensemble_config.json --symbol ETHUSDT --interval 5m --initial-capital 1000 --risk-per-trade 1.0

注意: 需要确保集成模型文件和配置文件存在，并且hunhe目录下的相关模块可以正常导入。
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # 模型相关参数
    parser.add_argument("--ensemble-model", required=True, help="集成模型文件路径 (.joblib)")
    parser.add_argument("--ensemble-config", required=True, help="集成模型配置文件路径 (.json)")
    
    # 数据相关参数
    parser.add_argument("--symbol", default="ETHUSDT", help="交易对符号")
    parser.add_argument("--interval", default="5m", help="K线间隔")
    parser.add_argument("--market", default="spot", choices=["spot", "futures"], help="市场类型")
    parser.add_argument("--db", default="coin_data.db", help="SQLite数据库路径")
    parser.add_argument("--start-time", help="回测开始时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--end-time", help="回测结束时间(北京时间, YYYY-MM-DD HH:MM)")
    
    # 交易相关参数
    parser.add_argument("--initial-capital", type=float, default=1000, help="初始资金")
    parser.add_argument("--risk-per-trade", type=float, default=1.0, help="单次交易风险比例(%%)")
    parser.add_argument("--stop-loss", type=float, help="止损百分比 (例如, 输入 2.5 表示 2.5%%)")
    parser.add_argument("--max-active-predictions", type=int, default=1000, help="最大同时活跃预测数")
    
    # 时间过滤参数
    parser.add_argument("--chushou-file", default="chushou.json", help="出手时间配置文件路径")
    parser.add_argument("--use-chushou", action='store_true', help="启用出手时间过滤")
    
    args = parser.parse_args()
    
    # 检查模型文件是否存在
    if not os.path.exists(args.ensemble_model):
        print(f"❌ 集成模型文件不存在: {args.ensemble_model}")
        return 1
    
    if not os.path.exists(args.ensemble_config):
        print(f"❌ 集成配置文件不存在: {args.ensemble_config}")
        return 1
    
    # 解析时间参数
    start_time = parse_time_input(args.start_time) if args.start_time else None
    end_time = parse_time_input(args.end_time) if args.end_time else None
    
    # 加载数据
    df = load_data_from_sqlite(args.db, args.symbol, args.interval, args.market, start_time, end_time)
    
    if df is None or df.empty:
        print("❌ 未加载到任何数据，程序退出。")
        return 1
    
    # 准备回测参数
    backtest_params = {
        'initial_capital': args.initial_capital,
        'risk_per_trade_pct': args.risk_per_trade,
        'stop_loss_pct': args.stop_loss,
        'max_active_predictions': args.max_active_predictions,
        'chushou_file': args.chushou_file if args.use_chushou else None
    }
    
    # 运行回测
    try:
        run_ensemble_backtest(args.ensemble_model, args.ensemble_config, df, **backtest_params)
        return 0
    except Exception as e:
        print(f"❌ 回测过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    exit(main())