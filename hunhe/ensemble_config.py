"""
集成模型配置管理模块
"""

import json
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
import logging
from .utils import validate_model_files, setup_logging

logger = setup_logging()

@dataclass
class ModelConfig:
    """单个模型的配置"""
    model_path: str
    config_path: str
    model_type: str  # 'standalone' or 'money'
    
    def validate(self) -> bool:
        """验证模型配置"""
        if not validate_model_files(self.model_path, self.config_path):
            logger.error(f"模型文件不存在: {self.model_path} 或 {self.config_path}")
            return False
        
        if self.model_type not in ['standalone', 'money']:
            logger.error(f"不支持的模型类型: {self.model_type}")
            return False
            
        return True

@dataclass 
class FeatureSelectionConfig:
    """特征选择配置"""
    use_traditional_features: bool = True
    traditional_feature_count: int = 20
    use_model_probabilities: bool = True
    use_model_predictions: bool = True
    feature_importance_threshold: float = 0.001
    
@dataclass
class TrainingConfig:
    """训练配置"""
    mode: str = 'train'  # 'train', 'validate', 'test', 'full'
    test_size: float = 0.2
    validation_size: float = 0.1
    random_state: int = 42
    cv_folds: int = 5
    early_stopping_rounds: int = 100
    optimize_hyperparameters: bool = False
    n_trials: int = 100
    
@dataclass
class DataSourceConfig:
    """数据源配置"""
    database_path: str = 'coin_data.db'
    symbol: str = 'ETHUSDT'
    timeframe_minutes: int = 5
    table_name: Optional[str] = None
    
@dataclass
class TimeRangeConfig:
    """时间范围配置"""
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    train_days: Optional[int] = None
    
@dataclass
class OutputConfig:
    """输出配置"""
    output_dir: str = "models"
    save_features: bool = False
    save_analysis: bool = False

@dataclass
class EnsembleConfig:
    """集成模型完整配置"""
    name: str
    timeframe_minutes: int
    up_threshold: float = 0.02
    down_threshold: float = 0.02
    max_lookforward_minutes: int = 240
    models: Dict[str, ModelConfig] = None
    feature_selection: FeatureSelectionConfig = None
    training: TrainingConfig = None
    data_source: DataSourceConfig = None
    time_range: TimeRangeConfig = None
    output: OutputConfig = None
    
    def __post_init__(self):
        """初始化后处理"""
        # 初始化默认值
        if self.models is None:
            self.models = {}
        if self.feature_selection is None:
            self.feature_selection = FeatureSelectionConfig()
        if self.training is None:
            self.training = TrainingConfig()
        if self.data_source is None:
            self.data_source = DataSourceConfig(timeframe_minutes=self.timeframe_minutes)
        if self.time_range is None:
            self.time_range = TimeRangeConfig()
        if self.output is None:
            self.output = OutputConfig()
        
        # 确保models字典中的值是ModelConfig对象
        if self.models:
            for key, model_config in self.models.items():
                if isinstance(model_config, dict):
                    self.models[key] = ModelConfig(**model_config)
        
        # 确保各配置对象是正确的类型
        if isinstance(self.feature_selection, dict):
            self.feature_selection = FeatureSelectionConfig(**self.feature_selection)
            
        if isinstance(self.training, dict):
            self.training = TrainingConfig(**self.training)
            
        if isinstance(self.data_source, dict):
            self.data_source = DataSourceConfig(**self.data_source)
            
        if isinstance(self.time_range, dict):
            self.time_range = TimeRangeConfig(**self.time_range)
            
        if isinstance(self.output, dict):
            self.output = OutputConfig(**self.output)
    
    def validate(self) -> bool:
        """验证完整配置"""
        # 验证基本参数
        if self.timeframe_minutes <= 0:
            logger.error("时间框架必须大于0")
            return False
            
        if not (0 < self.up_threshold < 1 and 0 < self.down_threshold < 1):
            logger.error("上涨阈值和下跌阈值都必须在0和1之间")
            return False
            
        if self.max_lookforward_minutes <= 0:
            logger.error("最大前瞻时间必须大于0")
            return False
        
        # 验证模型配置
        if not self.models:
            logger.error("必须至少配置一个模型")
            return False
            
        for model_name, model_config in self.models.items():
            if not model_config.validate():
                logger.error(f"模型 {model_name} 配置验证失败")
                return False
        
        # 验证输出目录
        if not os.path.exists(self.output.output_dir):
            logger.warning(f"输出目录不存在，将创建: {self.output.output_dir}")
            os.makedirs(self.output.output_dir, exist_ok=True)
            
        # 验证数据源配置
        if not os.path.exists(self.data_source.database_path):
            logger.error(f"数据库文件不存在: {self.data_source.database_path}")
            return False
            
        # 验证训练模式
        valid_modes = ['train', 'validate', 'test', 'full']
        if self.training.mode not in valid_modes:
            logger.error(f"无效的训练模式: {self.training.mode}，支持的模式: {valid_modes}")
            return False
            
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)
    
    def save(self, config_path: str) -> None:
        """保存配置到文件"""
        config_dict = self.to_dict()
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
        logger.info(f"配置已保存到: {config_path}")
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'EnsembleConfig':
        """从字典创建配置对象"""
        return cls(**config_dict)
    
    @classmethod
    def from_file(cls, config_path: str) -> 'EnsembleConfig':
        """从文件加载配置"""
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
            
        return cls.from_dict(config_dict)
    
    def get_model_config(self, model_type: str) -> Optional[ModelConfig]:
        """获取指定类型的模型配置"""
        for model_config in self.models.values():
            if model_config.model_type == model_type:
                return model_config
        return None
    
    def get_feature_columns(self) -> List[str]:
        """获取预期的特征列名"""
        feature_cols = []
        
        # 添加模型预测特征
        if self.feature_selection.use_model_probabilities:
            if 'standalone' in [m.model_type for m in self.models.values()]:
                feature_cols.extend([
                    'standalone_prob_lowest',
                    'standalone_prob_neutral', 
                    'standalone_prob_highest'
                ])
            
            if 'money' in [m.model_type for m in self.models.values()]:
                feature_cols.extend([
                    'money_prob_down',
                    'money_prob_up'
                ])
        
        if self.feature_selection.use_model_predictions:
            if 'standalone' in [m.model_type for m in self.models.values()]:
                feature_cols.append('standalone_prediction')
            
            if 'money' in [m.model_type for m in self.models.values()]:
                feature_cols.append('money_prediction')
        
        return feature_cols

def create_default_config(
    name: str,
    timeframe_minutes: int = 5,
    standalone_model_path: str = None,
    money_model_path: str = None
) -> EnsembleConfig:
    """
    创建默认配置
    
    Args:
        name: 配置名称
        timeframe_minutes: 时间框架（分钟）
        standalone_model_path: standalone模型路径
        money_model_path: money模型路径
        
    Returns:
        默认的EnsembleConfig对象
    """
    models = {}
    
    if standalone_model_path:
        models['standalone'] = ModelConfig(
            model_path=standalone_model_path,
            config_path=standalone_model_path.replace('_model.joblib', '_config.json').replace('.joblib', '_config.json'),
            model_type='standalone'
        )
    
    if money_model_path:
        models['money'] = ModelConfig(
            model_path=money_model_path,
            config_path=money_model_path.replace('_model.joblib', '_config.json').replace('.joblib', '_config.json'),
            model_type='money'
        )
    
    return EnsembleConfig(
        name=name,
        timeframe_minutes=timeframe_minutes,
        up_threshold=0.02,
        down_threshold=0.02,
        max_lookforward_minutes=240,
        models=models,
        feature_selection=FeatureSelectionConfig(),
        training=TrainingConfig()
    )

def load_config(config_path: str) -> EnsembleConfig:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        EnsembleConfig对象
        
    Raises:
        FileNotFoundError: 配置文件不存在
        ValueError: 配置验证失败
    """
    config = EnsembleConfig.from_file(config_path)
    
    if not config.validate():
        raise ValueError(f"配置验证失败: {config_path}")
    
    logger.info(f"成功加载配置: {config_path}")
    return config