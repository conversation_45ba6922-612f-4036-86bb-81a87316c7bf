#!/usr/bin/env python3
"""
ModelLoader 类的单元测试

测试模型加载、验证和预测功能
"""

import unittest
import pandas as pd
import numpy as np
import tempfile
import os
import json
import joblib
from unittest.mock import Mock, patch, MagicMock
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hunhe.model_loader import ModelLoader, ModelLoadError, ModelPredictionError
from hunhe.ensemble_config import ModelConfig


class TestModelLoader(unittest.TestCase):
    """ModelLoader 单元测试类"""
    
    def setUp(self):
        """测试前的设置"""
        self.model_loader = ModelLoader()
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建模拟的模型和配置文件
        self.standalone_model_path = os.path.join(self.temp_dir, "standalone_model.joblib")
        self.standalone_config_path = os.path.join(self.temp_dir, "standalone_config.json")
        self.money_model_path = os.path.join(self.temp_dir, "money_model.joblib")
        self.money_config_path = os.path.join(self.temp_dir, "money_config.json")
        
        # 创建模拟模型
        self.mock_standalone_model = Mock()
        self.mock_standalone_model.predict_proba.return_value = np.array([[0.2, 0.5, 0.3], [0.1, 0.6, 0.3]])
        self.mock_standalone_model.predict.return_value = np.array([1, 1])
        
        self.mock_money_model = Mock()
        self.mock_money_model.predict_proba.return_value = np.array([[0.4, 0.6], [0.3, 0.7]])
        self.mock_money_model.predict.return_value = np.array([1, 1])
        
        # 保存模拟模型
        joblib.dump(self.mock_standalone_model, self.standalone_model_path)
        joblib.dump(self.mock_money_model, self.money_model_path)
        
        # 创建模拟配置
        self.standalone_config = {
            "model_type": "standalone",
            "class_labels": {"0": "lowest", "1": "neutral", "2": "highest"},
            "feature_list": ["feature1", "feature2", "feature3"],
            "timeframe_minutes": 5
        }
        
        self.money_config = {
            "model_type": "money_management",
            "up_threshold": 0.02,
            "down_threshold": -0.02,
            "feature_list": ["feature1", "feature2", "feature3"],
            "timeframe_minutes": 5
        }
        
        # 保存配置文件
        with open(self.standalone_config_path, 'w') as f:
            json.dump(self.standalone_config, f)
        
        with open(self.money_config_path, 'w') as f:
            json.dump(self.money_config, f)
    
    def tearDown(self):
        """测试后的清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_init(self):
        """测试初始化"""
        loader = ModelLoader()
        self.assertIsNone(loader.standalone_model)
        self.assertIsNone(loader.standalone_config)
        self.assertIsNone(loader.money_model)
        self.assertIsNone(loader.money_config)
        self.assertEqual(loader.loaded_models, {})
    
    def test_load_standalone_model_success(self):
        """测试成功加载 standalone 模型"""
        result = self.model_loader.load_standalone_model(
            self.standalone_model_path, 
            self.standalone_config_path
        )
        
        self.assertTrue(result)
        self.assertIsNotNone(self.model_loader.standalone_model)
        self.assertIsNotNone(self.model_loader.standalone_config)
        self.assertTrue(self.model_loader.is_model_loaded('standalone'))
        self.assertIn('standalone', self.model_loader.loaded_models)
    
    def test_load_standalone_model_file_not_found(self):
        """测试加载不存在的 standalone 模型文件"""
        with self.assertRaises(ModelLoadError):
            self.model_loader.load_standalone_model(
                "nonexistent_model.joblib",
                "nonexistent_config.json"
            )
    
    def test_load_standalone_model_invalid_config(self):
        """测试加载无效配置的 standalone 模型"""
        # 创建无效配置（缺少 class_labels）
        invalid_config = {"model_type": "standalone", "feature_list": ["f1"]}
        invalid_config_path = os.path.join(self.temp_dir, "invalid_config.json")
        
        with open(invalid_config_path, 'w') as f:
            json.dump(invalid_config, f)
        
        with self.assertRaises(ModelLoadError):
            self.model_loader.load_standalone_model(
                self.standalone_model_path,
                invalid_config_path
            )
    
    def test_load_money_model_success(self):
        """测试成功加载 money 模型"""
        result = self.model_loader.load_money_model(
            self.money_model_path,
            self.money_config_path
        )
        
        self.assertTrue(result)
        self.assertIsNotNone(self.model_loader.money_model)
        self.assertIsNotNone(self.model_loader.money_config)
        self.assertTrue(self.model_loader.is_model_loaded('money'))
        self.assertIn('money', self.model_loader.loaded_models)
    
    def test_load_money_model_file_not_found(self):
        """测试加载不存在的 money 模型文件"""
        with self.assertRaises(ModelLoadError):
            self.model_loader.load_money_model(
                "nonexistent_model.joblib",
                "nonexistent_config.json"
            )
    
    def test_load_models_from_config(self):
        """测试从配置对象加载多个模型"""
        models_config = {
            'standalone': ModelConfig(
                model_path=self.standalone_model_path,
                config_path=self.standalone_config_path,
                model_type='standalone'
            ),
            'money': ModelConfig(
                model_path=self.money_model_path,
                config_path=self.money_config_path,
                model_type='money'
            )
        }
        
        result = self.model_loader.load_models_from_config(models_config)
        
        self.assertTrue(result)
        self.assertTrue(self.model_loader.is_model_loaded('standalone'))
        self.assertTrue(self.model_loader.is_model_loaded('money'))
    
    def test_validate_loaded_models(self):
        """测试验证已加载的模型"""
        # 没有加载任何模型时
        self.assertFalse(self.model_loader.validate_loaded_models())
        
        # 加载模型后
        self.model_loader.load_standalone_model(
            self.standalone_model_path,
            self.standalone_config_path
        )
        self.assertTrue(self.model_loader.validate_loaded_models())
    
    def test_get_model_info(self):
        """测试获取模型信息"""
        # 未加载模型时
        self.assertIsNone(self.model_loader.get_model_info('standalone'))
        
        # 加载模型后
        self.model_loader.load_standalone_model(
            self.standalone_model_path,
            self.standalone_config_path
        )
        
        info = self.model_loader.get_model_info('standalone')
        self.assertIsNotNone(info)
        self.assertIn('model', info)
        self.assertIn('config', info)
        self.assertIn('model_path', info)
        self.assertIn('config_path', info)
    
    def test_get_feature_list(self):
        """测试获取特征列表"""
        # 未加载模型时
        self.assertEqual(self.model_loader.get_feature_list('standalone'), [])
        
        # 加载模型后
        self.model_loader.load_standalone_model(
            self.standalone_model_path,
            self.standalone_config_path
        )
        
        features = self.model_loader.get_feature_list('standalone')
        self.assertEqual(features, ["feature1", "feature2", "feature3"])
    
    def test_get_loaded_model_types(self):
        """测试获取已加载的模型类型"""
        # 初始状态
        self.assertEqual(self.model_loader.get_loaded_model_types(), [])
        
        # 加载一个模型
        self.model_loader.load_standalone_model(
            self.standalone_model_path,
            self.standalone_config_path
        )
        self.assertEqual(self.model_loader.get_loaded_model_types(), ['standalone'])
        
        # 加载两个模型
        self.model_loader.load_money_model(
            self.money_model_path,
            self.money_config_path
        )
        self.assertCountEqual(self.model_loader.get_loaded_model_types(), ['standalone', 'money'])
    
    def test_clear_models(self):
        """测试清除模型"""
        # 加载模型
        self.model_loader.load_standalone_model(
            self.standalone_model_path,
            self.standalone_config_path
        )
        
        # 验证模型已加载
        self.assertTrue(self.model_loader.is_model_loaded('standalone'))
        
        # 清除模型
        self.model_loader.clear_models()
        
        # 验证模型已清除
        self.assertFalse(self.model_loader.is_model_loaded('standalone'))
        self.assertEqual(self.model_loader.loaded_models, {})
        self.assertIsNone(self.model_loader.standalone_model)
        self.assertIsNone(self.model_loader.standalone_config)
    
    def test_predict_standalone_success(self):
        """测试 standalone 模型预测成功"""
        # 加载模型
        self.model_loader.load_standalone_model(
            self.standalone_model_path,
            self.standalone_config_path
        )
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'feature1': [1.0, 2.0],
            'feature2': [3.0, 4.0],
            'feature3': [5.0, 6.0]
        })
        
        # 进行预测
        result = self.model_loader.predict_standalone(test_data)
        
        # 验证结果
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 2)
        expected_columns = [
            'standalone_prob_lowest',
            'standalone_prob_neutral',
            'standalone_prob_highest',
            'standalone_prediction'
        ]
        for col in expected_columns:
            self.assertIn(col, result.columns)
    
    def test_predict_standalone_model_not_loaded(self):
        """测试未加载模型时的 standalone 预测"""
        test_data = pd.DataFrame({'feature1': [1.0]})
        
        with self.assertRaises(ModelPredictionError):
            self.model_loader.predict_standalone(test_data)
    
    def test_predict_standalone_missing_features(self):
        """测试缺少特征时的 standalone 预测"""
        # 加载模型
        self.model_loader.load_standalone_model(
            self.standalone_model_path,
            self.standalone_config_path
        )
        
        # 创建缺少特征的测试数据
        test_data = pd.DataFrame({
            'feature1': [1.0, 2.0],
            'feature2': [3.0, 4.0]
            # 缺少 feature3
        })
        
        with self.assertRaises(ModelPredictionError):
            self.model_loader.predict_standalone(test_data)
    
    def test_predict_money_success(self):
        """测试 money 模型预测成功"""
        # 加载模型
        self.model_loader.load_money_model(
            self.money_model_path,
            self.money_config_path
        )
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'feature1': [1.0, 2.0],
            'feature2': [3.0, 4.0],
            'feature3': [5.0, 6.0]
        })
        
        # 进行预测
        result = self.model_loader.predict_money(test_data)
        
        # 验证结果
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 2)
        expected_columns = [
            'money_prob_down',
            'money_prob_up',
            'money_prediction'
        ]
        for col in expected_columns:
            self.assertIn(col, result.columns)
    
    def test_predict_money_model_not_loaded(self):
        """测试未加载模型时的 money 预测"""
        test_data = pd.DataFrame({'feature1': [1.0]})
        
        with self.assertRaises(ModelPredictionError):
            self.model_loader.predict_money(test_data)
    
    def test_predict_all_success(self):
        """测试所有模型预测成功"""
        # 加载两个模型
        self.model_loader.load_standalone_model(
            self.standalone_model_path,
            self.standalone_config_path
        )
        self.model_loader.load_money_model(
            self.money_model_path,
            self.money_config_path
        )
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'feature1': [1.0, 2.0],
            'feature2': [3.0, 4.0],
            'feature3': [5.0, 6.0]
        })
        
        # 进行预测
        result = self.model_loader.predict_all(test_data)
        
        # 验证结果
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 2)
        
        # 验证包含所有模型的预测列
        standalone_cols = [
            'standalone_prob_lowest',
            'standalone_prob_neutral',
            'standalone_prob_highest',
            'standalone_prediction'
        ]
        money_cols = [
            'money_prob_down',
            'money_prob_up',
            'money_prediction'
        ]
        
        for col in standalone_cols + money_cols:
            self.assertIn(col, result.columns)
    
    def test_predict_all_no_models(self):
        """测试没有加载模型时的所有模型预测"""
        test_data = pd.DataFrame({'feature1': [1.0]})
        
        with self.assertRaises(ModelPredictionError):
            self.model_loader.predict_all(test_data)
    
    def test_predict_batch_success(self):
        """测试批量预测成功"""
        # 加载模型
        self.model_loader.load_standalone_model(
            self.standalone_model_path,
            self.standalone_config_path
        )
        
        # 创建大量测试数据
        test_data = pd.DataFrame({
            'feature1': np.random.randn(100),
            'feature2': np.random.randn(100),
            'feature3': np.random.randn(100)
        })
        
        # 进行批量预测
        results = self.model_loader.predict_batch(test_data, batch_size=30)
        
        # 验证结果
        self.assertIsInstance(results, dict)
        self.assertIn('standalone', results)
        self.assertEqual(len(results['standalone']), 100)
    
    def test_predict_batch_empty_data(self):
        """测试空数据的批量预测"""
        empty_data = pd.DataFrame()
        
        results = self.model_loader.predict_batch(empty_data)
        
        self.assertEqual(results, {})
    
    def test_get_prediction_summary(self):
        """测试获取预测摘要"""
        # 创建模拟预测结果
        predictions_df = pd.DataFrame({
            'standalone_prob_lowest': [0.2, 0.1, 0.3],
            'standalone_prob_neutral': [0.5, 0.6, 0.4],
            'standalone_prob_highest': [0.3, 0.3, 0.3],
            'standalone_prediction': [1, 1, 0],
            'money_prob_down': [0.4, 0.3, 0.6],
            'money_prob_up': [0.6, 0.7, 0.4],
            'money_prediction': [1, 1, 0]
        })
        
        summary = self.model_loader.get_prediction_summary(predictions_df)
        
        # 验证摘要结构
        self.assertIn('standalone', summary)
        self.assertIn('money', summary)
        
        # 验证 standalone 摘要
        standalone_summary = summary['standalone']
        self.assertIn('total_predictions', standalone_summary)
        self.assertIn('class_distribution', standalone_summary)
        self.assertIn('class_percentages', standalone_summary)
        self.assertEqual(standalone_summary['total_predictions'], 3)
        
        # 验证 money 摘要
        money_summary = summary['money']
        self.assertIn('total_predictions', money_summary)
        self.assertIn('class_distribution', money_summary)
        self.assertIn('class_percentages', money_summary)
        self.assertEqual(money_summary['total_predictions'], 3)


class TestModelLoaderEdgeCases(unittest.TestCase):
    """ModelLoader 边界条件测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.model_loader = ModelLoader()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后的清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_load_model_with_corrupted_file(self):
        """测试加载损坏的模型文件"""
        # 创建损坏的模型文件
        corrupted_model_path = os.path.join(self.temp_dir, "corrupted_model.joblib")
        with open(corrupted_model_path, 'w') as f:
            f.write("this is not a valid joblib file")
        
        # 创建有效的配置文件
        config_path = os.path.join(self.temp_dir, "config.json")
        config = {
            "model_type": "standalone",
            "class_labels": {"0": "lowest", "1": "neutral", "2": "highest"},
            "feature_list": ["feature1"]
        }
        with open(config_path, 'w') as f:
            json.dump(config, f)
        
        with self.assertRaises(ModelLoadError):
            self.model_loader.load_standalone_model(corrupted_model_path, config_path)
    
    def test_load_model_with_invalid_json_config(self):
        """测试加载无效JSON配置文件"""
        # 创建有效的模型文件
        model_path = os.path.join(self.temp_dir, "model.joblib")
        mock_model = Mock()
        joblib.dump(mock_model, model_path)
        
        # 创建无效的JSON配置文件
        invalid_config_path = os.path.join(self.temp_dir, "invalid_config.json")
        with open(invalid_config_path, 'w') as f:
            f.write("{ invalid json content")
        
        with self.assertRaises(ModelLoadError):
            self.model_loader.load_standalone_model(model_path, invalid_config_path)
    
    def test_predict_with_nan_values(self):
        """测试包含NaN值的预测"""
        # 创建模拟模型和配置
        model_path = os.path.join(self.temp_dir, "model.joblib")
        config_path = os.path.join(self.temp_dir, "config.json")
        
        mock_model = Mock()
        mock_model.predict_proba.return_value = np.array([[0.2, 0.5, 0.3]])
        mock_model.predict.return_value = np.array([1])
        joblib.dump(mock_model, model_path)
        
        config = {
            "model_type": "standalone",
            "class_labels": {"0": "lowest", "1": "neutral", "2": "highest"},
            "feature_list": ["feature1", "feature2"]
        }
        with open(config_path, 'w') as f:
            json.dump(config, f)
        
        # 加载模型
        self.model_loader.load_standalone_model(model_path, config_path)
        
        # 创建包含NaN的测试数据
        test_data = pd.DataFrame({
            'feature1': [1.0, np.nan],
            'feature2': [3.0, 4.0]
        })
        
        # 预测应该成功（NaN会被处理）
        result = self.model_loader.predict_standalone(test_data)
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 2)


if __name__ == '__main__':
    unittest.main()