"""
特征生成器模块

负责生成集成模型的特征，包括传统技术指标和模型预测特征
"""

import pandas as pd
import numpy as np
import sys
import os
from typing import Dict, Any, List, Optional, Tuple
import logging
from .model_loader import ModelLoader, ModelPredictionError
from .ensemble_config import EnsembleConfig
from .utils import setup_logging, create_feature_name, FEATURE_PREFIXES

# 导入现有的特征计算函数
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    # 导入 standalone 模型的特征计算函数
    from oneway.standalone_utils import calculate_features as calculate_standalone_features
    # 导入 money management 模型的特征计算函数  
    from model_utils_815 import calculate_features as calculate_money_features
    logger = logging.getLogger(__name__)
    logger.info("成功导入特征计算函数")
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.error(f"无法导入特征计算函数: {e}")
    logger.error("请确保 oneway/standalone_utils.py 和 model_utils_815.py 存在")
    raise

logger = setup_logging()

class FeatureGenerationError(Exception):
    """特征生成错误"""
    pass

class FeatureGenerator:
    """
    特征生成器类
    
    负责生成集成模型训练所需的所有特征，包括：
    1. 传统技术指标特征（复用现有的 FinancialFeatureEngine）
    2. 模型预测特征（从 standalone 和 money 模型生成）
    3. 特征合并和验证
    """
    
    def __init__(self, model_loader: ModelLoader, config: EnsembleConfig):
        """
        初始化特征生成器
        
        Args:
            model_loader: 模型加载器实例
            config: 集成模型配置
        """
        self.model_loader = model_loader
        self.config = config
        self._feature_cache = {}
        self._cache_enabled = True
        
        logger.info("FeatureGenerator 初始化完成")
    
    def _prepare_base_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        准备基础数据，确保格式正确
        
        Args:
            df: 原始K线数据
            
        Returns:
            格式化后的DataFrame
        """
        # 确保数据有正确的列名
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        
        # 检查并重命名列（处理大小写问题）
        column_mapping = {}
        for col in df.columns:
            col_lower = col.lower()
            if col_lower in ['open', 'high', 'low', 'close', 'volume']:
                column_mapping[col] = col_lower
        
        if column_mapping:
            df = df.rename(columns=column_mapping)
        
        # 确保索引是datetime类型
        if not isinstance(df.index, pd.DatetimeIndex):
            if 'timestamp' in df.columns:
                df.index = pd.to_datetime(df['timestamp'])
            elif 'Timestamp' in df.columns:
                df.index = pd.to_datetime(df['Timestamp'])
        
        return df
    
    def calculate_base_features(self, df: pd.DataFrame, timeframe_minutes: int) -> pd.DataFrame:
        """
        计算基础技术指标特征
        
        使用现有模型的特征计算方法，确保完全兼容
        
        Args:
            df: 原始K线数据，必须包含 ['open', 'high', 'low', 'close', 'volume'] 列
            timeframe_minutes: 时间框架（分钟）
            
        Returns:
            包含技术指标的DataFrame
            
        Raises:
            FeatureGenerationError: 特征计算失败
        """
        try:
            logger.info(f"开始计算基础技术指标，时间框架: {timeframe_minutes} 分钟，数据行数: {len(df)}")
            
            # 准备基础数据
            df_prepared = self._prepare_base_data(df.copy())
            
            # 验证输入数据
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = set(required_columns) - set(df_prepared.columns)
            if missing_columns:
                raise FeatureGenerationError(f"输入数据缺少必需列: {missing_columns}")
            
            if df_prepared.empty:
                raise FeatureGenerationError("输入数据为空")
            
            # 检查数据质量
            null_counts = df_prepared[required_columns].isnull().sum()
            if null_counts.sum() > 0:
                logger.warning(f"输入数据存在缺失值: {null_counts[null_counts > 0].to_dict()}")
                # 前向填充缺失值
                df_prepared = df_prepared.fillna(method='ffill').fillna(method='bfill')
            
            # 使用现有的特征计算函数
            # 我们需要计算两种特征集合以支持不同的模型
            logger.debug("计算基础特征集合")
            
            # 首先使用money management的特征计算（更全面）
            features_df = calculate_money_features(df_prepared, timeframe_minutes)
            
            # 然后添加standalone模型需要的额外特征
            try:
                standalone_features = calculate_standalone_features(df_prepared.copy(), timeframe_minutes)
                
                # 合并standalone特有的特征
                standalone_only_features = []
                for col in standalone_features.columns:
                    if col not in features_df.columns and col not in ['open', 'high', 'low', 'close', 'volume', 'timestamp', 'Timestamp']:
                        standalone_only_features.append(col)
                
                if standalone_only_features:
                    logger.debug(f"添加standalone特有特征: {len(standalone_only_features)} 个")
                    for col in standalone_only_features:
                        features_df[col] = standalone_features[col]
                        
            except Exception as e:
                logger.warning(f"添加standalone特征时出错: {str(e)}")
                # 继续使用基础特征
            
            # 验证特征计算结果
            if features_df.empty:
                raise FeatureGenerationError("特征计算结果为空")
            
            # 检查特征数量
            original_cols = len(df_prepared.columns)
            new_cols = len(features_df.columns)
            added_features = new_cols - original_cols
            
            logger.info(f"基础特征计算完成，新增 {added_features} 个特征，总计 {new_cols} 列")
            
            return features_df
            
        except Exception as e:
            error_msg = f"计算基础技术指标失败: {str(e)}"
            logger.error(error_msg)
            raise FeatureGenerationError(error_msg) from e
    
    def generate_model_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成模型预测特征
        
        从已加载的 standalone 和 money management 模型生成概率特征
        这些概率将作为新的特征用于训练集成模型
        
        Args:
            df: 包含基础特征的DataFrame
            
        Returns:
            包含模型预测特征的DataFrame，列名格式：
            - standalone_prob_lowest, standalone_prob_neutral, standalone_prob_highest, standalone_prediction
            - money_prob_down, money_prob_up, money_prediction
            
        Raises:
            FeatureGenerationError: 模型特征生成失败
        """
        try:
            logger.info(f"开始生成模型预测特征，数据行数: {len(df)}")
            
            if df.empty:
                raise FeatureGenerationError("输入数据为空")
            
            # 获取已加载的模型类型
            loaded_models = self.model_loader.get_loaded_model_types()
            if not loaded_models:
                logger.warning("没有加载任何模型，返回空的模型特征")
                return pd.DataFrame(index=df.index)
            
            logger.info(f"已加载的模型: {loaded_models}")
            
            # 初始化模型特征DataFrame
            model_features = pd.DataFrame(index=df.index)
            
            # 处理 standalone 模型 (3类分类)
            if 'standalone' in loaded_models:
                try:
                    logger.debug("生成 standalone 模型特征")
                    standalone_pred = self.model_loader.predict_standalone(df)
                    
                    # 验证standalone预测结果的格式
                    if isinstance(standalone_pred, pd.DataFrame):
                        if len(standalone_pred.columns) >= 3:
                            # 假设前3列是概率，第4列是预测类别
                            model_features['standalone_prob_lowest'] = standalone_pred.iloc[:, 0]
                            model_features['standalone_prob_neutral'] = standalone_pred.iloc[:, 1] 
                            model_features['standalone_prob_highest'] = standalone_pred.iloc[:, 2]
                            
                            if len(standalone_pred.columns) >= 4:
                                model_features['standalone_prediction'] = standalone_pred.iloc[:, 3]
                            else:
                                # 如果没有预测类别，使用最大概率的索引
                                model_features['standalone_prediction'] = standalone_pred.iloc[:, :3].idxmax(axis=1)
                        else:
                            logger.warning(f"Standalone模型预测结果列数不足: {len(standalone_pred.columns)}")
                    else:
                        logger.warning(f"Standalone模型预测结果格式不正确: {type(standalone_pred)}")
                    
                    logger.info(f"Standalone 模型特征生成完成")
                    
                except ModelPredictionError as e:
                    logger.error(f"Standalone 模型预测失败: {str(e)}")
                except Exception as e:
                    logger.error(f"处理Standalone模型预测时出错: {str(e)}")
            
            # 处理 money management 模型 (2类分类)
            if 'money' in loaded_models:
                try:
                    logger.debug("生成 money management 模型特征")
                    money_pred = self.model_loader.predict_money(df)
                    
                    # 验证money预测结果的格式
                    if isinstance(money_pred, pd.DataFrame):
                        if len(money_pred.columns) >= 2:
                            # 假设前2列是概率，第3列是预测类别
                            model_features['money_prob_down'] = money_pred.iloc[:, 0]
                            model_features['money_prob_up'] = money_pred.iloc[:, 1]
                            
                            if len(money_pred.columns) >= 3:
                                model_features['money_prediction'] = money_pred.iloc[:, 2]
                            else:
                                # 如果没有预测类别，使用最大概率的索引
                                model_features['money_prediction'] = money_pred.iloc[:, :2].idxmax(axis=1)
                        else:
                            logger.warning(f"Money模型预测结果列数不足: {len(money_pred.columns)}")
                    else:
                        logger.warning(f"Money模型预测结果格式不正确: {type(money_pred)}")
                    
                    logger.info(f"Money management 模型特征生成完成")
                    
                except ModelPredictionError as e:
                    logger.error(f"Money management 模型预测失败: {str(e)}")
                except Exception as e:
                    logger.error(f"处理Money模型预测时出错: {str(e)}")
            
            # 验证生成的特征
            if model_features.empty or len(model_features.columns) == 0:
                logger.warning("没有成功生成任何模型特征")
                return pd.DataFrame(index=df.index)
            
            logger.info(f"模型特征生成完成，共 {len(model_features.columns)} 个特征: {list(model_features.columns)}")
            return model_features
                
        except Exception as e:
            error_msg = f"生成模型预测特征失败: {str(e)}"
            logger.error(error_msg)
            raise FeatureGenerationError(error_msg) from e
    
    def combine_features(self, base_features: pd.DataFrame, 
                        model_features: pd.DataFrame) -> pd.DataFrame:
        """
        合并传统技术指标与模型预测特征
        
        Args:
            base_features: 基础技术指标特征
            model_features: 模型预测特征
            
        Returns:
            合并后的特征DataFrame
            
        Raises:
            FeatureGenerationError: 特征合并失败
        """
        try:
            logger.info(f"开始合并特征，基础特征: {len(base_features.columns)} 列，模型特征: {len(model_features.columns)} 列")
            
            # 验证输入
            if base_features.empty:
                raise FeatureGenerationError("基础特征DataFrame为空")
            
            # 如果模型特征为空，只返回基础特征
            if model_features.empty:
                logger.warning("模型特征为空，只使用基础特征")
                return base_features.copy()
            
            # 检查索引对齐
            if not base_features.index.equals(model_features.index):
                logger.debug("对齐基础特征和模型特征的索引")
                # 使用内连接确保时间窗口一致
                common_index = base_features.index.intersection(model_features.index)
                if len(common_index) == 0:
                    raise FeatureGenerationError("基础特征和模型特征没有共同的时间索引")
                
                base_features = base_features.loc[common_index]
                model_features = model_features.loc[common_index]
                
                logger.info(f"索引对齐后保留 {len(common_index)} 行数据")
            
            # 合并特征
            combined_features = pd.concat([base_features, model_features], axis=1)
            
            # 验证合并结果
            if combined_features.empty:
                raise FeatureGenerationError("特征合并后结果为空")
            
            logger.info(f"特征合并完成，最终特征数: {len(combined_features.columns)} 列，数据行数: {len(combined_features)} 行")
            
            return combined_features
            
        except Exception as e:
            error_msg = f"合并特征失败: {str(e)}"
            logger.error(error_msg)
            raise FeatureGenerationError(error_msg) from e
    
    def generate_all_features(self, df: pd.DataFrame, timeframe_minutes: int) -> pd.DataFrame:
        """
        生成所有特征的完整流程
        
        包括基础特征计算、模型特征生成、特征合并
        
        Args:
            df: 原始K线数据
            timeframe_minutes: 时间框架（分钟）
            
        Returns:
            完整的特征DataFrame
            
        Raises:
            FeatureGenerationError: 特征生成失败
        """
        try:
            logger.info(f"开始完整特征生成流程，时间框架: {timeframe_minutes} 分钟")
            
            # 1. 计算基础技术指标特征
            logger.info("步骤 1: 计算基础技术指标")
            base_features = self.calculate_base_features(df, timeframe_minutes)
            
            # 2. 生成模型预测特征
            logger.info("步骤 2: 生成模型预测特征")
            model_features = self.generate_model_features(base_features)
            # 3. 合并所有特征
            logger.info("步骤 3: 合并特征")
            combined_features = self.combine_features(base_features, model_features)
            
            logger.info(f"完整特征生成完成，最终特征: {len(combined_features.columns)} 列，数据: {len(combined_features)} 行")
            
            return combined_features
            
        except Exception as e:
            error_msg = f"完整特征生成失败: {str(e)}"
            logger.error(error_msg)
            raise FeatureGenerationError(error_msg) from e
    
    def get_feature_info(self) -> Dict[str, Any]:
        """
        获取特征生成器的信息
        
        Returns:
            特征生成器信息字典
        """
        return {
            'cache_enabled': self._cache_enabled,
            'cached_features': len(self._feature_cache),
            'loaded_models': self.model_loader.get_loaded_model_types(),
            'config': {
                'use_traditional_features': self.config.feature_selection.use_traditional_features,
                'traditional_feature_count': self.config.feature_selection.traditional_feature_count,
                'use_model_probabilities': self.config.feature_selection.use_model_probabilities,
                'use_model_predictions': self.config.feature_selection.use_model_predictions
            }
        }