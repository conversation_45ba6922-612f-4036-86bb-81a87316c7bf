"""
特征生成器模块

负责生成集成模型的特征，包括传统技术指标和模型预测特征
"""

import pandas as pd
import numpy as np
import sys
import os
from typing import Dict, Any, List, Optional, Tuple
import logging
from .model_loader import ModelLoader, ModelPredictionError
from .ensemble_config import EnsembleConfig
from .utils import setup_logging, create_feature_name, FEATURE_PREFIXES

# 导入现有的特征计算引擎
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from model_utils import FinancialFeatureEngine
except ImportError:
    logger = setup_logging()
    logger.error("无法导入 FinancialFeatureEngine，请确保 model_utils.py 存在")
    raise

logger = setup_logging()

class FeatureGenerationError(Exception):
    """特征生成错误"""
    pass

class FeatureGenerator:
    """
    特征生成器类
    
    负责生成集成模型训练所需的所有特征，包括：
    1. 传统技术指标特征（复用现有的 FinancialFeatureEngine）
    2. 模型预测特征（从 standalone 和 money 模型生成）
    3. 特征合并和验证
    """
    
    def __init__(self, model_loader: ModelLoader, config: EnsembleConfig):
        """
        初始化特征生成器
        
        Args:
            model_loader: 模型加载器实例
            config: 集成模型配置
        """
        self.model_loader = model_loader
        self.config = config
        self.feature_engine = None
        self._feature_cache = {}
        self._cache_enabled = True
        
        logger.info("FeatureGenerator 初始化完成")
    
    def _get_feature_engine(self, timeframe_minutes: int) -> FinancialFeatureEngine:
        """
        获取或创建特征计算引擎
        
        Args:
            timeframe_minutes: 时间框架（分钟）
            
        Returns:
            FinancialFeatureEngine 实例
        """
        if self.feature_engine is None or self.feature_engine.timeframe != timeframe_minutes:
            self.feature_engine = FinancialFeatureEngine(timeframe_minutes)
            logger.debug(f"创建新的特征引擎，时间框架: {timeframe_minutes} 分钟")
        
        return self.feature_engine
    
    def _generate_cache_key(self, df: pd.DataFrame, operation: str, **kwargs) -> str:
        """
        生成缓存键
        
        Args:
            df: 数据DataFrame
            operation: 操作类型
            **kwargs: 额外参数
            
        Returns:
            缓存键字符串
        """
        # 使用数据的哈希值和参数生成缓存键
        data_hash = hash(tuple(df.index.tolist() + [df.shape[0], df.shape[1]]))
        params_hash = hash(tuple(sorted(kwargs.items())))
        return f"{operation}_{data_hash}_{params_hash}"
    
    def _get_from_cache(self, cache_key: str) -> Optional[pd.DataFrame]:
        """从缓存获取数据"""
        if self._cache_enabled and cache_key in self._feature_cache:
            logger.debug(f"从缓存获取特征: {cache_key}")
            return self._feature_cache[cache_key].copy()
        return None
    
    def _save_to_cache(self, cache_key: str, data: pd.DataFrame) -> None:
        """保存数据到缓存"""
        if self._cache_enabled:
            self._feature_cache[cache_key] = data.copy()
            logger.debug(f"保存特征到缓存: {cache_key}")
    
    def clear_cache(self) -> None:
        """清除特征缓存"""
        self._feature_cache.clear()
        logger.info("特征缓存已清除")
    
    def set_cache_enabled(self, enabled: bool) -> None:
        """设置是否启用缓存"""
        self._cache_enabled = enabled
        logger.info(f"特征缓存{'启用' if enabled else '禁用'}")
    
    def calculate_base_features(self, df: pd.DataFrame, timeframe_minutes: int) -> pd.DataFrame:
        """
        计算基础技术指标特征
        
        Args:
            df: 原始K线数据，必须包含 ['open', 'high', 'low', 'close', 'volume'] 列
            timeframe_minutes: 时间框架（分钟）
            
        Returns:
            包含技术指标的DataFrame
            
        Raises:
            FeatureGenerationError: 特征计算失败
        """
        try:
            logger.info(f"开始计算基础技术指标，时间框架: {timeframe_minutes} 分钟，数据行数: {len(df)}")
            
            # 检查缓存
            cache_key = self._generate_cache_key(df, "base_features", timeframe=timeframe_minutes)
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                logger.info("使用缓存的基础特征")
                return cached_result
            
            # 验证输入数据
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = set(required_columns) - set(df.columns)
            if missing_columns:
                raise FeatureGenerationError(f"输入数据缺少必需列: {missing_columns}")
            
            if df.empty:
                raise FeatureGenerationError("输入数据为空")
            
            # 检查数据质量
            null_counts = df[required_columns].isnull().sum()
            if null_counts.sum() > 0:
                logger.warning(f"输入数据存在缺失值: {null_counts[null_counts > 0].to_dict()}")
                # 前向填充缺失值
                df = df.fillna(method='ffill').fillna(method='bfill')
            
            # 获取特征引擎并计算特征
            feature_engine = self._get_feature_engine(timeframe_minutes)
            features_df = feature_engine.calculate_all_features(df.copy())
            
            # 验证特征计算结果
            if features_df.empty:
                raise FeatureGenerationError("特征计算结果为空")
            
            # 检查特征数量
            original_cols = len(df.columns)
            new_cols = len(features_df.columns)
            added_features = new_cols - original_cols
            
            logger.info(f"基础特征计算完成，新增 {added_features} 个特征，总计 {new_cols} 列")
            
            # 保存到缓存
            self._save_to_cache(cache_key, features_df)
            
            return features_df
            
        except Exception as e:
            error_msg = f"计算基础技术指标失败: {str(e)}"
            logger.error(error_msg)
            raise FeatureGenerationError(error_msg) from e
    
    def select_base_features(self, features_df: pd.DataFrame, 
                           feature_count: Optional[int] = None) -> pd.DataFrame:
        """
        选择和过滤基础特征
        
        Args:
            features_df: 包含所有特征的DataFrame
            feature_count: 要选择的特征数量，None表示使用配置中的设置
            
        Returns:
            过滤后的特征DataFrame
            
        Raises:
            FeatureGenerationError: 特征选择失败
        """
        try:
            if feature_count is None:
                feature_count = self.config.feature_selection.traditional_feature_count
            
            logger.info(f"开始选择基础特征，目标数量: {feature_count}")
            
            # 获取原始K线列
            original_columns = ['open', 'high', 'low', 'close', 'volume']
            
            # 获取计算出的特征列（排除原始列）
            feature_columns = [col for col in features_df.columns if col not in original_columns]
            
            if len(feature_columns) == 0:
                logger.warning("没有找到计算出的特征列")
                return features_df[original_columns].copy()
            
            # 如果特征数量不超过目标数量，返回所有特征
            if len(feature_columns) <= feature_count:
                logger.info(f"特征数量 ({len(feature_columns)}) 不超过目标数量，保留所有特征")
                selected_columns = original_columns + feature_columns
                return features_df[selected_columns].copy()
            
            # 特征选择策略：基于方差和相关性
            feature_data = features_df[feature_columns].copy()
            
            # 1. 移除方差过小的特征
            variances = feature_data.var()
            low_variance_threshold = variances.quantile(0.1)  # 移除方差最小的10%
            high_variance_features = variances[variances > low_variance_threshold].index.tolist()
            
            logger.debug(f"基于方差过滤后剩余 {len(high_variance_features)} 个特征")
            
            # 2. 如果还是太多，基于与价格的相关性选择
            if len(high_variance_features) > feature_count:
                # 计算与收盘价的相关性
                correlations = abs(features_df[high_variance_features].corrwith(features_df['close']))
                correlations = correlations.sort_values(ascending=False)
                
                # 选择相关性最高的特征
                selected_features = correlations.head(feature_count).index.tolist()
                logger.debug(f"基于相关性选择了 {len(selected_features)} 个特征")
            else:
                selected_features = high_variance_features
            
            # 构建最终的特征列表
            final_columns = original_columns + selected_features
            result_df = features_df[final_columns].copy()
            
            logger.info(f"特征选择完成，最终保留 {len(selected_features)} 个计算特征，总计 {len(final_columns)} 列")
            
            return result_df
            
        except Exception as e:
            error_msg = f"选择基础特征失败: {str(e)}"
            logger.error(error_msg)
            raise FeatureGenerationError(error_msg) from e
    
    def validate_base_features(self, features_df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        验证基础特征的完整性和质量
        
        Args:
            features_df: 特征DataFrame
            
        Returns:
            (是否有效, 问题列表)
        """
        issues = []
        
        try:
            # 检查基本要求
            if features_df.empty:
                issues.append("特征DataFrame为空")
                return False, issues
            
            # 检查必需的原始列
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = set(required_columns) - set(features_df.columns)
            if missing_columns:
                issues.append(f"缺少必需的原始列: {missing_columns}")
            
            # 检查缺失值
            null_counts = features_df.isnull().sum()
            null_columns = null_counts[null_counts > 0]
            if len(null_columns) > 0:
                issues.append(f"存在缺失值的列: {null_columns.to_dict()}")
            
            # 检查无穷值
            inf_counts = np.isinf(features_df.select_dtypes(include=[np.number])).sum()
            inf_columns = inf_counts[inf_counts > 0]
            if len(inf_columns) > 0:
                issues.append(f"存在无穷值的列: {inf_columns.to_dict()}")
            
            # 检查常数列
            constant_columns = []
            for col in features_df.select_dtypes(include=[np.number]).columns:
                if features_df[col].nunique() <= 1:
                    constant_columns.append(col)
            
            if constant_columns:
                issues.append(f"常数列（无变化）: {constant_columns}")
            
            # 检查数据类型
            non_numeric_columns = []
            for col in features_df.columns:
                if not pd.api.types.is_numeric_dtype(features_df[col]):
                    non_numeric_columns.append(col)
            
            if non_numeric_columns:
                issues.append(f"非数值列: {non_numeric_columns}")
            
            is_valid = len(issues) == 0
            
            if is_valid:
                logger.info("基础特征验证通过")
            else:
                logger.warning(f"基础特征验证发现 {len(issues)} 个问题")
                for issue in issues:
                    logger.warning(f"  - {issue}")
            
            return is_valid, issues
            
        except Exception as e:
            error_msg = f"验证基础特征时出错: {str(e)}"
            logger.error(error_msg)
            issues.append(error_msg)
            return False, issues    

    def generate_model_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成模型预测特征
        
        从已加载的 standalone 和 money management 模型生成概率特征
        
        Args:
            df: 包含基础特征的DataFrame
            
        Returns:
            包含模型预测特征的DataFrame
            
        Raises:
            FeatureGenerationError: 模型特征生成失败
        """
        try:
            logger.info(f"开始生成模型预测特征，数据行数: {len(df)}")
            
            # 检查缓存
            cache_key = self._generate_cache_key(df, "model_features")
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                logger.info("使用缓存的模型特征")
                return cached_result
            
            if df.empty:
                raise FeatureGenerationError("输入数据为空")
            
            # 获取已加载的模型类型
            loaded_models = self.model_loader.get_loaded_model_types()
            if not loaded_models:
                raise FeatureGenerationError("没有加载任何模型")
            
            logger.info(f"已加载的模型: {loaded_models}")
            
            # 生成模型预测特征
            model_predictions = {}
            
            # 处理 standalone 模型
            if 'standalone' in loaded_models:
                try:
                    logger.debug("生成 standalone 模型特征")
                    standalone_pred = self.model_loader.predict_standalone(df)
                    model_predictions['standalone'] = standalone_pred
                    logger.info(f"Standalone 模型预测完成: {len(standalone_pred)} 行")
                except ModelPredictionError as e:
                    logger.error(f"Standalone 模型预测失败: {str(e)}")
                    if self.config.feature_selection.use_model_probabilities:
                        raise FeatureGenerationError(f"Standalone 模型预测失败，但配置要求使用模型概率: {str(e)}")
            
            # 处理 money management 模型
            if 'money' in loaded_models:
                try:
                    logger.debug("生成 money management 模型特征")
                    money_pred = self.model_loader.predict_money(df)
                    model_predictions['money'] = money_pred
                    logger.info(f"Money management 模型预测完成: {len(money_pred)} 行")
                except ModelPredictionError as e:
                    logger.error(f"Money management 模型预测失败: {str(e)}")
                    if self.config.feature_selection.use_model_probabilities:
                        raise FeatureGenerationError(f"Money management 模型预测失败，但配置要求使用模型概率: {str(e)}")
            
            # 合并模型预测结果
            if model_predictions:
                combined_predictions = pd.concat(list(model_predictions.values()), axis=1)
                
                # 确保索引对齐
                combined_predictions = self._align_features_with_data(df, combined_predictions)
                
                logger.info(f"模型特征生成完成，共 {len(combined_predictions.columns)} 个特征")
                
                # 保存到缓存
                self._save_to_cache(cache_key, combined_predictions)
                
                return combined_predictions
            else:
                logger.warning("没有成功生成任何模型特征")
                # 返回空的预测特征DataFrame，保持索引一致
                return pd.DataFrame(index=df.index)
                
        except Exception as e:
            error_msg = f"生成模型预测特征失败: {str(e)}"
            logger.error(error_msg)
            raise FeatureGenerationError(error_msg) from e
    
    def _align_features_with_data(self, original_df: pd.DataFrame, 
                                 features_df: pd.DataFrame) -> pd.DataFrame:
        """
        对齐特征数据与原始数据的时间窗口
        
        Args:
            original_df: 原始数据DataFrame
            features_df: 特征数据DataFrame
            
        Returns:
            对齐后的特征DataFrame
        """
        try:
            # 确保索引类型一致
            if not features_df.index.equals(original_df.index):
                logger.debug("对齐特征数据索引")
                
                # 使用内连接确保时间窗口一致
                aligned_features = features_df.reindex(original_df.index)
                
                # 检查对齐后的缺失值
                null_counts = aligned_features.isnull().sum()
                if null_counts.sum() > 0:
                    logger.warning(f"特征对齐后存在缺失值: {null_counts[null_counts > 0].to_dict()}")
                    # 前向填充缺失值
                    aligned_features = aligned_features.fillna(method='ffill').fillna(0)
                
                return aligned_features
            
            return features_df
            
        except Exception as e:
            logger.error(f"特征对齐失败: {str(e)}")
            raise
    
    def create_model_feature_names(self, model_type: str, 
                                  include_probabilities: bool = True,
                                  include_predictions: bool = True) -> List[str]:
        """
        创建模型特征名称列表
        
        Args:
            model_type: 模型类型 ('standalone' 或 'money')
            include_probabilities: 是否包含概率特征
            include_predictions: 是否包含预测类别特征
            
        Returns:
            特征名称列表
        """
        feature_names = []
        
        if model_type == 'standalone':
            if include_probabilities:
                feature_names.extend([
                    'standalone_prob_lowest',
                    'standalone_prob_neutral', 
                    'standalone_prob_highest'
                ])
            if include_predictions:
                feature_names.append('standalone_prediction')
                
        elif model_type == 'money':
            if include_probabilities:
                feature_names.extend([
                    'money_prob_down',
                    'money_prob_up'
                ])
            if include_predictions:
                feature_names.append('money_prediction')
        
        return feature_names
    
    def get_all_model_feature_names(self) -> List[str]:
        """
        获取所有可能的模型特征名称
        
        Returns:
            所有模型特征名称列表
        """
        all_features = []
        
        # 获取已加载的模型类型
        loaded_models = self.model_loader.get_loaded_model_types()
        
        for model_type in loaded_models:
            model_features = self.create_model_feature_names(
                model_type,
                include_probabilities=self.config.feature_selection.use_model_probabilities,
                include_predictions=self.config.feature_selection.use_model_predictions
            )
            all_features.extend(model_features)
        
        return all_features
    
    def validate_model_features(self, model_features_df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        验证模型特征的完整性和质量
        
        Args:
            model_features_df: 模型特征DataFrame
            
        Returns:
            (是否有效, 问题列表)
        """
        issues = []
        
        try:
            # 检查基本要求
            if model_features_df.empty:
                issues.append("模型特征DataFrame为空")
                return False, issues
            
            # 获取期望的特征名称
            expected_features = self.get_all_model_feature_names()
            
            if not expected_features:
                logger.info("没有期望的模型特征（可能没有加载模型）")
                return True, []
            
            # 检查缺失的特征
            missing_features = set(expected_features) - set(model_features_df.columns)
            if missing_features:
                issues.append(f"缺少期望的模型特征: {missing_features}")
            
            # 检查概率特征的范围
            prob_columns = [col for col in model_features_df.columns if 'prob_' in col]
            for col in prob_columns:
                values = model_features_df[col]
                if values.min() < 0 or values.max() > 1:
                    issues.append(f"概率特征 {col} 超出 [0,1] 范围: min={values.min():.4f}, max={values.max():.4f}")
            
            # 检查 standalone 概率是否和为1
            standalone_prob_cols = [col for col in model_features_df.columns 
                                  if col.startswith('standalone_prob_')]
            if len(standalone_prob_cols) == 3:
                prob_sums = model_features_df[standalone_prob_cols].sum(axis=1)
                tolerance = 0.01  # 允许1%的误差
                invalid_sums = abs(prob_sums - 1.0) > tolerance
                if invalid_sums.any():
                    issues.append(f"Standalone 概率和不为1的行数: {invalid_sums.sum()}")
            
            # 检查 money 概率是否和为1
            money_prob_cols = [col for col in model_features_df.columns 
                             if col.startswith('money_prob_')]
            if len(money_prob_cols) == 2:
                prob_sums = model_features_df[money_prob_cols].sum(axis=1)
                tolerance = 0.01  # 允许1%的误差
                invalid_sums = abs(prob_sums - 1.0) > tolerance
                if invalid_sums.any():
                    issues.append(f"Money 概率和不为1的行数: {invalid_sums.sum()}")
            
            # 检查预测类别的范围
            if 'standalone_prediction' in model_features_df.columns:
                pred_values = model_features_df['standalone_prediction'].unique()
                expected_values = {0, 1, 2}
                invalid_values = set(pred_values) - expected_values
                if invalid_values:
                    issues.append(f"Standalone 预测类别包含无效值: {invalid_values}")
            
            if 'money_prediction' in model_features_df.columns:
                pred_values = model_features_df['money_prediction'].unique()
                expected_values = {0, 1}
                invalid_values = set(pred_values) - expected_values
                if invalid_values:
                    issues.append(f"Money 预测类别包含无效值: {invalid_values}")
            
            # 检查缺失值
            null_counts = model_features_df.isnull().sum()
            null_columns = null_counts[null_counts > 0]
            if len(null_columns) > 0:
                issues.append(f"模型特征存在缺失值: {null_columns.to_dict()}")
            
            is_valid = len(issues) == 0
            
            if is_valid:
                logger.info("模型特征验证通过")
            else:
                logger.warning(f"模型特征验证发现 {len(issues)} 个问题")
                for issue in issues:
                    logger.warning(f"  - {issue}")
            
            return is_valid, issues
            
        except Exception as e:
            error_msg = f"验证模型特征时出错: {str(e)}"
            logger.error(error_msg)
            issues.append(error_msg)
            return False, issues
    
    def combine_features(self, base_features: pd.DataFrame, 
                        model_features: pd.DataFrame) -> pd.DataFrame:
        """
        合并传统技术指标与模型预测特征
        
        Args:
            base_features: 基础技术指标特征
            model_features: 模型预测特征
            
        Returns:
            合并后的特征DataFrame
            
        Raises:
            FeatureGenerationError: 特征合并失败
        """
        try:
            logger.info(f"开始合并特征，基础特征: {len(base_features.columns)} 列，模型特征: {len(model_features.columns)} 列")
            
            # 验证输入
            if base_features.empty:
                raise FeatureGenerationError("基础特征DataFrame为空")
            
            # 如果模型特征为空，只返回基础特征
            if model_features.empty:
                logger.warning("模型特征为空，只使用基础特征")
                return base_features.copy()
            
            # 检查索引对齐
            if not base_features.index.equals(model_features.index):
                logger.debug("对齐基础特征和模型特征的索引")
                # 使用内连接确保时间窗口一致
                common_index = base_features.index.intersection(model_features.index)
                if len(common_index) == 0:
                    raise FeatureGenerationError("基础特征和模型特征没有共同的时间索引")
                
                base_features = base_features.loc[common_index]
                model_features = model_features.loc[common_index]
                
                logger.info(f"索引对齐后保留 {len(common_index)} 行数据")
            
            # 检查列名冲突
            base_columns = set(base_features.columns)
            model_columns = set(model_features.columns)
            conflicting_columns = base_columns.intersection(model_columns)
            
            if conflicting_columns:
                logger.warning(f"发现列名冲突: {conflicting_columns}")
                # 重命名冲突的模型特征列
                rename_dict = {col: f"model_{col}" for col in conflicting_columns}
                model_features = model_features.rename(columns=rename_dict)
                logger.info(f"重命名冲突列: {rename_dict}")
            
            # 合并特征
            combined_features = pd.concat([base_features, model_features], axis=1)
            
            # 验证合并结果
            if combined_features.empty:
                raise FeatureGenerationError("特征合并后结果为空")
            
            expected_columns = len(base_features.columns) + len(model_features.columns)
            actual_columns = len(combined_features.columns)
            
            if actual_columns != expected_columns:
                logger.warning(f"合并后列数不匹配，期望: {expected_columns}，实际: {actual_columns}")
            
            logger.info(f"特征合并完成，最终特征数: {len(combined_features.columns)} 列，数据行数: {len(combined_features)} 行")
            
            return combined_features
            
        except Exception as e:
            error_msg = f"合并特征失败: {str(e)}"
            logger.error(error_msg)
            raise FeatureGenerationError(error_msg) from e
    
    def preprocess_features(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """
        预处理合并后的特征
        
        包括缺失值处理、异常值处理和特征标准化
        
        Args:
            features_df: 合并后的特征DataFrame
            
        Returns:
            预处理后的特征DataFrame
            
        Raises:
            FeatureGenerationError: 特征预处理失败
        """
        try:
            logger.info(f"开始预处理特征，输入: {len(features_df)} 行 {len(features_df.columns)} 列")
            
            if features_df.empty:
                raise FeatureGenerationError("输入特征DataFrame为空")
            
            processed_df = features_df.copy()
            
            # 1. 处理缺失值
            null_counts = processed_df.isnull().sum()
            null_columns = null_counts[null_counts > 0]
            
            if len(null_columns) > 0:
                logger.info(f"处理 {len(null_columns)} 列的缺失值")
                
                # 对于数值列，使用前向填充然后用0填充
                numeric_columns = processed_df.select_dtypes(include=[np.number]).columns
                processed_df[numeric_columns] = processed_df[numeric_columns].fillna(method='ffill').fillna(0)
                
                # 检查是否还有缺失值
                remaining_nulls = processed_df.isnull().sum().sum()
                if remaining_nulls > 0:
                    logger.warning(f"仍有 {remaining_nulls} 个缺失值，用0填充")
                    processed_df = processed_df.fillna(0)
            
            # 2. 处理无穷值
            numeric_columns = processed_df.select_dtypes(include=[np.number]).columns
            inf_mask = np.isinf(processed_df[numeric_columns])
            inf_count = inf_mask.sum().sum()
            
            if inf_count > 0:
                logger.info(f"处理 {inf_count} 个无穷值")
                # 将无穷值替换为该列的最大/最小有限值
                for col in numeric_columns:
                    col_data = processed_df[col]
                    if np.isinf(col_data).any():
                        finite_values = col_data[np.isfinite(col_data)]
                        if len(finite_values) > 0:
                            # 正无穷替换为最大值的1.5倍，负无穷替换为最小值的1.5倍
                            max_val = finite_values.max() * 1.5
                            min_val = finite_values.min() * 1.5
                            processed_df[col] = processed_df[col].replace([np.inf, -np.inf], [max_val, min_val])
                        else:
                            # 如果没有有限值，用0替换
                            processed_df[col] = processed_df[col].replace([np.inf, -np.inf], 0)
            
            # 3. 处理异常值（使用IQR方法）
            outlier_count = 0
            for col in numeric_columns:
                if col in ['open', 'high', 'low', 'close', 'volume']:
                    # 跳过原始价格和成交量列
                    continue
                
                col_data = processed_df[col]
                Q1 = col_data.quantile(0.25)
                Q3 = col_data.quantile(0.75)
                IQR = Q3 - Q1
                
                if IQR > 0:  # 避免除零
                    lower_bound = Q1 - 3 * IQR  # 使用3倍IQR作为阈值
                    upper_bound = Q3 + 3 * IQR
                    
                    outliers = (col_data < lower_bound) | (col_data > upper_bound)
                    outlier_count += outliers.sum()
                    
                    # 将异常值限制在边界内
                    processed_df[col] = col_data.clip(lower_bound, upper_bound)
            
            if outlier_count > 0:
                logger.info(f"处理了 {outlier_count} 个异常值")
            
            # 4. 特征标准化（可选，根据配置决定）
            # 注意：概率特征通常不需要标准化，因为它们已经在[0,1]范围内
            # 这里只对传统技术指标进行标准化
            
            # 识别需要标准化的列（排除概率和预测列）
            prob_pred_columns = [col for col in processed_df.columns 
                               if 'prob_' in col or 'prediction' in col]
            original_columns = ['open', 'high', 'low', 'close', 'volume']
            
            standardize_columns = [col for col in processed_df.columns 
                                 if col not in prob_pred_columns and col not in original_columns]
            
            if standardize_columns:
                logger.debug(f"标准化 {len(standardize_columns)} 个特征列")
                
                # 使用Z-score标准化
                for col in standardize_columns:
                    col_data = processed_df[col]
                    mean_val = col_data.mean()
                    std_val = col_data.std()
                    
                    if std_val > 0:  # 避免除零
                        processed_df[col] = (col_data - mean_val) / std_val
                    else:
                        # 如果标准差为0，说明是常数列，设为0
                        processed_df[col] = 0
            
            logger.info(f"特征预处理完成，输出: {len(processed_df)} 行 {len(processed_df.columns)} 列")
            
            return processed_df
            
        except Exception as e:
            error_msg = f"特征预处理失败: {str(e)}"
            logger.error(error_msg)
            raise FeatureGenerationError(error_msg) from e
    
    def validate_combined_features(self, features_df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        验证合并后特征的完整性和质量
        
        Args:
            features_df: 合并后的特征DataFrame
            
        Returns:
            (是否有效, 问题列表)
        """
        issues = []
        
        try:
            logger.info("开始验证合并后的特征")
            
            # 基本检查
            if features_df.empty:
                issues.append("合并后的特征DataFrame为空")
                return False, issues
            
            # 检查必需的原始列
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_required = set(required_columns) - set(features_df.columns)
            if missing_required:
                issues.append(f"缺少必需的原始列: {missing_required}")
            
            # 检查是否有传统特征（如果配置要求）
            if self.config.feature_selection.use_traditional_features:
                traditional_features = [col for col in features_df.columns 
                                      if col not in required_columns and 
                                      'prob_' not in col and 'prediction' not in col]
                
                if len(traditional_features) == 0:
                    issues.append("配置要求使用传统特征，但没有找到传统特征列")
                elif len(traditional_features) < self.config.feature_selection.traditional_feature_count:
                    issues.append(f"传统特征数量不足，期望: {self.config.feature_selection.traditional_feature_count}，实际: {len(traditional_features)}")
            
            # 检查是否有模型特征（如果配置要求）
            if self.config.feature_selection.use_model_probabilities or self.config.feature_selection.use_model_predictions:
                model_features = [col for col in features_df.columns 
                                if 'prob_' in col or 'prediction' in col]
                
                if len(model_features) == 0:
                    issues.append("配置要求使用模型特征，但没有找到模型特征列")
            
            # 验证基础特征
            base_features = features_df[[col for col in features_df.columns 
                                       if 'prob_' not in col and 'prediction' not in col]]
            base_valid, base_issues = self.validate_base_features(base_features)
            if not base_valid:
                issues.extend([f"基础特征问题: {issue}" for issue in base_issues])
            
            # 验证模型特征
            model_features = features_df[[col for col in features_df.columns 
                                        if 'prob_' in col or 'prediction' in col]]
            if not model_features.empty:
                model_valid, model_issues = self.validate_model_features(model_features)
                if not model_valid:
                    issues.extend([f"模型特征问题: {issue}" for issue in model_issues])
            
            # 检查特征数量是否合理
            total_features = len(features_df.columns)
            if total_features < 10:
                issues.append(f"特征数量过少: {total_features}")
            elif total_features > 1000:
                issues.append(f"特征数量过多: {total_features}")
            
            # 检查数据质量
            numeric_columns = features_df.select_dtypes(include=[np.number]).columns
            
            # 检
    
    def get_feature_info(self) -> Dict[str, Any]:
        """
        获取特征生成器的信息
        
        Returns:
            特征生成器信息字典
        """
        return {
            'cache_enabled': self._cache_enabled,
            'cached_features': len(self._feature_cache),
            'feature_engine_timeframe': self.feature_engine.timeframe if self.feature_engine else None,
            'loaded_models': self.model_loader.get_loaded_model_types(),
            'config': {
                'use_traditional_features': self.config.feature_selection.use_traditional_features,
                'traditional_feature_count': self.config.feature_selection.traditional_feature_count,
                'use_model_probabilities': self.config.feature_selection.use_model_probabilities,
                'use_model_predictions': self.config.feature_selection.use_model_predictions
            }
        }