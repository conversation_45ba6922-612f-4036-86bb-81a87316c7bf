# 配置修复总结

## 修复的问题

### 1. 阈值验证错误

**问题**: 配置验证要求 `down_threshold` 在 0 和 1 之间，但实际应该是负值（如 -0.02 表示 -2%）

**修复**:
- 修改 `ensemble_config.py` 中的验证逻辑：
  ```python
  # 修复前
  if not (0 < self.up_threshold < 1 and 0 < self.down_threshold < 1):
  
  # 修复后  
  if not (0 < self.up_threshold < 1 and -1 < self.down_threshold < 0):
  ```

### 2. 默认配置错误

**问题**: `EnsembleConfig` 类和 `create_default_config` 函数都将 `down_threshold` 默认设置为 0.02，应该是 -0.02

**修复**:
- 修改 `EnsembleConfig` 类的默认值：
  ```python
  down_threshold: float = -0.02  # 原来是 0.02
  ```
- 修改 `create_default_config` 函数：
  ```python
  down_threshold=-0.02,  # 原来是 0.02
  ```

### 3. 配置覆盖错误

**问题**: `main.py` 中的 `_override_config_with_args` 函数使用字典访问方式访问 dataclass 对象

**修复**:
- 将所有字典访问改为属性访问：
  ```python
  # 修复前
  config.time_range['train_days'] = args.train_days
  config.training['mode'] = args.mode
  
  # 修复后
  config.time_range.train_days = args.train_days
  config.training.mode = args.mode
  ```

## 验证结果

### 配置加载测试
```bash
python -c "
from hunhe.ensemble_config import load_config
config = load_config('hunhe/sample_config.json')
print('✅ Configuration loaded successfully')
"
```

### 主脚本测试
```bash
python -m hunhe.main --config hunhe/sample_config.json --mode train --train-days 7
```

现在配置验证正常通过，唯一的错误是数据库中没有数据，这在测试环境中是预期的。

## 配置文件格式

修复后的正确配置文件格式：

```json
{
  "name": "eth_5m_ensemble",
  "timeframe_minutes": 5,
  "up_threshold": 0.02,
  "down_threshold": -0.02,
  "max_lookforward_minutes": 240,
  "models": {
    "standalone": {
      "model_path": "models/standalone_eth_5m_model.joblib",
      "config_path": "models/standalone_eth_5m_config.json",
      "model_type": "standalone"
    },
    "money": {
      "model_path": "models/eth_5m_model.joblib",
      "config_path": "models/eth_5m_config.json",
      "model_type": "money"
    }
  },
  "feature_selection": {
    "use_traditional_features": true,
    "traditional_feature_count": 20,
    "use_model_probabilities": true,
    "use_model_predictions": true,
    "feature_importance_threshold": 0.001
  },
  "training": {
    "mode": "train",
    "test_size": 0.2,
    "validation_size": 0.1,
    "random_state": 42,
    "cv_folds": 5,
    "early_stopping_rounds": 100,
    "optimize_hyperparameters": false,
    "n_trials": 100
  },
  "data_source": {
    "database_path": "coin_data.db",
    "symbol": "ETHUSDT",
    "timeframe_minutes": 5,
    "table_name": "ethusdt_5min_spot"
  },
  "time_range": {
    "train_days": 30
  },
  "output": {
    "output_dir": "models",
    "save_features": false,
    "save_analysis": false
  }
}
```

## 关键要点

1. **上涨阈值** (`up_threshold`): 必须在 0 和 1 之间，如 0.02 表示 2%
2. **下跌阈值** (`down_threshold`): 必须在 -1 和 0 之间，如 -0.02 表示 -2%
3. **配置结构**: 使用嵌套的 dataclass 结构，不是字典
4. **命令行覆盖**: 支持通过命令行参数覆盖配置文件中的设置

所有配置相关的问题现在都已修复，系统可以正常加载和验证配置文件。