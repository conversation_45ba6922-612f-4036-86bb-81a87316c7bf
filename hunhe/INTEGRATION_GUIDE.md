# 集成测试和使用指南

本指南详细介绍了集成模型训练系统的集成测试和使用示例。

## 目录

1. [集成测试概述](#集成测试概述)
2. [运行集成测试](#运行集成测试)
3. [使用示例](#使用示例)
4. [回测系统集成](#回测系统集成)
5. [故障排除](#故障排除)
6. [性能优化](#性能优化)

## 集成测试概述

集成测试验证整个系统的端到端功能，包括：

### 测试覆盖范围

- **端到端训练流程测试**: 验证从数据加载到模型保存的完整流程
- **模型兼容性测试**: 确保生成的模型与现有回测系统兼容
- **特征生成一致性测试**: 验证特征生成的可重复性
- **配置验证测试**: 测试配置文件的正确性和完整性
- **错误处理测试**: 验证系统对异常情况的处理能力
- **性能测试**: 测试大数据集处理和内存使用情况

### 测试文件结构

```
hunhe/
├── test_integration.py          # 主集成测试文件
├── test_model_loader.py         # 模型加载器单元测试
├── test_feature_generator.py    # 特征生成器单元测试
├── test_ensemble_trainer.py     # 集成训练器单元测试
└── example_usage.py            # 使用示例和演示
```

## 运行集成测试

### 1. 运行完整集成测试

```bash
# 运行所有集成测试
python hunhe/test_integration.py

# 运行特定测试类
python -m unittest hunhe.test_integration.TestEndToEndIntegration
python -m unittest hunhe.test_integration.TestBacktestIntegration
python -m unittest hunhe.test_integration.TestPerformanceIntegration
```

### 2. 运行单元测试

```bash
# 运行模型加载器测试
python hunhe/test_model_loader.py

# 运行特征生成器测试
python hunhe/test_feature_generator.py

# 运行集成训练器测试
python hunhe/test_ensemble_trainer.py
```

### 3. 测试输出解读

成功的测试输出示例：
```
=== 测试完整训练流程 ===
加载了 2000 条测试数据
✅ 训练成功完成
模型保存路径: /tmp/models/test_ensemble_integration_model.joblib

=== 测试模型预测兼容性 ===
✅ 预测成功
预测形状: (10,)
概率形状: (10, 2)

=== 测试回测系统兼容性 ===
✅ 回测兼容性验证通过
```

## 使用示例

### 1. 运行使用示例

```bash
# 运行完整使用示例演示
python hunhe/example_usage.py

# 运行综合示例（需要真实数据和模型）
python hunhe/comprehensive_example.py
```

### 2. 基本使用流程

#### 步骤1: 准备组件模型

确保有以下文件：
- `models/standalone_eth_5m_model.joblib` - Standalone模型
- `models/standalone_eth_5m_config.json` - Standalone配置
- `models/eth_5m_model.joblib` - Money management模型
- `models/eth_5m_config.json` - Money management配置

#### 步骤2: 准备数据

确保数据库文件存在：
- `coin_data.db` - 包含历史K线数据的SQLite数据库

#### 步骤3: 创建配置文件

```json
{
  "ensemble_config": {
    "name": "my_ensemble",
    "timeframe_minutes": 5,
    "up_threshold": 0.02,
    "down_threshold": -0.02,
    "max_lookforward_minutes": 240,
    "models": {
      "standalone": {
        "model_path": "models/standalone_eth_5m_model.joblib",
        "config_path": "models/standalone_eth_5m_config.json",
        "model_type": "standalone"
      },
      "money": {
        "model_path": "models/eth_5m_model.joblib",
        "config_path": "models/eth_5m_config.json",
        "model_type": "money"
      }
    },
    "feature_selection": {
      "use_traditional_features": true,
      "traditional_feature_count": 20,
      "use_model_probabilities": true,
      "use_model_predictions": true
    },
    "training": {
      "mode": "full",
      "optimize_hyperparameters": true,
      "n_trials": 100,
      "cv_folds": 5,
      "test_size": 0.2,
      "validation_size": 0.2
    },
    "data_source": {
      "database_path": "coin_data.db",
      "symbol": "ETHUSDT",
      "timeframe_minutes": 5,
      "table_name": "ethusdt_5min_spot"
    },
    "output": {
      "output_dir": "models",
      "save_features": true,
      "save_analysis": true
    }
  }
}
```

#### 步骤4: 运行训练

```bash
# 使用配置文件
python hunhe/main.py --config my_config.json

# 使用命令行参数
python hunhe/main.py \
  --name my_ensemble \
  --timeframe 5 \
  --standalone-model models/standalone_eth_5m_model.joblib \
  --money-model models/eth_5m_model.joblib \
  --mode full \
  --optimize \
  --save-features \
  --save-analysis
```

### 3. 高级使用选项

#### 特征选择优化

```bash
# 只使用模型特征，不使用传统技术指标
python hunhe/main.py --config config.json --no-traditional-features

# 使用更多传统特征
python hunhe/main.py --config config.json --feature-count 50

# 只使用概率特征，不使用预测类别
python hunhe/main.py --config config.json --no-model-predictions
```

#### 训练模式选择

```bash
# 快速训练（仅训练，不验证）
python hunhe/main.py --config config.json --mode train

# 包含验证的训练
python hunhe/main.py --config config.json --mode validate

# 完整流程（训练+验证+测试+性能对比）
python hunhe/main.py --config config.json --mode full
```

#### 超参数优化

```bash
# 启用超参数优化
python hunhe/main.py --config config.json --optimize --n-trials 200

# 使用更多交叉验证折数
python hunhe/main.py --config config.json --cv-folds 10
```

## 回测系统集成

### 1. 模型格式兼容性

集成模型生成的文件与现有回测系统完全兼容：

- **模型文件**: 使用标准joblib格式 (`.joblib`)
- **配置文件**: 使用JSON格式 (`_config.json`)
- **预测接口**: 支持 `predict()` 和 `predict_proba()` 方法

### 2. 配置文件结构

生成的配置文件包含回测所需的所有字段：

```json
{
  "model_type": "ensemble",
  "ensemble_config": {
    "name": "ensemble_model",
    "timeframe_minutes": 5,
    "up_threshold": 0.02,
    "down_threshold": -0.02,
    "max_lookforward_minutes": 240
  },
  "component_models": {
    "standalone": {...},
    "money": {...}
  },
  "feature_list": ["feature_1", "feature_2", ...],
  "timeframe_minutes": 5,
  "up_threshold": 0.02,
  "down_threshold": -0.02,
  "training_date": "2024-01-01 12:00:00",
  "model_version": "1.0"
}
```

### 3. 在回测中使用集成模型

#### 修改现有回测脚本

```python
# 原来的代码
model_path = "models/eth_5m_model.joblib"
config_path = "models/eth_5m_config.json"

# 修改为集成模型
model_path = "models/ensemble_eth_5m_model.joblib"
config_path = "models/ensemble_eth_5m_config.json"

# 其他代码保持不变
model = joblib.load(model_path)
with open(config_path, 'r') as f:
    config = json.load(f)

# 预测代码保持不变
predictions = model.predict(features)
probabilities = model.predict_proba(features)
```

#### 特征生成兼容性

集成模型使用的特征包括：
1. 传统技术指标特征（与现有模型相同）
2. 组件模型的概率特征
3. 组件模型的预测特征

确保回测时使用相同的特征生成逻辑。

### 4. 性能对比

可以在回测中对比不同模型的表现：

```python
# 加载不同模型
standalone_model = joblib.load("models/standalone_eth_5m_model.joblib")
money_model = joblib.load("models/eth_5m_model.joblib")
ensemble_model = joblib.load("models/ensemble_eth_5m_model.joblib")

# 分别进行回测
standalone_results = backtest(standalone_model, data)
money_results = backtest(money_model, data)
ensemble_results = backtest(ensemble_model, data)

# 对比结果
compare_results(standalone_results, money_results, ensemble_results)
```

## 故障排除

### 1. 常见错误和解决方案

#### 模型加载错误

**错误**: `ModelLoadError: 模型文件不存在`

**解决方案**:
- 检查模型文件路径是否正确
- 确保模型文件存在且可读
- 使用绝对路径或正确的相对路径

#### 数据库连接错误

**错误**: `sqlite3.OperationalError: no such table`

**解决方案**:
- 检查数据库文件路径
- 确认表名是否正确（如 `ethusdt_5min_spot`）
- 验证数据库文件完整性

#### 特征生成错误

**错误**: `FeatureGenerationError: 特征计算失败`

**解决方案**:
- 检查输入数据格式（必须包含OHLCV列）
- 确保数据量足够（建议至少500条记录）
- 检查数据中的异常值和缺失值

#### 内存不足错误

**错误**: `MemoryError` 或进程被杀死

**解决方案**:
- 减少特征数量：`--feature-count 10`
- 使用较小的数据集：`--train-days 7`
- 关闭超参数优化：不使用 `--optimize`
- 增加系统内存

### 2. 调试技巧

#### 启用详细日志

```bash
# 启用DEBUG级别日志
python hunhe/main.py --config config.json --log-level DEBUG

# 将日志保存到文件
python hunhe/main.py --config config.json --log-file training.log
```

#### 使用测试模式

```bash
# 使用较小的数据集进行快速测试
python hunhe/main.py --config config.json --train-days 3 --mode train

# 关闭超参数优化以加快测试
python hunhe/main.py --config config.json --mode train
```

#### 检查中间结果

```bash
# 保存特征重要性分析
python hunhe/main.py --config config.json --save-features

# 保存详细分析报告
python hunhe/main.py --config config.json --save-analysis
```

### 3. 性能监控

#### 监控训练进度

训练过程中会显示进度信息：
```
进度: 25.0% - 加载组件模型
进度: 50.0% - 生成特征
进度: 75.0% - 训练集成模型
进度: 100.0% - 保存模型和配置
```

#### 检查资源使用

```bash
# 监控内存使用
top -p $(pgrep -f "hunhe/main.py")

# 监控磁盘使用
df -h models/
```

## 性能优化

### 1. 训练速度优化

#### 减少数据量

```bash
# 使用较短的训练期间
python hunhe/main.py --config config.json --train-days 14

# 使用特定时间范围
python hunhe/main.py --config config.json \
  --start-date 2024-01-01 --end-date 2024-01-31
```

#### 减少特征数量

```bash
# 使用较少的传统特征
python hunhe/main.py --config config.json --feature-count 10

# 只使用模型特征
python hunhe/main.py --config config.json --no-traditional-features
```

#### 简化训练流程

```bash
# 只进行训练，跳过验证
python hunhe/main.py --config config.json --mode train

# 关闭超参数优化
python hunhe/main.py --config config.json  # 不使用 --optimize
```

### 2. 内存优化

#### 批量处理

系统自动使用批量处理来减少内存使用。可以通过以下方式进一步优化：

- 使用较小的数据集
- 减少特征数量
- 关闭不必要的分析功能

#### 垃圾回收

系统会自动进行垃圾回收，但在处理大数据集时可能需要手动优化：

```python
import gc
gc.collect()  # 手动触发垃圾回收
```

### 3. 并行处理

#### 多进程训练

某些操作支持多进程处理：

```bash
# 使用多个CPU核心进行交叉验证
python hunhe/main.py --config config.json --cv-folds 5
```

#### GPU加速

目前系统主要使用CPU，但可以通过以下方式利用GPU：

- 使用支持GPU的机器学习库
- 在特征计算中使用GPU加速的数值计算库

## 总结

本指南涵盖了集成模型训练系统的完整测试和使用流程。通过遵循这些指南，您可以：

1. 成功运行集成测试验证系统功能
2. 使用各种配置选项训练集成模型
3. 将集成模型集成到现有回测系统中
4. 解决常见问题和优化性能

如果遇到问题，请参考故障排除部分或查看详细的日志输出。