#!/usr/bin/env python3
"""
集成模型训练器测试脚本
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hunhe.ensemble_config import EnsembleConfig, create_default_config
from hunhe.ensemble_trainer import EnsembleTrainer

def create_mock_data(n_samples=1000):
    """创建模拟数据用于测试"""
    np.random.seed(42)
    
    # 生成基础OHLCV数据
    dates = pd.date_range('2024-01-01', periods=n_samples, freq='5T')
    
    # 模拟价格走势
    base_price = 2500.0
    price_changes = np.random.normal(0, 0.01, n_samples)
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    # 生成OHLCV数据
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        high = price * (1 + abs(np.random.normal(0, 0.005)))
        low = price * (1 - abs(np.random.normal(0, 0.005)))
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        volume = np.random.uniform(1000, 10000)
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data, index=dates)
    return df

def test_ensemble_trainer():
    """测试集成训练器"""
    print("=== 集成模型训练器测试 ===")
    
    # 1. 创建模拟配置
    print("1. 创建测试配置...")
    config = create_default_config(
        name="test_ensemble",
        timeframe_minutes=5,
        standalone_model_path="models/standalone_eth_5m_model.joblib",
        money_model_path="models/eth_5m_model.joblib"
    )
    
    # 修改配置以适应测试
    config.training.test_size = 0.2
    config.training.validation_size = 0.1
    config.feature_selection.traditional_feature_count = 5
    
    print(f"配置名称: {config.name}")
    print(f"时间框架: {config.timeframe_minutes}分钟")
    print(f"上涨阈值: {config.up_threshold*100}%")
    print(f"下跌阈值: {config.down_threshold*100}%")
    
    # 2. 创建训练器
    print("\n2. 创建训练器...")
    trainer = EnsembleTrainer(config)
    
    # 3. 创建模拟数据
    print("\n3. 创建模拟数据...")
    df = create_mock_data(1000)
    print(f"数据形状: {df.shape}")
    print(f"时间范围: {df.index[0]} 到 {df.index[-1]}")
    
    # 4. 测试数据准备
    print("\n4. 测试数据准备...")
    try:
        # 由于没有真实的模型文件，这里会失败，但我们可以测试其他部分
        print("注意: 由于缺少真实模型文件，将跳过特征生成测试")
        
        # 测试标签生成
        print("测试标签生成...")
        labels = trainer.create_labels(df)
        print(f"生成标签数量: {len(labels)}")
        if len(labels) > 0:
            print(f"标签分布: {labels.value_counts().to_dict()}")
        
        # 测试数据分割（使用模拟的完整数据）
        print("测试数据分割...")
        # 添加模拟标签列
        df_with_labels = df.copy()
        df_with_labels['label'] = np.random.choice([0, 1], size=len(df))
        
        train_df, val_df, test_df = trainer.split_data(df_with_labels)
        print(f"训练集大小: {len(train_df)}")
        print(f"验证集大小: {len(val_df)}")
        print(f"测试集大小: {len(test_df)}")
        
        print("\n✅ 基础功能测试通过")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ensemble_trainer()