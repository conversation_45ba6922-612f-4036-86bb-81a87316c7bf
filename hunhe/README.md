# 混合模型训练系统 (Ensemble Model Training System)

这个系统实现了一个集成模型训练框架，用于合并多个现有模型的预测来创建更强的混合模型。

## 项目结构

```
hunhe/
├── __init__.py              # 包初始化文件
├── main.py                  # 主训练脚本
├── ensemble_config.py       # 配置管理模块
├── model_loader.py          # 模型加载器
├── feature_generator.py     # 特征生成器
├── ensemble_trainer.py      # 集成模型训练器
├── utils.py                 # 工具函数和常量
├── sample_config.json       # 示例配置文件
└── README.md               # 项目文档
```

## 核心组件

### 1. EnsembleConfig (ensemble_config.py)
- 管理集成模型的完整配置
- 支持配置验证和文件加载/保存
- 包含模型路径、特征选择、训练参数等配置

### 2. ModelLoader (model_loader.py)
- 负责加载standalone和money management模型
- 提供统一的预测接口
- 处理模型文件验证和错误处理

### 3. FeatureGenerator (feature_generator.py)
- 生成集成模型的特征
- 合并传统技术指标和模型预测特征
- 支持特征选择和预处理

### 4. EnsembleTrainer (ensemble_trainer.py)
- 协调整个训练流程
- 处理数据准备、标签生成、模型训练
- 管理模型保存和配置生成

### 5. Utils (utils.py)
- 提供通用工具函数
- 定义系统常量
- 包含日志配置、文件验证等功能

## 配置文件格式

配置文件使用JSON格式，包含以下主要部分：

```json
{
  "name": "模型名称",
  "timeframe_minutes": 5,
  "up_threshold": 0.02,
  "down_threshold": 0.02,
  "max_lookforward_minutes": 240,
  "models": {
    "standalone": {
      "model_path": "模型文件路径",
      "config_path": "配置文件路径",
      "model_type": "standalone"
    },
    "money": {
      "model_path": "模型文件路径",
      "config_path": "配置文件路径", 
      "model_type": "money"
    }
  },
  "feature_selection": {
    "use_traditional_features": true,
    "traditional_feature_count": 20,
    "use_model_probabilities": true,
    "use_model_predictions": true
  },
  "training": {
    "test_size": 0.2,
    "validation_size": 0.1,
    "random_state": 42,
    "cv_folds": 5
  }
}
```

## 使用方法

### 1. 使用配置文件
```bash
python -m hunhe.main --config hunhe/sample_config.json
```

### 2. 使用命令行参数
```bash
python -m hunhe.main \
  --name eth_5m_ensemble \
  --timeframe 5 \
  --standalone-model models/standalone_eth_5m_model.joblib \
  --money-model models/eth_5m_model.joblib
```

### 3. 在Python代码中使用
```python
from hunhe import EnsembleConfig, EnsembleTrainer

# 加载配置
config = EnsembleConfig.from_file('config.json')

# 创建训练器
trainer = EnsembleTrainer(config)

# 开始训练（需要在后续任务中实现）
```

## 开发状态

当前项目处于基础结构阶段，已完成：
- ✅ 项目结构创建
- ✅ 配置管理系统
- ✅ 基础工具函数
- ✅ 模块框架搭建

待实现功能（后续任务）：
- ⏳ 模型加载器实现
- ⏳ 特征生成器实现  
- ⏳ 训练器实现
- ⏳ 测试和验证

## 依赖要求

- Python 3.7+
- pandas
- numpy
- scikit-learn
- lightgbm
- joblib

## 注意事项

1. 确保所有模型文件路径正确且文件存在
2. 配置文件中的参数需要与现有模型兼容
3. 特征生成需要与现有回测系统保持一致
4. 训练数据的时间范围和格式需要匹配