#!/usr/bin/env python3
"""
回测兼容性测试脚本

测试集成模型与现有回测系统的兼容性，包括：
1. 模型文件格式兼容性
2. 配置文件结构兼容性
3. 预测接口兼容性
4. 特征生成兼容性
"""

import unittest
import pandas as pd
import numpy as np
import tempfile
import os
import json
import joblib
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hunhe.ensemble_config import EnsembleConfig, create_default_config
from hunhe.ensemble_trainer import EnsembleTrainer
from hunhe.model_loader import ModelLoader
from hunhe.feature_generator import FeatureGenerator


class TestBacktestCompatibility(unittest.TestCase):
    """回测兼容性测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.models_dir = os.path.join(self.temp_dir, "models")
        os.makedirs(self.models_dir, exist_ok=True)
        
        # 创建模拟的组件模型
        self._create_mock_component_models()
    
    def tearDown(self):
        """测试后的清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def _create_mock_component_models(self):
        """创建模拟的组件模型"""
        from sklearn.ensemble import RandomForestClassifier
        
        # 创建模拟的standalone模型
        standalone_model = RandomForestClassifier(n_estimators=10, random_state=42)
        X_train = np.random.randn(100, 20)
        y_train = np.random.choice([0, 1, 2], size=100)
        standalone_model.fit(X_train, y_train)
        
        # 保存standalone模型
        self.standalone_model_path = os.path.join(self.models_dir, "standalone_eth_5m_model.joblib")
        joblib.dump(standalone_model, self.standalone_model_path)
        
        # 创建standalone配置
        standalone_config = {
            "model_type": "standalone",
            "class_labels": {"0": "lowest", "1": "neutral", "2": "highest"},
            "feature_list": [f"feature_{i}" for i in range(20)],
            "timeframe_minutes": 5,
            "up_threshold": 0.02,
            "down_threshold": -0.02,
            "max_lookforward_minutes": 240
        }
        
        self.standalone_config_path = os.path.join(self.models_dir, "standalone_eth_5m_config.json")
        with open(self.standalone_config_path, 'w') as f:
            json.dump(standalone_config, f)
        
        # 创建模拟的money模型
        money_model = RandomForestClassifier(n_estimators=10, random_state=42)
        y_money = np.random.choice([0, 1], size=100)
        money_model.fit(X_train, y_money)
        
        # 保存money模型
        self.money_model_path = os.path.join(self.models_dir, "eth_5m_model.joblib")
        joblib.dump(money_model, self.money_model_path)
        
        # 创建money配置
        money_config = {
            "model_type": "money_management",
            "up_threshold": 0.02,
            "down_threshold": -0.02,
            "feature_list": [f"feature_{i}" for i in range(20)],
            "timeframe_minutes": 5
        }
        
        self.money_config_path = os.path.join(self.models_dir, "eth_5m_config.json")
        with open(self.money_config_path, 'w') as f:
            json.dump(money_config, f)
    
    def test_ensemble_model_file_format(self):
        """测试集成模型文件格式兼容性"""
        print("\n=== 测试集成模型文件格式兼容性 ===")
        
        # 创建集成模型配置
        config = create_default_config(
            name="backtest_compatibility_test",
            timeframe_minutes=5,
            standalone_model_path=self.standalone_model_path,
            money_model_path=self.money_model_path
        )
        
        config.output.output_dir = self.models_dir
        
        # 创建训练器
        trainer = EnsembleTrainer(config)
        
        # 创建模拟数据
        test_data = pd.DataFrame({
            'open': np.random.randn(500) * 0.01 + 2500,
            'high': np.random.randn(500) * 0.01 + 2505,
            'low': np.random.randn(500) * 0.01 + 2495,
            'close': np.random.randn(500) * 0.01 + 2500,
            'volume': np.random.lognormal(8, 1, 500)
        }, index=pd.date_range('2024-01-01', periods=500, freq='5min'))
        
        try:
            # 训练集成模型
            model, model_path = trainer.run_training_pipeline(test_data)
            
            # 验证模型文件格式
            self.assertTrue(model_path.endswith('.joblib'))
            self.assertTrue(os.path.exists(model_path))
            
            # 验证可以用标准joblib加载
            loaded_model = joblib.load(model_path)
            self.assertIsNotNone(loaded_model)
            
            # 验证模型有必要的方法
            self.assertTrue(hasattr(loaded_model, 'predict'))
            self.assertTrue(hasattr(loaded_model, 'predict_proba'))
            
            print("✅ 集成模型文件格式兼容性验证通过")
            
        except Exception as e:
            self.fail(f"集成模型文件格式测试失败: {str(e)}")
    
    def test_ensemble_config_format(self):
        """测试集成模型配置文件格式兼容性"""
        print("\n=== 测试集成模型配置文件格式兼容性 ===")
        
        # 创建集成模型配置
        config = create_default_config(
            name="config_compatibility_test",
            timeframe_minutes=5,
            standalone_model_path=self.standalone_model_path,
            money_model_path=self.money_model_path
        )
        
        config.output.output_dir = self.models_dir
        
        # 创建训练器
        trainer = EnsembleTrainer(config)
        
        # 创建模拟数据
        test_data = pd.DataFrame({
            'open': np.random.randn(500) * 0.01 + 2500,
            'high': np.random.randn(500) * 0.01 + 2505,
            'low': np.random.randn(500) * 0.01 + 2495,
            'close': np.random.randn(500) * 0.01 + 2500,
            'volume': np.random.lognormal(8, 1, 500)
        }, index=pd.date_range('2024-01-01', periods=500, freq='5min'))
        
        try:
            # 训练集成模型
            model, model_path = trainer.run_training_pipeline(test_data)
            
            # 验证配置文件存在
            config_path = model_path.replace('.joblib', '_config.json')
            self.assertTrue(os.path.exists(config_path))
            
            # 加载并验证配置文件
            with open(config_path, 'r') as f:
                saved_config = json.load(f)
            
            # 验证必需的字段存在
            required_fields = [
                'model_type', 'feature_list', 'timeframe_minutes',
                'up_threshold', 'down_threshold'
            ]
            
            for field in required_fields:
                self.assertIn(field, saved_config, f"配置缺少必需字段: {field}")
            
            # 验证模型类型
            self.assertEqual(saved_config['model_type'], 'ensemble')
            
            # 验证特征列表不为空
            self.assertGreater(len(saved_config['feature_list']), 0)
            
            # 验证阈值设置
            self.assertIsInstance(saved_config['up_threshold'], (int, float))
            self.assertIsInstance(saved_config['down_threshold'], (int, float))
            
            # 验证时间框架
            self.assertEqual(saved_config['timeframe_minutes'], 5)
            
            print("✅ 集成模型配置文件格式兼容性验证通过")
            
        except Exception as e:
            self.fail(f"集成模型配置文件格式测试失败: {str(e)}")
    
    def test_prediction_interface_compatibility(self):
        """测试预测接口兼容性"""
        print("\n=== 测试预测接口兼容性 ===")
        
        # 创建集成模型配置
        config = create_default_config(
            name="prediction_compatibility_test",
            timeframe_minutes=5,
            standalone_model_path=self.standalone_model_path,
            money_model_path=self.money_model_path
        )
        
        config.output.output_dir = self.models_dir
        
        # 创建训练器
        trainer = EnsembleTrainer(config)
        
        # 创建模拟数据
        test_data = pd.DataFrame({
            'open': np.random.randn(500) * 0.01 + 2500,
            'high': np.random.randn(500) * 0.01 + 2505,
            'low': np.random.randn(500) * 0.01 + 2495,
            'close': np.random.randn(500) * 0.01 + 2500,
            'volume': np.random.lognormal(8, 1, 500)
        }, index=pd.date_range('2024-01-01', periods=500, freq='5min'))
        
        try:
            # 训练集成模型
            model, model_path = trainer.run_training_pipeline(test_data)
            
            # 加载保存的模型
            loaded_model = joblib.load(model_path)
            
            # 加载配置
            config_path = model_path.replace('.joblib', '_config.json')
            with open(config_path, 'r') as f:
                saved_config = json.load(f)
            
            # 创建测试特征数据
            feature_list = saved_config['feature_list']
            test_features = pd.DataFrame(
                np.random.randn(10, len(feature_list)),
                columns=feature_list
            )
            
            # 测试predict方法
            predictions = loaded_model.predict(test_features)
            self.assertEqual(len(predictions), 10)
            self.assertTrue(all(isinstance(p, (int, np.integer)) for p in predictions))
            
            # 测试predict_proba方法
            probabilities = loaded_model.predict_proba(test_features)
            self.assertEqual(probabilities.shape[0], 10)
            self.assertGreater(probabilities.shape[1], 1)  # 至少2个类别
            
            # 验证概率和为1
            prob_sums = np.sum(probabilities, axis=1)
            np.testing.assert_allclose(prob_sums, 1.0, rtol=1e-5)
            
            # 验证概率值在[0,1]范围内
            self.assertTrue(np.all(probabilities >= 0))
            self.assertTrue(np.all(probabilities <= 1))
            
            print("✅ 预测接口兼容性验证通过")
            print(f"预测形状: {predictions.shape}")
            print(f"概率形状: {probabilities.shape}")
            
        except Exception as e:
            self.fail(f"预测接口兼容性测试失败: {str(e)}")
    
    def test_feature_compatibility(self):
        """测试特征兼容性"""
        print("\n=== 测试特征兼容性 ===")
        
        # 创建模型加载器
        model_loader = ModelLoader()
        model_loader.load_standalone_model(self.standalone_model_path, self.standalone_config_path)
        model_loader.load_money_model(self.money_model_path, self.money_config_path)
        
        # 创建配置
        config = create_default_config(
            name="feature_compatibility_test",
            timeframe_minutes=5,
            standalone_model_path=self.standalone_model_path,
            money_model_path=self.money_model_path
        )
        
        # 创建特征生成器
        feature_generator = FeatureGenerator(model_loader, config)
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'open': np.random.randn(200) * 0.01 + 2500,
            'high': np.random.randn(200) * 0.01 + 2505,
            'low': np.random.randn(200) * 0.01 + 2495,
            'close': np.random.randn(200) * 0.01 + 2500,
            'volume': np.random.lognormal(8, 1, 200)
        }, index=pd.date_range('2024-01-01', periods=200, freq='5min'))
        
        try:
            # 生成特征
            features = feature_generator.generate_all_features(test_data, timeframe_minutes=5)
            
            # 验证特征数据格式
            self.assertIsInstance(features, pd.DataFrame)
            self.assertGreater(len(features.columns), 5)  # 应该有更多特征
            
            # 验证特征命名规范
            feature_names = features.columns.tolist()
            
            # 检查是否包含传统技术指标特征
            traditional_features = [col for col in feature_names if not col.startswith(('standalone_', 'money_'))]
            self.assertGreater(len(traditional_features), 0)
            
            # 检查是否包含模型特征（如果模型加载成功）
            model_features = [col for col in feature_names if col.startswith(('standalone_', 'money_'))]
            if model_loader.get_loaded_model_types():
                self.assertGreater(len(model_features), 0)
            
            # 验证特征数据质量
            self.assertFalse(features.isnull().all().any())  # 不应该有全为NaN的列
            
            print("✅ 特征兼容性验证通过")
            print(f"生成特征数量: {len(features.columns)}")
            print(f"传统特征数量: {len(traditional_features)}")
            print(f"模型特征数量: {len(model_features)}")
            
        except Exception as e:
            self.fail(f"特征兼容性测试失败: {str(e)}")
    
    def test_backtest_simulation(self):
        """模拟回测流程测试"""
        print("\n=== 模拟回测流程测试 ===")
        
        # 创建集成模型配置
        config = create_default_config(
            name="backtest_simulation_test",
            timeframe_minutes=5,
            standalone_model_path=self.standalone_model_path,
            money_model_path=self.money_model_path
        )
        
        config.output.output_dir = self.models_dir
        
        # 创建训练器
        trainer = EnsembleTrainer(config)
        
        # 创建训练数据
        train_data = pd.DataFrame({
            'open': np.random.randn(500) * 0.01 + 2500,
            'high': np.random.randn(500) * 0.01 + 2505,
            'low': np.random.randn(500) * 0.01 + 2495,
            'close': np.random.randn(500) * 0.01 + 2500,
            'volume': np.random.lognormal(8, 1, 500)
        }, index=pd.date_range('2024-01-01', periods=500, freq='5min'))
        
        # 创建回测数据
        backtest_data = pd.DataFrame({
            'open': np.random.randn(100) * 0.01 + 2500,
            'high': np.random.randn(100) * 0.01 + 2505,
            'low': np.random.randn(100) * 0.01 + 2495,
            'close': np.random.randn(100) * 0.01 + 2500,
            'volume': np.random.lognormal(8, 1, 100)
        }, index=pd.date_range('2024-02-01', periods=100, freq='5min'))
        
        try:
            # 1. 训练集成模型
            model, model_path = trainer.run_training_pipeline(train_data)
            
            # 2. 模拟回测流程
            # 加载模型和配置（模拟回测脚本的行为）
            loaded_model = joblib.load(model_path)
            config_path = model_path.replace('.joblib', '_config.json')
            
            with open(config_path, 'r') as f:
                model_config = json.load(f)
            
            # 3. 生成回测特征
            model_loader = ModelLoader()
            model_loader.load_standalone_model(self.standalone_model_path, self.standalone_config_path)
            model_loader.load_money_model(self.money_model_path, self.money_config_path)
            
            feature_generator = FeatureGenerator(model_loader, config)
            backtest_features = feature_generator.generate_all_features(backtest_data, timeframe_minutes=5)
            
            # 4. 进行预测（模拟回测中的预测步骤）
            feature_list = model_config['feature_list']
            prediction_features = backtest_features[feature_list]
            
            predictions = loaded_model.predict(prediction_features)
            probabilities = loaded_model.predict_proba(prediction_features)
            
            # 5. 验证回测结果
            self.assertEqual(len(predictions), len(backtest_data))
            self.assertEqual(probabilities.shape[0], len(backtest_data))
            
            # 6. 模拟回测统计
            unique_predictions = np.unique(predictions)
            prediction_counts = {pred: np.sum(predictions == pred) for pred in unique_predictions}
            
            print("✅ 回测流程模拟成功")
            print(f"回测数据量: {len(backtest_data)}")
            print(f"预测分布: {prediction_counts}")
            print(f"平均概率: {np.mean(probabilities, axis=0)}")
            
        except Exception as e:
            self.fail(f"回测流程模拟测试失败: {str(e)}")
    
    def test_legacy_interface_compatibility(self):
        """测试与旧版接口的兼容性"""
        print("\n=== 测试与旧版接口的兼容性 ===")
        
        # 创建集成模型配置
        config = create_default_config(
            name="legacy_compatibility_test",
            timeframe_minutes=5,
            standalone_model_path=self.standalone_model_path,
            money_model_path=self.money_model_path
        )
        
        config.output.output_dir = self.models_dir
        
        # 创建训练器
        trainer = EnsembleTrainer(config)
        
        # 创建模拟数据
        test_data = pd.DataFrame({
            'open': np.random.randn(500) * 0.01 + 2500,
            'high': np.random.randn(500) * 0.01 + 2505,
            'low': np.random.randn(500) * 0.01 + 2495,
            'close': np.random.randn(500) * 0.01 + 2500,
            'volume': np.random.lognormal(8, 1, 500)
        }, index=pd.date_range('2024-01-01', periods=500, freq='5min'))
        
        try:
            # 训练集成模型
            model, model_path = trainer.run_training_pipeline(test_data)
            
            # 加载模型和配置
            loaded_model = joblib.load(model_path)
            config_path = model_path.replace('.joblib', '_config.json')
            
            with open(config_path, 'r') as f:
                saved_config = json.load(f)
            
            # 测试旧版回测脚本可能使用的接口
            
            # 1. 测试模型属性访问
            self.assertTrue(hasattr(loaded_model, 'classes_'))
            self.assertTrue(hasattr(loaded_model, 'feature_importances_'))
            
            # 2. 测试配置字段访问
            legacy_fields = ['model_type', 'up_threshold', 'down_threshold', 'timeframe_minutes']
            for field in legacy_fields:
                self.assertIn(field, saved_config)
            
            # 3. 测试特征列表格式
            feature_list = saved_config['feature_list']
            self.assertIsInstance(feature_list, list)
            self.assertTrue(all(isinstance(f, str) for f in feature_list))
            
            # 4. 测试预测输出格式
            test_features = pd.DataFrame(
                np.random.randn(5, len(feature_list)),
                columns=feature_list
            )
            
            predictions = loaded_model.predict(test_features)
            probabilities = loaded_model.predict_proba(test_features)
            
            # 验证输出类型与旧版兼容
            self.assertIsInstance(predictions, np.ndarray)
            self.assertIsInstance(probabilities, np.ndarray)
            
            print("✅ 旧版接口兼容性验证通过")
            
        except Exception as e:
            self.fail(f"旧版接口兼容性测试失败: {str(e)}")


def run_backtest_compatibility_tests():
    """运行回测兼容性测试"""
    print("开始运行回测兼容性测试...")
    print("=" * 80)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加回测兼容性测试
    test_suite.addTest(TestBacktestCompatibility('test_ensemble_model_file_format'))
    test_suite.addTest(TestBacktestCompatibility('test_ensemble_config_format'))
    test_suite.addTest(TestBacktestCompatibility('test_prediction_interface_compatibility'))
    test_suite.addTest(TestBacktestCompatibility('test_feature_compatibility'))
    test_suite.addTest(TestBacktestCompatibility('test_backtest_simulation'))
    test_suite.addTest(TestBacktestCompatibility('test_legacy_interface_compatibility'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 80)
    if result.wasSuccessful():
        print("✅ 所有回测兼容性测试通过！")
        print("\n集成模型与现有回测系统完全兼容，可以直接替换使用。")
    else:
        print("❌ 部分回测兼容性测试失败")
        print(f"失败数量: {len(result.failures)}")
        print(f"错误数量: {len(result.errors)}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_backtest_compatibility_tests()
    sys.exit(0 if success else 1)