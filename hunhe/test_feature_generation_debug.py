#!/usr/bin/env python3
"""
调试特征生成的日志输出
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from hunhe.model_loader import ModelLoader
from hunhe.feature_generator import FeatureGenerator
from hunhe.ensemble_config import EnsembleConfig, FeatureSelectionConfig
from hunhe.utils import setup_logging

# 导入现有的特征计算函数
from model_utils_815 import calculate_features as calculate_money_features

logger = setup_logging()

def create_simple_test_data(n_rows=50):
    """创建简单的测试数据"""
    # 创建时间索引
    start_time = pd.Timestamp('2024-01-01')
    time_index = pd.date_range(start=start_time, periods=n_rows, freq='5min')
    
    # 生成模拟的OHLCV数据
    np.random.seed(42)
    
    # 生成价格数据
    base_price = 2500.0
    price_changes = np.random.normal(0, 0.001, n_rows)
    prices = base_price * np.exp(np.cumsum(price_changes))
    
    # 生成OHLCV数据
    data = []
    for i, price in enumerate(prices):
        high_offset = np.random.uniform(0, 0.005)
        low_offset = np.random.uniform(0, 0.005)
        
        high = price * (1 + high_offset)
        low = price * (1 - low_offset)
        
        open_price = np.random.uniform(low, high)
        close_price = np.random.uniform(low, high)
        volume = np.random.uniform(100, 1000)
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data, index=time_index)
    return df

def test_feature_generation_logging():
    """测试特征生成的日志输出"""
    logger.info("=" * 60)
    logger.info("开始测试特征生成日志输出")
    logger.info("=" * 60)
    
    # 创建测试数据
    test_data = create_simple_test_data(50)
    logger.info(f"创建测试数据完成，行数: {len(test_data)}")
    
    # 创建模型加载器
    model_loader = ModelLoader()
    
    # 尝试加载模型
    model_files = {
        'standalone': {
            'model': 'models/standalone_eth_5m_model.joblib',
            'config': 'models/standalone_eth_5m_config.json'
        },
        'money': {
            'model': 'models/eth_5m_model.joblib',
            'config': 'models/eth_5m_config.json'
        }
    }
    
    loaded_models = []
    for model_type, paths in model_files.items():
        model_path = Path(paths['model'])
        config_path = Path(paths['config'])
        
        if model_path.exists() and config_path.exists():
            try:
                if model_type == 'standalone':
                    model_loader.load_standalone_model(str(model_path), str(config_path))
                    loaded_models.append('standalone')
                    logger.info(f"✅ 成功加载 {model_type} 模型")
                elif model_type == 'money':
                    model_loader.load_money_model(str(model_path), str(config_path))
                    loaded_models.append('money')
                    logger.info(f"✅ 成功加载 {model_type} 模型")
            except Exception as e:
                logger.error(f"❌ 加载 {model_type} 模型失败: {str(e)}")
        else:
            logger.warning(f"⚠️  {model_type} 模型文件不存在")
    
    if not loaded_models:
        logger.error("没有成功加载任何模型，无法测试模型特征生成")
        return False
    
    # 创建配置
    config = EnsembleConfig(
        name="debug_test",
        timeframe_minutes=5,
        feature_selection=FeatureSelectionConfig(
            use_traditional_features=True,
            use_model_probabilities=True,
            use_model_predictions=True
        )
    )
    
    # 创建特征生成器
    feature_generator = FeatureGenerator(model_loader, config)
    
    # 准备特征数据
    logger.info("准备特征数据...")
    features_df = calculate_money_features(test_data.copy(), 5)
    
    # 添加standalone特征（如果需要）
    if 'standalone' in loaded_models:
        try:
            from oneway.standalone_utils import calculate_features as calculate_standalone_features
            standalone_features = calculate_standalone_features(test_data.copy(), 5)
            
            for col in standalone_features.columns:
                if col not in features_df.columns and col not in ['open', 'high', 'low', 'close', 'volume', 'timestamp', 'Timestamp']:
                    features_df[col] = standalone_features[col]
        except Exception as e:
            logger.warning(f"添加standalone特征时出错: {str(e)}")
    
    logger.info(f"特征数据准备完成，特征数: {len(features_df.columns)}")
    
    # 测试模型特征生成
    logger.info("=" * 40)
    logger.info("开始测试模型特征生成")
    logger.info("=" * 40)
    
    try:
        model_features = feature_generator.generate_model_features(features_df)
        
        logger.info("=" * 40)
        logger.info("模型特征生成测试结果:")
        logger.info(f"  生成的特征数: {len(model_features.columns)}")
        logger.info(f"  特征列名: {list(model_features.columns)}")
        logger.info(f"  数据行数: {len(model_features)}")
        logger.info("=" * 40)
        
        return True
        
    except Exception as e:
        logger.error(f"模型特征生成失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_feature_generation_logging()
    if success:
        logger.info("🎉 测试完成")
    else:
        logger.error("❌ 测试失败")