#!/usr/bin/env python3
"""
集成模型训练主脚本

使用示例:
python -m hunhe.main --config hunhe/sample_config.json
"""

import argparse
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from hunhe.ensemble_config import load_config, create_default_config, EnsembleConfig
from hunhe.ensemble_trainer import EnsembleTrainer
from hunhe.utils import setup_logging

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='集成模型训练系统')
    
    # 配置相关参数
    parser.add_argument(
        '--config', 
        type=str, 
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--name',
        type=str,
        default='ensemble_model',
        help='模型名称'
    )
    
    parser.add_argument(
        '--timeframe',
        type=int,
        default=5,
        help='时间框架（分钟）'
    )
    
    # 模型路径参数
    parser.add_argument(
        '--standalone-model',
        type=str,
        help='Standalone模型路径'
    )
    
    parser.add_argument(
        '--money-model', 
        type=str,
        help='Money management模型路径'
    )
    
    # 数据源配置参数
    parser.add_argument(
        '--symbol',
        type=str,
        default='ETHUSDT',
        help='交易对符号'
    )
    
    parser.add_argument(
        '--database',
        type=str,
        default='coin_data.db',
        help='数据库文件路径'
    )
    
    parser.add_argument(
        '--table-name',
        type=str,
        help='数据表名称（如果不指定，将根据symbol和timeframe自动生成）'
    )
    
    # 时间范围选择参数
    parser.add_argument(
        '--start-date',
        type=str,
        help='开始日期 (YYYY-MM-DD 格式)'
    )
    
    parser.add_argument(
        '--end-date',
        type=str,
        help='结束日期 (YYYY-MM-DD 格式)'
    )
    
    parser.add_argument(
        '--train-days',
        type=int,
        help='训练数据天数（从end-date往前计算）'
    )
    
    # 训练模式和参数配置
    parser.add_argument(
        '--mode',
        type=str,
        default='train',
        choices=['train', 'validate', 'test', 'full'],
        help='训练模式: train=仅训练, validate=训练+验证, test=训练+验证+测试, full=完整流程'
    )
    
    parser.add_argument(
        '--optimize',
        action='store_true',
        help='是否进行超参数优化'
    )
    
    parser.add_argument(
        '--n-trials',
        type=int,
        default=100,
        help='超参数优化试验次数'
    )
    
    parser.add_argument(
        '--cv-folds',
        type=int,
        default=5,
        help='交叉验证折数'
    )
    
    parser.add_argument(
        '--test-size',
        type=float,
        default=0.2,
        help='测试集比例'
    )
    
    parser.add_argument(
        '--validation-size',
        type=float,
        default=0.2,
        help='验证集比例'
    )
    
    # 特征选择参数
    parser.add_argument(
        '--feature-count',
        type=int,
        default=20,
        help='传统技术指标特征数量'
    )
    
    parser.add_argument(
        '--no-traditional-features',
        action='store_true',
        help='不使用传统技术指标特征'
    )
    
    parser.add_argument(
        '--no-model-probabilities',
        action='store_true',
        help='不使用模型概率特征'
    )
    
    parser.add_argument(
        '--no-model-predictions',
        action='store_true',
        help='不使用模型预测特征'
    )
    
    # 输出配置参数
    parser.add_argument(
        '--output-dir',
        type=str,
        default='models',
        help='模型输出目录'
    )
    
    parser.add_argument(
        '--save-features',
        action='store_true',
        help='保存特征重要性分析'
    )
    
    parser.add_argument(
        '--save-analysis',
        action='store_true',
        help='保存详细分析报告'
    )
    
    # 日志配置参数
    parser.add_argument(
        '--log-level',
        type=str,
        default='INFO',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='日志级别'
    )
    
    parser.add_argument(
        '--log-file',
        type=str,
        help='日志文件路径'
    )
    
    parser.add_argument(
        '--quiet',
        action='store_true',
        help='静默模式，只输出错误信息'
    )
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志级别（静默模式处理）
    log_level = 'ERROR' if args.quiet else args.log_level
    logger = setup_logging(log_level, args.log_file)
    
    if not args.quiet:
        logger.info("开始集成模型训练")
        logger.info(f"训练模式: {args.mode}")
        logger.info(f"数据源: {args.database}")
        logger.info(f"交易对: {args.symbol}")
        logger.info(f"时间框架: {args.timeframe}分钟")
    
    try:
        # 加载或创建配置
        if args.config:
            if not os.path.exists(args.config):
                logger.error(f"配置文件不存在: {args.config}")
                return 1
            config = load_config(args.config)
            # 用命令行参数覆盖配置文件中的设置
            _override_config_with_args(config, args)
        else:
            # 使用命令行参数创建配置
            config = _create_config_from_args(args)
            
        if not config.validate():
            logger.error("配置验证失败")
            return 1
        
        if not args.quiet:
            logger.info(f"使用配置: {config.name}")
            logger.info(f"包含模型: {list(config.models.keys())}")
            if args.start_date or args.end_date or args.train_days:
                logger.info(f"时间范围: {args.start_date} 到 {args.end_date}")
                if args.train_days:
                    logger.info(f"训练天数: {args.train_days}")
        
        # 创建训练器
        trainer = EnsembleTrainer(config)
        
        # 加载数据
        if not args.quiet:
            logger.info("加载训练数据...")
        
        data_df = _load_training_data(config, args, logger)
        if data_df is None or len(data_df) == 0:
            logger.error("无法加载训练数据")
            return 1
        
        if not args.quiet:
            logger.info(f"成功加载 {len(data_df)} 条数据记录")
            logger.info(f"数据时间范围: {data_df.index.min()} 到 {data_df.index.max()}")
        
        # 创建进度回调函数
        def progress_callback(step, total_steps, message):
            if not args.quiet:
                progress_pct = (step / total_steps) * 100
                logger.info(f"进度: {progress_pct:.1f}% - {message}")
        
        # 根据训练模式执行不同的流程
        if config.training.mode == 'train':
            # 仅训练模式
            if not args.quiet:
                logger.info("执行训练模式...")
            model, model_path = trainer.run_training_pipeline(
                data_df, 
                use_optimization=config.training.optimize_hyperparameters,
                progress_callback=progress_callback
            )
            
        elif config.training.mode == 'validate':
            # 训练+验证模式
            if not args.quiet:
                logger.info("执行训练+验证模式...")
            model, model_path = trainer.run_training_pipeline(
                data_df, 
                use_optimization=config.training.optimize_hyperparameters,
                progress_callback=progress_callback
            )
            _run_validation_analysis(trainer, model, data_df, config, logger)
            
        elif config.training.mode == 'test':
            # 训练+验证+测试模式
            if not args.quiet:
                logger.info("执行训练+验证+测试模式...")
            model, model_path = trainer.run_training_pipeline(
                data_df, 
                use_optimization=config.training.optimize_hyperparameters,
                progress_callback=progress_callback
            )
            _run_validation_analysis(trainer, model, data_df, config, logger)
            _run_test_analysis(trainer, model, data_df, config, logger)
            
        elif config.training.mode == 'full':
            # 完整流程模式
            if not args.quiet:
                logger.info("执行完整训练流程...")
            model, model_path = trainer.run_training_pipeline(
                data_df, 
                use_optimization=config.training.optimize_hyperparameters,
                progress_callback=progress_callback
            )
            _run_validation_analysis(trainer, model, data_df, config, logger)
            _run_test_analysis(trainer, model, data_df, config, logger)
            _run_performance_comparison(trainer, model, data_df, config, logger)
        
        # 保存分析报告
        if config.output.save_analysis:
            _save_training_report(trainer, model, data_df, config, args, logger)
        
        if not args.quiet:
            logger.info(f"集成模型训练完成，模型已保存到: {model_path}")
        
        return 0
        
    except Exception as e:
        logger.error(f"训练过程中发生错误: {str(e)}")
        return 1

def _create_config_from_args(args):
    """从命令行参数创建配置"""
    from hunhe.ensemble_config import EnsembleConfig
    
    # 构建数据源配置
    data_source = {
        'database_path': args.database,
        'symbol': args.symbol,
        'timeframe_minutes': args.timeframe
    }
    
    if args.table_name:
        data_source['table_name'] = args.table_name
    
    # 构建时间范围配置
    time_range = {}
    if args.start_date:
        time_range['start_date'] = args.start_date
    if args.end_date:
        time_range['end_date'] = args.end_date
    if args.train_days:
        time_range['train_days'] = args.train_days
    
    # 构建训练配置
    training_config = {
        'mode': args.mode,
        'optimize_hyperparameters': args.optimize,
        'n_trials': args.n_trials,
        'cv_folds': args.cv_folds,
        'test_size': args.test_size,
        'validation_size': args.validation_size
    }
    
    # 构建特征选择配置
    feature_selection = {
        'use_traditional_features': not args.no_traditional_features,
        'traditional_feature_count': args.feature_count,
        'use_model_probabilities': not args.no_model_probabilities,
        'use_model_predictions': not args.no_model_predictions
    }
    
    # 构建输出配置
    output_config = {
        'output_dir': args.output_dir,
        'save_features': args.save_features,
        'save_analysis': args.save_analysis
    }
    
    # 构建模型配置
    models = {}
    if args.standalone_model:
        models['standalone'] = {
            'model_path': args.standalone_model,
            'config_path': args.standalone_model.replace('.joblib', '_config.json'),
            'model_type': 'standalone'
        }
    if args.money_model:
        models['money'] = {
            'model_path': args.money_model,
            'config_path': args.money_model.replace('.joblib', '_config.json'),
            'model_type': 'money'
        }
    
    config_dict = {
        'name': args.name,
        'timeframe_minutes': args.timeframe,
        'data_source': data_source,
        'time_range': time_range,
        'training': training_config,
        'feature_selection': feature_selection,
        'output': output_config,
        'models': models
    }
    
    return EnsembleConfig.from_dict(config_dict)

def _override_config_with_args(config, args):
    """用命令行参数覆盖配置文件设置"""
    # 覆盖基本设置
    if args.name != 'ensemble_model':
        config.name = args.name
    
    # 覆盖数据源设置
    if hasattr(config, 'data_source') and config.data_source is not None:
        if args.database != 'coin_data.db':
            config.data_source.database_path = args.database
        if args.symbol != 'ETHUSDT':
            config.data_source.symbol = args.symbol
        if args.table_name:
            config.data_source.table_name = args.table_name
    
    # 覆盖时间范围设置
    if not hasattr(config, 'time_range') or config.time_range is None:
        from hunhe.ensemble_config import TimeRangeConfig
        config.time_range = TimeRangeConfig()
    if args.start_date:
        config.time_range.start_date = args.start_date
    if args.end_date:
        config.time_range.end_date = args.end_date
    if args.train_days:
        config.time_range.train_days = args.train_days
    
    # 覆盖训练设置
    if hasattr(config, 'training') and config.training is not None:
        if args.mode != 'train':
            config.training.mode = args.mode
        if args.optimize:
            config.training.optimize_hyperparameters = True
        if args.n_trials != 100:
            config.training.n_trials = args.n_trials
    
    # 覆盖特征选择设置
    if hasattr(config, 'feature_selection') and config.feature_selection is not None:
        if args.no_traditional_features:
            config.feature_selection.use_traditional_features = False
        if args.no_model_probabilities:
            config.feature_selection.use_model_probabilities = False
        if args.no_model_predictions:
            config.feature_selection.use_model_predictions = False
        if args.feature_count != 20:
            config.feature_selection.traditional_feature_count = args.feature_count
    
    # 覆盖输出设置
    if hasattr(config, 'output') and config.output is not None:
        if args.output_dir != 'models':
            config.output.output_dir = args.output_dir
        if args.save_features:
            config.output.save_features = True
        if args.save_analysis:
            config.output.save_analysis = True

def _load_training_data(config, args, logger):
    """加载训练数据"""
    import sqlite3
    import pandas as pd
    from datetime import datetime, timedelta
    
    try:
        # 连接数据库
        conn = sqlite3.connect(config.data_source.database_path)
        
        # 确定表名
        if config.data_source.table_name:
            table_name = config.data_source.table_name
        else:
            # 根据symbol和timeframe生成表名（保持原有大小写格式）
            symbol = config.data_source.symbol.upper()  # 改为大写
            timeframe = f"{config.data_source.timeframe_minutes}min"
            table_name = f"{symbol}_{timeframe}_spot"
        
        # 构建查询条件（只选择需要的列）
        query = f"SELECT timestamp, open, high, low, close, volume FROM {table_name}"
        conditions = []
        
        # 添加时间范围条件
        if config.time_range.start_date:
            # 将日期字符串转换为Unix时间戳
            start_ts = int(datetime.strptime(config.time_range.start_date, '%Y-%m-%d').timestamp())
            conditions.append(f"timestamp >= {start_ts}")
        
        if config.time_range.end_date:
            # 将日期字符串转换为Unix时间戳
            end_ts = int(datetime.strptime(config.time_range.end_date, '%Y-%m-%d').timestamp())
            conditions.append(f"timestamp <= {end_ts}")
        elif config.time_range.train_days:
            # 如果指定了训练天数，从当前日期往前计算
            end_date = datetime.now()
            start_date = end_date - timedelta(days=config.time_range.train_days)
            start_ts = int(start_date.timestamp())
            conditions.append(f"timestamp >= {start_ts}")
        
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        query += " ORDER BY timestamp"
        
        logger.info(f"执行查询: {query}")
        
        # 加载数据
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if len(df) == 0:
            logger.error(f"表 {table_name} 中没有找到数据")
            return None
        
        # 设置时间索引（从Unix时间戳转换）
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
        df.set_index('timestamp', inplace=True)
        
        # 确保必要的列存在
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            logger.error(f"数据缺少必要的列: {missing_cols}")
            return None
        
        return df
        
    except Exception as e:
        logger.error(f"加载数据时发生错误: {str(e)}")
        return None

def _run_validation_analysis(trainer, model, data_df, config, logger):
    """运行验证分析"""
    logger.info("执行验证分析...")
    
    # 这里可以添加更详细的验证分析逻辑
    # 例如：交叉验证、时序验证等
    
    logger.info("验证分析完成")

def _run_test_analysis(trainer, model, data_df, config, logger):
    """运行测试分析"""
    logger.info("执行测试分析...")
    
    # 这里可以添加更详细的测试分析逻辑
    # 例如：样本外测试、回测等
    
    logger.info("测试分析完成")

def _run_performance_comparison(trainer, model, data_df, config, logger):
    """运行性能对比分析"""
    logger.info("执行性能对比分析...")
    
    # 这里可以添加集成模型与单个组件模型的性能对比
    # 例如：AUC对比、准确率对比等
    
    logger.info("性能对比分析完成")

def _save_training_report(trainer, model, data_df, config, args, logger):
    """保存训练报告"""
    import os
    from datetime import datetime
    
    logger.info("生成训练报告...")
    
    report_content = f"""
# 集成模型训练报告

## 基本信息
- 模型名称: {config.name}
- 训练时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 数据源: {config.data_source.database_path}
- 交易对: {config.data_source.symbol}
- 时间框架: {config.timeframe_minutes}分钟

## 训练配置
- 训练模式: {config.training.mode}
- 超参数优化: {config.training.optimize_hyperparameters}
- 测试集比例: {config.training.test_size}
- 验证集比例: {config.training.validation_size}

## 数据信息
- 总数据量: {len(data_df)}条
- 数据时间范围: {data_df.index.min()} 到 {data_df.index.max()}

## 特征配置
- 使用传统特征: {config.feature_selection.use_traditional_features}
- 传统特征数量: {config.feature_selection.traditional_feature_count}
- 使用模型概率: {config.feature_selection.use_model_probabilities}
- 使用模型预测: {config.feature_selection.use_model_predictions}

## 组件模型
"""
    
    for model_name, model_config in config.models.items():
        report_content += f"- {model_name}: {model_config.model_path}\n"
    
    report_content += f"""
## 命令行参数
"""
    
    for arg, value in vars(args).items():
        if value is not None:
            report_content += f"- {arg}: {value}\n"
    
    # 保存报告
    report_filename = f"training_report_{config.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    report_path = os.path.join(config.output.output_dir, report_filename)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    logger.info(f"训练报告已保存到: {report_path}")

if __name__ == "__main__":
    sys.exit(main())