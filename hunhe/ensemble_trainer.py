"""
集成模型训练器模块

主要的训练逻辑和流程控制
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Tuple, Optional, List
import logging
from .ensemble_config import EnsembleConfig
from .model_loader import ModelLoader
from .feature_generator import FeatureGenerator
from .utils import setup_logging, get_timestamp

logger = setup_logging()

class EnsembleTrainer:
    """
    集成模型训练器类
    
    负责协调整个训练流程
    """
    
    def __init__(self, config: EnsembleConfig):
        """
        初始化训练器
        
        Args:
            config: 集成模型配置
        """
        self.config = config
        self.model_loader = ModelLoader()
        
        # 加载配置中指定的模型
        self._load_configured_models()
        
        self.feature_generator = FeatureGenerator(self.model_loader, config)
    
    def _load_configured_models(self):
        """加载配置中指定的模型"""
        logger.info("加载配置中的模型...")
        
        loaded_count = 0
        for model_name, model_config in self.config.models.items():
            try:
                logger.info(f"尝试加载 {model_name} 模型: {model_config.model_path}")
                
                if model_config.model_type == 'standalone':
                    self.model_loader.load_standalone_model(
                        model_config.model_path, 
                        model_config.config_path
                    )
                    loaded_count += 1
                    logger.info(f"✅ 成功加载 standalone 模型")
                    
                elif model_config.model_type == 'money':
                    self.model_loader.load_money_model(
                        model_config.model_path, 
                        model_config.config_path
                    )
                    loaded_count += 1
                    logger.info(f"✅ 成功加载 money management 模型")
                    
                else:
                    logger.warning(f"不支持的模型类型: {model_config.model_type}")
                    
            except Exception as e:
                logger.error(f"❌ 加载 {model_name} 模型失败: {str(e)}")
        
        if loaded_count == 0:
            logger.warning("没有成功加载任何模型，将只使用传统技术指标特征")
        else:
            logger.info(f"成功加载 {loaded_count} 个模型")
            
        # 验证加载的模型
        loaded_models = self.model_loader.get_loaded_model_types()
        logger.info(f"当前已加载的模型类型: {loaded_models}")
        
    def prepare_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        准备训练数据
        
        Args:
            df: 原始数据
            
        Returns:
            准备好的训练数据
        """
        logger.info("开始准备训练数据")
        
        # 1. 生成集成特征（包括传统技术指标和模型预测特征）
        logger.info("1.生成集成特征...")
        df_with_features = self.feature_generator.generate_all_features(df.copy(), timeframe_minutes=self.config.timeframe_minutes)
        
        # 2. 创建训练标签
        logger.info("2.创建训练标签...")
        target_labels = self.create_labels(df)
        
        # 3. 合并特征和标签
        logger.info("3.合并特征与标签...")
        df_combined = df_with_features.join(target_labels.rename('label'), how='inner')
        
        # 4. 清理缺失值
        df_clean = df_combined.dropna()
        logger.info(f"数据准备完成，清理后剩余 {len(df_clean)} 条记录")
        
        return df_clean
    
    def create_labels(self, df: pd.DataFrame) -> pd.Series:
        """
        创建训练标签 - 使用与现有模型相同的目标定义
        
        Args:
            df: 包含价格数据的DataFrame
            
        Returns:
            训练标签Series
        """
        logger.info("创建训练标签")
        
        up_threshold = self.config.up_threshold
        down_threshold = self.config.down_threshold
        max_lookforward_minutes = self.config.max_lookforward_minutes
        timeframe = self.config.timeframe_minutes
        
        max_lookforward_candles = max_lookforward_minutes // timeframe
        
        logger.info(f"标签参数: 先涨{up_threshold*100:.1f}%或先跌{down_threshold*100:.1f}%, "
                   f"最大前瞻{max_lookforward_minutes}分钟({max_lookforward_candles}根K线)")
        
        labels = []
        valid_indices = []
        
        for i in range(len(df)):
            if i % 10000 == 0:
                logger.info(f"标签生成进度: {i}/{len(df)} ({i/len(df)*100:.1f}%)")
            
            current_price = df.iloc[i]['close']
            up_target = current_price * (1 + up_threshold)
            down_target = current_price * (1 - down_threshold)  # down_threshold是正数
            
            label = None
            for j in range(1, min(max_lookforward_candles + 1, len(df) - i)):
                future_price = df.iloc[i + j]['close']
                if future_price >= up_target:
                    label = 1  # 先涨
                    break
                elif future_price <= down_target:
                    label = 0  # 先跌
                    break
            
            if label is not None:
                labels.append(label)
                valid_indices.append(i)
        
        logger.info(f"有效标签数量: {len(labels)}/{len(df)} ({len(labels)/len(df)*100:.1f}%)")
        
        if len(labels) > 0:
            up_count = sum(labels)
            down_count = len(labels) - up_count
            logger.info(f"标签分布: 先涨={up_count}({up_count/len(labels)*100:.1f}%), "
                       f"先跌={down_count}({down_count/len(labels)*100:.1f}%)")
        else:
            logger.warning("未生成任何有效标签")
        
        return pd.Series(index=df.index[valid_indices], data=labels)
    
    def split_data(self, df_clean: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        按时间顺序分割训练数据
        
        Args:
            df_clean: 清理后的完整数据
            
        Returns:
            训练集、验证集、测试集的元组
        """
        logger.info("分割训练数据")
        
        # 使用配置中的分割比例
        test_size = self.config.training.test_size
        validation_size = self.config.training.validation_size
        
        # 计算分割点
        total_size = len(df_clean)
        train_size = int(total_size * (1 - test_size - validation_size))
        val_size = int(total_size * validation_size)
        
        # 按时间顺序分割
        train_df = df_clean.iloc[:train_size]
        val_df = df_clean.iloc[train_size:train_size + val_size]
        test_df = df_clean.iloc[train_size + val_size:]
        
        logger.info(f"数据分割完成:")
        logger.info(f"  训练集: {len(train_df)} 条 ({train_df.index.min()} 到 {train_df.index.max()})")
        logger.info(f"  验证集: {len(val_df)} 条 ({val_df.index.min()} 到 {val_df.index.max()})")
        logger.info(f"  测试集: {len(test_df)} 条 ({test_df.index.min()} 到 {test_df.index.max()})")
        
        return train_df, val_df, test_df
    
    def train_model(self, X: pd.DataFrame, y: pd.Series) -> Any:
        """
        训练集成模型
        
        Args:
            X: 特征数据
            y: 标签数据
            
        Returns:
            训练好的模型
        """
        logger.info("开始训练集成模型")
        
        import lightgbm as lgb
        from sklearn.calibration import CalibratedClassifierCV
        
        # 使用LightGBM作为基础模型
        lgbm_params = {
            'objective': 'binary',
            'metric': 'auc',
            'random_state': self.config.training.random_state,
            'verbose': -1,
            'n_jobs': -1,
            'n_estimators': 1000,
            'learning_rate': 0.05,
            'max_depth': 6,
            'num_leaves': 31,
            'min_child_samples': 20,
            'subsample': 0.8,
            'colsample_bytree': 0.8
        }
        
        logger.info(f"训练LightGBM模型，特征数量: {len(X.columns)}")
        lgbm = lgb.LGBMClassifier(**lgbm_params)
        
        # 训练基础模型
        lgbm.fit(X, y)
        
        # 使用概率校准提高预测质量
        logger.info("应用概率校准")
        calibrated_model = CalibratedClassifierCV(lgbm, method='isotonic', cv=3)
        calibrated_model.fit(X, y)
        
        logger.info("模型训练完成")
        return calibrated_model
    
    def optimize_hyperparameters(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """
        使用时序交叉验证优化超参数
        
        Args:
            X: 特征数据
            y: 标签数据
            
        Returns:
            最佳超参数字典
        """
        logger.info("开始超参数优化")
        
        import lightgbm as lgb
        from sklearn.model_selection import TimeSeriesSplit
        from sklearn.metrics import roc_auc_score
        import optuna
        
        # 创建时序分割器
        tscv = TimeSeriesSplit(n_splits=self.config.training.cv_folds)
        
        def objective(trial):
            # 定义超参数搜索空间
            params = {
                'objective': 'binary',
                'metric': 'auc',
                'random_state': self.config.training.random_state,
                'verbose': -1,
                'n_jobs': -1,
                'n_estimators': trial.suggest_int('n_estimators', 500, 2000),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.1, log=True),
                'max_depth': trial.suggest_int('max_depth', 4, 10),
                'num_leaves': trial.suggest_int('num_leaves', 20, 100),
                'min_child_samples': trial.suggest_int('min_child_samples', 10, 100),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
            }
            
            fold_scores = []
            
            # 时序交叉验证
            for train_idx, val_idx in tscv.split(X):
                X_train_fold = X.iloc[train_idx]
                X_val_fold = X.iloc[val_idx]
                y_train_fold = y.iloc[train_idx]
                y_val_fold = y.iloc[val_idx]
                
                # 检查类别分布
                if len(y_train_fold.unique()) < 2 or len(y_val_fold.unique()) < 2:
                    continue
                
                try:
                    # 训练模型
                    lgbm = lgb.LGBMClassifier(**params)
                    lgbm.fit(
                        X_train_fold, y_train_fold,
                        eval_set=[(X_val_fold, y_val_fold)],
                        eval_metric='auc',
                        callbacks=[lgb.early_stopping(
                            stopping_rounds=self.config.training.early_stopping_rounds,
                            verbose=False
                        )]
                    )
                    
                    # 预测和评估
                    y_pred_proba = lgbm.predict_proba(X_val_fold)[:, 1]
                    auc_score = roc_auc_score(y_val_fold, y_pred_proba)
                    fold_scores.append(auc_score)
                    
                except Exception as e:
                    logger.warning(f"交叉验证fold失败: {e}")
                    continue
            
            if len(fold_scores) == 0:
                return 0.5  # 返回随机猜测的AUC
            
            return np.mean(fold_scores)
        
        # 运行优化
        optuna.logging.set_verbosity(optuna.logging.WARNING)
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=50)
        
        logger.info(f"超参数优化完成，最佳AUC: {study.best_value:.4f}")
        logger.info(f"最佳参数: {study.best_params}")
        
        return study.best_params
    
    def train_with_optimization(self, train_df: pd.DataFrame, val_df: pd.DataFrame, 
                               features: List[str]) -> Tuple[Any, Dict[str, Any]]:
        """
        使用超参数优化训练模型
        
        Args:
            train_df: 训练数据
            val_df: 验证数据
            features: 特征列表
            
        Returns:
            训练好的模型和最佳参数
        """
        logger.info("开始带优化的模型训练")
        
        import lightgbm as lgb
        from sklearn.calibration import CalibratedClassifierCV
        
        # 准备训练数据
        X_train = train_df[features]
        y_train = train_df['label']
        X_val = val_df[features]
        y_val = val_df['label']
        
        # 合并训练和验证数据用于超参数优化
        X_combined = pd.concat([X_train, X_val])
        y_combined = pd.concat([y_train, y_val])
        
        # 超参数优化
        best_params = self.optimize_hyperparameters(X_combined, y_combined)
        
        # 使用最佳参数训练最终模型
        final_params = {
            'objective': 'binary',
            'metric': 'auc',
            'random_state': self.config.training.random_state,
            'verbose': -1,
            'n_jobs': -1,
        }
        final_params.update(best_params)
        
        logger.info("使用最佳参数训练最终模型")
        lgbm = lgb.LGBMClassifier(**final_params)
        lgbm.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            eval_metric='auc',
            callbacks=[lgb.early_stopping(
                stopping_rounds=self.config.training.early_stopping_rounds,
                verbose=False
            )]
        )
        
        # 概率校准
        logger.info("应用概率校准")
        calibrated_model = CalibratedClassifierCV(lgbm, method='isotonic', cv='prefit')
        calibrated_model.fit(X_val, y_val)
        
        return calibrated_model, best_params
    
    def save_model(self, model: Any, config: Dict[str, Any]) -> str:
        """
        保存模型和配置
        
        Args:
            model: 训练好的模型
            config: 模型配置
            
        Returns:
            保存的文件路径
        """
        import joblib
        import json
        import os
        from datetime import datetime
        
        # 确保输出目录存在
        os.makedirs(self.config.output.output_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = get_timestamp()
        model_filename = f"{self.config.name}_ensemble_model.joblib"
        config_filename = f"{self.config.name}_ensemble_config.json"
        
        model_path = os.path.join(self.config.output.output_dir, model_filename)
        config_path = os.path.join(self.config.output.output_dir, config_filename)
        
        # 保存模型
        logger.info(f"保存模型到: {model_path}")
        joblib.dump(model, model_path)
        
        # 保存配置
        logger.info(f"保存配置到: {config_path}")
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        return model_path
    
    def generate_model_config(self, model: Any, features: List[str], 
                             train_size: int, val_size: int, test_size: int,
                             best_params: Optional[Dict[str, Any]] = None,
                             performance_metrics: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """
        生成模型配置文件
        
        Args:
            model: 训练好的模型
            features: 特征列表
            train_size: 训练集大小
            val_size: 验证集大小
            test_size: 测试集大小
            best_params: 最佳超参数
            performance_metrics: 性能指标
            
        Returns:
            配置字典
        """
        from datetime import datetime
        
        # 基础配置
        config = {
            'model_type': f'LGBM_Ensemble_{self.config.name}',
            'ensemble_name': self.config.name,
            'target_description': f'ensemble_predict_first_{self.config.up_threshold*100}%_move_within_{self.config.max_lookforward_minutes}_minutes',
            'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            
            # 数据集信息
            'train_size': train_size,
            'val_size': val_size,
            'test_size': test_size,
            
            # 模型参数
            'up_threshold': self.config.up_threshold,
            'down_threshold': self.config.down_threshold,
            'max_lookforward_minutes': self.config.max_lookforward_minutes,
            'timeframe_minutes': self.config.timeframe_minutes,
            
            # 特征信息
            'feature_list': features,
            'feature_count': len(features),
            
            # 组件模型信息
            'component_models': {},
            
            # 集成配置
            'ensemble_config': self.config.to_dict(),
            
            # 算法版本
            'algorithm_version': 'ensemble_v1',
            'ensemble_method': 'lgbm_with_model_features'
        }
        
        # 添加组件模型信息
        for model_name, model_config in self.config.models.items():
            config['component_models'][model_name] = {
                'model_path': model_config.model_path,
                'config_path': model_config.config_path,
                'model_type': model_config.model_type
            }
        
        # 添加超参数信息
        if best_params:
            config['best_params'] = best_params
            config['hyperparameter_optimization'] = True
        else:
            config['hyperparameter_optimization'] = False
        
        # 添加性能指标
        if performance_metrics:
            config.update(performance_metrics)
        
        # 添加特征选择配置
        config['feature_selection'] = {
            'use_traditional_features': self.config.feature_selection.use_traditional_features,
            'traditional_feature_count': self.config.feature_selection.traditional_feature_count,
            'use_model_probabilities': self.config.feature_selection.use_model_probabilities,
            'use_model_predictions': self.config.feature_selection.use_model_predictions,
            'feature_importance_threshold': self.config.feature_selection.feature_importance_threshold
        }
        
        # 添加训练配置
        config['training_config'] = {
            'test_size': self.config.training.test_size,
            'validation_size': self.config.training.validation_size,
            'random_state': self.config.training.random_state,
            'cv_folds': self.config.training.cv_folds,
            'early_stopping_rounds': self.config.training.early_stopping_rounds
        }
        
        return config
    
    def save_feature_importance(self, model: Any, features: List[str]) -> str:
        """
        保存特征重要性分析
        
        Args:
            model: 训练好的模型
            features: 特征列表
            
        Returns:
            特征重要性文件路径
        """
        import pandas as pd
        import os
        
        # 获取基础模型（从校准模型中提取）
        base_model = model.base_estimator if hasattr(model, 'base_estimator') else model
        
        if hasattr(base_model, 'feature_importances_'):
            # 创建特征重要性DataFrame
            importance_df = pd.DataFrame({
                'feature': features,
                'importance': base_model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            # 保存到文件
            importance_filename = f"feature_importance_ensemble_{self.config.name}.csv"
            importance_path = os.path.join(self.config.output.output_dir, importance_filename)
            
            importance_df.to_csv(importance_path, index=False)
            logger.info(f"特征重要性已保存到: {importance_path}")
            
            # 打印前15个重要特征
            logger.info("前15个重要特征:")
            for i, row in importance_df.head(15).iterrows():
                logger.info(f"  {row['feature']}: {row['importance']:.4f}")
            
            return importance_path
        else:
            logger.warning("模型不支持特征重要性分析")
            return ""
    
    def run_training_pipeline(self, df: pd.DataFrame, use_optimization: bool = True, 
                             progress_callback=None) -> Tuple[Any, str]:
        """
        运行完整的训练流程
        
        Args:
            df: 原始数据
            use_optimization: 是否使用超参数优化
            progress_callback: 进度回调函数
            
        Returns:
            训练好的模型和模型文件路径
        """
        def report_progress(step, total_steps, message):
            if progress_callback:
                progress_callback(step, total_steps, message)
            logger.info(f"[{step}/{total_steps}] {message}")
        
        total_steps = 8
        report_progress(0, total_steps, "开始集成模型训练流程")
        
        # 1. 准备数据
        report_progress(1, total_steps, "准备训练数据...")
        df_clean = self.prepare_data(df)
        print(df_clean.iloc[1])
        # 2. 分割数据
        report_progress(2, total_steps, "分割训练数据...")
        train_df, val_df, test_df = self.split_data(df_clean)
        
        # 3. 获取特征列表
        report_progress(3, total_steps, "准备特征列表...")
        feature_cols = self.config.get_feature_columns()
        
        # 添加传统技术指标特征
        if self.config.feature_selection.use_traditional_features:
            # 获取所有可用特征，排除标签列和基础OHLCV列
            available_features = [col for col in df_clean.columns 
                                if col not in ['label', 'open', 'high', 'low', 'close', 'volume']]
            
            # 选择前N个重要的传统特征
            traditional_features = available_features[:self.config.feature_selection.traditional_feature_count]
            feature_cols.extend(traditional_features)
        
        # 去重并确保特征存在
        features = list(set(feature_cols))
        features = [f for f in features if f in df_clean.columns]
        
        logger.info(f"使用 {len(features)} 个特征进行训练")
        print(features)
        # 4. 训练模型
        if use_optimization:
            report_progress(4, total_steps, "训练模型（包含超参数优化）...")
            model, best_params = self.train_with_optimization(train_df, val_df, features)
        else:
            report_progress(4, total_steps, "训练模型...")
            X_train = train_df[features]
            y_train = train_df['label']
            model = self.train_model(X_train, y_train)
            best_params = None
        
        # 5. 评估模型
        report_progress(5, total_steps, "评估模型性能...")
        performance_metrics = self._evaluate_model(model, test_df, features)
        
        # 6. 生成配置
        report_progress(6, total_steps, "生成模型配置...")
        config = self.generate_model_config(
            model, features, len(train_df), len(val_df), len(test_df),
            best_params, performance_metrics
        )
        
        # 7. 保存模型和配置
        report_progress(7, total_steps, "保存模型和配置...")
        model_path = self.save_model(model, config)
        
        # 8. 保存特征重要性
        report_progress(8, total_steps, "保存特征重要性分析...")
        if self.config.output.save_features:
            self.save_feature_importance(model, features)
        
        report_progress(8, total_steps, "集成模型训练流程完成")
        return model, model_path
    
    def _evaluate_model(self, model: Any, test_df: pd.DataFrame, features: List[str]) -> Dict[str, float]:
        """
        评估模型性能
        
        Args:
            model: 训练好的模型
            test_df: 测试数据
            features: 特征列表
            
        Returns:
            性能指标字典
        """
        from sklearn.metrics import accuracy_score, roc_auc_score, precision_score, recall_score, f1_score
        
        X_test = test_df[features]
        y_test = test_df['label']
        
        # 预测
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1]
        
        # 计算指标
        metrics = {
            'test_accuracy': float(accuracy_score(y_test, y_pred)),
            'test_auc': float(roc_auc_score(y_test, y_pred_proba)),
            'test_precision': float(precision_score(y_test, y_pred, average='weighted')),
            'test_recall': float(recall_score(y_test, y_pred, average='weighted')),
            'test_f1': float(f1_score(y_test, y_pred, average='weighted'))
        }
        
        logger.info("测试集性能:")
        for metric, value in metrics.items():
            logger.info(f"  {metric}: {value:.4f}")
        
        return metrics