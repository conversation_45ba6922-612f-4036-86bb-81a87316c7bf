"""
工具函数和常量定义
"""

import os
import logging
from typing import Dict, Any, Optional
import pandas as pd
import numpy as np
from datetime import datetime

# 常量定义
DEFAULT_TIMEFRAMES = {
    '1m': 1,
    '5m': 5, 
    '15m': 15,
    '1h': 60,
    '4h': 240,
    '1d': 1440
}

# 模型类型常量
MODEL_TYPES = {
    'STANDALONE': 'standalone',
    'MONEY': 'money',
    'ENSEMBLE': 'ensemble'
}

# 预测类别常量
STANDALONE_CLASSES = {
    'LOWEST': 0,
    'NEUTRAL': 1, 
    'HIGHEST': 2
}

MONEY_CLASSES = {
    'DOWN': 0,
    'UP': 1
}

# 特征前缀
FEATURE_PREFIXES = {
    'STANDALONE': 'standalone_',
    'MONEY': 'money_',
    'TRADITIONAL': 'tech_'
}

def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None) -> logging.Logger:
    """
    设置日志配置
    
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
        log_file: 日志文件路径，如果为None则只输出到控制台
        
    Returns:
        配置好的logger对象
    """
    logger = logging.getLogger('ensemble_training')
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有的handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件handler（如果指定了文件路径）
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def validate_model_files(model_path: str, config_path: str) -> bool:
    """
    验证模型文件是否存在
    
    Args:
        model_path: 模型文件路径
        config_path: 配置文件路径
        
    Returns:
        bool: 文件是否都存在
    """
    return os.path.exists(model_path) and os.path.exists(config_path)

def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    安全除法，避免除零错误
    
    Args:
        numerator: 分子
        denominator: 分母
        default: 当分母为0时的默认值
        
    Returns:
        除法结果或默认值
    """
    if denominator == 0 or pd.isna(denominator):
        return default
    return numerator / denominator

def calculate_price_change_pct(current_price: float, future_price: float) -> float:
    """
    计算价格变化百分比
    
    Args:
        current_price: 当前价格
        future_price: 未来价格
        
    Returns:
        价格变化百分比
    """
    return safe_divide(future_price - current_price, current_price) * 100

def get_timeframe_minutes(timeframe: str) -> int:
    """
    获取时间框架对应的分钟数
    
    Args:
        timeframe: 时间框架字符串 (如 '5m', '15m', '1h')
        
    Returns:
        对应的分钟数
    """
    return DEFAULT_TIMEFRAMES.get(timeframe, 5)

def create_feature_name(prefix: str, feature: str) -> str:
    """
    创建标准化的特征名称
    
    Args:
        prefix: 特征前缀
        feature: 特征名称
        
    Returns:
        标准化的特征名称
    """
    return f"{prefix}{feature}"

def validate_dataframe(df: pd.DataFrame, required_columns: list) -> bool:
    """
    验证DataFrame是否包含必需的列
    
    Args:
        df: 要验证的DataFrame
        required_columns: 必需的列名列表
        
    Returns:
        bool: 是否包含所有必需的列
    """
    missing_columns = set(required_columns) - set(df.columns)
    if missing_columns:
        logging.warning(f"DataFrame缺少必需的列: {missing_columns}")
        return False
    return True

def get_timestamp() -> str:
    """
    获取当前时间戳字符串
    
    Returns:
        格式化的时间戳字符串
    """
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def ensure_directory(directory_path: str) -> None:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        directory_path: 目录路径
    """
    os.makedirs(directory_path, exist_ok=True)

def clean_feature_names(df: pd.DataFrame) -> pd.DataFrame:
    """
    清理DataFrame的列名，移除特殊字符
    
    Args:
        df: 输入DataFrame
        
    Returns:
        列名已清理的DataFrame
    """
    df = df.copy()
    df.columns = [col.replace('[', '').replace(']', '').replace('<', '').replace('>', '') 
                  for col in df.columns]
    return df

def calculate_feature_stats(df: pd.DataFrame, feature_cols: list) -> Dict[str, Any]:
    """
    计算特征的统计信息
    
    Args:
        df: 包含特征的DataFrame
        feature_cols: 特征列名列表
        
    Returns:
        特征统计信息字典
    """
    stats = {}
    for col in feature_cols:
        if col in df.columns:
            stats[col] = {
                'mean': df[col].mean(),
                'std': df[col].std(),
                'min': df[col].min(),
                'max': df[col].max(),
                'null_count': df[col].isnull().sum(),
                'null_pct': df[col].isnull().sum() / len(df) * 100
            }
    return stats