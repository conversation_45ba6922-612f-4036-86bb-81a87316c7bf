#!/usr/bin/env python3
"""
集成测试脚本

测试集成模型训练系统的端到端功能，包括：
1. 完整训练流程测试
2. 与现有回测系统的兼容性测试
3. 模型保存和加载测试
4. 配置文件兼容性测试
"""

import unittest
import pandas as pd
import numpy as np
import tempfile
import os
import json
import joblib
import sqlite3
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hunhe.ensemble_config import EnsembleConfig, create_default_config
from hunhe.ensemble_trainer import EnsembleTrainer
from hunhe.model_loader import ModelLoader
from hunhe.feature_generator import FeatureGenerator
from hunhe.utils import setup_logging


class TestEndToEndIntegration(unittest.TestCase):
    """端到端集成测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_db_path = os.path.join(self.temp_dir, "test_data.db")
        self.models_dir = os.path.join(self.temp_dir, "models")
        os.makedirs(self.models_dir, exist_ok=True)
        
        # 创建测试数据库和数据
        self._create_test_database()
        
        # 创建模拟的组件模型
        self._create_mock_component_models()
    
    def tearDown(self):
        """测试后的清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def _create_test_database(self):
        """创建测试数据库和数据"""
        conn = sqlite3.connect(self.test_db_path)
        
        # 创建测试表
        conn.execute("""
            CREATE TABLE ethusdt_5min_spot (
                timestamp TEXT PRIMARY KEY,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                volume REAL
            )
        """)
        
        # 生成测试数据
        np.random.seed(42)
        start_time = datetime(2024, 1, 1)
        n_records = 2000  # 约7天的5分钟数据
        
        data = []
        base_price = 2500.0
        current_price = base_price
        
        for i in range(n_records):
            timestamp = start_time + timedelta(minutes=5*i)
            
            # 模拟价格变动
            price_change = np.random.normal(0, 0.005)
            current_price *= (1 + price_change)
            
            # 生成OHLCV数据
            open_price = current_price
            high_price = current_price * (1 + abs(np.random.normal(0, 0.003)))
            low_price = current_price * (1 - abs(np.random.normal(0, 0.003)))
            close_price = current_price * (1 + np.random.normal(0, 0.001))
            volume = np.random.lognormal(8, 1)
            
            data.append((
                timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                open_price, high_price, low_price, close_price, volume
            ))
            
            current_price = close_price
        
        # 插入数据
        conn.executemany(
            "INSERT INTO ethusdt_5min_spot VALUES (?, ?, ?, ?, ?, ?)",
            data
        )
        conn.commit()
        conn.close()
    
    def _create_mock_component_models(self):
        """创建模拟的组件模型"""
        # 创建模拟的standalone模型
        from sklearn.ensemble import RandomForestClassifier
        standalone_model = RandomForestClassifier(n_estimators=10, random_state=42)
        
        # 创建模拟训练数据来训练模型
        X_train = np.random.randn(100, 20)
        y_train = np.random.choice([0, 1, 2], size=100)
        standalone_model.fit(X_train, y_train)
        
        # 保存standalone模型
        self.standalone_model_path = os.path.join(self.models_dir, "standalone_eth_5m_model.joblib")
        joblib.dump(standalone_model, self.standalone_model_path)
        
        # 创建standalone配置
        standalone_config = {
            "model_type": "standalone",
            "class_labels": {"0": "lowest", "1": "neutral", "2": "highest"},
            "feature_list": [f"feature_{i}" for i in range(20)],
            "timeframe_minutes": 5,
            "up_threshold": 0.02,
            "down_threshold": -0.02,
            "max_lookforward_minutes": 240
        }
        
        self.standalone_config_path = os.path.join(self.models_dir, "standalone_eth_5m_config.json")
        with open(self.standalone_config_path, 'w') as f:
            json.dump(standalone_config, f)
        
        # 创建模拟的money模型
        money_model = RandomForestClassifier(n_estimators=10, random_state=42)
        y_money = np.random.choice([0, 1], size=100)
        money_model.fit(X_train, y_money)
        
        # 保存money模型
        self.money_model_path = os.path.join(self.models_dir, "eth_5m_model.joblib")
        joblib.dump(money_model, self.money_model_path)
        
        # 创建money配置
        money_config = {
            "model_type": "money_management",
            "up_threshold": 0.02,
            "down_threshold": -0.02,
            "feature_list": [f"feature_{i}" for i in range(20)],
            "timeframe_minutes": 5
        }
        
        self.money_config_path = os.path.join(self.models_dir, "eth_5m_config.json")
        with open(self.money_config_path, 'w') as f:
            json.dump(money_config, f)
    
    def test_complete_training_pipeline(self):
        """测试完整的训练流程"""
        print("\n=== 测试完整训练流程 ===")
        
        # 创建配置
        config = create_default_config(
            name="test_ensemble_integration",
            timeframe_minutes=5,
            standalone_model_path=self.standalone_model_path,
            money_model_path=self.money_model_path
        )
        
        # 设置数据源
        config.data_source = {
            'database_path': self.test_db_path,
            'symbol': 'ETHUSDT',
            'timeframe_minutes': 5,
            'table_name': 'ethusdt_5min_spot'
        }
        
        # 设置输出目录
        config.output.output_dir = self.models_dir
        
        # 调整训练参数以加快测试
        config.training.test_size = 0.3
        config.training.validation_size = 0.2
        config.feature_selection.traditional_feature_count = 10
        
        # 创建训练器
        trainer = EnsembleTrainer(config)
        
        # 加载数据
        conn = sqlite3.connect(self.test_db_path)
        df = pd.read_sql_query("SELECT * FROM ethusdt_5min_spot ORDER BY timestamp", conn)
        conn.close()
        
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        
        print(f"加载了 {len(df)} 条测试数据")
        
        # 执行训练
        try:
            model, model_path = trainer.run_training_pipeline(df)
            
            print(f"✅ 训练成功完成")
            print(f"模型保存路径: {model_path}")
            
            # 验证模型文件存在
            self.assertTrue(os.path.exists(model_path))
            
            # 验证配置文件存在
            config_path = model_path.replace('_ensemble_model.joblib', '_ensemble_config.json')
            self.assertTrue(os.path.exists(config_path))
            
            # 验证可以加载保存的模型
            loaded_model = joblib.load(model_path)
            self.assertIsNotNone(loaded_model)
            
            # 验证配置文件内容
            with open(config_path, 'r') as f:
                saved_config = json.load(f)
            
            self.assertIn('ensemble_config', saved_config)
            self.assertIn('component_models', saved_config)
            
            return model, model_path
            
        except Exception as e:
            self.fail(f"训练流程失败: {str(e)}")
    
    def test_model_prediction_compatibility(self):
        """测试模型预测兼容性"""
        print("\n=== 测试模型预测兼容性 ===")
        
        # 先运行完整训练流程
        model, model_path = self.test_complete_training_pipeline()
        
        # 加载保存的模型和配置
        loaded_model = joblib.load(model_path)
        config_path = model_path.replace('_ensemble_model.joblib', '_ensemble_config.json')
        
        with open(config_path, 'r') as f:
            saved_config = json.load(f)
        
        # 创建测试特征数据
        feature_list = saved_config['feature_list']
        test_features = pd.DataFrame(
            np.random.randn(10, len(feature_list)),
            columns=feature_list
        )
        
        # 测试预测功能
        try:
            predictions = loaded_model.predict(test_features)
            probabilities = loaded_model.predict_proba(test_features)
            
            print(f"✅ 预测成功")
            print(f"预测形状: {predictions.shape}")
            print(f"概率形状: {probabilities.shape}")
            
            # 验证预测结果格式
            self.assertEqual(len(predictions), 10)
            self.assertEqual(probabilities.shape[0], 10)
            
            # 验证概率和为1
            prob_sums = np.sum(probabilities, axis=1)
            np.testing.assert_allclose(prob_sums, 1.0, rtol=1e-5)
            
        except Exception as e:
            self.fail(f"模型预测失败: {str(e)}")
    
    def test_backtest_compatibility(self):
        """测试与回测系统的兼容性"""
        print("\n=== 测试回测系统兼容性 ===")
        
        # 先运行完整训练流程
        model, model_path = self.test_complete_training_pipeline()
        
        # 验证模型文件格式兼容性
        try:
            # 测试模型可以被标准joblib加载
            loaded_model = joblib.load(model_path)
            self.assertIsNotNone(loaded_model)
            
            # 测试配置文件格式
            config_path = model_path.replace('_ensemble_model.joblib', '_ensemble_config.json')
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            # 验证配置包含回测所需的关键字段
            required_fields = [
                'model_type', 'feature_list', 'timeframe_minutes',
                'up_threshold', 'down_threshold'
            ]
            
            for field in required_fields:
                self.assertIn(field, config, f"配置缺少必需字段: {field}")
            
            # 验证模型类型
            self.assertEqual(config['model_type'], 'ensemble')
            
            # 验证特征列表不为空
            self.assertGreater(len(config['feature_list']), 0)
            
            print("✅ 回测兼容性验证通过")
            
        except Exception as e:
            self.fail(f"回测兼容性测试失败: {str(e)}")
    
    def test_feature_generation_consistency(self):
        """测试特征生成的一致性"""
        print("\n=== 测试特征生成一致性 ===")
        
        # 创建配置
        config = create_default_config(
            name="test_feature_consistency",
            timeframe_minutes=5,
            standalone_model_path=self.standalone_model_path,
            money_model_path=self.money_model_path
        )
        
        # 创建模型加载器和特征生成器
        model_loader = ModelLoader()
        model_loader.load_standalone_model(self.standalone_model_path, self.standalone_config_path)
        model_loader.load_money_model(self.money_model_path, self.money_config_path)
        
        feature_generator = FeatureGenerator(model_loader, config)
        
        # 加载测试数据
        conn = sqlite3.connect(self.test_db_path)
        df = pd.read_sql_query("SELECT * FROM ethusdt_5min_spot ORDER BY timestamp LIMIT 500", conn)
        conn.close()
        
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        
        try:
            # 生成特征两次，验证结果一致
            features1 = feature_generator.generate_all_features(df, timeframe_minutes=5)
            features2 = feature_generator.generate_all_features(df, timeframe_minutes=5)
            
            # 验证特征数据一致性
            pd.testing.assert_frame_equal(features1, features2)
            
            print("✅ 特征生成一致性验证通过")
            print(f"生成特征数量: {len(features1.columns)}")
            
        except Exception as e:
            self.fail(f"特征生成一致性测试失败: {str(e)}")
    
    def test_configuration_validation(self):
        """测试配置验证"""
        print("\n=== 测试配置验证 ===")
        
        # 测试有效配置
        valid_config = create_default_config(
            name="test_config_validation",
            timeframe_minutes=5,
            standalone_model_path=self.standalone_model_path,
            money_model_path=self.money_model_path
        )
        
        self.assertTrue(valid_config.validate())
        
        # 测试无效配置（缺少模型路径）
        invalid_config = create_default_config(
            name="test_invalid_config",
            timeframe_minutes=5,
            standalone_model_path="nonexistent_model.joblib",
            money_model_path="nonexistent_model.joblib"
        )
        
        self.assertFalse(invalid_config.validate())
        
        print("✅ 配置验证测试通过")
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n=== 测试错误处理 ===")
        
        # 测试缺少数据的情况
        config = create_default_config(
            name="test_error_handling",
            timeframe_minutes=5,
            standalone_model_path=self.standalone_model_path,
            money_model_path=self.money_model_path
        )
        
        trainer = EnsembleTrainer(config)
        
        # 测试空数据框
        empty_df = pd.DataFrame()
        
        with self.assertRaises(Exception):
            trainer.run_training_pipeline(empty_df)
        
        # 测试数据不足的情况
        small_df = pd.DataFrame({
            'open': [1, 2, 3],
            'high': [1.1, 2.1, 3.1],
            'low': [0.9, 1.9, 2.9],
            'close': [1.05, 2.05, 3.05],
            'volume': [100, 200, 300]
        })
        
        with self.assertRaises(Exception):
            trainer.run_training_pipeline(small_df)
        
        print("✅ 错误处理测试通过")


class TestBacktestIntegration(unittest.TestCase):
    """回测集成测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.models_dir = os.path.join(self.temp_dir, "models")
        os.makedirs(self.models_dir, exist_ok=True)
    
    def tearDown(self):
        """测试后的清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_ensemble_model_format_compatibility(self):
        """测试集成模型格式与现有回测系统的兼容性"""
        print("\n=== 测试集成模型格式兼容性 ===")
        
        # 创建模拟的集成模型配置
        ensemble_config = {
            "model_type": "ensemble",
            "ensemble_config": {
                "name": "test_ensemble",
                "timeframe_minutes": 5,
                "up_threshold": 0.02,
                "down_threshold": -0.02,
                "max_lookforward_minutes": 240
            },
            "component_models": {
                "standalone": {
                    "model_path": "models/standalone_eth_5m_model.joblib",
                    "config_path": "models/standalone_eth_5m_config.json",
                    "model_type": "standalone"
                },
                "money": {
                    "model_path": "models/eth_5m_model.joblib", 
                    "config_path": "models/eth_5m_config.json",
                    "model_type": "money"
                }
            },
            "feature_list": [f"feature_{i}" for i in range(25)],
            "timeframe_minutes": 5,
            "up_threshold": 0.02,
            "down_threshold": -0.02,
            "training_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "model_version": "1.0"
        }
        
        # 保存配置文件
        config_path = os.path.join(self.models_dir, "test_ensemble_config.json")
        with open(config_path, 'w') as f:
            json.dump(ensemble_config, f, indent=2)
        
        # 验证配置文件可以被正确读取
        with open(config_path, 'r') as f:
            loaded_config = json.load(f)
        
        # 验证关键字段存在
        self.assertEqual(loaded_config['model_type'], 'ensemble')
        self.assertIn('ensemble_config', loaded_config)
        self.assertIn('component_models', loaded_config)
        self.assertIn('feature_list', loaded_config)
        
        print("✅ 集成模型格式兼容性验证通过")
    
    def test_prediction_interface_compatibility(self):
        """测试预测接口兼容性"""
        print("\n=== 测试预测接口兼容性 ===")
        
        # 创建模拟的集成模型
        from sklearn.ensemble import RandomForestClassifier
        
        # 创建并训练模型
        model = RandomForestClassifier(n_estimators=10, random_state=42)
        X_train = np.random.randn(100, 25)
        y_train = np.random.choice([0, 1], size=100)  # 二分类，兼容money management
        model.fit(X_train, y_train)
        
        # 保存模型
        model_path = os.path.join(self.models_dir, "test_ensemble_model.joblib")
        joblib.dump(model, model_path)
        
        # 测试预测接口
        test_features = pd.DataFrame(
            np.random.randn(10, 25),
            columns=[f"feature_{i}" for i in range(25)]
        )
        
        try:
            # 加载模型
            loaded_model = joblib.load(model_path)
            
            # 测试预测方法
            predictions = loaded_model.predict(test_features)
            probabilities = loaded_model.predict_proba(test_features)
            
            # 验证输出格式
            self.assertEqual(len(predictions), 10)
            self.assertEqual(probabilities.shape, (10, 2))
            
            # 验证概率和为1
            prob_sums = np.sum(probabilities, axis=1)
            np.testing.assert_allclose(prob_sums, 1.0, rtol=1e-5)
            
            print("✅ 预测接口兼容性验证通过")
            
        except Exception as e:
            self.fail(f"预测接口兼容性测试失败: {str(e)}")


class TestPerformanceIntegration(unittest.TestCase):
    """性能集成测试"""
    
    def test_large_dataset_handling(self):
        """测试大数据集处理能力"""
        print("\n=== 测试大数据集处理 ===")
        
        # 创建大型测试数据集
        n_samples = 10000
        n_features = 50
        
        large_df = pd.DataFrame(
            np.random.randn(n_samples, 5),
            columns=['open', 'high', 'low', 'close', 'volume'],
            index=pd.date_range('2024-01-01', periods=n_samples, freq='5min')
        )
        
        # 测试特征生成性能
        from hunhe.feature_generator import FeatureGenerator
        from hunhe.model_loader import ModelLoader
        from hunhe.ensemble_config import create_default_config
        
        config = create_default_config(
            name="performance_test",
            timeframe_minutes=5,
            standalone_model_path="dummy_path",
            money_model_path="dummy_path"
        )
        
        model_loader = ModelLoader()
        feature_generator = FeatureGenerator(model_loader, config)
        
        try:
            import time
            start_time = time.time()
            
            # 只测试基础特征计算（不需要真实模型）
            base_features = feature_generator.calculate_base_features(large_df, timeframe_minutes=5)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"✅ 大数据集处理成功")
            print(f"处理 {n_samples} 条记录耗时: {processing_time:.2f} 秒")
            print(f"平均每条记录: {processing_time/n_samples*1000:.2f} 毫秒")
            
            # 验证结果
            self.assertGreater(len(base_features), 0)
            self.assertGreater(len(base_features.columns), 5)  # 应该有更多特征
            
        except Exception as e:
            self.fail(f"大数据集处理测试失败: {str(e)}")
    
    def test_memory_usage(self):
        """测试内存使用情况"""
        print("\n=== 测试内存使用 ===")
        
        try:
            import psutil
        except ImportError:
            print("⚠️  psutil 模块未安装，跳过内存使用测试")
            return
        
        import gc
        
        # 获取初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建测试数据
        test_df = pd.DataFrame(
            np.random.randn(5000, 5),
            columns=['open', 'high', 'low', 'close', 'volume'],
            index=pd.date_range('2024-01-01', periods=5000, freq='5min')
        )
        
        # 执行特征生成
        from hunhe.feature_generator import FeatureGenerator
        from hunhe.model_loader import ModelLoader
        from hunhe.ensemble_config import create_default_config
        
        config = create_default_config(
            name="memory_test",
            timeframe_minutes=5,
            standalone_model_path="dummy_path",
            money_model_path="dummy_path"
        )
        
        model_loader = ModelLoader()
        feature_generator = FeatureGenerator(model_loader, config)
        
        try:
            # 生成特征
            features = feature_generator.calculate_base_features(test_df, timeframe_minutes=5)
            
            # 获取峰值内存使用
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = peak_memory - initial_memory
            
            print(f"✅ 内存使用测试完成")
            print(f"初始内存: {initial_memory:.1f} MB")
            print(f"峰值内存: {peak_memory:.1f} MB")
            print(f"内存增长: {memory_increase:.1f} MB")
            
            # 清理内存
            del features, test_df
            gc.collect()
            
            # 验证内存增长在合理范围内（小于500MB）
            self.assertLess(memory_increase, 500)
            
        except Exception as e:
            self.fail(f"内存使用测试失败: {str(e)}")


def run_integration_tests():
    """运行所有集成测试"""
    print("开始运行集成测试...")
    print("=" * 80)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加端到端测试
    test_suite.addTest(TestEndToEndIntegration('test_complete_training_pipeline'))
    test_suite.addTest(TestEndToEndIntegration('test_model_prediction_compatibility'))
    test_suite.addTest(TestEndToEndIntegration('test_backtest_compatibility'))
    test_suite.addTest(TestEndToEndIntegration('test_feature_generation_consistency'))
    test_suite.addTest(TestEndToEndIntegration('test_configuration_validation'))
    test_suite.addTest(TestEndToEndIntegration('test_error_handling'))
    
    # 添加回测集成测试
    test_suite.addTest(TestBacktestIntegration('test_ensemble_model_format_compatibility'))
    test_suite.addTest(TestBacktestIntegration('test_prediction_interface_compatibility'))
    
    # 添加性能测试
    test_suite.addTest(TestPerformanceIntegration('test_large_dataset_handling'))
    test_suite.addTest(TestPerformanceIntegration('test_memory_usage'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 80)
    if result.wasSuccessful():
        print("✅ 所有集成测试通过！")
    else:
        print("❌ 部分集成测试失败")
        print(f"失败数量: {len(result.failures)}")
        print(f"错误数量: {len(result.errors)}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)