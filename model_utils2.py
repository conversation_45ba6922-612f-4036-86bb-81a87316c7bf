# enhanced_model_utils.py
# 基于Optiver竞赛经验的300个金融特征工程系统
# 新增 get_optimized_feature_list 函数用于特征选择

import pandas as pd
import numpy as np
import json
import os
from typing import List, Dict, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

class FinancialFeatureEngine:
    """
    综合金融特征工程引擎
    实现300+个特征，涵盖价格、技术指标、统计、时间、成交量和高阶特征
    """
    
    def __init__(self, timeframe: int = 15):
        self.timeframe = timeframe  # 时间框架（分钟）
        self.epsilon = 1e-9  # 防止除零
        
        # 特征计算参数
        self.price_windows = [5, 10, 20, 50, 100, 200]
        self.stat_windows = [5, 10, 20, 50, 100]
        self.momentum_windows = [3, 5, 10, 20, 60]
        
    def load_config(self, config_file='config.json'):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return None

    def load_and_prepare_data(self, csv_file: str, price_multiplier: float = 1.0) -> Optional[pd.DataFrame]:
        """加载和预处理数据"""
        print(f"加载和预处理数据: {csv_file}")
        
        try:
            df = pd.read_csv(csv_file)
            
            # 检查必需列
            required_cols = ['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume']
            if not all(col in df.columns for col in required_cols):
                raise ValueError(f"CSV文件缺少必要列: {required_cols}")
            
            # 数据预处理
            df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
            df.set_index('Timestamp', inplace=True)
            
            # 标准化列名
            df.rename(columns={
                'Open': 'open', 'High': 'high', 'Low': 'low', 
                'Close': 'close', 'Volume': 'volume'
            }, inplace=True)
            
            # 添加额外的成交量数据如果存在
            volume_cols = ['QuoteVolume', 'TradeCount', 'TakerBuyBaseVolume', 'TakerBuyQuoteVolume']
            for col in volume_cols:
                if col in df.columns:
                    df[col.lower()] = df[col]
            
            # 价格缩放
            if price_multiplier != 1.0:
                price_cols = ['open', 'high', 'low', 'close']
                for col in price_cols:
                    df[col] = df[col] * price_multiplier
            
            df.dropna(inplace=True)
            df.sort_index(inplace=True)
            
            print(f"数据预处理完成，共有 {len(df)} 条记录")
            return df
            
        except Exception as e:
            print(f"加载数据时出错: {e}")
            return None

    def calculate_all_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算所有300个特征"""
        
        # 1. 基础价格特征 (40个)
        df = self._calculate_basic_price_features(df)
        
        # 2. 技术指标特征 (80个)
        df = self._calculate_technical_indicators(df)
        
        # 3. 统计特征 (60个)
        df = self._calculate_statistical_features(df)
        
        # 4. 时间特征 (30个)
        df = self._calculate_time_features(df)
        
        # 5. 成交量和流动性特征 (40个)
        df = self._calculate_volume_features(df)
        
        # 6. 高阶特征 (50个)
        df = self._calculate_advanced_features(df)
        return df

    def _calculate_basic_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """1. 基础价格特征 (40个)"""
        
        # OHLC变换 (12个)
        df['log_open'] = np.log(df['open'] + self.epsilon)
        df['log_high'] = np.log(df['high'] + self.epsilon)
        df['log_low'] = np.log(df['low'] + self.epsilon)
        df['log_close'] = np.log(df['close'] + self.epsilon)
        
        df['open_diff'] = df['open'].diff()
        df['high_diff'] = df['high'].diff()
        df['low_diff'] = df['low'].diff()
        df['close_diff'] = df['close'].diff()
        
        df['high_low_ratio'] = df['high'] / (df['low'] + self.epsilon)
        df['close_open_ratio'] = df['close'] / (df['open'] + self.epsilon)
        df['hl_range_pct'] = (df['high'] - df['low']) / (df['close'] + self.epsilon)
        df['co_change_pct'] = (df['close'] - df['open']) / (df['open'] + self.epsilon)
        
        # 价格统计特征 (28个)
        for window in self.price_windows:
            if window <= len(df):
                # 移动平均
                df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
                df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
                
                # 价格位置
                df[f'close_sma_ratio_{window}'] = df['close'] / (df[f'sma_{window}'] + self.epsilon)
                df[f'price_position_{window}'] = (df['close'] - df['close'].rolling(window).min()) / \
                                                (df['close'].rolling(window).max() - df['close'].rolling(window).min() + self.epsilon)
                
                # 价格标准差和变异系数
                df[f'price_std_{window}'] = df['close'].rolling(window=window).std()
                df[f'price_cv_{window}'] = df[f'price_std_{window}'] / (df[f'sma_{window}'] + self.epsilon)
        
        return df

    def _calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """2. 技术指标特征 (80个)"""
        
        # RSI 系列 (10个)
        for period in [9, 14, 21]:
            df[f'rsi_{period}'] = self._calculate_rsi(df['close'], period)
            df[f'rsi_{period}_sma_3'] = df[f'rsi_{period}'].rolling(3).mean()
            df[f'rsi_{period}_diff'] = df[f'rsi_{period}'].diff()
        
        # MACD 系列 (8个)
        df['macd'], df['macd_signal'], df['macd_histogram'] = self._calculate_macd(df['close'])
        df['macd_diff'] = df['macd'].diff()
        df['macd_signal_diff'] = df['macd_signal'].diff()
        df['macd_cross'] = np.where(df['macd'] > df['macd_signal'], 1, -1)
        df['macd_cross_change'] = df['macd_cross'].diff()
        df['macd_histogram_diff'] = df['macd_histogram'].diff()
        
        # Bollinger Bands 系列 (12个)
        for period in [10, 20, 50]:
            upper, middle, lower = self._calculate_bollinger_bands(df['close'], period)
            df[f'bb_upper_{period}'] = upper
            df[f'bb_middle_{period}'] = middle
            df[f'bb_lower_{period}'] = lower
            df[f'bb_width_{period}'] = (upper - lower) / (middle + self.epsilon)
            df[f'bb_position_{period}'] = (df['close'] - lower) / (upper - lower + self.epsilon)
        
        # Stochastic 系列 (6个)
        for k_period in [14, 21]:
            df[f'stoch_k_{k_period}'], df[f'stoch_d_{k_period}'] = self._calculate_stochastic(df, k_period)
            df[f'stoch_cross_{k_period}'] = np.where(df[f'stoch_k_{k_period}'] > df[f'stoch_d_{k_period}'], 1, -1)
        
        # Williams %R 系列 (3个)
        for period in [14, 21]:
            df[f'williams_r_{period}'] = self._calculate_williams_r(df, period)
        df['williams_r_avg'] = (df['williams_r_14'] + df['williams_r_21']) / 2
        
        # CCI (Commodity Channel Index) 系列 (3个)
        for period in [14, 20]:
            df[f'cci_{period}'] = self._calculate_cci(df, period)
        df['cci_cross'] = np.where(df['cci_14'] > 0, 1, -1)
        
        # ATR 和波动率指标 (10个)
        for period in [7, 14, 21]:
            df[f'atr_{period}'] = self._calculate_atr(df, period)
            df[f'atr_pct_{period}'] = df[f'atr_{period}'] / (df['close'] + self.epsilon)
        
        df['atr_ratio_14_7'] = df['atr_14'] / (df['atr_7'] + self.epsilon)
        
        # ADX (Average Directional Index) 系列 (6个)
        df['adx_14'], df['di_plus_14'], df['di_minus_14'] = self._calculate_adx(df, 14)
        df['dx_14'] = abs(df['di_plus_14'] - df['di_minus_14']) / (df['di_plus_14'] + df['di_minus_14'] + self.epsilon)
        df['di_cross_14'] = np.where(df['di_plus_14'] > df['di_minus_14'], 1, -1)
        df['adx_trend_strength'] = np.where(df['adx_14'] > 25, 1, 0)
        
        # Momentum 系列 (6个)
        for period in [10, 14, 20]:
            df[f'momentum_{period}'] = df['close'] / (df['close'].shift(period) + self.epsilon) - 1
        
        for period in [5, 10]:
            df[f'roc_{period}'] = ((df['close'] - df['close'].shift(period)) / (df['close'].shift(period) + self.epsilon)) * 100
        
        # 自定义组合指标 (16个)
        df['rsi_bb_combo'] = df['rsi_14'] * df['bb_position_20']
        df['macd_rsi_combo'] = df['macd'] * df['rsi_14'] / 100
        df['volume_price_trend'] = df['volume'] * df['co_change_pct']
        df['price_momentum_combo'] = df['momentum_10'] * df['rsi_14']
        
        # 趋势强度指标
        df['trend_consistency'] = df['macd_cross'].rolling(10).sum() / 10
        df['volatility_momentum'] = df['atr_pct_14'].diff()
        
        return df

    def _calculate_statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """3. 统计特征 (60个)"""
        
        # 滚动统计 (30个)
        for window in self.stat_windows:
            if window <= len(df):
                # 基础统计
                df[f'close_mean_{window}'] = df['close'].rolling(window).mean()
                df[f'close_std_{window}'] = df['close'].rolling(window).std()
                df[f'close_skew_{window}'] = df['close'].rolling(window).skew()
                df[f'close_kurt_{window}'] = df['close'].rolling(window).kurt()
                
                # 分位数
                df[f'close_q25_{window}'] = df['close'].rolling(window).quantile(0.25)
                df[f'close_q75_{window}'] = df['close'].rolling(window).quantile(0.75)
        
        # Z-score 标准化 (15个)
        for window in [20, 50, 100]:
            if window <= len(df):
                rolling_mean = df['close'].rolling(window).mean()
                rolling_std = df['close'].rolling(window).std()
                df[f'zscore_{window}'] = (df['close'] - rolling_mean) / (rolling_std + self.epsilon)
                
                # 成交量Z-score
                vol_mean = df['volume'].rolling(window).mean()
                vol_std = df['volume'].rolling(window).std()
                df[f'volume_zscore_{window}'] = (df['volume'] - vol_mean) / (vol_std + self.epsilon)
                
                # 波动率Z-score
                returns = df['close'].pct_change()
                ret_std = returns.rolling(window).std()
                ret_std_mean = ret_std.rolling(window).mean()
                ret_std_std = ret_std.rolling(window).std()
                df[f'vol_zscore_{window}'] = (ret_std - ret_std_mean) / (ret_std_std + self.epsilon)
        
        # 百分位排名 (15个)
        for window in [20, 50, 100]:
            if window <= len(df):
                df[f'price_percentile_{window}'] = df['close'].rolling(window).rank(pct=True)
                df[f'volume_percentile_{window}'] = df['volume'].rolling(window).rank(pct=True)
                df[f'range_percentile_{window}'] = (df['high'] - df['low']).rolling(window).rank(pct=True)
        
        return df

    def _calculate_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """4. 时间特征 (30个)"""
        
        # 基础时间特征 (6个)
        df['hour'] = df.index.hour
        df['day_of_week'] = df.index.dayofweek
        df['month'] = df.index.month
        df['quarter'] = df.index.quarter
        df['day_of_month'] = df.index.day
        df['week_of_year'] = df.index.isocalendar().week
        
        # 交易时段特征 (4个)
        df['is_market_open'] = ((df.index.hour >= 9) & (df.index.hour < 16)).astype(int)
        df['is_pre_market'] = ((df.index.hour >= 4) & (df.index.hour < 9)).astype(int)
        df['is_after_hours'] = ((df.index.hour >= 16) | (df.index.hour < 4)).astype(int)
        df['minutes_from_open'] = np.where(df['is_market_open'], 
                                          (df.index.hour - 9) * 60 + df.index.minute, -1)
        
        # 小时 one-hot 编码 (20个，选择主要交易时段)
        main_hours = list(range(9, 16))  # 主要交易时段
        for hour in main_hours:
            df[f'hour_{hour}'] = (df.index.hour == hour).astype(int)
        
        return df

    def _calculate_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """5. 成交量和流动性特征 (40个)"""
        
        # 基础成交量特征 (15个)
        for window in [5, 10, 20, 50]:
            df[f'volume_sma_{window}'] = df['volume'].rolling(window).mean()
            df[f'volume_ratio_{window}'] = df['volume'] / (df[f'volume_sma_{window}'] + self.epsilon)
        
        # 成交量趋势
        df['volume_trend_5'] = df['volume'].rolling(5).apply(lambda x: 1 if x.iloc[-1] > x.iloc[0] else -1)
        df['volume_momentum'] = df['volume'].pct_change(5)
        df['volume_acceleration'] = df['volume_momentum'].diff()
        
        # VWAP 相关特征 (10个)
        for window in [20, 50, 100]:
            typical_price = (df['high'] + df['low'] + df['close']) / 3
            df[f'vwap_{window}'] = (typical_price * df['volume']).rolling(window).sum() / \
                                  (df['volume'].rolling(window).sum() + self.epsilon)
            df[f'price_vwap_ratio_{window}'] = df['close'] / (df[f'vwap_{window}'] + self.epsilon)
        
        df['vwap_trend'] = np.where(df['vwap_20'] > df['vwap_50'], 1, -1)
        
        # OBV (On Balance Volume) 和相关 (5个)
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).cumsum()
        df['obv_sma_10'] = df['obv'].rolling(10).mean()
        df['obv_ratio'] = df['obv'] / (df['obv_sma_10'] + self.epsilon)
        df['obv_momentum'] = df['obv'].pct_change(5)
        df['price_obv_corr'] = df['close'].rolling(20).corr(df['obv'])
        
        # 如果有额外的成交量数据
        if 'quotevolume' in df.columns:
            df['quote_base_ratio'] = df['quotevolume'] / (df['volume'] + self.epsilon)
        
        if 'tradecount' in df.columns:
            df['avg_trade_size'] = df['volume'] / (df['tradecount'] + self.epsilon)
            df['trade_intensity'] = df['tradecount'] / (df.index.to_series().diff().dt.seconds / 60 + self.epsilon)
        
        if 'takerbuybasevolume' in df.columns:
            df['buy_sell_ratio'] = df['takerbuybasevolume'] / (df['volume'] - df['takerbuybasevolume'] + self.epsilon)
            df['buy_pressure'] = df['takerbuybasevolume'] / (df['volume'] + self.epsilon)
        
        return df

    def _calculate_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """6. 高阶特征 (50个)"""
        
        # 特征交互 (20个)
        df['rsi_volume_combo'] = df['rsi_14'] * df['volume_ratio_20']
        df['macd_volatility_combo'] = df['macd'] * df['atr_pct_14']
        df['bb_momentum_combo'] = df['bb_position_20'] * df['momentum_10']
        df['price_volume_correlation'] = df['close'].rolling(20).corr(df['volume'])
        
        # 市场状态特征
        df['high_volatility_regime'] = (df['atr_pct_14'] > df['atr_pct_14'].rolling(50).quantile(0.8)).astype(int)
        df['high_volume_regime'] = (df['volume'] > df['volume'].rolling(50).quantile(0.8)).astype(int)
        df['trending_market'] = (df['adx_14'] > 25).astype(int)
        
        # 价格模式识别
        df['higher_highs'] = (df['high'].rolling(5).max() == df['high']).astype(int)
        df['lower_lows'] = (df['low'].rolling(5).min() == df['low']).astype(int)
        df['inside_bar'] = ((df['high'] < df['high'].shift(1)) & (df['low'] > df['low'].shift(1))).astype(int)
        df['outside_bar'] = ((df['high'] > df['high'].shift(1)) & (df['low'] < df['low'].shift(1))).astype(int)
        
        # 滞后特征 (15个)
        important_features = ['close', 'volume', 'rsi_14', 'macd', 'bb_position_20']
        for feature in important_features:
            if feature in df.columns:
                for lag in [1, 2, 3]:
                    df[f'{feature}_lag_{lag}'] = df[feature].shift(lag)
        
        # 差分和变化率特征 (15个)
        df['price_acceleration'] = df['close'].diff().diff()
        df['volume_acceleration'] = df['volume'].diff().diff()
        df['rsi_velocity'] = df['rsi_14'].diff()
        df['volatility_change'] = df['atr_pct_14'].diff()
        df['momentum_consistency'] = df['momentum_10'].rolling(5).std()
        
        return df

    # 辅助计算函数 (以下函数保持不变)
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / (loss + self.epsilon)
        return 100 - (100 / (1 + rs))

    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        macd_histogram = macd - macd_signal
        return macd, macd_signal, macd_histogram

    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        middle = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        upper = middle + std_dev * std
        lower = middle - std_dev * std
        return upper, middle, lower

    def _calculate_stochastic(self, df: pd.DataFrame, k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        lowest_low = df['low'].rolling(window=k_period).min()
        highest_high = df['high'].rolling(window=k_period).max()
        k_percent = 100 * (df['close'] - lowest_low) / (highest_high - lowest_low + self.epsilon)
        d_percent = k_percent.rolling(window=d_period).mean()
        return k_percent, d_percent

    def _calculate_williams_r(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        highest_high = df['high'].rolling(window=period).max()
        lowest_low = df['low'].rolling(window=period).min()
        return -100 * (highest_high - df['close']) / (highest_high - lowest_low + self.epsilon)

    def _calculate_cci(self, df: pd.DataFrame, period: int = 20) -> pd.Series:
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        sma_tp = typical_price.rolling(window=period).mean()
        mean_dev = typical_price.rolling(window=period).apply(lambda x: np.abs(x - x.mean()).mean())
        return (typical_price - sma_tp) / (0.015 * mean_dev + self.epsilon)

    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        high_low = df['high'] - df['low']
        high_prev_close = np.abs(df['high'] - df['close'].shift())
        low_prev_close = np.abs(df['low'] - df['close'].shift())
        tr = np.maximum(high_low, np.maximum(high_prev_close, low_prev_close))
        return tr.rolling(window=period).mean()

    def _calculate_adx(self, df: pd.DataFrame, period: int = 14) -> Tuple[pd.Series, pd.Series, pd.Series]:
        high_diff = df['high'].diff()
        low_diff = df['low'].diff()
        plus_dm = np.where((high_diff > low_diff) & (high_diff > 0), high_diff, 0)
        minus_dm = np.where((low_diff > high_diff) & (low_diff > 0), low_diff, 0)
        atr = self._calculate_atr(df, period)
        plus_di = 100 * (pd.Series(plus_dm, index=df.index).rolling(period).mean() / (atr + self.epsilon))
        minus_di = 100 * (pd.Series(minus_dm, index=df.index).rolling(period).mean() / (atr + self.epsilon))
        dx_denominator = plus_di + minus_di
        dx = 100 * np.abs(plus_di - minus_di) / (dx_denominator + self.epsilon)
        adx = dx.rolling(period).mean()
        return adx, plus_di, minus_di

    def create_percentage_target(self, df: pd.DataFrame, up_threshold: float, down_threshold: float, 
                               max_lookforward_minutes: int) -> pd.Series:
        max_lookforward_candles = max_lookforward_minutes // self.timeframe
        print(f"生成交易标签 (涨{up_threshold*100:.1f}% vs 跌{down_threshold*100:.1f}%)")
        labels = []
        valid_indices = []
        for i in range(len(df)):
            current_price = df.iloc[i]['close']
            up_target = current_price * (1 + up_threshold)
            down_target = current_price * (1 - down_threshold)
            label = None
            for j in range(1, min(max_lookforward_candles + 1, len(df) - i)):
                future_price = df.iloc[i + j]['close']
                if future_price >= up_target:
                    label = 1; break
                elif future_price <= down_target:
                    label = 0; break
            if label is not None:
                labels.append(label)
                valid_indices.append(i)
        return pd.Series(index=df.index[valid_indices], data=labels, name='target')

    def get_feature_importance_groups(self) -> Dict[str, List[str]]:
        return {
            'core_price': ['close', 'open', 'high', 'low', 'volume'],
            'technical_oscillators': ['rsi_14', 'macd', 'stoch_k_14', 'williams_r_14'],
            'trend_indicators': ['sma_20', 'ema_20', 'adx_14', 'bb_position_20'],
            'volume_indicators': ['volume_ratio_20', 'vwap_20', 'obv', 'buy_pressure'],
            'volatility_indicators': ['atr_pct_14', 'bb_width_20', 'volatility_ratio_120'],
            'statistical_features': ['zscore_50', 'price_percentile_50', 'close_skew_20'],
            'time_features': ['hour', 'day_of_week', 'is_market_open'],
            'advanced_combos': ['rsi_volume_combo', 'macd_volatility_combo', 'price_volume_correlation']
        }

# --- 新增的特征优化函数 ---
def get_optimized_feature_list(
    df_features: pd.DataFrame, 
    importances_df: pd.DataFrame, 
    top_n: int = 50,
    corr_threshold: float = 0.9) -> List[str]:
    """
    根据特征重要性和相关性，从一个大的特征集中筛选出最优的特征列表。

    Args:
        df_features (pd.DataFrame): 包含了所有特征和基础价格数据的DataFrame。
        importances_df (pd.DataFrame): 包含两列'feature'和'importance'的DataFrame。
        top_n (int): 初步筛选时保留的最重要特征的数量。
        corr_threshold (float): 用于移除高度相关特征的阈值。

    Returns:
        list: 优化后的特征名称列表。
    """
    print(f"\n--- 开始特征优化 ---")
    print(f"原始特征数量: {len(importances_df)}")

    # --- 第1步：基于重要性选择Top N特征 ---
    if len(importances_df) < top_n:
        print(f"警告: 可用特征数量({len(importances_df)}) 小于请求的 top_n({top_n})。将使用所有可用特征。")
        top_n = len(importances_df)
        
    top_features = importances_df.nlargest(top_n, 'importance')['feature'].tolist()
    print(f"步骤 1: 已根据重要性选择前 {top_n} 个特征。")

    # --- 第2步：移除高度相关的特征 ---
    top_features_df = df_features[top_features]
    corr_matrix = top_features_df.corr().abs()
    
    upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
    
    # 获取每个特征的重要性，以便比较
    importances_map = importances_df.set_index('feature')['importance'].to_dict()
    
    features_to_keep = list(top_features)
    removed_count = 0
    
    # 遍历相关性矩阵
    for i in range(len(upper.columns)):
        for j in range(i):
            if upper.iloc[j, i] > corr_threshold:
                feat1 = upper.columns[i]
                feat2 = upper.index[j]
                
                # 如果两个特征都还在我们的保留列表中
                if feat1 in features_to_keep and feat2 in features_to_keep:
                    # 决定移除哪个：移除重要性较低的那个
                    if importances_map.get(feat1, 0) >= importances_map.get(feat2, 0):
                        feature_to_remove = feat2
                    else:
                        feature_to_remove = feat1
                    
                    features_to_keep.remove(feature_to_remove)
                    removed_count += 1
                    # print(f"  - 移除 '{feature_to_remove}' (因与 '{feat1 if feature_to_remove==feat2 else feat2}' 相关性高)")

    print(f"步骤 2: 已移除 {removed_count} 个高度相关 (阈值>{corr_threshold}) 的特征。")
    print(f"最终优化后的特征数量: {len(features_to_keep)}")
    print(f"--- 特征优化完成 ---\n")
    
    return features_to_keep

# 便捷函数，保持向后兼容
def load_config(config_file='config.json'):
    engine = FinancialFeatureEngine()
    return engine.load_config(config_file)

def load_and_prepare_data(csv_file, price_multiplier=1.0):
    engine = FinancialFeatureEngine()
    return engine.load_and_prepare_data(csv_file, price_multiplier)

def calculate_features(df, timeframe=15):
    engine = FinancialFeatureEngine(timeframe)
    return engine.calculate_all_features(df)

def create_percentage_target(df, up_threshold, down_threshold, max_lookforward_minutes, timeframe):
    engine = FinancialFeatureEngine(timeframe)
    return engine.create_percentage_target(df, up_threshold, down_threshold, max_lookforward_minutes)

def get_feature_list(df_clean, time_frame=15):
    base_columns = ['open', 'high', 'low', 'close', 'volume','label','day_of_month','week_of_year','month']
    feature_columns = [col for col in df_clean.columns if col not in base_columns]
    print(f"可用特征总数: {len(feature_columns)}")
    return feature_columns

# 其他辅助函数保持不变...
def get_core_features(df_clean, timeframe=15):
    engine = FinancialFeatureEngine(timeframe)
    importance_groups = engine.get_feature_importance_groups()
    core_features = []
    for group_name, features in importance_groups.items():
        if group_name in ['technical_oscillators', 'trend_indicators', 'volume_indicators', 'volatility_indicators']:
            core_features.extend([f for f in features if f in df_clean.columns])
    basic_stats = ['hour', 'day_of_week', 'close_std_20', 'volume_ratio_20', 'momentum_10']
    core_features.extend([f for f in basic_stats if f in df_clean.columns])
    print(f"核心特征数量: {len(core_features)}")
    return core_features

def get_lstm_input_features(df):
    lstm_friendly = [
        'rsi_14', 'macd', 'bb_position_20', 'atr_pct_14', 'volume_ratio_20',
        'close_std_20', 'momentum_10', 'price_vwap_ratio_20', 'stoch_k_14',
        'williams_r_14', 'cci_20', 'adx_14', 'obv_momentum'
    ]
    available_features = [f for f in lstm_friendly if f in df.columns]
    print(f"LSTM输入特征数量: {len(available_features)}")
    return available_features

def get_coin_config(coin_name, config_file='config.json'):
    config = load_config(config_file)
    if config is None: return None
    if coin_name not in config['coin_configs']:
        print(f"错误: 配置中未找到币种 '{coin_name}'")
        print(f"可用币种: {list(config['coin_configs'].keys())}")
        return None
    return config['coin_configs'][coin_name]

def get_output_dir():
    config = load_config("config.json")
    if config is None: return None
    return config['output_dir']

# 使用示例
if __name__ == "__main__":
    engine = FinancialFeatureEngine(timeframe=15)
    # 示例用法需要一个实际的CSV文件，这里仅作结构演示
    # df = engine.load_and_prepare_data("your_data.csv")
    # ...