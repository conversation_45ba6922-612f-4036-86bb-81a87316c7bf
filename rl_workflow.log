2025-09-03 13:39:54,432 - INFO - Initialized workflow for ETH 5m
2025-09-03 13:39:54,432 - INFO - Output directory: rl_results/ETH_5m_20250903_133954
2025-09-03 13:39:54,432 - INFO - Creating default config
2025-09-03 13:40:40,964 - INFO - Initialized workflow for ETH 5m
2025-09-03 13:40:40,964 - INFO - Output directory: rl_results/ETH_5m_20250903_134040
2025-09-03 13:40:40,964 - INFO - Creating default config
2025-09-03 13:40:40,964 - WARNING - Config validation error: 'dict' object has no attribute 'data', continuing anyway...
2025-09-03 13:40:40,964 - INFO - Starting complete RL trading workflow...
2025-09-03 13:40:40,964 - INFO - Loading config from rl_results/ETH_5m_20250903_134040/config.json
2025-09-03 13:40:40,964 - WARNING - Config validation error: 'dict' object has no attribute 'data', continuing anyway...
2025-09-03 13:40:40,965 - INFO - Step 1: Preparing data...
2025-09-03 13:40:40,965 - INFO - Using model: models/eth_5m_model.joblib
2025-09-03 13:40:40,965 - INFO - Using config: models/eth_5m_config.json
2025-09-03 13:40:40,966 - ERROR - Workflow failed: cannot import name 'DataLoader' from 'data_loader' (/Users/<USER>/project/ai/vibe/daily/eth-trade/data_loader.py)
2025-09-03 13:41:01,837 - INFO - Initialized workflow for ETH 5m
2025-09-03 13:41:01,837 - INFO - Output directory: rl_results/ETH_5m_20250903_134101
2025-09-03 13:41:01,837 - INFO - Creating default config
2025-09-03 13:41:01,837 - WARNING - Config validation error: 'dict' object has no attribute 'data', continuing anyway...
2025-09-03 13:41:01,837 - INFO - Starting complete RL trading workflow...
2025-09-03 13:41:01,837 - INFO - Loading config from rl_results/ETH_5m_20250903_134101/config.json
2025-09-03 13:41:01,838 - WARNING - Config validation error: 'dict' object has no attribute 'data', continuing anyway...
2025-09-03 13:41:01,838 - INFO - Step 1: Preparing data...
2025-09-03 13:41:01,838 - INFO - Using model: models/eth_5m_model.joblib
2025-09-03 13:41:01,838 - INFO - Using config: models/eth_5m_config.json
2025-09-03 13:41:01,839 - WARNING - Data loading test failed: Can't instantiate abstract class DataProvider without an implementation for abstract methods 'add_new_data', 'get_current_data', 'get_current_position', 'get_initial_data', 'get_next_data', 'get_total_count', 'reset', but continuing...
2025-09-03 13:41:01,839 - INFO - Step 2: Training RL agent...
2025-09-03 13:41:01,839 - ERROR - Workflow failed: 'dict' object has no attribute 'logging'
2025-09-03 13:41:27,895 - INFO - Initialized workflow for ETH 5m
2025-09-03 13:41:27,895 - INFO - Output directory: rl_results/ETH_5m_20250903_134127
2025-09-03 13:41:27,896 - INFO - Creating default config
2025-09-03 13:41:27,896 - WARNING - Config validation error: 'dict' object has no attribute 'data', continuing anyway...
2025-09-03 13:41:27,896 - INFO - Starting complete RL trading workflow...
2025-09-03 13:41:27,896 - ERROR - RL modules are not compatible with current configuration format: 'dict' object has no attribute 'logging'
2025-09-03 13:55:10,104 - INFO - Initialized workflow for ETH 5m
2025-09-03 13:55:10,105 - INFO - Output directory: rl_results/ETH_5m_20250903_135510
2025-09-03 13:55:10,105 - INFO - Starting complete RL trading workflow...
2025-09-03 13:55:10,105 - ERROR - RL modules are not compatible with current configuration format: 'dict' object has no attribute 'logging'
