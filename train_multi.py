# -*- coding: utf-8 -*-
"""
多币种联合训练脚本（SQLite数据源版）

该脚本整合了数据加载、特征工程和模型训练的全过程：
1. 直接从指定的SQLite数据库读取所有可用表的K线数据。
2. 为多个币种计算技术指标和生成标签。
3. 按时间划分训练集和测试集。
4. 使用LightGBM模型进行联合训练。
5. 评估模型并保存模型文件、配置文件和特征重要性。

不再需要config.json中的币种配置，直接从数据库中发现所有可用的表。
所有训练参数通过命令行传入。
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import joblib
import json
import sqlite3
import os
import argparse
import re
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, roc_auc_score

# 假设这些函数存在于 model_utils.py 中
# 如果没有，您需要将原始脚本中的这些函数定义移入 model_utils.py
from model_utils_815 import get_feature_list, create_percentage_target, calculate_features

def get_table_name(symbol: str, interval: str, market: str = "futures") -> str:
    """根据交易对、间隔和市场类型生成表名 (与数据获取脚本保持一致)"""
    safe_symbol = symbol.replace('/', '_').replace('-', '_')
    safe_interval = interval.replace('m', 'min').replace('h', 'hour').replace('d', 'day')
    return f"{safe_symbol}_{safe_interval}_{market}"

def normalize_interval(interval: str) -> str:
    """
    标准化时间间隔格式
    将 "15min" 转换为 "15m"，"1hour" 转换为 "1h" 等
    """
    if not interval:
        return interval

    # 转换映射
    interval = interval.lower()
    if interval.endswith('min'):
        return interval.replace('min', 'm')
    elif interval.endswith('hour'):
        return interval.replace('hour', 'h')
    elif interval.endswith('day'):
        return interval.replace('day', 'd')

    return interval

def get_all_available_tables(db_path: str, interval: str = None, market: str = None):
    """
    从SQLite数据库中获取所有可用的表

    Args:
        db_path (str): SQLite数据库文件路径
        interval (str, optional): 过滤特定时间间隔的表，如 "15min" 或 "15m"
        market (str, optional): 过滤特定市场的表，如 "spot"

    Returns:
        list: 包含表信息的字典列表，每个字典包含 table_name, symbol, interval, market
    """
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件未找到 -> {db_path}")
        return []

    # 标准化间隔格式
    normalized_interval = normalize_interval(interval)

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 首先尝试从 table_info 表获取信息（新版数据库）
        try:
            query = "SELECT table_name, symbol, interval, market FROM table_info"
            conditions = []
            params = []

            if normalized_interval:
                conditions.append("interval = ?")
                params.append(normalized_interval)

            if market:
                conditions.append("market = ?")
                params.append(market)

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            query += " ORDER BY symbol, interval"

            cursor.execute(query, params)
            tables = cursor.fetchall()

            result = []
            for table_name, symbol, table_interval, table_market in tables:
                result.append({
                    'table_name': table_name,
                    'symbol': symbol,
                    'interval': table_interval,
                    'market': table_market or 'unknown'
                })

            conn.close()
            return result

        except sqlite3.OperationalError:
            # 如果 table_info 表不存在，回退到解析表名（旧版数据库）
            print("警告: 未找到 table_info 表，尝试从表名解析...")
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            table_names = [row[0] for row in cursor.fetchall()]

            result = []
            for table_name in table_names:
                # 解析表名格式: SYMBOL_INTERVAL_MARKET
                parsed = parse_table_name(table_name)
                if parsed and (not normalized_interval or parsed['interval'] == normalized_interval) and (not market or parsed['market'] == market):
                    result.append({
                        'table_name': table_name,
                        'symbol': parsed['symbol'],
                        'interval': parsed['interval'],
                        'market': parsed['market']
                    })

            conn.close()
            return result

    except Exception as e:
        print(f"错误: 获取数据库表信息失败: {e}")
        return []

def parse_table_name(table_name: str):
    """
    解析表名，提取币种、时间间隔和市场信息
    表名格式: SYMBOL_INTERVAL_MARKET (例如: BTCUSDT_15min_spot)
    """
    # 使用正则表达式解析表名
    pattern = r'^([A-Z0-9]+USDT)_(\d+(?:min|hour|day))_([a-z]+)$'
    match = re.match(pattern, table_name)

    if match:
        symbol, interval, market = match.groups()
        return {
            'symbol': symbol,
            'interval': interval,
            'market': market
        }
    return None

def load_data_from_db(db_path: str, table_name: str, price_multiplier: float = 1.0,start_time: str = None, end_time: str = None):
    """
    从SQLite数据库加载并准备数据。
    
    Args:
        db_path (str): SQLite数据库文件路径。
        table_name (str): 要查询的数据表名。
        price_multiplier (float): 价格乘数，用于统一价格单位 (例如，对于1000PEPE，可能需要乘以1000)。

    Returns:
        pd.DataFrame: 包含OHLCV数据的DataFrame，或在失败时返回None。
    """
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件未找到 -> {db_path}")
        return None
        
    try:
        conn = sqlite3.connect(db_path)
        # 从数据库读取数据
        # 动态构建SQL查询
        query = f"SELECT * FROM {table_name}"
        conditions = []
        params = []

        if start_time:
            # 转换为UNIX时间戳 (秒)
            start_ts = int(pd.to_datetime(start_time).timestamp())
            conditions.append("timestamp >= ?")
            params.append(start_ts)

        if end_time:
            # 转换为时间戳。为包含结束当天的所有数据，将日期加一天，然后使用小于号
            end_dt = pd.to_datetime(end_time) + pd.Timedelta(days=1)
            end_ts = int(end_dt.timestamp())
            conditions.append("timestamp < ?")
            params.append(end_ts)

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        print(f"  执行SQL查询: {query} with params {params}")
        # 使用 params 参数安全地传递参数
        df = pd.read_sql_query(query, conn, params=params)
        # --- 新增和修改部分结束 ---

    except Exception as e:
        print(f"错误: 从数据库表 '{table_name}' 读取数据失败: {e}")
        return None
    finally:
        if 'conn' in locals() and conn:
            conn.close()

    if df.empty:
        print(f"警告: 从表 '{table_name}' 加载的数据为空。")
        return None

    # 数据预处理
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
    df = df.set_index('timestamp').sort_index()
    
    # 重命名列以匹配特征工程函数的要求
    df = df.rename(columns={'trade_count': 'trades'})
    
    # 应用价格乘数
    if price_multiplier != 1.0:
        for col in ['open', 'high', 'low', 'close']:
            if col in df.columns:
                df[col] *= price_multiplier
    
    # 确保所需列存在
    required_cols = ['open', 'high', 'low', 'close', 'volume']
    if not all(col in df.columns for col in required_cols):
        print(f"错误: 表 '{table_name}' 中缺少必要的列 (open, high, low, close, volume)。")
        return None
        
    return df


def prepare_multi_coin_features(table_list, db_path, up_threshold, down_threshold,
                               max_lookforward_minutes, timeframe_minutes,
                               test_start_date=None, start_time=None, end_time=None):
    """
    从SQLite准备多币种特征数据，并按时间划分训练集和测试集。

    Args:
        table_list: 包含表信息的字典列表
        db_path: 数据库路径
        up_threshold: 上涨阈值
        down_threshold: 下跌阈值
        max_lookforward_minutes: 最大前瞻时间（分钟）
        timeframe_minutes: 时间框架（分钟）
        test_start_date: 测试集开始日期
        start_time: 数据开始时间
        end_time: 数据结束时间
    """
    train_dfs = []
    test_dfs = []

    for table_info in table_list:
        table_name = table_info['table_name']
        symbol = table_info['symbol']

        print(f"--- 正在处理表: {table_name} ({symbol}) ---")

        # 1. 加载数据
        df = load_data_from_db(
            db_path,
            table_name,
            price_multiplier=1.0,  # 默认不使用价格乘数
            start_time=start_time,
            end_time=end_time
        )

        if df is None or len(df) == 0:
            print(f"跳过 {table_name}，数据加载失败或数据为空")
            continue

        # 2. 特征工程
        print(f"  计算特征...")
        df_feat = calculate_features(df, timeframe=timeframe_minutes)
        df_feat['coin_id'] = symbol  # 增加币种ID

        # 3. 标签生成
        print(f"  生成标签...")
        label_series = create_percentage_target(
            df_feat, up_threshold, down_threshold,
            max_lookforward_minutes, timeframe_minutes
        )
        df_feat = df_feat.loc[label_series.index].copy()
        df_feat['label'] = label_series

        # 4. 丢弃NaN值
        df_feat = df_feat.dropna()
        if df_feat.empty:
            print(f"跳过 {table_name}，在处理后没有剩余数据。")
            continue

        # 5. 按时间划分训练集和测试集
        if test_start_date is not None:
            test_start = pd.to_datetime(test_start_date)
            train_df = df_feat[df_feat.index < test_start].copy()
            test_df = df_feat[df_feat.index >= test_start].copy()
        else:
            # 默认使用最后20%的数据作为测试集
            n = len(df_feat)
            n_test = int(n * 0.2)
            train_df = df_feat.iloc[:-n_test].copy()
            test_df = df_feat.iloc[-n_test:].copy()

        # 打乱数据（可选，但对于非时序模型训练是好习惯）
        train_df = train_df.sample(frac=1, random_state=42).reset_index(drop=True)
        test_df = test_df.sample(frac=1, random_state=42).reset_index(drop=True)

        print(f"  {symbol}: 训练集 {len(train_df)} 样本, 测试集 {len(test_df)} 样本")

        train_dfs.append(train_df)
        test_dfs.append(test_df)

    # 合并所有币种数据
    if not train_dfs or not test_dfs:
        print("错误: 没有成功处理任何币种的数据，无法继续。")
        return None, None

    df_train = pd.concat(train_dfs, axis=0).reset_index(drop=True)
    df_test = pd.concat(test_dfs, axis=0).reset_index(drop=True)

    print("\n--- 数据准备完成 ---")
    print(f"合并后总训练集样本数: {len(df_train)}")
    print(f"合并后总测试集样本数: {len(df_test)}")
    return df_train, df_test


def main():
    parser = argparse.ArgumentParser(description="Multi-coin joint training script (SQLite data source)")
    parser.add_argument('--db-path', default='coin_data.db', help='SQLite database file path')
    parser.add_argument('--model-file', default='multi_coin_lgbm_model.joblib', help='Model file name to save')
    parser.add_argument('--test-start-date', default=None, help="Test set start date (format: 'YYYY-MM-DD'). If not provided, use last 20%% of data.")
    parser.add_argument('--start-time', default=None, help="Data loading start date (format: 'YYYY-MM-DD')")
    parser.add_argument('--end-time', default=None, help="Data loading end date (format: 'YYYY-MM-DD')")

    # Training parameters
    parser.add_argument('--interval', default='15min', help='Time interval filter (e.g.: 15min, 5min, 1hour)')
    parser.add_argument('--market', default='spot', help='Market type filter (e.g.: spot, futures)')
    parser.add_argument('--up-threshold', type=float, default=0.05, help='Up threshold (default: 0.05)')
    parser.add_argument('--down-threshold', type=float, default=0.05, help='Down threshold (default: 0.05)')
    parser.add_argument('--max-lookforward-minutes', type=int, default=1440, help='Max lookforward time in minutes (default: 1440)')
    parser.add_argument('--timeframe-minutes', type=int, default=15, help='Timeframe in minutes (default: 15)')
    parser.add_argument('--best-threshold', type=float, default=0.7, help='Best prediction threshold (default: 0.7)')

    args = parser.parse_args()

    # 1. 从数据库中发现所有可用的表
    print(f"正在扫描数据库: {args.db_path}")
    print(f"过滤条件 - 时间间隔: {args.interval}, 市场: {args.market}")

    table_list = get_all_available_tables(args.db_path, args.interval, args.market)

    if not table_list:
        print("错误: 未找到符合条件的数据表，训练终止。")
        return

    print(f"找到 {len(table_list)} 个符合条件的数据表:")
    for table_info in table_list:
        print(f"  - {table_info['table_name']} ({table_info['symbol']}, {table_info['interval']}, {table_info['market']})")

    # 2. 准备特征和标签
    df_train, df_test = prepare_multi_coin_features(
        table_list, args.db_path,
        args.up_threshold, args.down_threshold,
        args.max_lookforward_minutes, args.timeframe_minutes,
        args.test_start_date,
        start_time=args.start_time,
        end_time=args.end_time
    )

    if df_train is None or df_test is None or df_train.empty or df_test.empty:
        print("数据准备失败，训练终止。")
        return

    # 3. 提取特征列和标签
    all_feature_cols = get_feature_list(df_train)

    # 过滤掉非数值列
    numeric_feature_cols = []
    for col in all_feature_cols:
        if col in df_train.columns:
            # 检查列的数据类型
            if df_train[col].dtype in ['int64', 'float64', 'int32', 'float32', 'bool']:
                numeric_feature_cols.append(col)
            else:
                print(f"跳过非数值列: {col} (类型: {df_train[col].dtype})")

    feature_cols = numeric_feature_cols
    print(f"\n--- 模型训练开始 ---")
    print(f"使用特征数: {len(feature_cols)}")
    print("特征名:", feature_cols)

    X_train_full = df_train[feature_cols]
    y_train_full = df_train['label']
    X_test = df_test[feature_cols]
    y_test = df_test['label']

    # 4. 从完整训练集中划分出验证集 (用于早停)
    X_train, X_val, y_train, y_val = train_test_split(
        X_train_full, y_train_full, test_size=0.2, random_state=42, stratify=y_train_full
    )
    print(f"实际训练集: {len(X_train)}, 验证集: {len(X_val)}, 测试集: {len(X_test)}")

    # 5. 训练LightGBM模型
    print("正在训练 LightGBM 模型...")
    model = lgb.LGBMClassifier(
        objective='binary',
        n_estimators=2000,
        learning_rate=0.05,
        n_jobs=-1,
        random_state=42,
        verbose=-1
    )
    model.fit(
        X_train, y_train,
        eval_set=[(X_val, y_val)],
        eval_metric='auc',
        callbacks=[lgb.early_stopping(stopping_rounds=100, verbose=True)]
    )

    # 6. 在测试集上评估模型
    print("\n--- 模型评估 (测试集) ---")
    y_pred_test = (model.predict_proba(X_test)[:, 1] >= args.best_threshold).astype(int)
    y_prob_test = model.predict_proba(X_test)[:, 1]

    print(classification_report(y_test, y_pred_test))
    print(f'AUC: {roc_auc_score(y_test, y_prob_test):.4f}')

    # 7. 保存模型
    joblib.dump(model, args.model_file)
    print(f"\n模型已保存到: {args.model_file}")

    # 8. 保存特征重要性
    importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    importance_file = args.model_file.replace('.joblib', '_feature_importance.csv')
    importance_df.to_csv(importance_file, index=False)
    print(f"特征重要性已保存到: {importance_file}")

    # 9. 保存通用配置文件
    config_file = args.model_file.replace('.joblib', '_config.json')
    config_data = {
        'best_threshold': args.best_threshold,
        'feature_list': feature_cols,
        'model_type': 'LGBM_multi_coin_sqlite',
        'target_description': f'predict_first_{args.up_threshold*100:.1f}%_move_within_{args.max_lookforward_minutes}_minutes',
        'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'train_size': len(y_train),
        'val_size': len(y_val),
        'test_size': len(y_test),
        'up_threshold': args.up_threshold,
        'down_threshold': args.down_threshold,
        'max_lookforward_minutes': args.max_lookforward_minutes,
        'timeframe_minutes': args.timeframe_minutes,
        'source_database': args.db_path,
        'interval_filter': args.interval,
        'market_filter': args.market,
        'processed_tables': [t['table_name'] for t in table_list]
    }
    with open(config_file, 'w') as f:
        json.dump(config_data, f, indent=4)
    print(f"通用配置文件已保存到: {config_file}")

    print(f"\n✅ 训练完成！")
    print(f"📊 处理了 {len(table_list)} 个数据表")
    print(f"🎯 预测阈值: {args.best_threshold}")
    print(f"📈 上涨阈值: {args.up_threshold}, 下跌阈值: {args.down_threshold}")
    print(f"⏰ 最大前瞻时间: {args.max_lookforward_minutes} 分钟")


if __name__ == '__main__':
    main()